// Test script to spawn a zombie in the game
// Run this in the browser console

// Function to spawn a zombie in front of the player
function spawnZombie() {
    // Check if gameInstance exists
    if (!window.gameInstance || !window.gameInstance.dungeonHandler) {
        console.log('Game not fully initialized yet. Waiting...');
        // Try again in 1 second
        setTimeout(spawnZombie, 1000);
        return;
    }
    
    // Get the DungeonHandler instance
    const dungeonHandler = window.gameInstance.dungeonHandler;
    
    // Get player position
    const playerPosition = dungeonHandler.player.position.clone();
    
    // Spawn zombie 5 units in front of the player
    const spawnPosition = playerPosition.clone();
    spawnPosition.z += 5; // Assuming player is facing +Z
    
    console.log('Spawning zombie at position:', spawnPosition);
    
    // Spawn the zombie
    const zombie = dungeonHandler._spawnEnemy('zombie', spawnPosition);
    
    if (zombie) {
        console.log('Zombie spawned successfully!');
    } else {
        console.error('Failed to spawn zombie!');
    }
    
    return zombie;
}

// Function to spawn multiple enemies for comparison
function spawnEnemies() {
    // Check if gameInstance exists
    if (!window.gameInstance || !window.gameInstance.dungeonHandler) {
        console.log('Game not fully initialized yet. Waiting...');
        // Try again in 1 second
        setTimeout(spawnEnemies, 1000);
        return;
    }
    
    // Get the DungeonHandler instance
    const dungeonHandler = window.gameInstance.dungeonHandler;
    
    // Get player position
    const playerPosition = dungeonHandler.player.position.clone();
    
    // Spawn zombie
    const zombiePos = playerPosition.clone();
    zombiePos.z += 5;
    zombiePos.x -= 3;
    const zombie = dungeonHandler._spawnEnemy('zombie', zombiePos);
    
    // Spawn skeleton archer
    const skeletonPos = playerPosition.clone();
    skeletonPos.z += 5;
    const skeleton = dungeonHandler._spawnEnemy('skeleton_archer', skeletonPos);
    
    // Spawn bat
    const batPos = playerPosition.clone();
    batPos.z += 5;
    batPos.x += 3;
    const bat = dungeonHandler._spawnEnemy('bat', batPos);
    
    console.log('Spawned all enemies for comparison');
    
    return { zombie, skeleton, bat };
}

// Execute the function with a slight delay to ensure the game is loaded
console.log('Waiting for game to initialize...');
setTimeout(spawnZombie, 3000);

// Log instructions for spawning multiple enemies
console.log('To spawn multiple enemies for comparison, run: spawnEnemies()');
