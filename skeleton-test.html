<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skeleton Animation Test</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #000; }
        canvas { display: block; width: 100%; height: 100%; }
        #controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            color: white;
            font-family: Arial, sans-serif;
        }
        button {
            margin: 5px;
            padding: 5px 10px;
            cursor: pointer;
        }
        .active {
            background-color: #4CAF50;
            color: white;
        }
    </style>
</head>
<body>
    <div id="controls">
        <h3>Skeleton Animation Test</h3>
        <div>
            <button id="idle-btn" class="active">Idle</button>
            <button id="walk-btn">Walk</button>
            <button id="aim-btn">Aim</button>
            <button id="shoot-btn">Shoot</button>
            <button id="hit-btn">Hit Reaction</button>
            <button id="dodge-btn">Dodge</button>
        </div>
        <div>
            <button id="rotate-left-btn">Rotate Left</button>
            <button id="rotate-right-btn">Rotate Right</button>
            <button id="reset-view-btn">Reset View</button>
        </div>
        <div>
            <button id="spawn-archer-btn">Spawn Archer</button>
            <button id="spawn-warrior-btn">Spawn Warrior</button>
            <button id="spawn-assassin-btn">Spawn Assassin</button>
            <button id="spawn-boss-btn">Spawn Boss</button>
        </div>
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "./libs/three/build/three.module.js",
                "three/addons/": "./libs/three/examples/jsm/"
            }
        }
    </script>
    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { createSkeletonEnemyModel } from './src/generators/prefabs/skeletonEnemy.js';
        import { AIStates } from './src/ai/AIStates.js';

        // Scene setup
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x111111);
        
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 2, 5);
        
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        document.body.appendChild(renderer.domElement);
        
        // Controls
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.target.set(0, 1, 0);
        controls.update();
        
        // Lighting
        const ambientLight = new THREE.AmbientLight(0x404040);
        scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 1024;
        directionalLight.shadow.mapSize.height = 1024;
        scene.add(directionalLight);
        
        // Ground
        const groundGeometry = new THREE.PlaneGeometry(20, 20);
        const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        scene.add(ground);
        
        // Skeleton model
        let skeleton = null;
        let leftLeg = null;
        let rightLeg = null;
        let leftArm = null;
        let rightArm = null;
        let currentState = AIStates.IDLE;
        let animationTime = 0;
        let timer = 0;
        
        // Spawn skeleton
        function spawnSkeleton(type = 'archer', scale = 2.5) {
            // Remove existing skeleton if any
            if (skeleton) {
                scene.remove(skeleton);
            }
            
            // Create new skeleton
            skeleton = createSkeletonEnemyModel(scale);
            skeleton.position.set(0, 0, 0);
            skeleton.castShadow = true;
            skeleton.receiveShadow = true;
            
            // Get limbs
            leftLeg = skeleton.getObjectByName('leftLeg');
            rightLeg = skeleton.getObjectByName('rightLeg');
            leftArm = skeleton.getObjectByName('leftArm');
            rightArm = skeleton.getObjectByName('rightArm');
            
            // Set type-specific properties
            if (type === 'warrior') {
                skeleton.name = 'SkeletonWarrior';
                skeleton.userData.speed = 1.8;
            } else if (type === 'assassin') {
                skeleton.name = 'SkeletonAssassin';
                skeleton.userData.speed = 2.0;
            } else if (type === 'boss') {
                skeleton.name = 'SkeletonBoss';
                skeleton.userData.speed = 1.6;
            } else {
                skeleton.name = 'SkeletonArcher';
                skeleton.userData.speed = 1.5;
            }
            
            scene.add(skeleton);
            
            // Reset animation state
            currentState = AIStates.IDLE;
            updateButtonStates();
        }
        
        // Animation functions
        function applyIdleAnimation() {
            if (!leftLeg || !rightLeg) return;
            
            const idleSpeed = 1.5;
            const idleBobAmplitude = 0.05;
            const idleSwayAmplitude = Math.PI / 32;
            
            // Subtle idle movement
            const bobOffset = Math.sin(animationTime * idleSpeed) * idleBobAmplitude;
            const swayOffset = Math.sin(animationTime * idleSpeed * 0.7) * idleSwayAmplitude;
            
            // Apply to limbs
            if (leftLeg) leftLeg.rotation.x = swayOffset;
            if (rightLeg) rightLeg.rotation.x = -swayOffset;
            if (leftArm) leftArm.rotation.x = swayOffset * 1.5;
            if (rightArm) rightArm.rotation.x = -swayOffset * 1.5;
            
            // Add slight body movement
            if (skeleton) skeleton.position.y = bobOffset * 0.1;
        }
        
        function applyWalkAnimation() {
            if (!leftLeg || !rightLeg) return;
            
            const walkSpeed = skeleton.userData.speed * 2.0;
            const walkAmplitude = Math.PI / 10;
            
            // Walk cycle
            if (leftLeg) leftLeg.rotation.x = Math.sin(animationTime * walkSpeed) * walkAmplitude;
            if (rightLeg) rightLeg.rotation.x = Math.sin(animationTime * walkSpeed + Math.PI) * walkAmplitude;
            
            // Arm swing
            if (leftArm) leftArm.rotation.x = Math.sin(animationTime * walkSpeed + Math.PI) * walkAmplitude * 0.5;
            if (rightArm) rightArm.rotation.x = Math.sin(animationTime * walkSpeed) * walkAmplitude * 0.5;
        }
        
        function applyAimAnimation() {
            if (!leftLeg || !rightLeg) return;
            
            const idleSwayAmplitude = Math.PI / 64; // Reduced sway
            const swayOffset = Math.sin(animationTime * 1.0) * idleSwayAmplitude; // Slower sway
            
            // Add slight breathing animation to prevent looking frozen
            const breathingOffset = Math.sin(animationTime * 1.5) * 0.005;
            if (skeleton) skeleton.position.y = breathingOffset;
            
            // Position arms for aiming
            if (leftArm) {
                leftArm.rotation.set(Math.PI / 8, 0, 0); // Extend arm forward
            }
            
            if (rightArm) {
                // Calculate pullback using rotation.y based on timer
                const aimDuration = 0.5; // Default aim duration
                const pullbackProgress = Math.min(1.0, timer / aimDuration); // Ensure progress is 0 to 1
                const pullbackAngleY = pullbackProgress * -Math.PI / 32; // Apply the minimal pullback angle
                
                // Add time-based animation to prevent getting stuck
                const armSwayY = Math.sin(animationTime * 0.9) * (Math.PI / 128);
                rightArm.rotation.set(0, pullbackAngleY + armSwayY, -swayOffset); // No X rotation, apply Y pullback, minimal sway
            }
            
            // Keep legs neutral during aiming but add slight movement
            if (leftLeg) leftLeg.rotation.set(Math.sin(animationTime * 0.7) * 0.02, 0, 0);
            if (rightLeg) rightLeg.rotation.set(Math.sin(animationTime * 0.7 + Math.PI) * 0.02, 0, 0);
            
            // Increment timer for pullback animation
            timer += 0.016; // Approximately 60fps
            if (timer > 0.5) timer = 0.5; // Cap at max pullback
        }
        
        function applyShootAnimation() {
            if (!leftLeg || !rightLeg) return;
            
            // Release animation for shooting with slight continuous movement
            if (leftArm) {
                const armSwayX = Math.sin(animationTime * 0.8) * (Math.PI / 128);
                leftArm.rotation.set(-Math.PI / 16 + armSwayX, 0, 0); // Steady with slight movement
            }
            
            if (rightArm) {
                const armSwayY = Math.sin(animationTime * 0.9) * (Math.PI / 128);
                rightArm.rotation.set(0, Math.PI / 16 + armSwayY, 0); // Follow through with slight movement
            }
            
            // Stable stance with minimal movement
            if (leftLeg) leftLeg.rotation.set(Math.sin(animationTime * 0.7) * 0.01, 0, 0);
            if (rightLeg) rightLeg.rotation.set(Math.sin(animationTime * 0.7 + Math.PI) * 0.01, 0, 0);
        }
        
        function applyHitReactionAnimation() {
            if (!leftLeg || !rightLeg) return;
            
            // Stagger backwards
            if (leftLeg) leftLeg.rotation.x = -Math.PI / 8; // Leg back
            if (rightLeg) rightLeg.rotation.x = -Math.PI / 6; // Leg further back
            
            // Arms flail slightly
            if (leftArm) {
                leftArm.rotation.x = Math.PI / 4; // Arm up
                leftArm.rotation.z = -Math.PI / 8; // Arm out
            }
            
            if (rightArm) {
                rightArm.rotation.x = Math.PI / 3; // Arm up higher
                rightArm.rotation.z = Math.PI / 6; // Arm out
            }
        }
        
        function applyDodgeAnimation() {
            if (!leftLeg || !rightLeg) return;
            
            // Dodge to the side (right)
            if (leftLeg) leftLeg.rotation.set(Math.PI / 6, 0, Math.PI / 12); // Leg bent, angled out
            if (rightLeg) rightLeg.rotation.set(-Math.PI / 8, 0, Math.PI / 8); // Pushing off
            
            // Arms for balance
            if (leftArm) leftArm.rotation.set(0, 0, -Math.PI / 6); // Out for balance
            if (rightArm) rightArm.rotation.set(Math.PI / 8, 0, Math.PI / 4); // Up for balance
            
            // Slight body tilt
            if (skeleton) {
                skeleton.rotation.z = -Math.PI / 16; // Tilt body
                
                // Dodge movement
                const dodgeProgress = (timer % 0.5) / 0.5; // 0 to 1 over 0.5 seconds
                const dodgeOffset = Math.sin(dodgeProgress * Math.PI) * 0.5; // Peak in middle
                skeleton.position.x = dodgeOffset;
            }
            
            // Increment timer for dodge animation
            timer += 0.016; // Approximately 60fps
            if (timer > 0.5) timer = 0; // Reset for continuous dodge
        }
        
        // Animation update
        function updateAnimation() {
            animationTime += 0.016; // Approximately 60fps
            
            switch (currentState) {
                case AIStates.IDLE:
                    applyIdleAnimation();
                    break;
                case AIStates.MOVING:
                    applyWalkAnimation();
                    break;
                case AIStates.AIMING:
                    applyAimAnimation();
                    break;
                case AIStates.SHOOTING:
                    applyShootAnimation();
                    break;
                case AIStates.KNOCKBACK:
                    applyHitReactionAnimation();
                    break;
                case AIStates.DODGING:
                    applyDodgeAnimation();
                    break;
                default:
                    // Reset pose for other states
                    if (leftLeg) leftLeg.rotation.set(0, 0, 0);
                    if (rightLeg) rightLeg.rotation.set(0, 0, 0);
                    if (leftArm) leftArm.rotation.set(0, 0, 0);
                    if (rightArm) rightArm.rotation.set(0, 0, 0);
                    break;
            }
        }
        
        // Button event handlers
        function updateButtonStates() {
            document.querySelectorAll('#controls button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            switch (currentState) {
                case AIStates.IDLE:
                    document.getElementById('idle-btn').classList.add('active');
                    break;
                case AIStates.MOVING:
                    document.getElementById('walk-btn').classList.add('active');
                    break;
                case AIStates.AIMING:
                    document.getElementById('aim-btn').classList.add('active');
                    break;
                case AIStates.SHOOTING:
                    document.getElementById('shoot-btn').classList.add('active');
                    break;
                case AIStates.KNOCKBACK:
                    document.getElementById('hit-btn').classList.add('active');
                    break;
                case AIStates.DODGING:
                    document.getElementById('dodge-btn').classList.add('active');
                    break;
            }
        }
        
        document.getElementById('idle-btn').addEventListener('click', () => {
            currentState = AIStates.IDLE;
            timer = 0;
            updateButtonStates();
        });
        
        document.getElementById('walk-btn').addEventListener('click', () => {
            currentState = AIStates.MOVING;
            timer = 0;
            updateButtonStates();
        });
        
        document.getElementById('aim-btn').addEventListener('click', () => {
            currentState = AIStates.AIMING;
            timer = 0;
            updateButtonStates();
        });
        
        document.getElementById('shoot-btn').addEventListener('click', () => {
            currentState = AIStates.SHOOTING;
            timer = 0;
            updateButtonStates();
        });
        
        document.getElementById('hit-btn').addEventListener('click', () => {
            currentState = AIStates.KNOCKBACK;
            timer = 0;
            updateButtonStates();
        });
        
        document.getElementById('dodge-btn').addEventListener('click', () => {
            currentState = AIStates.DODGING;
            timer = 0;
            updateButtonStates();
        });
        
        document.getElementById('rotate-left-btn').addEventListener('click', () => {
            if (skeleton) skeleton.rotation.y += Math.PI / 8;
        });
        
        document.getElementById('rotate-right-btn').addEventListener('click', () => {
            if (skeleton) skeleton.rotation.y -= Math.PI / 8;
        });
        
        document.getElementById('reset-view-btn').addEventListener('click', () => {
            if (skeleton) skeleton.rotation.y = Math.PI;
            if (skeleton) skeleton.rotation.z = 0;
            if (skeleton) skeleton.position.x = 0;
            controls.reset();
        });
        
        document.getElementById('spawn-archer-btn').addEventListener('click', () => {
            spawnSkeleton('archer');
        });
        
        document.getElementById('spawn-warrior-btn').addEventListener('click', () => {
            spawnSkeleton('warrior');
        });
        
        document.getElementById('spawn-assassin-btn').addEventListener('click', () => {
            spawnSkeleton('assassin');
        });
        
        document.getElementById('spawn-boss-btn').addEventListener('click', () => {
            spawnSkeleton('boss', 3.5);
        });
        
        // Window resize handler
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            if (skeleton) {
                updateAnimation();
            }
            
            controls.update();
            renderer.render(scene, camera);
        }
        
        // Initialize
        spawnSkeleton('archer');
        animate();
    </script>
</body>
</html>
