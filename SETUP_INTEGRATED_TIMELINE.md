# Setup Catacomb Boss with Epic Battle Timeline (Integrated Mode)

This document provides a direct command to set up the catacomb boss with the epic battle timeline in integrated mode, which combines the timeline with music sync.

## Direct Setup Command

Copy and paste this entire command into your browser console (F12) when you're in the catacomb boss room:

```javascript
// Direct setup command for catacomb boss with epic battle timeline in integrated mode
(function setupCatacombBossIntegrated() {
    try {
        // Get references to key objects
        const boss = game.dungeonHandler.currentRoom.boss;
        if (!boss) {
            console.error("[SETUP] No boss found in current room. Try teleporting to the boss first with F10.");
            return;
        }
        
        const bossController = boss.bossController;
        if (!bossController) {
            console.error("[SETUP] Boss controller not found.");
            return;
        }
        
        // Load the timeline in integrated mode (false = use timeline patterns with music sync triggers)
        const success = bossController.loadTimeline("epic_battle", false);
        if (!success) {
            console.error("[SETUP] Failed to load timeline.");
            return;
        }
        
        // Verify timeline is loaded
        const timeline = bossController.musicTimeline;
        console.log("[SETUP] Timeline loaded:", bossController.currentTimelineName);
        console.log("[SETUP] Timeline entries:", timeline.timeline.length);
        console.log("[SETUP] First entry:", timeline.timeline[0].description);
        
        // Force an update to the current music time
        const musicTime = bossController.musicAnalyzer.getCurrentTime();
        timeline.update(musicTime);
        
        // Verify current state
        console.log("[SETUP] Current music time:", musicTime);
        console.log("[SETUP] Current timeline time:", timeline.currentTime);
        console.log("[SETUP] Current pattern:", timeline.currentPattern);
        console.log("[SETUP] Current projectile:", timeline.currentProjectileType);
        
        // Success message
        console.log("[SETUP] Catacomb boss successfully set up with epic battle timeline in integrated mode!");
        console.log("[SETUP] The boss will use timeline patterns triggered by music sync.");
        
    } catch (error) {
        console.error("[SETUP] Error setting up catacomb boss:", error);
    }
})();
```

## How to Use

1. Start a game and enter the catacomb boss room
   - If you're not at the boss, press F10 or T to teleport there
2. Press F12 to open the browser's developer console
3. Copy the entire command above and paste it into the console
4. Press Enter to execute the command
5. Check the console for confirmation messages

## What to Expect

In integrated mode:

1. The timeline will determine WHICH patterns and projectiles to use at each time point
2. The music sync system will determine WHEN to trigger those patterns based on beats and melody
3. You'll see debug messages in the console showing the current timeline section, pattern, and projectile type
4. The patterns will change automatically as the music progresses through the timeline sections

## Timeline Structure

The epic battle timeline includes these sections:

1. **0-20 seconds**: Mysterious intro with petal spread patterns and shadow bolts
2. **20-40 seconds**: Building tension with circle ripple patterns and shadow bolts
3. **40-60 seconds**: First attack phase with laser grid patterns and lightning bolts
4. **60-80 seconds**: Increasing intensity with spiral wave patterns and fireballs
5. **80-100 seconds**: Complex attack patterns with boomerang arcs and fireballs
6. **100-120 seconds**: Fast chain attacks with lightning chain patterns and lightning bolts
7. **120-140 seconds**: Climax with hellburst patterns and lightning bolts (most intense section)
8. **140-160 seconds**: Breathing phase with inhale-exhale patterns and poison clouds
9. **160-180 seconds**: Second wave with spiral wave patterns and fireballs
10. **180-200 seconds**: Winding down with circle ripple patterns and shadow bolts
11. **200-220 seconds**: Gentle finish with petal spread patterns and shadow bolts

## Troubleshooting

If you encounter issues:

1. Make sure you're in a room with a boss
2. Try teleporting to the boss with F10 before running the command
3. Check the console for error messages
4. Try reloading the page and running the command again
