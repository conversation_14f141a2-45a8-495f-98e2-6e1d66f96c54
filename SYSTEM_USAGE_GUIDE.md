# System Usage Guide

This guide explains how to use and extend various systems in the game, including Item Drops, Enemy Spawns, and the Adaptive Music System.

## Table of Contents

1. [Adding New Items](#adding-new-items)
2. [Defining Enemy Spawns](#defining-enemy-spawns)
3. [Customizing Drop Rates](#customizing-drop-rates)
4. [Testing Your Changes](#testing-your-changes)
5. [Adaptive Music System](#adaptive-music-system)

## Adding New Items

### Step 1: Define the Item Type

Add your new item to the `ITEM_TYPES` object in `src/entities/ItemTypes.js`:

```javascript
export const ITEM_TYPES = {
    // Existing items...

    // Add your new item
    MY_NEW_ITEM: 'my_new_item',
};
```

### Step 2: Define the Item Data

Add the item data to the `ITEM_DATA` object in the same file:

```javascript
const ITEM_DATA = {
    // Existing items...

    // Add your new item
    my_new_item: {
        name: 'My New Item',
        description: 'This is a description of my new item',
        category: ITEM_CATEGORY.WEAPON, // Choose an appropriate category
        rarity: ITEM_RARITY.RARE, // Choose an appropriate rarity
        effect: 'custom_effect', // Define what the item does
        effectValue: 10, // The value/strength of the effect
        voxelModel: 'my_new_item', // The model name (must match in voxelItemModels.js)
        allowedBiomes: ['crystal_caves', 'any'], // Biomes where this item can drop
        allowedRoomTypes: ['normal', 'elite', 'boss'], // Room types where this item can drop
        glow: {
            color: 0xff00ff, // Hexadecimal color for the glow
            intensity: 1.2 // Glow intensity
        },
        blockedBy: [], // Story progression items that block this item
        soulWeightInfluence: 'neutral' // How soul weight affects this item's drop chance
    },
};
```

### Step 3: Create the Voxel Model

Add the voxel model definition to `src/generators/prefabs/voxelItemModels.js`:

```javascript
const VOXEL_MODELS = {
    // Existing models...

    // Add your new item model
    my_new_item: {
        voxels: [
            // Define the voxels that make up your item
            // Each voxel is defined by its position and color
            { x: 0, y: 0, z: 0, color: COLORS.RED },
            { x: 1, y: 0, z: 0, color: COLORS.RED },
            { x: 0, y: 1, z: 0, color: COLORS.BLUE },
            { x: 1, y: 1, z: 0, color: COLORS.BLUE },
            // Add more voxels as needed
        ],
        scale: 0.1 // Adjust scale as needed
    },
};
```

### Step 4: Add the Item to Item Pools

Add your item to the appropriate pools in `src/gameData/ItemPools.js`:

```javascript
// Add to biome-specific pools
export const BIOME_ITEM_POOLS = {
    // Existing biomes...
    crystal_caves: [
        // Existing items...
        ITEM_TYPES.MY_NEW_ITEM, // Add your new item
    ],
};

// Add to room type-specific pools
export const ROOM_TYPE_ITEM_POOLS = {
    // Existing room types...
    boss: [
        // Existing items...
        ITEM_TYPES.MY_NEW_ITEM, // Add your new item
    ],
};

// Add to enemy type-specific pools (optional)
export const ENEMY_TYPE_ITEM_POOLS = {
    // Existing enemy types...
    skeleton_boss: [
        // Existing items...
        ITEM_TYPES.MY_NEW_ITEM, // Add your new item
    ],
};
```

### Step 5: Implement the Item Effect

Add the effect handling in the `applyEffect` method in `src/entities/Item.js`:

```javascript
applyEffect(player) {
    if (!player) return;

    const { effect, effectValue } = this.itemData;

    switch (effect) {
        // Existing effects...

        // Add your new effect
        case 'custom_effect':
            // Implement your custom effect
            console.log(`Applied custom effect with value ${effectValue}`);
            // Example: player.customStat += effectValue;
            break;

        default:
            console.warn(`Unknown effect: ${effect}`);
    }
}
```

## Defining Enemy Spawns

### Step 1: Define Enemy Roles

If your new enemy has a specific role, add it to the `ENEMY_ROLE_MAP` in `src/systems/EnemySpawnManager.js`:

```javascript
const ENEMY_ROLE_MAP = {
    // Existing enemies...

    // Add your new enemy
    my_new_enemy: ENEMY_ROLES.HEAVY, // Choose an appropriate role
};
```

### Step 2: Add to Biome-Specific Enemy Pools

Add your enemy to the appropriate biome pools in `src/systems/EnemySpawnManager.js`:

```javascript
const BIOME_ENEMY_POOLS = {
    // Existing biomes...
    crystal_caves: {
        // Existing roles...
        [ENEMY_ROLES.HEAVY]: ['skeleton_warrior', 'my_new_enemy'], // Add your new enemy
        // Other roles...
    },
};
```

### Step 3: Define Enemy Data

Make sure your enemy is defined in `src/entities/EnemyTypes.js`:

```javascript
const ENEMY_DATA = {
    // Existing enemies...

    // Add your new enemy
    my_new_enemy: {
        name: 'My New Enemy',
        health: 50,
        damage: 10,
        speed: 1.5,
        attackRange: 1.2,
        attackSpeed: 1.0,
        aiType: AI_BRAIN_TYPES.MELEE,
        difficulty: 2, // 1 = normal, 2 = elite, 3 = mini-boss, 4 = boss
        // Other properties...
    },
};
```

### Step 4: Create Predefined Enemy Groups (Optional)

If you want to create predefined enemy groups for specific rooms, you can do so in the floor layout:

```javascript
// In your floor layout definition
const roomData = {
    id: 5,
    type: 'normal',
    areaId: 'crystal_caves',
    floorLevel: 3,
    state: {
        initialEnemies: ['skeleton', 'my_new_enemy', 'bat'], // Predefined enemy group
        enemiesCleared: false
    }
};
```

## Customizing Drop Rates

### Adjusting Rarity Weights

Modify the `RARITY_WEIGHTS` object in `src/gameData/ItemPools.js` to adjust drop rates for different rarities:

```javascript
export const RARITY_WEIGHTS = {
    // Existing weights...

    // Modify weights for a specific context
    normal_enemy: {
        [ITEM_RARITY.COMMON]: 85, // Changed from 90 to 85
        [ITEM_RARITY.RARE]: 15,   // Changed from 10 to 15
        [ITEM_RARITY.EPIC]: 0,
        [ITEM_RARITY.LEGENDARY]: 0
    },
};
```

### Adjusting Soul Weight Influence

Modify the `SOUL_WEIGHT_MODIFIERS` object to adjust how soul weight affects item drops:

```javascript
export const SOUL_WEIGHT_MODIFIERS = {
    // Existing modifiers...

    // Modify light soul weight modifiers
    light: {
        light: 2.5,    // Changed from 2.0 to 2.5
        dark: 0.5,
        balanced: 1.0,
        neutral: 1.0
    },
};
```

### Adjusting Relic Influence

Modify the `RELIC_MODIFIERS` object to adjust how relics affect item drops:

```javascript
export const RELIC_MODIFIERS = {
    // Existing modifiers...

    // Modify or add a relic modifier
    zeituhr: {
        modifiedItems: [
            ITEM_TYPES.SOUL_BLADE,
            ITEM_TYPES.SPECTRAL_BOW,
            ITEM_TYPES.MY_NEW_ITEM // Add your new item
        ],
        modifier: 2.0 // Changed from 1.5 to 2.0
    },
};
```

## Testing Your Changes

### Using the Test Script

1. Add your new items and enemies to the test cases in `test-item-drop.js`:

```javascript
const testCases = [
    // Existing test cases...

    // Add a test case for your new item/enemy
    {
        enemyType: 'my_new_enemy',
        roomType: 'elite',
        biome: 'crystal_caves',
        seed: 56789
    },
];
```

2. Open `test-item-drop.html` in a web browser to run the tests.

### Testing in the Game

1. Make sure your changes are properly integrated with the DungeonHandler.

2. Run the game and check the console for debug output:

```javascript
// Enable debug mode
itemDropManager.setDebugMode(true);
enemySpawnManager.setDebugMode(true);
```

3. Verify that your items drop correctly and enemies spawn as expected.

## Advanced Usage

### Creating Custom Drop Logic

If you need custom drop logic beyond what the system provides, you can extend the `getRandomDrop` method in `ItemDropManager.js`:

```javascript
// Example of custom drop logic
getRandomDrop(enemyType, roomType, biome, seed = Date.now()) {
    // Special case for a specific enemy type
    if (enemyType === 'my_special_enemy') {
        // Always drop a specific item
        return ITEM_TYPES.MY_NEW_ITEM;
    }

    // Otherwise use the standard logic
    // ... (existing code)
}
```

### Creating Custom Enemy Group Composition

If you need custom enemy group composition, you can extend the `chooseEnemyGroup` method in `EnemySpawnManager.js`:

```javascript
// Example of custom enemy group composition
chooseEnemyGroup(context) {
    const { biome, roomType, floorLevel, seed } = context;

    // Special case for a specific room type and biome
    if (roomType === 'special' && biome === 'crystal_caves') {
        return ['my_new_enemy', 'skeleton', 'skeleton'];
    }

    // Otherwise use the standard logic
    // ... (existing code)
}
```

## Troubleshooting

### Item Not Dropping

1. Check if the item is properly defined in `ItemTypes.js`.
2. Verify that the item is added to the appropriate item pools.
3. Check if the item is blocked by any story progression items.
4. Enable debug mode to see detailed drop information:
   ```javascript
   itemDropManager.setDebugMode(true);
   ```

### Enemy Not Spawning

1. Check if the enemy is properly defined in `EnemyTypes.js`.
2. Verify that the enemy is added to the appropriate biome pools.
3. Check if the enemy role is correctly mapped.
4. Enable debug mode to see detailed spawn information:
   ```javascript
   enemySpawnManager.setDebugMode(true);
   ```

### Visual Issues with Items

1. Check if the voxel model is correctly defined in `voxelItemModels.js`.
2. Verify that the colors and scale are appropriate.
3. Check if the glow effect is properly configured.

## Conclusion

This guide covers the basics of using and extending the Item Drop and Enemy Spawn systems. By following these steps, you can add new items and enemies to the game, customize drop rates, and test your changes.

For more advanced usage, refer to the source code and comments in the relevant files:

- `src/entities/ItemTypes.js`
- `src/gameData/ItemPools.js`
- `src/entities/Item.js`
- `src/generators/prefabs/voxelItemModels.js`
- `src/systems/ItemDropManager.js`
- `src/systems/EnemySpawnManager.js`

## Adaptive Music System

The Adaptive Music System provides dynamic, context-aware music that responds to gameplay events and player actions. Inspired by LucasArts' iMUSE system, it supports seamless transitions, leitmotifs, and real-time audio effects.

### Key Features

- Seamless transitions between areas and sub-rooms
- Story-driven leitmotif fades
- Player and enemy-based real-time music reactions
- Tempo shifting without pitch distortion
- FX-based dynamics for a single music track
- Automatic documentation for future content expansion

### Adding New Area Music

#### Step 1: Create Music Files

Create the necessary music files for your area:

1. Main loop: `assets/music/your_area/loop.mp3`
2. Transitions (optional): `assets/music/your_area/transitions/to_other_area.mp3`
3. Leitmotifs (optional): `assets/music/your_area/leitmotifs/your_leitmotif.mp3`
4. Special room music (optional): `assets/music/your_area/specialRooms/room_type/loop.mp3`

#### Step 2: Update Music Data

Add your area to the `MusicData` object in `src/audio/musicData.js`:

```javascript
export const MusicData = {
    // Existing areas...

    "your_area": {
        loop: "assets/music/your_area/loop.mp3",
        tempo: 100, // BPM
        leitmotifs: {
            "your_leitmotif": "assets/music/your_area/leitmotifs/your_leitmotif.mp3"
        },
        specialRooms: {
            "shop": {
                loop: "assets/music/your_area/specialRooms/shop/loop.mp3",
                enterTransition: "assets/music/your_area/specialRooms/shop/enter.mp3",
                exitTransition: "assets/music/your_area/specialRooms/shop/exit.mp3"
            }
        },
        transitions: {
            "other_area": "assets/music/your_area/transitions/to_other_area.mp3"
        }
    }
};
```

### Adding New Stingers

#### Step 1: Create Stinger File

Create your stinger audio file in `assets/music/stingers/your_stinger.mp3`.

#### Step 2: Update Music Data

Add your stinger to the `stingers` object in `src/audio/musicData.js`:

```javascript
export const MusicData = {
    // Existing areas...

    "stingers": {
        // Existing stingers...
        "your_stinger": "assets/music/stingers/your_stinger.mp3"
    }
};
```

#### Step 3: Trigger the Stinger

Trigger your stinger in code when appropriate:

```javascript
// In your game code
if (someCondition) {
    audioManager.musicConductor.triggerStinger("your_stinger");
}
```

### Using Leitmotifs

Leitmotifs are musical themes associated with characters, places, or concepts. They can be layered on top of the main area music.

```javascript
// Activate a leitmotif
audioManager.musicConductor.setLeitmotifActive("your_leitmotif", true, 2.0); // name, active, fadeTime

// Deactivate a leitmotif
audioManager.musicConductor.setLeitmotifActive("your_leitmotif", false, 1.0);
```

### Responding to Combat

The music system automatically responds to combat by adjusting tempo and applying effects based on enemy count and player health. This is handled in `DungeonHandler.js`.

If you need to manually update the combat state:

```javascript
audioManager.updateCombatState(enemyCount, currentHealth, maxHealth);
```

#### Health-Based Filtering

The music system applies a low-pass filter based on player health:

- **Health 10 or above**: Filter fully open (20kHz) - normal sound
- **Health below 10**: Filter gradually closes as health decreases
- **Health 0**: Filter heavily closed (100Hz) - extremely muffled sound

This creates an immersive effect where the music becomes more muffled as the player's health decreases, adding tension to low-health situations.

### Handling Player Damage

When the player takes damage, the music system applies a temporary distortion effect and plays a stinger. This is automatically triggered in `PlayerController.js`.

### Debugging the Music System

Press 'M' in-game to toggle the music debug HUD, which shows:

- Current area and special room
- Current tempo and tempo stretch
- Active leitmotifs
- Combat state
- Health state
- Transition state
- Waveform display with bar indicator

### Advanced: Creating Custom Effects

To create custom audio effects, modify the `createMusicEffects` function in `src/audio/MusicEffects.js`:

```javascript
export function createMusicEffects() {
    // Existing effects...

    // Add your custom effect
    const yourCustomEffect = new Tone.YourEffect({
        // Effect parameters
    });

    // Add to appropriate chain
    mainChain.chain(yourCustomEffect, mainVolume, Tone.Destination);

    // Return in effects object
    return {
        // Existing effects...
        yourCustomEffect,
        // Existing chains...
    };
}
```

### Troubleshooting

#### Music Not Playing

1. Check browser console for errors
2. Verify that music files exist and are correctly referenced in `musicData.js`
3. Ensure that the music system is initialized after user interaction
4. Check if the audio is muted

#### Transitions Not Working

1. Verify that transition files exist and are correctly referenced
2. Check that area IDs match between the game and `musicData.js`
3. Enable the debug HUD to see the current state

#### Audio Effects Not Working

1. Check if Tone.js is properly loaded
2. Verify that the effects chain is correctly set up
3. Check browser compatibility with Web Audio API

### Conclusion

The Adaptive Music System provides a powerful way to enhance the game's atmosphere and respond to gameplay events. By following these steps, you can add new music, stingers, and leitmotifs to create a dynamic audio experience.

For more details, refer to the source code and comments in the relevant files:

- `src/audio/MusicConductor.js`
- `src/audio/musicData.js`
- `src/audio/MusicEffects.js`
- `src/audio/MusicDebugHUD.js`
- `assets/music/README.md`