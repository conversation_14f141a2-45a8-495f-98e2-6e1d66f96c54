# Item Drop and Enemy Spawn Systems

This document provides an overview of the newly implemented Item Drop and Enemy Spawn systems for the game.

## Item Drop System

The Item Drop System handles the generation, filtering, and spawning of items in the game world. It supports:

- Biome-specific item pools
- Room-type-specific item pools
- Enemy-type-specific item pools
- Rarity-based drop chances
- Soul weight influence on drop probabilities
- Active relic influence on drop probabilities
- Voxel representation of items in the world
- Item collection and effect application

### Key Components

1. **ItemTypes.js**: Defines all item types, rarities, and properties.
2. **ItemPools.js**: Defines item pools for different biomes, room types, and enemy types.
3. **Item.js**: Represents an item in the game world that can be picked up by the player.
4. **voxelItemModels.js**: Generates voxel models for items.
5. **ItemDropManager.js**: Manages the item drop system, including drop generation, filtering, and spawning.

### Usage

```javascript
// Get a random item drop based on context
const itemType = itemDropManager.getRandomDrop(enemyType, roomType, biome, seed);

// Spawn an item in the world
const item = itemDropManager.spawnVoxelItem(scene, itemType, position);

// Collect an item and apply its effect
const collectedItem = itemDropManager.collectItem(item, player);
```

## Enemy Spawn System

The Enemy Spawn System handles the generation of balanced enemy groups for rooms. It supports:

- Biome-specific enemy pools
- Room-type-specific enemy groups
- Floor level difficulty scaling
- Role-based enemy group composition
- Synergistic enemy groups

### Key Components

1. **EnemySpawnManager.js**: Manages the enemy spawn system, including group composition and difficulty scaling.

### Usage

```javascript
// Set the current floor level
enemySpawnManager.setFloorLevel(floorLevel);

// Choose a balanced enemy group for a room
const enemyGroup = enemySpawnManager.chooseEnemyGroup({
    biome,
    roomType,
    floorLevel,
    seed
});

// Spawn an enemy in the world
const enemy = enemySpawnManager.spawnEnemy(dungeonHandler, enemyType, position);
```

## Integration with DungeonHandler

The systems are integrated with the DungeonHandler to:

1. Generate and spawn enemies when a room is loaded
2. Drop items when enemies are defeated
3. Allow players to collect items
4. Clean up items when rooms are changed or the game is reset

## Testing

A test script is provided to verify the functionality of both systems:

```
test-item-drop.html
test-item-drop.js
```

Run the test by opening `test-item-drop.html` in a web browser.

## Future Enhancements

1. Add more item types and enemy types
2. Implement special room types (shop, challenge, etc.)
3. Add more visual effects for item collection
4. Implement inventory UI for collected items
5. Add more complex enemy group behaviors
