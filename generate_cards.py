# generate_cards.py
import math
import json
import random
import os # Added to specify output directory

print("--- Tarot Card Generation Script ---")

# --- Define the 10 Questions and Options ---
# Mirroring the structure in main.js, with keywords for description logic
questions_data = [
    { "text": "Where do you awaken?", "options": ["Mystic Woods", "Desert Ruins", "Starlit Peaks"], "keywords": ["forest", "sand", "mountain"] },
    { "text": "Who stands by your side?", "options": ["Fire Spirit", "Shadow Cat", "Sky Drone"], "keywords": ["flame", "shadow", "steel"] },
    { "text": "What power flows through you?", "options": ["Flame Burst", "Ice Veil", "Thunder Strike"], "keywords": ["inferno", "frost", "storm"] },
    { "text": "What drives your journey?", "options": ["Revenge", "Discovery", "Honor"], "keywords": ["vengeance", "knowledge", "duty"] },
    { "text": "What's your title?", "options": ["Lost Wanderer", "Blade Seeker", "Storm Caller"], "keywords": ["nomad", "warrior", "mage"] },
    { "text": "When faced with despair, what flame flickers within you?", "options": ["Ember of Hope", "Spark of Pragmatism", "Coal of Defiance"], "keywords": ["hopeful", "calculating", "rebellious"] },
    { "text": "To gain ultimate knowledge, what price is too high?", "options": ["Betraying Oath", "Losing Self", "Sacrificing Innocent"], "keywords": ["loyal", "grounded", "merciful"] },
    { "text": "What whispers echo in the silence of your doubt?", "options": ["Past Failures", "Forbidden Power", "Profound Stillness"], "keywords": ["haunted", "tempted", "serene"] },
    { "text": "How do you view the fragile balance of creation and destruction?", "options": ["Sacred Dance", "Cycle to Manipulate", "Illusion of Chaos"], "keywords": ["preserver", "controller", "nihilist"] },
    { "text": "What's your final demand of fate?", "options": ["Eternal Peace", "Ascension", "Shatter Destiny"], "keywords": ["savior", "godling", "anarch"] }
]

# --- Naming Components ---
name_adjectives_origin = ["Mystic", "Desert", "Starlit"]
name_nouns_companion = ["Spirit", "Shadow", "Drone"]
name_nouns_power = ["Flame", "Ice", "Thunder"]
name_adjectives_drive = ["Vengeful", "Curious", "Honorable"]
name_titles_base = ["Wanderer", "Seeker", "Caller"]
name_adjectives_cope = ["Hopeful", "Pragmatic", "Defiant"]
name_concepts_price_alt = ["Oathbreaker", "Unbound", "Ruthless"]
name_concepts_doubt = ["Haunted", "Tempted", "Silent"]
name_concepts_balance = ["Guardian", "Manipulator", "Harbinger"]
name_concepts_fate = ["Peaceful", "Ascendant", "Destinybreaker"]

# --- Helper Functions ---
def get_option_indices_from_index(index, num_questions=10, num_options=3):
    """Calculates the list of option indices (0, 1, or 2) for a given card ID."""
    indices = []
    current_index = index
    max_power = num_questions - 1
    for i in range(num_questions):
        multiplier = num_options ** max_power
        safe_multiplier = max(1, multiplier) # Prevent division by zero
        option_index = math.floor(current_index / safe_multiplier)
        indices.append(option_index)
        current_index %= safe_multiplier
        max_power -= 1
    return indices

# --- Generate Name (Revised for More Variety) ---
def generate_name(option_indices):
    """Generates a more varied card name based on selected option indices."""
    # Get all components
    adj0 = name_adjectives_origin[option_indices[0]]
    noun1 = name_nouns_companion[option_indices[1]]
    noun2 = name_nouns_power[option_indices[2]]
    adj3 = name_adjectives_drive[option_indices[3]]
    title4 = name_titles_base[option_indices[4]]
    adj6 = name_adjectives_cope[option_indices[5]]
    concept7 = name_concepts_price_alt[option_indices[6]] # Assumes price paid
    concept8 = name_concepts_doubt[option_indices[7]]
    concept9 = name_concepts_balance[option_indices[8]]
    concept10 = name_concepts_fate[option_indices[9]]

    # Define more potential patterns using lambda functions
    # These combine different elements from the answers
    patterns = [
        lambda: f"{adj6} {concept9} of {adj0}",           # Hopeful Guardian of Mystic
        lambda: f"{adj3} {title4} of the {noun1}",       # Vengeful Seeker of the Shadow
        lambda: f"{concept10} {noun2} {concept8}",       # Destinybreaker Flame Haunted
        lambda: f"{concept8} {noun1} from the {adj0} Peaks" if adj0 == "Starlit" else f"{concept8} {noun1} of {adj0}", # Haunted Shadow from the Starlit Peaks / Haunted Spirit of Mystic
        lambda: f"{adj3} {concept7} {noun2}",             # Honorable Oathbreaker Ice
        lambda: f"{adj0}'s {adj6} {title4}",            # Desert's Defiant Caller
        lambda: f"The {concept9}'s {noun2}-Marked {noun1}", # The Manipulator's Thunder-Marked Spirit
        lambda: f"{title4} of {adj0} {concept10}",         # Wanderer of Mystic Destinybreaker
        lambda: f"{adj6} {noun2} {concept7}",            # Pragmatic Flame Unbound
        lambda: f"{concept8} {adj3} {title4}",             # Silent Vengeful Seeker
        lambda: f"{noun1}-Guided {concept10}",           # Drone-Guided Ascendant
        lambda: f"The {adj0} {concept7}",                 # The Starlit Ruthless
        lambda: f"{noun2} {concept9}",                   # Ice Manipulator
        lambda: f"{adj6} {adj3} {noun1}",                # Hopeful Honorable Spirit
        lambda: f"{title4} Born of {noun2}",            # Seeker Born of Thunder
    ]

    # Select a pattern based on a combination of indices for better distribution
    # Use sum of several indices modulo number of patterns
    # Using indices 1, 3, 5, 7, 9 for variety
    pattern_index = (option_indices[1] + option_indices[3] + option_indices[5] + option_indices[7] + option_indices[9]) % len(patterns)

    # Generate the name using the selected pattern's lambda function
    try:
        # Execute the chosen lambda function to create the name string
        return patterns[pattern_index]()
    except IndexError:
        # Fallback to the first pattern if index is out of bounds (should not happen with modulo)
        print(f"Warning: Pattern index {pattern_index} out of bounds for option indices {option_indices}. Using default pattern.")
        return patterns[0]()

# --- Generate Description ---
def generate_description(answers, option_indices):
    """Generates a unique card description with structural variety."""
    q_data = questions_data # Alias

    # --- Phrase Banks (using f-strings directly for simplicity here) ---
    origin_phrases = [ f"Awakened amidst the {answers[0]}", f"Hailing from the {answers[0]}", f"Their journey began in the {answers[0]}" ]
    companion_phrases = [ f"accompanied by a watchful {answers[1]}", f"forever bound to a {answers[1]}", f"drawing strength from their {answers[1]} companion" ]
    power_phrases = [ f"wielding the power of {answers[2]}", f"commanding potent {q_data[2]['keywords'][option_indices[2]]} energies", f"marked by the touch of {answers[2]}" ]
    drive_phrases = [ f"Their path is one of {answers[3]}", f"Driven by an insatiable need for {answers[3]}", f"{answers[3]} fuels their every step" ]
    title_phrases = [ f"known across the lands as the {answers[4]}", f"they carry the title of {answers[4]}", f"a soul whispered to be the {answers[4]}" ]

    # Nested dictionaries for phrase banks based on the specific answer
    cope_phrases = { "Ember of Hope": ["though despair looms, an ember of hope flickers within", "clinging to a stubborn hope against the encroaching shadows", "their resolve is sustained by a fragile ember of hope"], "Spark of Pragmatism": ["facing darkness with cold, pragmatic resolve", "despair is met not with passion, but calculated realism", "a spark of pragmatism guides them through the bleakest times"], "Coal of Defiance": ["within them burns a defiant coal against the crushing weight of despair", "meeting hopelessness with sheer, unyielding defiance", "darkness only fuels their rebellious spirit"] }
    price_interpretations = { "Betraying Oath": ["viewing oaths as shackles to be broken for true understanding", "believing loyalty is a luxury unaffordable in the pursuit of knowledge", "readily sacrificing sworn words on the altar of enlightenment"], "Losing Self": ["willing to risk their very identity for ultimate knowledge", "seeing the dissolution of self as a potential price for cosmic secrets", "questioning if the 'self' is worth preserving when true knowledge calls"], "Sacrificing Innocent": ["contemplating the necessity of sacrificing the innocent for the greater pursuit", "weighing the cost of innocent lives against the acquisition of profound truths", "recognizing that the path to knowledge may demand unthinkable sacrifices"] }
    doubt_phrases = { "Past Failures": ["haunted by the echoes of past failures", "their confidence often shaken by remembered shortcomings", "carrying the weight of failures in moments of quiet doubt"], "Forbidden Power": ["tempted by whispers of forbidden power in their darkest moments", "the allure of proscribed energies echoes in their silent contemplation", "struggling against the siren call of powers best left untouched"], "Profound Stillness": ["finding not answers, but a profound, unnerving stillness in their doubt", "confronted by an echoing void when introspection turns to uncertainty", "their doubts manifest as a silent, deep emptiness"] }
    balance_phrases = { "Sacred Dance": ["they perceive existence as a sacred dance, striving to maintain its delicate balance", "believing in the inherent harmony of creation and destruction, a balance to be preserved", "their philosophy centers on the sacred equilibrium of all things"], "Cycle to Manipulate": ["viewing the cycle of creation and destruction as a force to be understood and manipulated", "seeing existence not as sacred, but as a system whose levers of power can be pulled", "believing true mastery lies in controlling the fundamental cycles of the cosmos"], "Illusion of Chaos": ["convinced that the perceived balance is merely an illusion masking true, underlying chaos", "embracing the inherent entropy and unpredictability of the universe", "seeing order as a fleeting dream within an ocean of chaos"] }
    fate_phrases = { "Eternal Peace": ["ultimately, they yearn for eternal peace, an end to all conflict", "their final demand of fate is serenity for all souls", "seeking a future where strife ceases and tranquility reigns"], "Ascension": ["driven by a desire to ascend beyond mortal limits", "their ultimate goal is transcendence, leaving the mundane world behind", "demanding apotheosis, a place beyond the reach of fate itself"], "Shatter Destiny": ["they demand the right to shatter the very chains of destiny", "seeking not peace or power, but the absolute freedom to defy fate", "their final act would be to break the preordained path for all"] }

    # --- Structural Variation ---
    desc_parts = []
    structure_type = option_indices[1] % 4 # Vary structure based on companion choice

    # Part 1: Origin, Companion, Power
    opening_choice = random.choice([1, 2])
    if opening_choice == 1:
        desc_parts.append(f"{random.choice(origin_phrases)}, {random.choice(companion_phrases)}, {random.choice(power_phrases)}.")
    else:
        desc_parts.append(f"{random.choice(power_phrases).capitalize()}, they are {random.choice(companion_phrases)}, having first awakened in the {answers[0]}.")

    # Part 2: Drive, Title
    drive_title_choice = random.choice([1, 2])
    if drive_title_choice == 1:
        desc_parts.append(f"{random.choice(title_phrases).capitalize()}. {random.choice(drive_phrases)}.")
    else:
         desc_parts.append(f"{random.choice(drive_phrases)}. They are {random.choice(title_phrases)}.")

    # Part 3: Inner State (Cope, Doubt)
    inner_state_choice = random.choice([1, 2, 3])
    cope_text = random.choice(cope_phrases[answers[5]]) # Select random phrase for the chosen coping mechanism
    doubt_text = random.choice(doubt_phrases[answers[7]]) # Select random phrase for the chosen doubt
    if inner_state_choice == 1:
        desc_parts.append(f"Internally, {cope_text}, yet simultaneously {doubt_text}.")
    elif inner_state_choice == 2:
        desc_parts.append(f"{doubt_text.capitalize()}, however, {cope_text}.")
    else:
         desc_parts.append(f"Their inner world is a battleground where {cope_text} contends with being {doubt_text}.")

    # Part 4: Philosophy (Price, Balance, Fate)
    philosophy_choice = random.choice([1, 2])
    price_text = random.choice(price_interpretations[answers[6]])
    balance_text = random.choice(balance_phrases[answers[8]])
    fate_text = random.choice(fate_phrases[answers[9]])
    if philosophy_choice == 1:
         desc_parts.append(f"Philosophically, {price_text}; {balance_text}. {fate_text.capitalize()}.")
    else:
         desc_parts.append(f"Regarding existence, {balance_text}, while {price_text}. {fate_text.capitalize()}.")

    # --- Combine based on Structure Type ---
    if structure_type == 0: # Standard flow
        final_desc = " ".join(desc_parts)
    elif structure_type == 1: # Inner state first
        final_desc = f"{desc_parts[2]} {desc_parts[0]} {desc_parts[1]} {desc_parts[3]}"
    elif structure_type == 2: # Philosophy emphasized
        final_desc = f"{desc_parts[0]} {desc_parts[1]} {desc_parts[3]} {desc_parts[2]}"
    else: # Drive/Title first
        final_desc = f"{desc_parts[1]} {desc_parts[0]} {desc_parts[2]} {desc_parts[3]}"

    # Add conditional flair based on specific combinations
    if option_indices[3] == 0 and option_indices[6] == 2: # Revenge + Sacrifice Innocent
        final_desc += " A path paved with ruthless determination."
    if option_indices[8] == 2 and option_indices[9] == 2: # Chaos + Shatter Destiny
        final_desc += " They seek to unravel reality itself."
    if option_indices[5] == 0 and option_indices[9] == 0: # Hope + Peace
        final_desc += " A beacon against the encroaching darkness."

    return final_desc.strip()

# --- Calculate Rarity ---
def calculate_rarity(option_indices):
    """Calculates card rarity based on point values assigned to options."""
    points = sum([
        [1, 2, 3][option_indices[0]], [2, 1, 3][option_indices[1]], [2, 1, 3][option_indices[2]],
        [2, 1, 3][option_indices[3]], [1, 2, 3][option_indices[4]], [1, 2, 3][option_indices[5]],
        [1, 2, 3][option_indices[6]], [1, 3, 2][option_indices[7]], [1, 2, 3][option_indices[8]],
        [1, 2, 3][option_indices[9]]
    ])
    # Define rarity thresholds
    if points <= 15: return "Common"
    if points <= 19: return "Rare"
    if points <= 23: return "Super Rare"
    if points <= 26: return "Ultra Rare"
    return "Legendary"

# --- Calculate Stats ---
def calculate_stats(option_indices, rarity):
    """Calculates Attack and Defense stats based on rarity and option choices."""
    base_atk, base_def = 50, 50
    rarity_map = {"Common": 0, "Rare": 50, "Super Rare": 120, "Ultra Rare": 200, "Legendary": 300}
    rarity_bonus = rarity_map.get(rarity, 0)
    base_atk += rarity_bonus // 2
    base_def += rarity_bonus - (rarity_bonus // 2)

    # Define stat bonuses/penalties for each option [[atk, def], [atk, def], [atk, def]]
    q_bonuses = [
        [[0, 15], [10, 5], [15, 0]],    # Q0: Woods(D), Ruins(A/D), Peaks(A)
        [[20, 0], [0, 20], [5, 15]],    # Q1: Spirit(A), Cat(D), Drone(A/D)
        [[25, 0], [0, 25], [15, 10]],   # Q2: Flame(A), Ice(D), Thunder(A/D)
        [[15, 0], [0, 15], [5, 10]],    # Q3: Revenge(A), Discovery(D), Honor(A/D)
        [[0, 10], [15, 0], [10, 0]],    # Q4: Wanderer(D), Seeker(A), Caller(A)
        [[0, 20], [5, 5], [20, 0]],     # Q6: Hope(D), Prag(A/D), Defiance(A)
        [[5, 5], [-10, 20], [15, -10]], # Q7: Innocent(A/D), Self(D-A), Oath(A-D) <- Swapped order for logic
        [[0, -10], [20, 20], [5, 5]],   # Q8: Fail(D-), Power(A+,D+), Still(A/D)
        [[0, 25], [15, 5], [30, -5]],   # Q9: Dance(D+), Manip(A+), Chaos(A++, D-)
        [[0, 40], [20, 20], [40, 0]]    # Q10: Peace(D++), Ascend(A+,D+), Shatter(A++)
    ]

    # Sum bonuses from all answers
    total_atk_bonus = sum(q_bonuses[i][option_indices[i]][0] for i in range(10))
    total_def_bonus = sum(q_bonuses[i][option_indices[i]][1] for i in range(10))

    final_atk = base_atk + total_atk_bonus
    final_def = base_def + total_def_bonus

    # Add slight randomization (+/- 5%) & Clamp stats (10-999)
    final_atk = max(10, min(999, final_atk + math.floor(final_atk * 0.05 * (random.random() * 2 - 1))))
    final_def = max(10, min(999, final_def + math.floor(final_def * 0.05 * (random.random() * 2 - 1))))

    return final_atk, final_def

# --- Main Generation Logic ---
def main():
    """Generates all card data and saves it to cards.json."""
    all_cards_data = []
    num_combinations = 3**10 # 59049
    output_filename = "cards.json" # Output file name

    print(f"Starting generation of {num_combinations} unique cards...")

    for i in range(num_combinations):
        if i > 0 and i % 5000 == 0: # Print progress indicator every 5000 cards
            print(f"Generated {i}/{num_combinations} cards...")

        option_indices = get_option_indices_from_index(i)
        # Get actual answer texts based on indices for description generation
        answers = [questions_data[q]["options"][option_indices[q]] for q in range(10)]

        name = generate_name(option_indices)
        description = generate_description(answers, option_indices)
        rarity = calculate_rarity(option_indices)
        attack, defense = calculate_stats(option_indices, rarity)

        # --- Generate image path from name --- 
        # 1. Lowercase
        safe_name = name.lower()
        # 2. Replace spaces with underscores
        safe_name = safe_name.replace(' ', '_')
        # 3. Remove characters other than letters, numbers, underscores
        safe_name = ''.join(c for c in safe_name if c.isalnum() or c == '_')
        # 4. Prevent excessively long filenames (optional, but good practice)
        max_len = 100
        if len(safe_name) > max_len:
            safe_name = safe_name[:max_len]
        # 5. Construct the path
        image_path = f"assets/covers/{safe_name}.png"
        # --- End image path generation ---

        card_entry = {
            "id": i,
            "name": name,
            "rarity": rarity,
            "imagePath": image_path, # Use the generated image path
            "attack": attack,
            "defense": defense,
            "description": description
        }
        all_cards_data.append(card_entry)

    print(f"Generation complete ({num_combinations} cards).")
    print(f"Converting data to JSON format for {output_filename}...")

    # Convert the list of dictionaries to a JSON string
    # Using indent=2 for readability during development. Remove indent for smaller file size.
    try:
        full_json_content = json.dumps(all_cards_data, indent=2)
        json_size_mb = len(full_json_content) / (1024*1024)
        print(f"JSON conversion complete. Estimated size: {json_size_mb:.2f} MB")
    except Exception as e:
         print(f"\nError during JSON conversion: {e}")
         return # Stop if conversion fails

    # Save the JSON data to the specified file
    try:
        print(f"Attempting to write data to {output_filename}...")
        with open(output_filename, 'w', encoding='utf-8') as f:
            f.write(full_json_content)
        print(f"Successfully wrote {num_combinations} card entries to {output_filename}.")
    except IOError as e:
        print(f"\nError writing to file {output_filename}: {e}")
    except Exception as e:
        print(f"\nAn unexpected error occurred during file writing: {e}")

if __name__ == "__main__":
    main() 