{"metadata": {"filename": "catacomb_boss.wav", "description": "Comprehensive data for catacomb boss music", "generatedAt": "2025-04-15T03:03:20.967Z", "duration": 224}, "timeline": [{"startTime": 0, "endTime": 4, "patterns": ["spiral_wave"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Intro - spiral pattern"}, {"startTime": 4, "endTime": 7, "patterns": ["lightning_chain"], "projectileTypes": ["fireball"], "intensity": 0.6, "speedMultiplier": 1, "triggerIntervalMin": 1, "triggerIntervalMax": 1.8, "description": "First buildup - lightning pattern"}, {"startTime": 7, "endTime": 9, "patterns": ["laser_grid"], "projectileTypes": ["ash_cinders"], "intensity": 0.4, "speedMultiplier": 0.8, "triggerIntervalMin": 1.5, "triggerIntervalMax": 2.5, "description": "Break - grid pattern"}, {"startTime": 9, "endTime": 13, "patterns": ["petal_spread"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Verse - petal pattern"}, {"startTime": 13, "endTime": 16, "patterns": ["spiral_bloom"], "projectileTypes": ["lava_droplets"], "intensity": 0.6, "speedMultiplier": 1, "triggerIntervalMin": 1, "triggerIntervalMax": 1.5, "description": "Arpeggios - bloom pattern"}, {"startTime": 16, "endTime": 20, "patterns": ["circle_ripple"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Verse - ripple pattern"}, {"startTime": 20, "endTime": 24, "patterns": ["alchemical_chain"], "projectileTypes": ["lava_droplets"], "intensity": 0.7, "speedMultiplier": 1.1, "triggerIntervalMin": 0.8, "triggerIntervalMax": 1.5, "description": "Arpeggios - reactive chain pattern"}, {"startTime": 24, "endTime": 37, "patterns": ["spiral_rain"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Medium shots - falling spiral pattern"}, {"startTime": 37, "endTime": 42, "patterns": ["lightning_chain"], "projectileTypes": ["fireball"], "intensity": 0.6, "speedMultiplier": 1, "triggerIntervalMin": 1, "triggerIntervalMax": 1.8, "description": "Buildup - lightning pattern"}, {"startTime": 42, "endTime": 45, "patterns": ["laser_grid"], "projectileTypes": ["ash_cinders"], "intensity": 0.4, "speedMultiplier": 0.8, "triggerIntervalMin": 1.5, "triggerIntervalMax": 2.5, "description": "Break - grid pattern"}, {"startTime": 45, "endTime": 48, "patterns": ["petal_spread"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Verse - petal pattern"}, {"startTime": 48, "endTime": 51, "patterns": ["alchemical_chain"], "projectileTypes": ["lava_droplets"], "intensity": 0.6, "speedMultiplier": 1, "triggerIntervalMin": 1, "triggerIntervalMax": 1.5, "description": "Arpeggios - reactive chain pattern"}, {"startTime": 51, "endTime": 64, "patterns": ["boomerang_arcs"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Medium shots - arcing boomerang pattern"}, {"startTime": 64, "endTime": 70, "patterns": ["vortex_wings"], "projectileTypes": ["fireball"], "intensity": 0.7, "speedMultiplier": 1.2, "triggerIntervalMin": 0.7, "triggerIntervalMax": 1.3, "description": "Faster shots in player direction at 360 degree - wing-like vortex pattern"}, {"startTime": 70, "endTime": 76, "patterns": ["spiral_rain"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Medium shots - falling spiral pattern"}, {"startTime": 76, "endTime": 83, "patterns": ["soul_magnetism"], "projectileTypes": ["fireball"], "intensity": 0.7, "speedMultiplier": 1.2, "triggerIntervalMin": 0.7, "triggerIntervalMax": 1.3, "description": "Faster shots - magnetic pattern"}, {"startTime": 83, "endTime": 90, "patterns": ["alchemical_chain"], "projectileTypes": ["lava_droplets"], "intensity": 0.8, "speedMultiplier": 1.3, "triggerIntervalMin": 0.5, "triggerIntervalMax": 1, "description": "Very fast burst and arpeggios - reactive chain pattern"}, {"startTime": 90, "endTime": 96, "patterns": ["shifting_polarity"], "projectileTypes": ["blood_threads"], "intensity": 0.7, "speedMultiplier": 1.1, "triggerIntervalMin": 0.7, "triggerIntervalMax": 1.3, "description": "Triplet shots - changing behavior pattern"}, {"startTime": 96, "endTime": 103, "patterns": ["alchemical_chain"], "projectileTypes": ["lava_droplets"], "intensity": 0.7, "speedMultiplier": 1.2, "triggerIntervalMin": 0.7, "triggerIntervalMax": 1.3, "description": "Arpeggios - reactive chain pattern"}, {"startTime": 103, "endTime": 106, "patterns": ["delay_accelerate"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Medium shots - delayed acceleration pattern"}, {"startTime": 106, "endTime": 109, "patterns": ["fire_kraken"], "projectileTypes": ["blood_threads"], "intensity": 0.6, "speedMultiplier": 1, "triggerIntervalMin": 0.8, "triggerIntervalMax": 1.5, "description": "Bursts of short sounds - tentacle pattern"}, {"startTime": 109, "endTime": 118, "patterns": ["spiral_rain"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1, "triggerIntervalMax": 2, "description": "Shots in 360 degree - falling spiral pattern"}, {"startTime": 118, "endTime": 131, "patterns": ["inhale_exhale"], "projectileTypes": ["fireball"], "intensity": 0.6, "speedMultiplier": 1.1, "triggerIntervalMin": 0.8, "triggerIntervalMax": 1.5, "description": "Faster shots in player direction at 90 degree - push/pull pattern"}, {"startTime": 131, "endTime": 135, "patterns": ["shifting_polarity"], "projectileTypes": ["blood_threads"], "intensity": 0.65, "speedMultiplier": 1, "triggerIntervalMin": 0.8, "triggerIntervalMax": 1.5, "description": "Bursts and repeating notes - changing behavior pattern"}, {"startTime": 135, "endTime": 138, "patterns": ["fire_kraken"], "projectileTypes": ["blood_threads"], "intensity": 0.6, "speedMultiplier": 1, "triggerIntervalMin": 0.8, "triggerIntervalMax": 1.5, "description": "Bursts of short sounds - tentacle pattern"}, {"startTime": 138, "endTime": 140, "patterns": ["spiral_rain"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1, "triggerIntervalMax": 2, "description": "Shots in 360 degree - falling spiral pattern"}, {"startTime": 140, "endTime": 144, "patterns": ["inhale_exhale"], "projectileTypes": ["fireball"], "intensity": 0.6, "speedMultiplier": 1.1, "triggerIntervalMin": 0.8, "triggerIntervalMax": 1.5, "description": "Faster shots in player direction at 90 degree - push/pull pattern"}, {"startTime": 144, "endTime": 150, "patterns": ["shifting_polarity"], "projectileTypes": ["blood_threads"], "intensity": 0.65, "speedMultiplier": 1, "triggerIntervalMin": 0.8, "triggerIntervalMax": 1.5, "description": "Bursts and repeating notes - changing behavior pattern"}, {"startTime": 150, "endTime": 158, "patterns": ["delay_accelerate"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Medium shots - delayed acceleration pattern"}, {"startTime": 158, "endTime": 163, "patterns": ["divine_divide"], "projectileTypes": ["fireball"], "intensity": 0.7, "speedMultiplier": 1, "triggerIntervalMin": 0.8, "triggerIntervalMax": 1.5, "description": "360 degree intense - divided pattern"}, {"startTime": 163, "endTime": 170, "patterns": ["breath_trails"], "projectileTypes": ["fireball"], "intensity": 0.7, "speedMultiplier": 1.2, "triggerIntervalMin": 0.7, "triggerIntervalMax": 1.3, "description": "Faster shots in player direction at 90 degree - trailing pattern"}, {"startTime": 170, "endTime": 173, "patterns": ["alchemical_chain"], "projectileTypes": ["lava_droplets"], "intensity": 0.7, "speedMultiplier": 1.2, "triggerIntervalMin": 0.7, "triggerIntervalMax": 1.3, "description": "Arpeggios - reactive chain pattern"}, {"startTime": 173, "endTime": 176, "patterns": ["fake_out_pulse"], "projectileTypes": ["blood_threads"], "intensity": 0.75, "speedMultiplier": 1.1, "triggerIntervalMin": 0.6, "triggerIntervalMax": 1.2, "description": "Fast bursts along with arp - pulsing fake-out pattern"}, {"startTime": 176, "endTime": 189, "patterns": ["boomerang_arcs"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Medium shots - arcing boomerang pattern"}, {"startTime": 189, "endTime": 192, "patterns": ["vortex_wings"], "projectileTypes": ["fireball"], "intensity": 0.7, "speedMultiplier": 1.2, "triggerIntervalMin": 0.7, "triggerIntervalMax": 1.3, "description": "Faster shots in player direction at 360 degree - wing-like vortex pattern"}, {"startTime": 192, "endTime": 198, "patterns": ["spiral_rain"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Medium shots - falling spiral pattern"}, {"startTime": 198, "endTime": 202, "patterns": ["soul_magnetism"], "projectileTypes": ["fireball"], "intensity": 0.7, "speedMultiplier": 1.2, "triggerIntervalMin": 0.7, "triggerIntervalMax": 1.3, "description": "Faster shots - magnetic pattern"}, {"startTime": 202, "endTime": 208, "patterns": ["alchemical_chain"], "projectileTypes": ["lava_droplets"], "intensity": 0.8, "speedMultiplier": 1.3, "triggerIntervalMin": 0.5, "triggerIntervalMax": 1, "description": "Very fast burst and arpeggios - reactive chain pattern"}, {"startTime": 208, "endTime": 211, "patterns": ["shifting_polarity"], "projectileTypes": ["blood_threads"], "intensity": 0.7, "speedMultiplier": 1.1, "triggerIntervalMin": 0.7, "triggerIntervalMax": 1.3, "description": "Triplet shots - changing behavior pattern"}, {"startTime": 211, "endTime": 220, "patterns": ["alchemical_chain"], "projectileTypes": ["lava_droplets"], "intensity": 0.7, "speedMultiplier": 1.2, "triggerIntervalMin": 0.7, "triggerIntervalMax": 1.3, "description": "Arpeggios - reactive chain pattern"}, {"startTime": 220, "endTime": 224, "patterns": ["delay_accelerate"], "projectileTypes": ["fireball"], "intensity": 0.5, "speedMultiplier": 0.9, "triggerIntervalMin": 1.2, "triggerIntervalMax": 2, "description": "Medium shots - delayed acceleration pattern"}], "screenShakeTimestamps": [{"time": 0, "intensity": 0.25, "duration": 300}, {"time": 4, "intensity": 0.25, "duration": 300}, {"time": 7, "intensity": 0.25, "duration": 300}, {"time": 9, "intensity": 0.25, "duration": 300}, {"time": 13, "intensity": 0.25, "duration": 300}, {"time": 16, "intensity": 0.25, "duration": 300}, {"time": 20, "intensity": 0.25, "duration": 300}, {"time": 24, "intensity": 0.25, "duration": 300}, {"time": 37, "intensity": 0.25, "duration": 300}, {"time": 42, "intensity": 0.25, "duration": 300}, {"time": 45, "intensity": 0.25, "duration": 300}, {"time": 48, "intensity": 0.25, "duration": 300}, {"time": 51, "intensity": 0.25, "duration": 300}, {"time": 64, "intensity": 0.25, "duration": 300}, {"time": 70, "intensity": 0.25, "duration": 300}, {"time": 76, "intensity": 0.25, "duration": 300}, {"time": 83, "intensity": 0.25, "duration": 300}, {"time": 90, "intensity": 0.25, "duration": 300}, {"time": 96, "intensity": 0.25, "duration": 300}, {"time": 103, "intensity": 0.25, "duration": 300}, {"time": 106, "intensity": 0.25, "duration": 300}, {"time": 109, "intensity": 0.25, "duration": 300}, {"time": 118, "intensity": 0.25, "duration": 300}, {"time": 131, "intensity": 0.25, "duration": 300}, {"time": 135, "intensity": 0.25, "duration": 300}, {"time": 138, "intensity": 0.25, "duration": 300}, {"time": 140, "intensity": 0.25, "duration": 300}, {"time": 144, "intensity": 0.25, "duration": 300}, {"time": 150, "intensity": 0.25, "duration": 300}, {"time": 158, "intensity": 0.25, "duration": 300}, {"time": 163, "intensity": 0.25, "duration": 300}, {"time": 170, "intensity": 0.25, "duration": 300}, {"time": 173, "intensity": 0.25, "duration": 300}, {"time": 176, "intensity": 0.25, "duration": 300}, {"time": 189, "intensity": 0.25, "duration": 300}, {"time": 192, "intensity": 0.25, "duration": 300}, {"time": 198, "intensity": 0.25, "duration": 300}, {"time": 202, "intensity": 0.25, "duration": 300}, {"time": 208, "intensity": 0.25, "duration": 300}, {"time": 211, "intensity": 0.25, "duration": 300}, {"time": 220, "intensity": 0.25, "duration": 300}, {"time": 224, "intensity": 0.25, "duration": 300}]}