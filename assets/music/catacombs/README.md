# Catacombs Music Files

This directory contains music files for the catacombs area using the Loop Chain Playback Structure with Hit Interrupts, Temporal Insertions, and Emotive Cues.

## Loop Chain Structure

Each area's main theme is structured as a chain of 5 modular 4-bar loops that cycle in sequence:

- `loop_a.mp3` - First loop in the chain
- `loop_b.mp3` - Second loop in the chain
- `loop_c.mp3` - Third loop in the chain
- `loop_d.mp3` - Fourth loop in the chain
- `loop_e.mp3` - Fifth loop in the chain

After playing `loop_e.mp3`, the system automatically restarts from `loop_a.mp3`.

## Interrupts

Interrupts are played when specific gameplay events occur, then the system returns to the next loop in the chain:

- `interrupts/hit_interrupt.mp3` - Played when the player takes damage
- `interrupts/danger_interrupt.mp3` - Played when multiple enemies are present

## Temporal Insertions

One-off loops that play for narrative or emotional moments, then the system returns to the next loop in the chain:

- `insertions/memory_recall.mp3` - Played during memory/flashback sequences
- `insertions/dialogue.mp3` - Played during important dialogue moments
- `insertions/fear.mp3` - Played during fear-inducing moments

## Special Rooms

Special rooms have their own loop chains:

- `specialRooms/shop_a.mp3` and `specialRooms/shop_b.mp3` - Shop room music loop chain
- `specialRooms/event_a.mp3` and `specialRooms/event_b.mp3` - Event room music loop chain

## Legacy Support

### Leitmotifs
- `leitmotifs/mother_layer.mp3` - Mother-related story elements
- `leitmotifs/guilt_layer.mp3` - Guilt-related story moments

### Transitions
- `intro.mp3` - Used as a placeholder for transitions between areas

## How It Works

1. The system plays each loop in the chain in sequence (loop_a → loop_b → loop_c → loop_d → loop_e → loop_a...)
2. At the end of each loop, the system checks for pending events:
   - If a player hit is detected → play hit_interrupt.mp3, then continue to the next loop
   - If a narrative moment occurs → play the appropriate insertion, then continue to the next loop
   - If an area transition is requested → transition to the new area at the loop boundary

## Technical Requirements

- All loops should be at 100 BPM (catacombs area)
- Each loop should be exactly 4 bars long for proper synchronization
- All files should be high-quality MP3 files (at least 192kbps)
- Loops should have clean loop points with no audible gaps or clicks
- Interrupts and insertions should be designed to flow smoothly back into the main loop chain

## Benefits

- Seamless musical storytelling through modular loop chains
- Layered narrative feedback without breaking the core musical identity
- Emotional resonance and symbolic moments that align directly with gameplay
- Long, evolving themes that are lightweight, reactive, and expressive
