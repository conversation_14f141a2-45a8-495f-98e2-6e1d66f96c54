
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Screen Shake Timestamps Visualization</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .container { max-width: 1200px; margin: 0 auto; }
    .timeline { position: relative; height: 100px; border: 1px solid #ccc; margin: 20px 0; overflow-x: auto; }
    .timestamp { position: absolute; width: 2px; background: red; top: 0; height: 100%; }
    .controls { margin: 20px 0; }
    .metadata { margin: 20px 0; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .zoom { margin-right: 10px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Screen Shake Timestamps Visualization</h1>

    <div class="metadata">
      <h2>Metadata</h2>
      <table>
        <tr><th>Filename</th><td>catacomb_boss.wav</td></tr>
        <tr><th>Description</th><td>Screen shake timestamps for boss music</td></tr>
        <tr><th>Generated At</th><td>2025-04-15T03:00:14.425Z</td></tr>
        <tr><th>Timestamps</th><td>42</td></tr>
      </table>
    </div>

    <div class="controls">
      <button class="zoom" data-factor="0.5">Zoom Out</button>
      <button class="zoom" data-factor="2">Zoom In</button>
      <span id="time-display">0.00s</span>
    </div>

    <div class="timeline" id="timeline">
      <!-- Timestamps -->
      <div class="timestamp" style="left: 0%" title="Timestamp at 0s"></div><div class="timestamp" style="left: 1.7857142857142856%" title="Timestamp at 4s"></div><div class="timestamp" style="left: 3.125%" title="Timestamp at 7s"></div><div class="timestamp" style="left: 4.017857142857143%" title="Timestamp at 9s"></div><div class="timestamp" style="left: 5.803571428571429%" title="Timestamp at 13s"></div><div class="timestamp" style="left: 7.142857142857142%" title="Timestamp at 16s"></div><div class="timestamp" style="left: 8.928571428571429%" title="Timestamp at 20s"></div><div class="timestamp" style="left: 10.714285714285714%" title="Timestamp at 24s"></div><div class="timestamp" style="left: 16.517857142857142%" title="Timestamp at 37s"></div><div class="timestamp" style="left: 18.75%" title="Timestamp at 42s"></div><div class="timestamp" style="left: 20.089285714285715%" title="Timestamp at 45s"></div><div class="timestamp" style="left: 21.428571428571427%" title="Timestamp at 48s"></div><div class="timestamp" style="left: 22.767857142857142%" title="Timestamp at 51s"></div><div class="timestamp" style="left: 28.57142857142857%" title="Timestamp at 64s"></div><div class="timestamp" style="left: 31.25%" title="Timestamp at 70s"></div><div class="timestamp" style="left: 33.92857142857143%" title="Timestamp at 76s"></div><div class="timestamp" style="left: 37.05357142857143%" title="Timestamp at 83s"></div><div class="timestamp" style="left: 40.17857142857143%" title="Timestamp at 90s"></div><div class="timestamp" style="left: 42.857142857142854%" title="Timestamp at 96s"></div><div class="timestamp" style="left: 45.982142857142854%" title="Timestamp at 103s"></div><div class="timestamp" style="left: 47.32142857142857%" title="Timestamp at 106s"></div><div class="timestamp" style="left: 48.660714285714285%" title="Timestamp at 109s"></div><div class="timestamp" style="left: 52.67857142857143%" title="Timestamp at 118s"></div><div class="timestamp" style="left: 58.48214285714286%" title="Timestamp at 131s"></div><div class="timestamp" style="left: 60.26785714285714%" title="Timestamp at 135s"></div><div class="timestamp" style="left: 61.60714285714286%" title="Timestamp at 138s"></div><div class="timestamp" style="left: 62.5%" title="Timestamp at 140s"></div><div class="timestamp" style="left: 64.28571428571429%" title="Timestamp at 144s"></div><div class="timestamp" style="left: 66.96428571428571%" title="Timestamp at 150s"></div><div class="timestamp" style="left: 70.53571428571429%" title="Timestamp at 158s"></div><div class="timestamp" style="left: 72.76785714285714%" title="Timestamp at 163s"></div><div class="timestamp" style="left: 75.89285714285714%" title="Timestamp at 170s"></div><div class="timestamp" style="left: 77.23214285714286%" title="Timestamp at 173s"></div><div class="timestamp" style="left: 78.57142857142857%" title="Timestamp at 176s"></div><div class="timestamp" style="left: 84.375%" title="Timestamp at 189s"></div><div class="timestamp" style="left: 85.71428571428571%" title="Timestamp at 192s"></div><div class="timestamp" style="left: 88.39285714285714%" title="Timestamp at 198s"></div><div class="timestamp" style="left: 90.17857142857143%" title="Timestamp at 202s"></div><div class="timestamp" style="left: 92.85714285714286%" title="Timestamp at 208s"></div><div class="timestamp" style="left: 94.19642857142857%" title="Timestamp at 211s"></div><div class="timestamp" style="left: 98.21428571428571%" title="Timestamp at 220s"></div><div class="timestamp" style="left: 100%" title="Timestamp at 224s"></div>
    </div>

    <div class="timestamp-list">
      <h2>Timestamp List</h2>
      <table>
        <tr>
          <th>Time (s)</th>
          <th>Intensity</th>
          <th>Duration (ms)</th>
        </tr>
        
          <tr>
            <td>0</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>4</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>7</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>9</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>13</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>16</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>20</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>24</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>37</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>42</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>45</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>48</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>51</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>64</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>70</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>76</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>83</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>90</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>96</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>103</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>106</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>109</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>118</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>131</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>135</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>138</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>140</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>144</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>150</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>158</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>163</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>170</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>173</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>176</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>189</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>192</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>198</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>202</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>208</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>211</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>220</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
          <tr>
            <td>224</td>
            <td>0.25</td>
            <td>300</td>
          </tr>
        
      </table>
    </div>
  </div>

  <script>
    // Zoom functionality
    document.querySelectorAll('.zoom').forEach(button => {
      button.addEventListener('click', () => {
        const timeline = document.getElementById('timeline');
        const factor = parseFloat(button.dataset.factor);
        const currentWidth = timeline.style.width ? parseInt(timeline.style.width) : 100;
        const newWidth = currentWidth * factor;
        timeline.style.width = newWidth + '%';
      });
    });

    // Mouse position time display
    const timeline = document.getElementById('timeline');
    const timeDisplay = document.getElementById('time-display');
    const duration = 224; // Total duration in seconds

    timeline.addEventListener('mousemove', (e) => {
      const rect = timeline.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = x / rect.width;
      const time = percentage * duration;
      timeDisplay.textContent = time.toFixed(2) + 's';
    });
  </script>
</body>
</html>
