
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comprehensive Music Data Visualization</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .container { max-width: 1200px; margin: 0 auto; }
    .timeline { position: relative; height: 200px; border: 1px solid #ccc; margin: 20px 0; overflow-x: auto; }
    .entry { position: absolute; height: 80px; top: 0; border-radius: 3px; opacity: 0.7; }
    .shake { position: absolute; width: 2px; background: red; top: 0; height: 100%; }
    .controls { margin: 20px 0; }
    .metadata { margin: 20px 0; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .zoom { margin-right: 10px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Comprehensive Music Data Visualization</h1>
    
    <div class="metadata">
      <h2>Metadata</h2>
      <table>
        <tr><th>Filename</th><td>catacomb_boss.wav</td></tr>
        <tr><th>Description</th><td>Comprehensive data for catacomb boss music</td></tr>
        <tr><th>Generated At</th><td>2025-04-15T03:03:20.967Z</td></tr>
        <tr><th>Duration</th><td>224 seconds</td></tr>
        <tr><th>Timeline Entries</th><td>41</td></tr>
        <tr><th>Screen Shake Timestamps</th><td>42</td></tr>
      </table>
    </div>
    
    <div class="controls">
      <button class="zoom" data-factor="0.5">Zoom Out</button>
      <button class="zoom" data-factor="2">Zoom In</button>
      <span id="time-display">0.00s</span>
    </div>
    
    <div class="timeline" id="timeline">
      <!-- Timeline entries -->
      <div class="entry" style="left: 0%; width: 1.7857142857142856%; background: #FF5733;" title="Intro - spiral pattern (0s - 4s)
Pattern: spiral_wave, Projectile: fireball
Intensity: 0.5, Speed: 0.9">spiral_wave</div><div class="entry" style="left: 1.7857142857142856%; width: 1.3392857142857142%; background: #33A8FF;" title="First buildup - lightning pattern (4s - 7s)
Pattern: lightning_chain, Projectile: fireball
Intensity: 0.6, Speed: 1">lightning_chain</div><div class="entry" style="left: 3.125%; width: 0.8928571428571428%; background: #33FF57;" title="Break - grid pattern (7s - 9s)
Pattern: laser_grid, Projectile: ash_cinders
Intensity: 0.4, Speed: 0.8">laser_grid</div><div class="entry" style="left: 4.017857142857143%; width: 1.7857142857142856%; background: #FF33A8;" title="Verse - petal pattern (9s - 13s)
Pattern: petal_spread, Projectile: fireball
Intensity: 0.5, Speed: 0.9">petal_spread</div><div class="entry" style="left: 5.803571428571429%; width: 1.3392857142857142%; background: #A833FF;" title="Arpeggios - bloom pattern (13s - 16s)
Pattern: spiral_bloom, Projectile: lava_droplets
Intensity: 0.6, Speed: 1">spiral_bloom</div><div class="entry" style="left: 7.142857142857142%; width: 1.7857142857142856%; background: #FFFF33;" title="Verse - ripple pattern (16s - 20s)
Pattern: circle_ripple, Projectile: fireball
Intensity: 0.5, Speed: 0.9">circle_ripple</div><div class="entry" style="left: 8.928571428571429%; width: 1.7857142857142856%; background: #33FFFF;" title="Arpeggios - reactive chain pattern (20s - 24s)
Pattern: alchemical_chain, Projectile: lava_droplets
Intensity: 0.7, Speed: 1.1">alchemical_chain</div><div class="entry" style="left: 10.714285714285714%; width: 5.803571428571429%; background: #FF8333;" title="Medium shots - falling spiral pattern (24s - 37s)
Pattern: spiral_rain, Projectile: fireball
Intensity: 0.5, Speed: 0.9">spiral_rain</div><div class="entry" style="left: 16.517857142857142%; width: 2.232142857142857%; background: #33A8FF;" title="Buildup - lightning pattern (37s - 42s)
Pattern: lightning_chain, Projectile: fireball
Intensity: 0.6, Speed: 1">lightning_chain</div><div class="entry" style="left: 18.75%; width: 1.3392857142857142%; background: #33FF57;" title="Break - grid pattern (42s - 45s)
Pattern: laser_grid, Projectile: ash_cinders
Intensity: 0.4, Speed: 0.8">laser_grid</div><div class="entry" style="left: 20.089285714285715%; width: 1.3392857142857142%; background: #FF33A8;" title="Verse - petal pattern (45s - 48s)
Pattern: petal_spread, Projectile: fireball
Intensity: 0.5, Speed: 0.9">petal_spread</div><div class="entry" style="left: 21.428571428571427%; width: 1.3392857142857142%; background: #33FFFF;" title="Arpeggios - reactive chain pattern (48s - 51s)
Pattern: alchemical_chain, Projectile: lava_droplets
Intensity: 0.6, Speed: 1">alchemical_chain</div><div class="entry" style="left: 22.767857142857142%; width: 5.803571428571429%; background: #8333FF;" title="Medium shots - arcing boomerang pattern (51s - 64s)
Pattern: boomerang_arcs, Projectile: fireball
Intensity: 0.5, Speed: 0.9">boomerang_arcs</div><div class="entry" style="left: 28.57142857142857%; width: 2.6785714285714284%; background: #33FF83;" title="Faster shots in player direction at 360 degree - wing-like vortex pattern (64s - 70s)
Pattern: vortex_wings, Projectile: fireball
Intensity: 0.7, Speed: 1.2">vortex_wings</div><div class="entry" style="left: 31.25%; width: 2.6785714285714284%; background: #FF8333;" title="Medium shots - falling spiral pattern (70s - 76s)
Pattern: spiral_rain, Projectile: fireball
Intensity: 0.5, Speed: 0.9">spiral_rain</div><div class="entry" style="left: 33.92857142857143%; width: 3.125%; background: #FF3383;" title="Faster shots - magnetic pattern (76s - 83s)
Pattern: soul_magnetism, Projectile: fireball
Intensity: 0.7, Speed: 1.2">soul_magnetism</div><div class="entry" style="left: 37.05357142857143%; width: 3.125%; background: #33FFFF;" title="Very fast burst and arpeggios - reactive chain pattern (83s - 90s)
Pattern: alchemical_chain, Projectile: lava_droplets
Intensity: 0.8, Speed: 1.3">alchemical_chain</div><div class="entry" style="left: 40.17857142857143%; width: 2.6785714285714284%; background: #3383FF;" title="Triplet shots - changing behavior pattern (90s - 96s)
Pattern: shifting_polarity, Projectile: blood_threads
Intensity: 0.7, Speed: 1.1">shifting_polarity</div><div class="entry" style="left: 42.857142857142854%; width: 3.125%; background: #33FFFF;" title="Arpeggios - reactive chain pattern (96s - 103s)
Pattern: alchemical_chain, Projectile: lava_droplets
Intensity: 0.7, Speed: 1.2">alchemical_chain</div><div class="entry" style="left: 45.982142857142854%; width: 1.3392857142857142%; background: #83FFFF;" title="Medium shots - delayed acceleration pattern (103s - 106s)
Pattern: delay_accelerate, Projectile: fireball
Intensity: 0.5, Speed: 0.9">delay_accelerate</div><div class="entry" style="left: 47.32142857142857%; width: 1.3392857142857142%; background: #FF3333;" title="Bursts of short sounds - tentacle pattern (106s - 109s)
Pattern: fire_kraken, Projectile: blood_threads
Intensity: 0.6, Speed: 1">fire_kraken</div><div class="entry" style="left: 48.660714285714285%; width: 4.017857142857143%; background: #FF8333;" title="Shots in 360 degree - falling spiral pattern (109s - 118s)
Pattern: spiral_rain, Projectile: fireball
Intensity: 0.5, Speed: 0.9">spiral_rain</div><div class="entry" style="left: 52.67857142857143%; width: 5.803571428571429%; background: #33FF33;" title="Faster shots in player direction at 90 degree - push/pull pattern (118s - 131s)
Pattern: inhale_exhale, Projectile: fireball
Intensity: 0.6, Speed: 1.1">inhale_exhale</div><div class="entry" style="left: 58.48214285714286%; width: 1.7857142857142856%; background: #3383FF;" title="Bursts and repeating notes - changing behavior pattern (131s - 135s)
Pattern: shifting_polarity, Projectile: blood_threads
Intensity: 0.65, Speed: 1">shifting_polarity</div><div class="entry" style="left: 60.26785714285714%; width: 1.3392857142857142%; background: #FF3333;" title="Bursts of short sounds - tentacle pattern (135s - 138s)
Pattern: fire_kraken, Projectile: blood_threads
Intensity: 0.6, Speed: 1">fire_kraken</div><div class="entry" style="left: 61.60714285714286%; width: 0.8928571428571428%; background: #FF8333;" title="Shots in 360 degree - falling spiral pattern (138s - 140s)
Pattern: spiral_rain, Projectile: fireball
Intensity: 0.5, Speed: 0.9">spiral_rain</div><div class="entry" style="left: 62.5%; width: 1.7857142857142856%; background: #33FF33;" title="Faster shots in player direction at 90 degree - push/pull pattern (140s - 144s)
Pattern: inhale_exhale, Projectile: fireball
Intensity: 0.6, Speed: 1.1">inhale_exhale</div><div class="entry" style="left: 64.28571428571429%; width: 2.6785714285714284%; background: #3383FF;" title="Bursts and repeating notes - changing behavior pattern (144s - 150s)
Pattern: shifting_polarity, Projectile: blood_threads
Intensity: 0.65, Speed: 1">shifting_polarity</div><div class="entry" style="left: 66.96428571428571%; width: 3.571428571428571%; background: #83FFFF;" title="Medium shots - delayed acceleration pattern (150s - 158s)
Pattern: delay_accelerate, Projectile: fireball
Intensity: 0.5, Speed: 0.9">delay_accelerate</div><div class="entry" style="left: 70.53571428571429%; width: 2.232142857142857%; background: #3333FF;" title="360 degree intense - divided pattern (158s - 163s)
Pattern: divine_divide, Projectile: fireball
Intensity: 0.7, Speed: 1">divine_divide</div><div class="entry" style="left: 72.76785714285714%; width: 3.125%; background: #FFFF83;" title="Faster shots in player direction at 90 degree - trailing pattern (163s - 170s)
Pattern: breath_trails, Projectile: fireball
Intensity: 0.7, Speed: 1.2">breath_trails</div><div class="entry" style="left: 75.89285714285714%; width: 1.3392857142857142%; background: #33FFFF;" title="Arpeggios - reactive chain pattern (170s - 173s)
Pattern: alchemical_chain, Projectile: lava_droplets
Intensity: 0.7, Speed: 1.2">alchemical_chain</div><div class="entry" style="left: 77.23214285714286%; width: 1.3392857142857142%; background: #FF83FF;" title="Fast bursts along with arp - pulsing fake-out pattern (173s - 176s)
Pattern: fake_out_pulse, Projectile: blood_threads
Intensity: 0.75, Speed: 1.1">fake_out_pulse</div><div class="entry" style="left: 78.57142857142857%; width: 5.803571428571429%; background: #8333FF;" title="Medium shots - arcing boomerang pattern (176s - 189s)
Pattern: boomerang_arcs, Projectile: fireball
Intensity: 0.5, Speed: 0.9">boomerang_arcs</div><div class="entry" style="left: 84.375%; width: 1.3392857142857142%; background: #33FF83;" title="Faster shots in player direction at 360 degree - wing-like vortex pattern (189s - 192s)
Pattern: vortex_wings, Projectile: fireball
Intensity: 0.7, Speed: 1.2">vortex_wings</div><div class="entry" style="left: 85.71428571428571%; width: 2.6785714285714284%; background: #FF8333;" title="Medium shots - falling spiral pattern (192s - 198s)
Pattern: spiral_rain, Projectile: fireball
Intensity: 0.5, Speed: 0.9">spiral_rain</div><div class="entry" style="left: 88.39285714285714%; width: 1.7857142857142856%; background: #FF3383;" title="Faster shots - magnetic pattern (198s - 202s)
Pattern: soul_magnetism, Projectile: fireball
Intensity: 0.7, Speed: 1.2">soul_magnetism</div><div class="entry" style="left: 90.17857142857143%; width: 2.6785714285714284%; background: #33FFFF;" title="Very fast burst and arpeggios - reactive chain pattern (202s - 208s)
Pattern: alchemical_chain, Projectile: lava_droplets
Intensity: 0.8, Speed: 1.3">alchemical_chain</div><div class="entry" style="left: 92.85714285714286%; width: 1.3392857142857142%; background: #3383FF;" title="Triplet shots - changing behavior pattern (208s - 211s)
Pattern: shifting_polarity, Projectile: blood_threads
Intensity: 0.7, Speed: 1.1">shifting_polarity</div><div class="entry" style="left: 94.19642857142857%; width: 4.017857142857143%; background: #33FFFF;" title="Arpeggios - reactive chain pattern (211s - 220s)
Pattern: alchemical_chain, Projectile: lava_droplets
Intensity: 0.7, Speed: 1.2">alchemical_chain</div><div class="entry" style="left: 98.21428571428571%; width: 1.7857142857142856%; background: #83FFFF;" title="Medium shots - delayed acceleration pattern (220s - 224s)
Pattern: delay_accelerate, Projectile: fireball
Intensity: 0.5, Speed: 0.9">delay_accelerate</div>
      
      <!-- Screen shake timestamps -->
      <div class="shake" style="left: 0%" title="Screen shake at 0s"></div><div class="shake" style="left: 1.7857142857142856%" title="Screen shake at 4s"></div><div class="shake" style="left: 3.125%" title="Screen shake at 7s"></div><div class="shake" style="left: 4.017857142857143%" title="Screen shake at 9s"></div><div class="shake" style="left: 5.803571428571429%" title="Screen shake at 13s"></div><div class="shake" style="left: 7.142857142857142%" title="Screen shake at 16s"></div><div class="shake" style="left: 8.928571428571429%" title="Screen shake at 20s"></div><div class="shake" style="left: 10.714285714285714%" title="Screen shake at 24s"></div><div class="shake" style="left: 16.517857142857142%" title="Screen shake at 37s"></div><div class="shake" style="left: 18.75%" title="Screen shake at 42s"></div><div class="shake" style="left: 20.089285714285715%" title="Screen shake at 45s"></div><div class="shake" style="left: 21.428571428571427%" title="Screen shake at 48s"></div><div class="shake" style="left: 22.767857142857142%" title="Screen shake at 51s"></div><div class="shake" style="left: 28.57142857142857%" title="Screen shake at 64s"></div><div class="shake" style="left: 31.25%" title="Screen shake at 70s"></div><div class="shake" style="left: 33.92857142857143%" title="Screen shake at 76s"></div><div class="shake" style="left: 37.05357142857143%" title="Screen shake at 83s"></div><div class="shake" style="left: 40.17857142857143%" title="Screen shake at 90s"></div><div class="shake" style="left: 42.857142857142854%" title="Screen shake at 96s"></div><div class="shake" style="left: 45.982142857142854%" title="Screen shake at 103s"></div><div class="shake" style="left: 47.32142857142857%" title="Screen shake at 106s"></div><div class="shake" style="left: 48.660714285714285%" title="Screen shake at 109s"></div><div class="shake" style="left: 52.67857142857143%" title="Screen shake at 118s"></div><div class="shake" style="left: 58.48214285714286%" title="Screen shake at 131s"></div><div class="shake" style="left: 60.26785714285714%" title="Screen shake at 135s"></div><div class="shake" style="left: 61.60714285714286%" title="Screen shake at 138s"></div><div class="shake" style="left: 62.5%" title="Screen shake at 140s"></div><div class="shake" style="left: 64.28571428571429%" title="Screen shake at 144s"></div><div class="shake" style="left: 66.96428571428571%" title="Screen shake at 150s"></div><div class="shake" style="left: 70.53571428571429%" title="Screen shake at 158s"></div><div class="shake" style="left: 72.76785714285714%" title="Screen shake at 163s"></div><div class="shake" style="left: 75.89285714285714%" title="Screen shake at 170s"></div><div class="shake" style="left: 77.23214285714286%" title="Screen shake at 173s"></div><div class="shake" style="left: 78.57142857142857%" title="Screen shake at 176s"></div><div class="shake" style="left: 84.375%" title="Screen shake at 189s"></div><div class="shake" style="left: 85.71428571428571%" title="Screen shake at 192s"></div><div class="shake" style="left: 88.39285714285714%" title="Screen shake at 198s"></div><div class="shake" style="left: 90.17857142857143%" title="Screen shake at 202s"></div><div class="shake" style="left: 92.85714285714286%" title="Screen shake at 208s"></div><div class="shake" style="left: 94.19642857142857%" title="Screen shake at 211s"></div><div class="shake" style="left: 98.21428571428571%" title="Screen shake at 220s"></div><div class="shake" style="left: 100%" title="Screen shake at 224s"></div>
    </div>
    
    <div class="timeline-list">
      <h2>Timeline Entries</h2>
      <table>
        <tr>
          <th>Time</th>
          <th>Pattern</th>
          <th>Projectile</th>
          <th>Intensity</th>
          <th>Speed</th>
          <th>Description</th>
        </tr>
        
          <tr>
            <td>0s - 4s</td>
            <td>spiral_wave</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Intro - spiral pattern</td>
          </tr>
        
          <tr>
            <td>4s - 7s</td>
            <td>lightning_chain</td>
            <td>fireball</td>
            <td>0.6</td>
            <td>1</td>
            <td>First buildup - lightning pattern</td>
          </tr>
        
          <tr>
            <td>7s - 9s</td>
            <td>laser_grid</td>
            <td>ash_cinders</td>
            <td>0.4</td>
            <td>0.8</td>
            <td>Break - grid pattern</td>
          </tr>
        
          <tr>
            <td>9s - 13s</td>
            <td>petal_spread</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Verse - petal pattern</td>
          </tr>
        
          <tr>
            <td>13s - 16s</td>
            <td>spiral_bloom</td>
            <td>lava_droplets</td>
            <td>0.6</td>
            <td>1</td>
            <td>Arpeggios - bloom pattern</td>
          </tr>
        
          <tr>
            <td>16s - 20s</td>
            <td>circle_ripple</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Verse - ripple pattern</td>
          </tr>
        
          <tr>
            <td>20s - 24s</td>
            <td>alchemical_chain</td>
            <td>lava_droplets</td>
            <td>0.7</td>
            <td>1.1</td>
            <td>Arpeggios - reactive chain pattern</td>
          </tr>
        
          <tr>
            <td>24s - 37s</td>
            <td>spiral_rain</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Medium shots - falling spiral pattern</td>
          </tr>
        
          <tr>
            <td>37s - 42s</td>
            <td>lightning_chain</td>
            <td>fireball</td>
            <td>0.6</td>
            <td>1</td>
            <td>Buildup - lightning pattern</td>
          </tr>
        
          <tr>
            <td>42s - 45s</td>
            <td>laser_grid</td>
            <td>ash_cinders</td>
            <td>0.4</td>
            <td>0.8</td>
            <td>Break - grid pattern</td>
          </tr>
        
          <tr>
            <td>45s - 48s</td>
            <td>petal_spread</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Verse - petal pattern</td>
          </tr>
        
          <tr>
            <td>48s - 51s</td>
            <td>alchemical_chain</td>
            <td>lava_droplets</td>
            <td>0.6</td>
            <td>1</td>
            <td>Arpeggios - reactive chain pattern</td>
          </tr>
        
          <tr>
            <td>51s - 64s</td>
            <td>boomerang_arcs</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Medium shots - arcing boomerang pattern</td>
          </tr>
        
          <tr>
            <td>64s - 70s</td>
            <td>vortex_wings</td>
            <td>fireball</td>
            <td>0.7</td>
            <td>1.2</td>
            <td>Faster shots in player direction at 360 degree - wing-like vortex pattern</td>
          </tr>
        
          <tr>
            <td>70s - 76s</td>
            <td>spiral_rain</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Medium shots - falling spiral pattern</td>
          </tr>
        
          <tr>
            <td>76s - 83s</td>
            <td>soul_magnetism</td>
            <td>fireball</td>
            <td>0.7</td>
            <td>1.2</td>
            <td>Faster shots - magnetic pattern</td>
          </tr>
        
          <tr>
            <td>83s - 90s</td>
            <td>alchemical_chain</td>
            <td>lava_droplets</td>
            <td>0.8</td>
            <td>1.3</td>
            <td>Very fast burst and arpeggios - reactive chain pattern</td>
          </tr>
        
          <tr>
            <td>90s - 96s</td>
            <td>shifting_polarity</td>
            <td>blood_threads</td>
            <td>0.7</td>
            <td>1.1</td>
            <td>Triplet shots - changing behavior pattern</td>
          </tr>
        
          <tr>
            <td>96s - 103s</td>
            <td>alchemical_chain</td>
            <td>lava_droplets</td>
            <td>0.7</td>
            <td>1.2</td>
            <td>Arpeggios - reactive chain pattern</td>
          </tr>
        
          <tr>
            <td>103s - 106s</td>
            <td>delay_accelerate</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Medium shots - delayed acceleration pattern</td>
          </tr>
        
          <tr>
            <td>106s - 109s</td>
            <td>fire_kraken</td>
            <td>blood_threads</td>
            <td>0.6</td>
            <td>1</td>
            <td>Bursts of short sounds - tentacle pattern</td>
          </tr>
        
          <tr>
            <td>109s - 118s</td>
            <td>spiral_rain</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Shots in 360 degree - falling spiral pattern</td>
          </tr>
        
          <tr>
            <td>118s - 131s</td>
            <td>inhale_exhale</td>
            <td>fireball</td>
            <td>0.6</td>
            <td>1.1</td>
            <td>Faster shots in player direction at 90 degree - push/pull pattern</td>
          </tr>
        
          <tr>
            <td>131s - 135s</td>
            <td>shifting_polarity</td>
            <td>blood_threads</td>
            <td>0.65</td>
            <td>1</td>
            <td>Bursts and repeating notes - changing behavior pattern</td>
          </tr>
        
          <tr>
            <td>135s - 138s</td>
            <td>fire_kraken</td>
            <td>blood_threads</td>
            <td>0.6</td>
            <td>1</td>
            <td>Bursts of short sounds - tentacle pattern</td>
          </tr>
        
          <tr>
            <td>138s - 140s</td>
            <td>spiral_rain</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Shots in 360 degree - falling spiral pattern</td>
          </tr>
        
          <tr>
            <td>140s - 144s</td>
            <td>inhale_exhale</td>
            <td>fireball</td>
            <td>0.6</td>
            <td>1.1</td>
            <td>Faster shots in player direction at 90 degree - push/pull pattern</td>
          </tr>
        
          <tr>
            <td>144s - 150s</td>
            <td>shifting_polarity</td>
            <td>blood_threads</td>
            <td>0.65</td>
            <td>1</td>
            <td>Bursts and repeating notes - changing behavior pattern</td>
          </tr>
        
          <tr>
            <td>150s - 158s</td>
            <td>delay_accelerate</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Medium shots - delayed acceleration pattern</td>
          </tr>
        
          <tr>
            <td>158s - 163s</td>
            <td>divine_divide</td>
            <td>fireball</td>
            <td>0.7</td>
            <td>1</td>
            <td>360 degree intense - divided pattern</td>
          </tr>
        
          <tr>
            <td>163s - 170s</td>
            <td>breath_trails</td>
            <td>fireball</td>
            <td>0.7</td>
            <td>1.2</td>
            <td>Faster shots in player direction at 90 degree - trailing pattern</td>
          </tr>
        
          <tr>
            <td>170s - 173s</td>
            <td>alchemical_chain</td>
            <td>lava_droplets</td>
            <td>0.7</td>
            <td>1.2</td>
            <td>Arpeggios - reactive chain pattern</td>
          </tr>
        
          <tr>
            <td>173s - 176s</td>
            <td>fake_out_pulse</td>
            <td>blood_threads</td>
            <td>0.75</td>
            <td>1.1</td>
            <td>Fast bursts along with arp - pulsing fake-out pattern</td>
          </tr>
        
          <tr>
            <td>176s - 189s</td>
            <td>boomerang_arcs</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Medium shots - arcing boomerang pattern</td>
          </tr>
        
          <tr>
            <td>189s - 192s</td>
            <td>vortex_wings</td>
            <td>fireball</td>
            <td>0.7</td>
            <td>1.2</td>
            <td>Faster shots in player direction at 360 degree - wing-like vortex pattern</td>
          </tr>
        
          <tr>
            <td>192s - 198s</td>
            <td>spiral_rain</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Medium shots - falling spiral pattern</td>
          </tr>
        
          <tr>
            <td>198s - 202s</td>
            <td>soul_magnetism</td>
            <td>fireball</td>
            <td>0.7</td>
            <td>1.2</td>
            <td>Faster shots - magnetic pattern</td>
          </tr>
        
          <tr>
            <td>202s - 208s</td>
            <td>alchemical_chain</td>
            <td>lava_droplets</td>
            <td>0.8</td>
            <td>1.3</td>
            <td>Very fast burst and arpeggios - reactive chain pattern</td>
          </tr>
        
          <tr>
            <td>208s - 211s</td>
            <td>shifting_polarity</td>
            <td>blood_threads</td>
            <td>0.7</td>
            <td>1.1</td>
            <td>Triplet shots - changing behavior pattern</td>
          </tr>
        
          <tr>
            <td>211s - 220s</td>
            <td>alchemical_chain</td>
            <td>lava_droplets</td>
            <td>0.7</td>
            <td>1.2</td>
            <td>Arpeggios - reactive chain pattern</td>
          </tr>
        
          <tr>
            <td>220s - 224s</td>
            <td>delay_accelerate</td>
            <td>fireball</td>
            <td>0.5</td>
            <td>0.9</td>
            <td>Medium shots - delayed acceleration pattern</td>
          </tr>
        
      </table>
    </div>
  </div>
  
  <script>
    // Zoom functionality
    document.querySelectorAll('.zoom').forEach(button => {
      button.addEventListener('click', () => {
        const timeline = document.getElementById('timeline');
        const factor = parseFloat(button.dataset.factor);
        const currentWidth = timeline.style.width ? parseInt(timeline.style.width) : 100;
        const newWidth = currentWidth * factor;
        timeline.style.width = newWidth + '%';
      });
    });
    
    // Mouse position time display
    const timeline = document.getElementById('timeline');
    const timeDisplay = document.getElementById('time-display');
    const duration = 224; // Total duration in seconds
    
    timeline.addEventListener('mousemove', (e) => {
      const rect = timeline.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = x / rect.width;
      const time = percentage * duration;
      timeDisplay.textContent = time.toFixed(2) + 's';
    });
  </script>
</body>
</html>
