# Adaptive Music System

This folder contains the music assets for the game's adaptive music system, inspired by LucasArts' iMUSE system.

## Structure

The music system is organized by area, with each area having its own folder containing:

- `loop.mp3` - The main looping theme for the area
- `leitmotifs/` - Folder containing leitmotif layers that can be mixed with the main theme
- `specialRooms/` - Folder containing music for special rooms (shops, altars, etc.)
- `transitions/` - Folder containing transition music between areas

## Areas

### Catacombs
- **Tempo**: 95 BPM
- **Main Loop**: `catacombs/loop.mp3`
- **Leitmotifs**: 
  - `mother_layer.mp3` - Plays when encountering mother-related story elements
  - `guilt_layer.mp3` - Plays during guilt-related story moments
- **Special Rooms**:
  - Shop: `specialRooms/shop/loop.mp3`
  - Altar: `specialRooms/altar/loop.mp3`
- **Transitions**:
  - To Lava Tubes: `transitions/to_lava_tubes.mp3`

### <PERSON><PERSON>
- **Tempo**: 110 BPM
- **Main Loop**: `lava_tubes/loop.mp3`
- **Transitions**:
  - To Catacombs: `transitions/to_catacombs.mp3`

## Stingers

One-shot musical elements that play during specific events:

- `player_hit.mp3` - Plays when the player takes damage
- `player_death.mp3` - Plays when the player dies
- `enemy_death.mp3` - Plays when an enemy is defeated
- `boss_appear.mp3` - Plays when a boss appears

## Adding New Music

1. Create the appropriate folder structure for your area
2. Add the main loop, transitions, and special room music
3. Update `src/audio/musicData.js` with the new area information
4. Test transitions between areas

## Technical Details

The music system supports:
- Seamless transitions between areas
- Dynamic layering of leitmotifs
- Tempo stretching based on combat intensity
- Health-based audio filtering
- Distortion effects on player damage

Press 'M' in-game to toggle the music debug HUD.
