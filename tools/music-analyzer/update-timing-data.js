/**
 * Update Timing Data
 * 
 * This script updates the comprehensive data file with more precise timing information
 * to ensure better synchronization between music and projectiles.
 */

const fs = require('fs');
const path = require('path');

// Get the data file path from command line arguments
const dataFilePath = process.argv[2];

if (!dataFilePath) {
  console.error('Please provide a data file path');
  console.error('Usage: node update-timing-data.js path/to/data.json');
  process.exit(1);
}

// Load the data file
try {
  const data = JSON.parse(fs.readFileSync(dataFilePath, 'utf8'));
  
  console.log(`Loaded data file with ${data.timeline.length} timeline entries and ${data.screenShakeTimestamps.length} screen shake timestamps`);
  
  // Update timing information
  // 1. Add precise trigger intervals based on musical structure
  // 2. Add timing metadata
  
  // Add timing metadata
  data.metadata.timingInfo = {
    recommendedBufferTimeMs: 200,
    recommendedSpeedMultiplier: 1.2,
    baseTempoMs: 500, // 120 BPM = 500ms per beat
    updated: new Date().toISOString()
  };
  
  // Update each timeline entry with more precise trigger intervals
  data.timeline.forEach(entry => {
    // Calculate trigger intervals based on the entry's musical characteristics
    // For example, faster intervals for more intense sections
    if (entry.intensity >= 0.7) {
      // High intensity sections - faster triggers
      entry.triggerIntervalMin = Math.max(0.4, entry.triggerIntervalMin * 0.8);
      entry.triggerIntervalMax = Math.max(0.8, entry.triggerIntervalMax * 0.8);
    } else if (entry.intensity <= 0.4) {
      // Low intensity sections - slower triggers
      entry.triggerIntervalMin = Math.min(2.0, entry.triggerIntervalMin * 1.2);
      entry.triggerIntervalMax = Math.min(3.0, entry.triggerIntervalMax * 1.2);
    }
    
    // Adjust speed multipliers to make projectiles faster
    entry.originalSpeedMultiplier = entry.speedMultiplier;
    entry.speedMultiplier = entry.speedMultiplier * 1.2;
    
    // Add timing flags
    entry.timingFlags = {
      requiresPreciseTiming: entry.patterns.some(p => 
        p.includes('chain') || p.includes('bloom') || p.includes('ripple')
      ),
      isRhythmicSection: entry.description.includes('arpeggio') || 
                         entry.description.includes('burst') ||
                         entry.description.includes('beat')
    };
  });
  
  // Save the updated data
  const outputPath = dataFilePath.replace('.json', '_updated.json');
  fs.writeFileSync(outputPath, JSON.stringify(data, null, 2));
  
  console.log(`Updated data saved to: ${outputPath}`);
  console.log(`- Added timing metadata`);
  console.log(`- Updated trigger intervals based on intensity`);
  console.log(`- Adjusted speed multipliers`);
  console.log(`- Added timing flags for precise synchronization`);
  
} catch (error) {
  console.error('Error updating timing data:', error);
  process.exit(1);
}
