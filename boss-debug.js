/**
 * Simple debug overlay for the boss battle system
 */

// Create debug overlay
function createDebugOverlay() {
    // Check if overlay already exists
    if (document.getElementById('boss-debug-overlay')) {
        return;
    }
    
    // Create overlay container
    const overlay = document.createElement('div');
    overlay.id = 'boss-debug-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '10px';
    overlay.style.left = '10px';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    overlay.style.color = '#fff';
    overlay.style.padding = '10px';
    overlay.style.borderRadius = '5px';
    overlay.style.fontFamily = 'monospace';
    overlay.style.zIndex = '1000';
    overlay.style.pointerEvents = 'none'; // Don't block mouse events
    
    // Create title
    const title = document.createElement('h3');
    title.textContent = 'Boss Battle Debug';
    title.style.margin = '0 0 5px 0';
    overlay.appendChild(title);
    
    // Create intensity display
    const intensityDisplay = document.createElement('div');
    intensityDisplay.id = 'intensity-display';
    intensityDisplay.textContent = 'Music Intensity: 0%';
    overlay.appendChild(intensityDisplay);
    
    // Create pattern display
    const patternDisplay = document.createElement('div');
    patternDisplay.id = 'pattern-display';
    patternDisplay.textContent = 'Current Pattern: None';
    overlay.appendChild(patternDisplay);
    
    // Create beat display
    const beatDisplay = document.createElement('div');
    beatDisplay.id = 'beat-display';
    beatDisplay.textContent = 'Beat Detected: No';
    overlay.appendChild(beatDisplay);
    
    // Create FPS display
    const fpsDisplay = document.createElement('div');
    fpsDisplay.id = 'fps-display';
    fpsDisplay.textContent = 'FPS: 0';
    overlay.appendChild(fpsDisplay);
    
    // Add to document
    document.body.appendChild(overlay);
    
    console.log('Boss debug overlay created');
}

// Update debug overlay
function updateDebugOverlay() {
    // Check if we have access to the game
    if (!window.sceneManager || !window.sceneManager.currentHandler) {
        return;
    }
    
    const dungeonHandler = window.sceneManager.currentHandler;
    
    // Check if we have active bosses
    if (!dungeonHandler.activeBosses || dungeonHandler.activeBosses.length === 0) {
        document.getElementById('intensity-display').textContent = 'Music Intensity: No active boss';
        return;
    }
    
    // Get the first active boss
    const boss = dungeonHandler.activeBosses[0];
    if (!boss || !boss.userData || !boss.userData.aiController || !boss.userData.aiController.bossController) {
        return;
    }
    
    // Get boss controller and debug info
    const bossController = boss.userData.aiController.bossController;
    const debugInfo = bossController.getDebugInfo();
    
    // Update debug display
    document.getElementById('intensity-display').textContent = `Music Intensity: ${Math.round(debugInfo.intensity)}%`;
    document.getElementById('pattern-display').textContent = `Current Pattern: ${debugInfo.lastPattern || 'None'}`;
    document.getElementById('beat-display').textContent = `Beat Detected: ${debugInfo.beatDetected ? 'Yes' : 'No'}`;
}

// Calculate and display FPS
let frameCount = 0;
let lastTime = performance.now();

function updateFPS() {
    frameCount++;
    
    const currentTime = performance.now();
    const elapsed = currentTime - lastTime;
    
    if (elapsed >= 1000) {
        const fps = Math.round((frameCount * 1000) / elapsed);
        
        if (document.getElementById('fps-display')) {
            document.getElementById('fps-display').textContent = `FPS: ${fps}`;
        }
        
        frameCount = 0;
        lastTime = currentTime;
    }
    
    requestAnimationFrame(updateFPS);
}

// Initialize debug overlay
function initDebugOverlay() {
    createDebugOverlay();
    
    // Update debug overlay every 200ms (less frequent to reduce lag)
    setInterval(updateDebugOverlay, 200);
    
    // Start FPS counter
    updateFPS();
    
    console.log('Boss debug overlay initialized');
}

// Initialize when the page loads
window.addEventListener('load', initDebugOverlay);

// Export for direct use
export { initDebugOverlay };
