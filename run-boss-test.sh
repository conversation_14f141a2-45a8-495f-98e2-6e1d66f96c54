#!/bin/bash

# Run the boss battle test
echo "Starting vibe-reactive boss battle test..."
echo "Opening test-boss-battle.html in your default browser..."

# Open the test HTML file in the default browser
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    open test-boss-battle.html
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    xdg-open test-boss-battle.html
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    start test-boss-battle.html
else
    echo "Unsupported OS. Please open test-boss-battle.html manually."
fi

echo "Test started! The boss battle should begin automatically after the game loads."
echo "The bullet patterns should react to the music's intensity and rhythm."
echo "Check the debug overlay in the top-left corner for real-time music analysis info."
