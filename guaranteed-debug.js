/**
 * Guaranteed visible debug overlay for the boss battle system
 */

// Global FPS tracking
window.lastFps = 60;
window.frameCount = 0;
window.lastTime = performance.now();

// Update FPS
function updateFps() {
    window.frameCount++;
    
    const currentTime = performance.now();
    const elapsed = currentTime - window.lastTime;
    
    if (elapsed >= 1000) {
        window.lastFps = Math.round((window.frameCount * 1000) / elapsed);
        window.frameCount = 0;
        window.lastTime = currentTime;
    }
    
    requestAnimationFrame(updateFps);
}

// Start FPS tracking
updateFps();

// Create the debug overlay with absolute positioning and high z-index
function createGuaranteedDebugOverlay() {
    // Remove any existing overlay
    const existingOverlay = document.getElementById('guaranteed-debug-overlay');
    if (existingOverlay) {
        document.body.removeChild(existingOverlay);
    }
    
    // Create overlay container with absolute positioning
    const overlay = document.createElement('div');
    overlay.id = 'guaranteed-debug-overlay';
    
    // Style to ensure visibility
    overlay.style.position = 'fixed';
    overlay.style.top = '10px';
    overlay.style.left = '10px';
    overlay.style.width = '250px';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    overlay.style.color = '#fff';
    overlay.style.padding = '10px';
    overlay.style.borderRadius = '5px';
    overlay.style.fontFamily = 'monospace';
    overlay.style.fontSize = '14px';
    overlay.style.zIndex = '9999'; // Very high z-index
    overlay.style.pointerEvents = 'none'; // Don't block mouse events
    overlay.style.border = '2px solid #ff0000'; // Red border for visibility
    overlay.style.boxShadow = '0 0 10px rgba(255, 0, 0, 0.5)'; // Red glow
    
    // Create title
    const title = document.createElement('div');
    title.textContent = '🎮 BOSS BATTLE DEBUG 🎮';
    title.style.fontWeight = 'bold';
    title.style.marginBottom = '5px';
    title.style.textAlign = 'center';
    title.style.color = '#ff0000'; // Red text
    overlay.appendChild(title);
    
    // Create intensity display
    const intensityDisplay = document.createElement('div');
    intensityDisplay.id = 'intensity-display';
    intensityDisplay.textContent = '🎵 Music Intensity: 0%';
    overlay.appendChild(intensityDisplay);
    
    // Create pattern display
    const patternDisplay = document.createElement('div');
    patternDisplay.id = 'pattern-display';
    patternDisplay.textContent = '🔄 Pattern: None';
    overlay.appendChild(patternDisplay);
    
    // Create beat display
    const beatDisplay = document.createElement('div');
    beatDisplay.id = 'beat-display';
    beatDisplay.textContent = '🥁 Beat: No';
    overlay.appendChild(beatDisplay);
    
    // Create FPS display
    const fpsDisplay = document.createElement('div');
    fpsDisplay.id = 'fps-display';
    fpsDisplay.textContent = '⚡ FPS: 0';
    overlay.appendChild(fpsDisplay);
    
    // Create projectile count display
    const projectileDisplay = document.createElement('div');
    projectileDisplay.id = 'projectile-display';
    projectileDisplay.textContent = '🔫 Projectiles: 0';
    overlay.appendChild(projectileDisplay);
    
    // Add to document
    document.body.appendChild(overlay);
    
    console.log('Guaranteed debug overlay created');
    return overlay;
}

// Update the debug overlay
function updateDebugOverlay() {
    // Make sure the overlay exists
    let overlay = document.getElementById('guaranteed-debug-overlay');
    if (!overlay) {
        overlay = createGuaranteedDebugOverlay();
    }
    
    try {
        // Update FPS display
        const fpsDisplay = document.getElementById('fps-display');
        if (fpsDisplay) {
            fpsDisplay.textContent = `⚡ FPS: ${window.lastFps || 0}`;
            
            // Change color based on FPS
            if (window.lastFps < 20) {
                fpsDisplay.style.color = '#ff0000'; // Red for low FPS
            } else if (window.lastFps < 40) {
                fpsDisplay.style.color = '#ffff00'; // Yellow for medium FPS
            } else {
                fpsDisplay.style.color = '#00ff00'; // Green for high FPS
            }
        }
        
        // Check if we have access to the game
        if (!window.sceneManager || !window.sceneManager.currentHandler) {
            document.getElementById('intensity-display').textContent = '🎵 Music Intensity: No game';
            return;
        }
        
        const dungeonHandler = window.sceneManager.currentHandler;
        
        // Update projectile count
        const projectileDisplay = document.getElementById('projectile-display');
        if (projectileDisplay && dungeonHandler.activeProjectiles) {
            projectileDisplay.textContent = `🔫 Projectiles: ${dungeonHandler.activeProjectiles.length}`;
        }
        
        // Check if we have active bosses
        if (!dungeonHandler.activeBosses || dungeonHandler.activeBosses.length === 0) {
            document.getElementById('intensity-display').textContent = '🎵 Music Intensity: No boss';
            return;
        }
        
        // Get the first active boss
        const boss = dungeonHandler.activeBosses[0];
        if (!boss || !boss.userData || !boss.userData.aiController || !boss.userData.aiController.bossController) {
            document.getElementById('intensity-display').textContent = '🎵 Music Intensity: No controller';
            return;
        }
        
        // Get boss controller and debug info
        const bossController = boss.userData.aiController.bossController;
        const debugInfo = bossController.getDebugInfo();
        
        // Update debug display
        document.getElementById('intensity-display').textContent = `🎵 Music Intensity: ${Math.round(debugInfo.intensity)}%`;
        document.getElementById('pattern-display').textContent = `🔄 Pattern: ${debugInfo.lastPattern || 'None'}`;
        document.getElementById('beat-display').textContent = `🥁 Beat: ${debugInfo.beatDetected ? 'Yes' : 'No'}`;
    } catch (error) {
        console.error("Error updating debug overlay:", error);
        
        // Try to display the error
        const intensityDisplay = document.getElementById('intensity-display');
        if (intensityDisplay) {
            intensityDisplay.textContent = `Error: ${error.message}`;
        }
    }
}

// Initialize and start updating
function initGuaranteedDebugOverlay() {
    // Create the overlay
    createGuaranteedDebugOverlay();
    
    // Update every 100ms
    setInterval(updateDebugOverlay, 100);
    
    console.log('Guaranteed debug overlay initialized');
}

// Initialize immediately
console.log('Guaranteed debug script loaded');
initGuaranteedDebugOverlay();

// Also initialize when the page loads (backup)
window.addEventListener('load', function() {
    console.log('Window loaded, ensuring debug overlay is visible');
    initGuaranteedDebugOverlay();
});
