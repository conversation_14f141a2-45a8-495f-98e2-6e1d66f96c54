import SceneManager from './src/core/SceneManager.js';

// --- Global Variables (that might be needed across modules or for debugging) ---
// Keep essential variables if needed, otherwise move them into relevant modules.
// Example: Maybe keep font globally accessible initially, though it's better managed.
let font = null;

// --- Constants (if not moved to constants.js yet) ---
// Remove STATE object, questions_data, dialogues, etc., they will be handled by specific modules.

// --- Core Logic ---
function main() {
    console.log("Starting application...");
    const sceneManager = new SceneManager();

    // TODO: Load assets here (like the font) before starting the SceneManager fully
    // For now, SceneManager's start method handles placeholder loading

    sceneManager.start();

    // Apply the NES preset on game start and then customize specific values
    setTimeout(() => {
        if (sceneManager.consolePresetEffect && sceneManager.consolePresetEffect.manager) {
            console.log("Applying NES preset on game start");
            // First apply the NES preset
            sceneManager.consolePresetEffect.manager.applyPreset('nes');

            // Then customize specific values
            if (sceneManager.hdrFramerateEffect && sceneManager.hdrFramerateEffect.manager) {
                console.log("Customizing visual settings");
                // First apply the NES preset in HDR settings
                sceneManager.hdrFramerateEffect.manager.applyPreset('nes');
                // Force the current preset to be 'nes'
                sceneManager.hdrFramerateEffect.manager.currentPreset = 'nes';
                // Then override specific values
                // Set bit depth to 4.0
                sceneManager.hdrFramerateEffect.manager.adjustParameter('bitDepth', 4.0);
                // Set dither strength to 0.1 as requested
                sceneManager.hdrFramerateEffect.manager.adjustParameter('ditherStrength', 0.1);
                // Make sure dithering is enabled
                sceneManager.hdrFramerateEffect.manager.adjustParameter('ditherEnabled', true);
            }

            // HDR and framerate settings are applied first
            // We'll apply CRT settings after a short delay to ensure proper order

            // Update the control panels if they exist
            if (sceneManager.consolePresetEffect.controlPanel) {
                sceneManager.consolePresetEffect.controlPanel.updateControls();
            }

            // First update, then try again after a delay to ensure it takes effect
            if (sceneManager.hdrFramerateEffect && sceneManager.hdrFramerateEffect.controlPanel) {
                console.log("First attempt to force NES preset selection");
                sceneManager.hdrFramerateEffect.controlPanel.forceSelectPreset('nes');
                sceneManager.hdrFramerateEffect.controlPanel.updateSliders();

                // Try again after a short delay
                setTimeout(() => {
                    console.log("Second attempt to force NES preset selection");
                    sceneManager.hdrFramerateEffect.controlPanel.forceSelectPreset('nes');

                    // As a last resort, directly modify the DOM
                    const select = document.getElementById('hdr-preset-select');
                    if (select) {
                        console.log("Direct DOM manipulation to select NES preset");
                        // Try to find the NES option
                        for (let i = 0; i < select.options.length; i++) {
                            if (select.options[i].value === 'nes') {
                                select.selectedIndex = i;
                                // Trigger change event
                                const event = new Event('change');
                                select.dispatchEvent(event);
                                console.log(`Selected NES preset at index ${i} via direct DOM access`);
                                break;
                            }
                        }
                    }
                }, 500);
            }

            // Apply CRT settings after HDR settings with a delay to ensure proper initialization
            setTimeout(() => {
                if (sceneManager.crtEffect && sceneManager.crtEffect.manager) {
                    console.log("Applying Arcade Machine CRT preset");
                    // Apply preset first, then enable
                    sceneManager.crtEffect.manager.applyPreset('arcade_monitor');
                    // Force enable (using the new enable method)
                    sceneManager.crtEffect.manager.enable();
                    // Force the current preset to be 'arcade_monitor'
                    sceneManager.crtEffect.manager.currentPreset = 'arcade_monitor';
                    // Enable Line-By-Line Refresh visualization with speed 0.10
                    sceneManager.crtEffect.manager.enableRefreshVisualization(true, 0.10);

                    // Update the CRT control panel
                    if (sceneManager.crtEffect.controlPanel) {
                        // Force select the arcade_monitor preset in the UI
                        sceneManager.crtEffect.controlPanel.forceSelectPreset('arcade_monitor');
                        console.log("CRT control panel updated with arcade_monitor preset");
                    }
                }
            }, 1000); // 1 second delay after HDR settings
        }
    }, 2000); // Increased delay to ensure everything is fully initialized

    // Make sceneManager accessible globally for debugging if needed
    window.sceneManager = sceneManager;

    // Final check to ensure CRT settings are applied correctly
    setTimeout(() => {
        if (sceneManager.crtEffect && sceneManager.crtEffect.manager) {
            console.log("Final check for CRT settings");
            if (sceneManager.crtEffect.manager.currentPreset !== 'arcade_monitor' || !sceneManager.crtEffect.manager.enabled) {
                console.log("Re-applying Arcade Machine CRT preset");
                // Apply preset first, then enable
                sceneManager.crtEffect.manager.applyPreset('arcade_monitor');
                // Force enable (using the new enable method)
                sceneManager.crtEffect.manager.enable();
                // Force the current preset to be 'arcade_monitor'
                sceneManager.crtEffect.manager.currentPreset = 'arcade_monitor';
                sceneManager.crtEffect.manager.enableRefreshVisualization(true, 0.10);

                if (sceneManager.crtEffect.controlPanel) {
                    // Force select the arcade_monitor preset in the UI
                    sceneManager.crtEffect.controlPanel.forceSelectPreset('arcade_monitor');
                    console.log("Final check: CRT control panel updated with arcade_monitor preset");
                }
            }
        }
    }, 5000); // Check 5 seconds after initialization
}

// --- Initialization ---
// Ensure the DOM is ready before starting
if (document.readyState === 'loading') {  // Loading hasn't finished yet
    document.addEventListener('DOMContentLoaded', () => {
        console.log("DOMContentLoaded event fired. Running main()...");
        main();
    });
} else {  // `DOMContentLoaded` has already fired
    console.log("DOM was already ready. Running main()...");
    main();
}

// --- Remove Old Code ---
// Delete all the functions like init, createHeroPage, createQuestionnaireScene,
// createCardRevealScene, animate, onWindowResize, onMouseMove, onClick,
// typeWriter, fadeTransition, clearScene, etc.
// Delete all the global variable declarations for scene, camera, renderer, meshes,
// animation states, interactableObjects, environmentObjects, etc.
// These responsibilities are being moved to SceneManager and specific scene handlers.
