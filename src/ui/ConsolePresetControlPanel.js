/**
 * Control panel for console presets and game FPS settings
 */
class ConsolePresetControlPanel {
    /**
     * Create a new Console Preset Control Panel
     * @param {ConsolePresetManager} consoleManager - The Console Preset Manager
     */
    constructor(consoleManager) {
        this.consoleManager = consoleManager;
        this.visible = false;

        // Create container
        this.container = document.createElement('div');
        this.container.style.position = 'absolute';
        this.container.style.top = '50%';
        this.container.style.left = '50%';
        this.container.style.transform = 'translate(-50%, -50%)';
        this.container.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
        this.container.style.border = '2px solid #00f';
        this.container.style.boxShadow = '0 0 20px #00f';
        this.container.style.color = '#00f';
        this.container.style.fontFamily = 'monospace';
        this.container.style.padding = '20px';
        this.container.style.zIndex = '1000';
        this.container.style.width = '500px';
        this.container.style.maxHeight = '80vh';
        this.container.style.overflowY = 'auto';
        this.container.style.display = 'none'; // Initially hidden

        // Create title
        const title = document.createElement('h2');
        title.textContent = 'CONSOLE PRESETS & GAME FPS';
        title.style.textAlign = 'center';
        title.style.margin = '0 0 20px 0';
        title.style.textShadow = '0 0 5px #00f';
        title.style.fontFamily = 'monospace';
        this.container.appendChild(title);

        // Create preset selector
        this.createPresetSelector();

        // Create game FPS controls
        this.createGameFPSControls();

        // Create 8-bit code controls
        this.createEightBitCodeControls();

        // Create toggle button
        this.createToggleButton();

        // Add to document
        document.body.appendChild(this.container);

        // Note: F3 keyboard shortcut is handled in ConsolePresetIntegration.js

        // Add custom CSS for retro styling
        this.addRetroStyles();
    }

    /**
     * Add retro styling CSS to document
     */
    addRetroStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .console-section {
                margin-bottom: 20px;
                border: 1px solid #00f;
                padding: 15px;
                background-color: rgba(0, 0, 40, 0.3);
            }

            .console-section-title {
                font-size: 16px;
                margin-top: -25px;
                margin-bottom: 15px;
                background-color: black;
                display: inline-block;
                padding: 0 10px;
                color: #00f;
                font-weight: bold;
                text-shadow: 0 0 5px #00f;
            }

            .console-control-panel button {
                background-color: #000;
                color: #00f;
                border: 1px solid #00f;
                padding: 8px 12px;
                margin: 5px;
                font-family: monospace;
                cursor: pointer;
                transition: all 0.2s;
                text-shadow: 0 0 5px #00f;
                box-shadow: 0 0 5px #00f;
            }

            .console-control-panel button:hover {
                background-color: #003;
                box-shadow: 0 0 10px #00f;
            }

            .console-control-panel select, .console-control-panel input[type="text"] {
                background-color: #000;
                color: #00f;
                border: 1px solid #00f;
                padding: 5px;
                font-family: monospace;
                box-shadow: 0 0 5px #00f;
            }

            .console-control-panel select:focus, .console-control-panel input[type="text"]:focus {
                outline: none;
                box-shadow: 0 0 10px #00f;
            }

            .console-control-panel input[type="range"] {
                -webkit-appearance: none;
                width: 100%;
                height: 8px;
                background: #000;
                border: 1px solid #00f;
                box-shadow: 0 0 5px #00f;
            }

            .console-control-panel input[type="range"]::-webkit-slider-thumb {
                -webkit-appearance: none;
                width: 15px;
                height: 15px;
                background: #00f;
                cursor: pointer;
                box-shadow: 0 0 5px #00f;
            }

            .console-control-panel input[type="checkbox"] {
                -webkit-appearance: none;
                width: 20px;
                height: 20px;
                background: #000;
                border: 1px solid #00f;
                box-shadow: 0 0 5px #00f;
                display: inline-block;
                position: relative;
                margin-right: 10px;
                vertical-align: middle;
            }

            .console-control-panel input[type="checkbox"]:checked::after {
                content: '';
                position: absolute;
                width: 12px;
                height: 12px;
                background-color: #00f;
                top: 3px;
                left: 3px;
                box-shadow: 0 0 5px #00f;
            }

            .preset-description {
                font-style: italic;
                margin-top: 5px;
                opacity: 0.8;
                font-size: 12px;
                text-align: center;
            }
        `;
        document.head.appendChild(style);

        // Add class to container
        this.container.classList.add('console-control-panel');
    }

    /**
     * Create preset selector with console presets
     */
    createPresetSelector() {
        const presetSection = document.createElement('div');
        presetSection.className = 'console-section';

        const sectionTitle = document.createElement('div');
        sectionTitle.className = 'console-section-title';
        sectionTitle.textContent = 'SELECT CONSOLE PRESET';
        presetSection.appendChild(sectionTitle);

        const presetContainer = document.createElement('div');
        presetContainer.style.display = 'flex';
        presetContainer.style.alignItems = 'center';
        presetContainer.style.marginBottom = '15px';

        const label = document.createElement('label');
        label.textContent = 'PRESET: ';
        label.style.marginRight = '10px';
        presetContainer.appendChild(label);

        const select = document.createElement('select');
        select.style.flex = '1';

        // Add presets
        const presets = this.consoleManager.getPresetList();
        presets.forEach(preset => {
            const option = document.createElement('option');
            option.value = preset;
            option.textContent = preset.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            select.appendChild(option);
        });

        // Set current preset
        select.value = this.consoleManager.getCurrentPreset() || 'none';

        // Add change event
        select.addEventListener('change', () => {
            this.consoleManager.applyPreset(select.value);
            this.updatePresetDescription(select.value);
        });

        presetContainer.appendChild(select);
        presetSection.appendChild(presetContainer);

        // Add preset description
        const descriptionElement = document.createElement('div');
        descriptionElement.className = 'preset-description';
        descriptionElement.textContent = this.consoleManager.getPresetDescription(select.value) || '';
        presetSection.appendChild(descriptionElement);
        this.presetDescriptionElement = descriptionElement;

        // Add buttons for quick presets
        const quickButtonsContainer = document.createElement('div');
        quickButtonsContainer.style.display = 'flex';
        quickButtonsContainer.style.justifyContent = 'center';
        quickButtonsContainer.style.flexWrap = 'wrap';
        quickButtonsContainer.style.marginTop = '15px';

        // Add quick preset buttons for popular options
        const quickPresets = ['none', 'nes', 'ps1', 'modern', 'matrix', 'code_32bit', 'cyberpunk', 'retro_code'];
        quickPresets.forEach(preset => {
            const button = document.createElement('button');
            button.textContent = preset.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            button.addEventListener('click', () => {
                this.consoleManager.applyPreset(preset);
                select.value = preset;
                this.updatePresetDescription(preset);
            });
            quickButtonsContainer.appendChild(button);
        });

        presetSection.appendChild(quickButtonsContainer);
        this.container.appendChild(presetSection);
        this.presetSelect = select;
    }

    /**
     * Create game FPS controls
     */
    createGameFPSControls() {
        const fpsSection = document.createElement('div');
        fpsSection.className = 'console-section';

        const sectionTitle = document.createElement('div');
        sectionTitle.className = 'console-section-title';
        sectionTitle.textContent = 'GAME FPS SETTINGS';
        fpsSection.appendChild(sectionTitle);

        // FPS slider
        const fpsContainer = document.createElement('div');
        fpsContainer.style.marginBottom = '15px';

        const fpsLabel = document.createElement('div');
        fpsLabel.textContent = 'Target Game FPS:';
        fpsLabel.style.marginBottom = '5px';
        fpsContainer.appendChild(fpsLabel);

        const fpsSliderContainer = document.createElement('div');
        fpsSliderContainer.style.display = 'flex';
        fpsSliderContainer.style.alignItems = 'center';

        const fpsSlider = document.createElement('input');
        fpsSlider.type = 'range';
        fpsSlider.min = '15';
        fpsSlider.max = '120';
        fpsSlider.step = '1';
        fpsSlider.value = this.consoleManager.targetGameFPS;
        fpsSlider.style.flex = '1';

        const fpsValue = document.createElement('span');
        fpsValue.textContent = fpsSlider.value;
        fpsValue.style.marginLeft = '10px';
        fpsValue.style.width = '30px';
        fpsValue.style.textAlign = 'right';

        fpsSlider.addEventListener('input', () => {
            const fps = parseInt(fpsSlider.value);
            this.consoleManager.setGameFPS(fps);
            fpsValue.textContent = fps;
        });

        fpsSliderContainer.appendChild(fpsSlider);
        fpsSliderContainer.appendChild(fpsValue);
        fpsContainer.appendChild(fpsSliderContainer);
        fpsSection.appendChild(fpsContainer);

        // FPS lock checkbox
        const lockContainer = document.createElement('div');
        lockContainer.style.display = 'flex';
        lockContainer.style.alignItems = 'center';

        const lockCheckbox = document.createElement('input');
        lockCheckbox.type = 'checkbox';
        lockCheckbox.checked = this.consoleManager.gameFPSLocked;

        lockCheckbox.addEventListener('change', () => {
            this.consoleManager.toggleGameFPSLock(lockCheckbox.checked);
        });

        const lockLabel = document.createElement('label');
        lockLabel.textContent = 'Lock Game FPS';
        lockLabel.style.marginLeft = '5px';

        lockContainer.appendChild(lockCheckbox);
        lockContainer.appendChild(lockLabel);
        fpsSection.appendChild(lockContainer);

        // Quick FPS buttons
        const quickFPSContainer = document.createElement('div');
        quickFPSContainer.style.display = 'flex';
        quickFPSContainer.style.justifyContent = 'center';
        quickFPSContainer.style.marginTop = '15px';

        [30, 60, 120].forEach(fps => {
            const button = document.createElement('button');
            button.textContent = `${fps} FPS`;
            button.addEventListener('click', () => {
                this.consoleManager.setGameFPS(fps);
                fpsSlider.value = fps;
                fpsValue.textContent = fps;
            });
            quickFPSContainer.appendChild(button);
        });

        fpsSection.appendChild(quickFPSContainer);
        this.container.appendChild(fpsSection);

        // Store references
        this.fpsSlider = fpsSlider;
        this.fpsValue = fpsValue;
        this.lockCheckbox = lockCheckbox;
    }

    /**
     * Create code visualization effect controls
     */
    createEightBitCodeControls() {
        const codeSection = document.createElement('div');
        codeSection.className = 'console-section';

        const sectionTitle = document.createElement('div');
        sectionTitle.className = 'console-section-title';
        sectionTitle.textContent = 'CODE VISUALIZATION EFFECT';
        codeSection.appendChild(sectionTitle);

        // Toggle button
        const toggleContainer = document.createElement('div');
        toggleContainer.style.display = 'flex';
        toggleContainer.style.justifyContent = 'center';
        toggleContainer.style.marginBottom = '15px';

        const toggleButton = document.createElement('button');
        toggleButton.textContent = this.consoleManager.eightBitCodeEnabled ? 'DISABLE CODE EFFECT' : 'ENABLE CODE EFFECT';
        toggleButton.style.padding = '10px 20px';

        toggleButton.addEventListener('click', () => {
            this.consoleManager.toggleEightBitCode();
            toggleButton.textContent = this.consoleManager.eightBitCodeEnabled ? 'DISABLE CODE EFFECT' : 'ENABLE CODE EFFECT';
        });

        toggleContainer.appendChild(toggleButton);
        codeSection.appendChild(toggleContainer);

        // Description
        const description = document.createElement('div');
        description.style.textAlign = 'center';
        description.style.marginBottom = '15px';
        description.style.fontSize = '12px';
        description.textContent = 'The code visualization effect makes the game look like a terminal or "The Matrix" style code. Available in both 8-bit and 32-bit modes.';
        codeSection.appendChild(description);

        // Note about presets
        const note = document.createElement('div');
        note.style.textAlign = 'center';
        note.style.fontSize = '12px';
        note.style.opacity = '0.8';
        note.style.fontStyle = 'italic';
        note.textContent = 'Use "Matrix"/"Retro Code" for 8-bit style or "Code 32bit"/"Cyberpunk" for modern 32-bit visualization.';
        codeSection.appendChild(note);

        this.container.appendChild(codeSection);

        // Store reference
        this.toggleCodeButton = toggleButton;
    }

    /**
     * Create toggle button with retro styling
     */
    createToggleButton() {
        const toggleSection = document.createElement('div');
        toggleSection.className = 'console-section';

        const buttonsContainer = document.createElement('div');
        buttonsContainer.style.display = 'flex';
        buttonsContainer.style.justifyContent = 'space-between';
        buttonsContainer.style.marginBottom = '15px';

        const closeButton = document.createElement('button');
        closeButton.textContent = 'CLOSE MENU';
        closeButton.style.flex = '1';

        closeButton.addEventListener('click', () => {
            this.toggleVisibility();
        });

        buttonsContainer.appendChild(closeButton);
        toggleSection.appendChild(buttonsContainer);

        // Add keyboard shortcut info
        const shortcutInfo = document.createElement('div');
        shortcutInfo.textContent = 'Press F3 to toggle this menu';
        shortcutInfo.style.textAlign = 'center';
        shortcutInfo.style.fontSize = '12px';
        shortcutInfo.style.opacity = '0.7';
        shortcutInfo.style.marginTop = '5px';
        toggleSection.appendChild(shortcutInfo);

        this.container.appendChild(toggleSection);
    }

    /**
     * Update preset description
     * @param {string} presetName - Name of the preset
     */
    updatePresetDescription(presetName) {
        const description = this.consoleManager.getPresetDescription(presetName) || '';
        this.presetDescriptionElement.textContent = description;
    }

    /**
     * Update controls to match current settings
     */
    updateControls() {
        // Update preset selector
        this.presetSelect.value = this.consoleManager.getCurrentPreset() || 'none';
        this.updatePresetDescription(this.presetSelect.value);

        // Update FPS controls
        this.fpsSlider.value = this.consoleManager.targetGameFPS;
        this.fpsValue.textContent = this.consoleManager.targetGameFPS;
        this.lockCheckbox.checked = this.consoleManager.gameFPSLocked;

        // Update code effect toggle
        this.toggleCodeButton.textContent = this.consoleManager.eightBitCodeEnabled ? 'DISABLE CODE EFFECT' : 'ENABLE CODE EFFECT';
    }

    /**
     * Toggle control panel visibility with animation
     */
    toggleVisibility() {
        this.visible = !this.visible;

        if (this.visible) {
            // Update controls to match current settings
            this.updateControls();

            // Show panel with fade-in effect
            this.container.style.display = 'block';
            this.container.style.opacity = '0';

            // Add a slight scale effect
            this.container.style.transform = 'translate(-50%, -50%) scale(0.95)';

            // Trigger animation
            setTimeout(() => {
                this.container.style.transition = 'opacity 0.2s ease-out, transform 0.2s ease-out';
                this.container.style.opacity = '1';
                this.container.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 10);
        } else {
            // Hide with fade-out effect
            this.container.style.transition = 'opacity 0.2s ease-in, transform 0.2s ease-in';
            this.container.style.opacity = '0';
            this.container.style.transform = 'translate(-50%, -50%) scale(0.95)';

            // Actually hide after animation completes
            setTimeout(() => {
                this.container.style.display = 'none';
            }, 200);
        }
    }
}

export { ConsolePresetControlPanel };
