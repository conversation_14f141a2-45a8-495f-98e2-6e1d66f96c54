import * as THREE from 'three';

class SoulOrbHealthBar {
    constructor(camera, maxHealth) {
        this.mainCamera = camera;
        this.maxHealth = maxHealth;
        this.currentHealth = maxHealth;
        this.orbs = [];
        
        // Get the container element
        this.container = document.getElementById('health-orbs-container');
        if (!this.container) {
            console.error('Health orbs container not found!');
            return;
        }
        
        this._createOrbs();
    }

    _createOrbs() {
        // Clear any existing orbs
        this.container.innerHTML = '';
        this.orbs = [];
        
        // Calculate how many orbs we need (2 health per orb)
        const numOrbs = Math.ceil(this.maxHealth / 2);
        
        for (let i = 0; i < numOrbs; i++) {
            const orb = this._createSingleOrb();
            this.orbs.push(orb);
            this.container.appendChild(orb);
        }

        // Initial update to set correct health
        this.updateHealth(this.currentHealth);
    }

    _createSingleOrb() {
        const orb = document.createElement('div');
        orb.className = 'health-orb';
        orb.style.cssText = `
            width: 30px;
            height: 30px;
            background: rgba(51, 102, 255, 0.2);
            border-radius: 50%;
            margin: 5px;
            position: relative;
            display: inline-block;
            box-shadow: 0 0 10px rgba(51, 102, 255, 0.2);
            transition: all 0.3s ease;
        `;
        
        // Add fill level element
        const fillLevel = document.createElement('div');
        fillLevel.className = 'health-orb-fill';
        fillLevel.style.cssText = `
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 0%;
            background: radial-gradient(circle at 30% 30%, #99ccff, #3366ff);
            border-radius: 50%;
            transition: all 0.3s ease;
        `;
        orb.appendChild(fillLevel);
        
        // Add glow effect
        const glow = document.createElement('div');
        glow.className = 'health-orb-glow';
        glow.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            box-shadow: 0 0 15px #99ccff;
            opacity: 0;
            transition: all 0.3s ease;
        `;
        orb.appendChild(glow);
        
        return orb;
    }

    updateHealth(currentHealth) {
        this.currentHealth = Math.max(0, Math.min(currentHealth, this.maxHealth));
        
        // Calculate how many orbs we need based on current health
        const neededOrbs = Math.ceil(this.currentHealth / 2);
        
        // Remove excess orbs if health decreased
        while (this.orbs.length > neededOrbs) {
            const lastOrb = this.orbs.pop();
            this.container.removeChild(lastOrb);
        }
        
        // Add new orbs if health increased
        while (this.orbs.length < neededOrbs) {
            const orb = this._createSingleOrb();
            this.orbs.push(orb);
            this.container.appendChild(orb);
        }
        
        // Update remaining orbs' fill amounts
        this.orbs.forEach((orb, index) => {
            const orbStartHealth = index * 2;
            const healthForThisOrb = Math.max(0, Math.min(2, this.currentHealth - orbStartHealth));
            const fillPercentage = (healthForThisOrb / 2) * 100;
            
            const fillElement = orb.querySelector('.health-orb-fill');
            const glowElement = orb.querySelector('.health-orb-glow');
            
            if (fillElement) {
                fillElement.style.height = `${fillPercentage}%`;
                fillElement.style.opacity = 1;
            }
            
            if (glowElement) {
                glowElement.style.opacity = fillPercentage / 100;
            }
            
            orb.style.opacity = 1;
        });
    }

    update(deltaTime) {
        // No need for update with CSS animations
    }

    dispose() {
        // Remove all orbs
        if (this.container) {
            this.container.innerHTML = '';
        }
        this.orbs = [];
    }
}

export default SoulOrbHealthBar; 