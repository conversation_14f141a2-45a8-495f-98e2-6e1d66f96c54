import * as THREE from 'three';

/**
 * Creates a floating text element that rises and fades out
 * @param {THREE.Scene} scene - The scene to add the text to
 * @param {THREE.Vector3} position - The position to spawn the text
 * @param {string} text - The text to display
 * @param {Object} options - Optional parameters
 * @param {number} options.duration - How long the text should last in seconds
 * @param {number} options.size - The size of the text
 * @param {string} options.color - The color of the text
 * @param {number} options.riseSpeed - How fast the text rises
 * @param {boolean} options.fadeOut - Whether the text should fade out
 * @returns {Object} The created text object with update method
 */
export function createFloatingText(scene, position, text, options = {}) {
    // Default options
    const {
        duration = 1.5,
        size = 0.5,
        color = '#ffffff',
        riseSpeed = 1.0,
        fadeOut = true
    } = options;

    // Create canvas for text rendering
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 256;
    canvas.height = 128;

    // Clear canvas
    context.clearRect(0, 0, canvas.width, canvas.height);

    // Set text properties
    context.font = 'bold 72px Arial';
    context.fillStyle = color;
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    
    // Add stroke for better visibility
    context.strokeStyle = '#000000';
    context.lineWidth = 4;
    context.strokeText(text, canvas.width / 2, canvas.height / 2);
    
    // Fill text
    context.fillText(text, canvas.width / 2, canvas.height / 2);

    // Create texture from canvas
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // Create material with transparency
    const material = new THREE.SpriteMaterial({
        map: texture,
        transparent: true,
        opacity: 1.0
    });

    // Create sprite
    const sprite = new THREE.Sprite(material);
    sprite.position.copy(position);
    sprite.position.y += 1.0; // Start slightly above the target
    sprite.scale.set(size, size / 2, 1);

    // Add to scene
    scene.add(sprite);

    // Create object with update method
    const floatingText = {
        sprite,
        material,
        texture,
        canvas,
        timer: 0,
        duration,
        riseSpeed,
        fadeOut,
        
        /**
         * Update the floating text
         * @param {number} deltaTime - Time since last update
         * @returns {boolean} Whether the text should be removed
         */
        update(deltaTime) {
            this.timer += deltaTime;
            
            // Rise up
            this.sprite.position.y += this.riseSpeed * deltaTime;
            
            // Fade out
            if (this.fadeOut) {
                const progress = this.timer / this.duration;
                this.material.opacity = 1.0 - progress;
            }
            
            // Remove when duration is reached
            return this.timer >= this.duration;
        },
        
        /**
         * Remove the floating text from the scene
         */
        remove() {
            scene.remove(this.sprite);
            this.material.dispose();
            this.texture.dispose();
        }
    };
    
    return floatingText;
}

/**
 * Creates a damage number that floats up and fades out
 * @param {THREE.Scene} scene - The scene to add the text to
 * @param {THREE.Vector3} position - The position to spawn the text
 * @param {number} damage - The damage amount to display
 * @param {boolean} isCritical - Whether this is a critical hit
 * @returns {Object} The created text object with update method
 */
export function createDamageNumber(scene, position, damage, isCritical = false) {
    // Format damage number
    const damageText = Math.round(damage).toString();
    
    // Set options based on damage amount and critical status
    const options = {
        duration: isCritical ? 2.0 : 1.5,
        size: isCritical ? 0.8 : 0.5,
        color: isCritical ? '#ff0000' : '#ffff00',
        riseSpeed: isCritical ? 1.5 : 1.0,
        fadeOut: true
    };
    
    return createFloatingText(scene, position, damageText, options);
}
