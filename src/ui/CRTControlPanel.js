/**
 * CRT Control Panel
 * UI for controlling the CRT effect with retro game styling
 */
class CRTControlPanel {
    constructor(crtManager) {
        this.crtManager = crtManager;
        this.container = null;
        this.visible = false;
        this.savedPresets = this.loadSavedPresets();
    }

    /**
     * Load saved presets from localStorage
     */
    loadSavedPresets() {
        try {
            const savedPresets = localStorage.getItem('crtSavedPresets');
            return savedPresets ? JSON.parse(savedPresets) : {};
        } catch (e) {
            console.error('Error loading saved CRT presets:', e);
            return {};
        }
    }

    /**
     * Save presets to localStorage
     */
    savePresets() {
        try {
            localStorage.setItem('crtSavedPresets', JSON.stringify(this.savedPresets));
            console.log('CRT presets saved successfully');
        } catch (e) {
            console.error('Error saving CRT presets:', e);
        }
    }

    /**
     * Initialize the control panel with retro game styling
     */
    init() {
        // Create container with retro game styling
        this.container = document.createElement('div');
        this.container.className = 'crt-control-panel';
        this.container.style.position = 'absolute';
        this.container.style.top = '50%';
        this.container.style.left = '50%';
        this.container.style.transform = 'translate(-50%, -50%)';
        this.container.style.backgroundColor = '#111';
        this.container.style.color = '#0f0';
        this.container.style.padding = '15px';
        this.container.style.border = '3px solid #0f0';
        this.container.style.boxShadow = '0 0 10px #0f0, inset 0 0 10px #0f0';
        this.container.style.fontFamily = '"Press Start 2P", monospace';
        this.container.style.zIndex = '1000';
        this.container.style.display = 'none';
        this.container.style.maxWidth = '600px';
        this.container.style.maxHeight = '80vh';
        this.container.style.overflowY = 'auto';
        this.container.style.textShadow = '0 0 5px #0f0';

        // Create title with retro styling
        const title = document.createElement('h3');
        title.textContent = 'CRT DISPLAY SETTINGS';
        title.style.margin = '0 0 15px 0';
        title.style.textAlign = 'center';
        title.style.fontSize = '18px';
        title.style.letterSpacing = '2px';
        title.style.textTransform = 'uppercase';
        this.container.appendChild(title);

        // Create preset selector
        this.createPresetSelector();

        // Create parameter sliders
        this.createParameterSliders();

        // Create save preset controls
        this.createSavePresetControls();

        // Create toggle button
        this.createToggleButton();

        // Create refresh visualization controls
        this.createRefreshControls();

        // Add to document
        document.body.appendChild(this.container);

        // Note: F1 keyboard shortcut is handled in CRTIntegration.js

        // Add custom CSS for retro styling
        this.addRetroStyles();
    }

    /**
     * Add retro styling CSS to document
     */
    addRetroStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .crt-control-panel button {
                background-color: #111;
                color: #0f0;
                border: 2px solid #0f0;
                padding: 8px 12px;
                margin: 5px;
                font-family: inherit;
                cursor: pointer;
                text-transform: uppercase;
                font-size: 12px;
                letter-spacing: 1px;
                text-shadow: 0 0 5px #0f0;
                transition: all 0.2s;
            }

            .crt-control-panel button:hover {
                background-color: #0f0;
                color: #111;
                box-shadow: 0 0 10px #0f0;
            }

            .crt-control-panel select {
                background-color: #111;
                color: #0f0;
                border: 2px solid #0f0;
                padding: 5px;
                font-family: inherit;
                text-shadow: 0 0 5px #0f0;
                font-size: 12px;
            }

            .crt-control-panel input[type="range"] {
                -webkit-appearance: none;
                height: 8px;
                background: #111;
                border: 1px solid #0f0;
                border-radius: 0;
            }

            .crt-control-panel input[type="range"]::-webkit-slider-thumb {
                -webkit-appearance: none;
                width: 15px;
                height: 15px;
                background: #0f0;
                cursor: pointer;
                box-shadow: 0 0 5px #0f0;
            }

            .crt-control-panel input[type="text"] {
                background-color: #111;
                color: #0f0;
                border: 2px solid #0f0;
                padding: 5px;
                font-family: inherit;
                text-shadow: 0 0 5px #0f0;
                font-size: 12px;
                margin-right: 5px;
            }

            .crt-control-panel .section {
                margin-bottom: 15px;
                padding-bottom: 15px;
                border-bottom: 1px solid #0f0;
            }

            .crt-control-panel .section-title {
                margin-bottom: 10px;
                font-size: 14px;
                text-transform: uppercase;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Create preset selector with built-in and saved presets
     */
    createPresetSelector() {
        const presetSection = document.createElement('div');
        presetSection.className = 'section';

        const sectionTitle = document.createElement('div');
        sectionTitle.className = 'section-title';
        sectionTitle.textContent = 'SELECT PRESET';
        presetSection.appendChild(sectionTitle);

        const presetContainer = document.createElement('div');
        presetContainer.style.display = 'flex';
        presetContainer.style.alignItems = 'center';
        presetContainer.style.marginBottom = '15px';

        const label = document.createElement('label');
        label.textContent = 'PRESET: ';
        label.style.marginRight = '10px';
        presetContainer.appendChild(label);

        const select = document.createElement('select');

        // Add built-in presets
        const builtInGroup = document.createElement('optgroup');
        builtInGroup.label = 'BUILT-IN PRESETS';

        const presets = this.crtManager.getPresetList();
        presets.forEach(preset => {
            // Skip 'none' preset as we'll handle it separately
            if (preset !== 'none') {
                const option = document.createElement('option');
                option.value = preset;
                option.textContent = preset.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                builtInGroup.appendChild(option);
            }
        });

        select.appendChild(builtInGroup);

        // Add saved presets if any exist
        if (Object.keys(this.savedPresets).length > 0) {
            const savedGroup = document.createElement('optgroup');
            savedGroup.label = 'SAVED PRESETS';

            Object.keys(this.savedPresets).forEach(presetName => {
                const option = document.createElement('option');
                option.value = 'saved:' + presetName;
                option.textContent = presetName;
                savedGroup.appendChild(option);
            });

            select.appendChild(savedGroup);
        }

        // Add 'Off' option at the top
        const offOption = document.createElement('option');
        offOption.value = 'none';
        offOption.textContent = 'OFF';
        select.insertBefore(offOption, select.firstChild);

        // Set current preset
        select.value = this.crtManager.getCurrentPreset();

        // Add change event
        select.addEventListener('change', () => {
            if (select.value.startsWith('saved:')) {
                // Apply saved preset
                const presetName = select.value.substring(6);
                const presetSettings = this.savedPresets[presetName];
                this.crtManager.applyCustomPreset(presetSettings);
            } else {
                // Apply built-in preset
                this.crtManager.applyPreset(select.value);
            }
            this.updateSliders();
        });

        presetContainer.appendChild(select);
        presetSection.appendChild(presetContainer);

        // Add buttons for quick presets
        const quickButtonsContainer = document.createElement('div');
        quickButtonsContainer.style.display = 'flex';
        quickButtonsContainer.style.justifyContent = 'center';
        quickButtonsContainer.style.flexWrap = 'wrap';

        // Add quick preset buttons for popular options
        const quickPresets = ['sony_trinitron', 'arcade_monitor', 'commodore_1084'];
        quickPresets.forEach(preset => {
            const button = document.createElement('button');
            button.textContent = preset.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            button.addEventListener('click', () => {
                this.crtManager.applyPreset(preset);
                select.value = preset;
                this.updateSliders();
            });
            quickButtonsContainer.appendChild(button);
        });

        // Add Off button
        const offButton = document.createElement('button');
        offButton.textContent = 'OFF';
        offButton.addEventListener('click', () => {
            this.crtManager.applyPreset('none');
            select.value = 'none';
            this.updateSliders();
        });
        quickButtonsContainer.appendChild(offButton);

        presetSection.appendChild(quickButtonsContainer);
        this.container.appendChild(presetSection);
        this.presetSelect = select;
    }

    /**
     * Create save preset controls
     */
    createSavePresetControls() {
        const saveSection = document.createElement('div');
        saveSection.className = 'section';

        const sectionTitle = document.createElement('div');
        sectionTitle.className = 'section-title';
        sectionTitle.textContent = 'SAVE CURRENT SETTINGS';
        saveSection.appendChild(sectionTitle);

        const saveContainer = document.createElement('div');
        saveContainer.style.display = 'flex';
        saveContainer.style.alignItems = 'center';
        saveContainer.style.marginBottom = '10px';

        const nameInput = document.createElement('input');
        nameInput.type = 'text';
        nameInput.placeholder = 'Preset Name';
        nameInput.style.flex = '1';
        saveContainer.appendChild(nameInput);

        const saveButton = document.createElement('button');
        saveButton.textContent = 'SAVE';
        saveButton.addEventListener('click', () => {
            const presetName = nameInput.value.trim();
            if (presetName) {
                // Get current settings from CRT manager
                const currentSettings = {};

                // Copy all current uniform values
                for (const paramName in this.crtManager.crtPass.uniforms) {
                    // Skip tDiffuse, time, resolution, and refreshScanline
                    if (['tDiffuse', 'time', 'resolution', 'refreshScanline'].includes(paramName)) {
                        continue;
                    }
                    currentSettings[paramName] = this.crtManager.crtPass.uniforms[paramName].value;
                }

                // Add enabled flag
                currentSettings.enabled = this.crtManager.enabled;

                // Save to savedPresets
                this.savedPresets[presetName] = currentSettings;
                this.savePresets();

                // Update preset selector
                this.updatePresetSelector(presetName);

                // Clear input
                nameInput.value = '';

                // Show confirmation
                alert(`Preset "${presetName}" saved successfully!`);
            } else {
                alert('Please enter a name for your preset');
            }
        });
        saveContainer.appendChild(saveButton);

        saveSection.appendChild(saveContainer);

        // Add delete saved preset controls
        const deleteContainer = document.createElement('div');
        deleteContainer.style.display = 'flex';
        deleteContainer.style.alignItems = 'center';

        const deleteSelect = document.createElement('select');
        deleteSelect.style.flex = '1';
        this.updateDeleteSelector(deleteSelect);
        deleteContainer.appendChild(deleteSelect);

        const deleteButton = document.createElement('button');
        deleteButton.textContent = 'DELETE';
        deleteButton.addEventListener('click', () => {
            const presetName = deleteSelect.value;
            if (presetName && confirm(`Are you sure you want to delete the preset "${presetName}"?`)) {
                delete this.savedPresets[presetName];
                this.savePresets();
                this.updatePresetSelector();
                this.updateDeleteSelector(deleteSelect);
            }
        });
        deleteContainer.appendChild(deleteButton);

        saveSection.appendChild(deleteContainer);
        this.container.appendChild(saveSection);
    }

    /**
     * Force select a preset in the UI
     * @param {string} presetName - Name of the preset to select
     */
    forceSelectPreset(presetName) {
        if (this.presetSelect && presetName) {
            this.presetSelect.value = presetName;
            // Update sliders to match the preset
            this.updateSliders();
        }
    }

    /**
     * Update preset selector with new saved preset
     * @param {string} [selectedPreset] - Optional preset to select
     */
    updatePresetSelector(selectedPreset = null) {
        // Clear existing options
        while (this.presetSelect.firstChild) {
            this.presetSelect.removeChild(this.presetSelect.firstChild);
        }

        // Add 'Off' option
        const offOption = document.createElement('option');
        offOption.value = 'none';
        offOption.textContent = 'OFF';
        this.presetSelect.appendChild(offOption);

        // Add built-in presets
        const builtInGroup = document.createElement('optgroup');
        builtInGroup.label = 'BUILT-IN PRESETS';

        const presets = this.crtManager.getPresetList();
        presets.forEach(preset => {
            if (preset !== 'none') {
                const option = document.createElement('option');
                option.value = preset;
                option.textContent = preset.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                builtInGroup.appendChild(option);
            }
        });

        this.presetSelect.appendChild(builtInGroup);

        // Add saved presets if any exist
        if (Object.keys(this.savedPresets).length > 0) {
            const savedGroup = document.createElement('optgroup');
            savedGroup.label = 'SAVED PRESETS';

            Object.keys(this.savedPresets).forEach(presetName => {
                const option = document.createElement('option');
                option.value = 'saved:' + presetName;
                option.textContent = presetName;
                savedGroup.appendChild(option);
            });

            this.presetSelect.appendChild(savedGroup);
        }

        // Set selected preset if provided
        if (selectedPreset) {
            this.presetSelect.value = 'saved:' + selectedPreset;
        } else {
            this.presetSelect.value = this.crtManager.getCurrentPreset();
        }
    }

    /**
     * Update delete selector with current saved presets
     * @param {HTMLSelectElement} deleteSelect - The select element to update
     */
    updateDeleteSelector(deleteSelect) {
        // Clear existing options
        while (deleteSelect.firstChild) {
            deleteSelect.removeChild(deleteSelect.firstChild);
        }

        // Add placeholder if no saved presets
        if (Object.keys(this.savedPresets).length === 0) {
            const placeholder = document.createElement('option');
            placeholder.value = '';
            placeholder.textContent = 'No saved presets';
            placeholder.disabled = true;
            placeholder.selected = true;
            deleteSelect.appendChild(placeholder);
            return;
        }

        // Add saved presets
        Object.keys(this.savedPresets).forEach(presetName => {
            const option = document.createElement('option');
            option.value = presetName;
            option.textContent = presetName;
            deleteSelect.appendChild(option);
        });
    }

    /**
     * Create parameter sliders with categorized sections
     */
    createParameterSliders() {
        const slidersSection = document.createElement('div');
        slidersSection.className = 'section';

        const sectionTitle = document.createElement('div');
        sectionTitle.className = 'section-title';
        sectionTitle.textContent = 'ADJUST PARAMETERS';
        slidersSection.appendChild(sectionTitle);

        // Organize parameters into categories
        const categories = [
            {
                name: 'SCREEN',
                params: [
                    { name: 'curvature', min: 0, max: 10, step: 0.1, label: 'Screen Curve' },
                    { name: 'vignetteIntensity', min: 0, max: 1, step: 0.01, label: 'Vignette' }
                ]
            },
            {
                name: 'SCANLINES',
                params: [
                    { name: 'scanlineIntensity', min: 0, max: 1, step: 0.01, label: 'Intensity' },
                    { name: 'scanlineCount', min: 100, max: 800, step: 10, label: 'Count' }
                ]
            },
            {
                name: 'MASK',
                params: [
                    { name: 'maskType', min: 0, max: 1, step: 1, label: 'Type', options: ['Aperture Grille', 'Shadow Mask'] },
                    { name: 'maskIntensity', min: 0, max: 1, step: 0.01, label: 'Intensity' },
                    { name: 'maskSize', min: 0.5, max: 5, step: 0.1, label: 'Size' }
                ]
            },
            {
                name: 'PHOSPHOR',
                params: [
                    { name: 'phosphorProfile', min: 0, max: 2, step: 1, label: 'Profile', options: ['Sony', 'Panasonic', 'Generic'] },
                    { name: 'bleedingAmount', min: 0, max: 1, step: 0.01, label: 'Bleeding' },
                    { name: 'ghostingAmount', min: 0, max: 1, step: 0.01, label: 'Ghosting' }
                ]
            },
            {
                name: 'IMPERFECTIONS',
                params: [
                    { name: 'convergenceFailureX', min: 0, max: 2, step: 0.1, label: 'Convergence X' },
                    { name: 'convergenceFailureY', min: 0, max: 2, step: 0.1, label: 'Convergence Y' },
                    { name: 'noiseAmount', min: 0, max: 0.2, step: 0.01, label: 'Noise' },
                    { name: 'flickerAmount', min: 0, max: 0.2, step: 0.01, label: 'Flicker' }
                ]
            }
        ];

        const slidersContainer = document.createElement('div');
        slidersContainer.style.maxHeight = '300px';
        slidersContainer.style.overflowY = 'auto';
        slidersContainer.style.marginBottom = '15px';
        slidersContainer.style.padding = '0 5px';

        this.sliders = {};

        // Create sliders for each category
        categories.forEach(category => {
            const categoryContainer = document.createElement('div');
            categoryContainer.style.marginBottom = '15px';

            const categoryTitle = document.createElement('div');
            categoryTitle.textContent = category.name;
            categoryTitle.style.borderBottom = '1px solid #0f0';
            categoryTitle.style.marginBottom = '8px';
            categoryTitle.style.paddingBottom = '3px';
            categoryTitle.style.fontSize = '14px';
            categoryContainer.appendChild(categoryTitle);

            category.params.forEach(param => {
                const paramContainer = document.createElement('div');
                paramContainer.style.marginBottom = '8px';
                paramContainer.style.display = 'flex';
                paramContainer.style.alignItems = 'center';

                const label = document.createElement('div');
                label.textContent = `${param.label}:`;
                label.style.width = '120px';
                label.style.flexShrink = '0';
                paramContainer.appendChild(label);

                const controlsContainer = document.createElement('div');
                controlsContainer.style.flex = '1';
                controlsContainer.style.display = 'flex';
                controlsContainer.style.alignItems = 'center';

                // For parameters with discrete options (like maskType)
                if (param.options) {
                    const select = document.createElement('select');
                    select.style.flex = '1';

                    param.options.forEach((option, index) => {
                        const optionEl = document.createElement('option');
                        optionEl.value = index;
                        optionEl.textContent = option;
                        select.appendChild(optionEl);
                    });

                    select.value = this.crtManager.crtPass.uniforms[param.name].value;

                    select.addEventListener('change', () => {
                        this.crtManager.adjustParameter(param.name, parseInt(select.value));
                    });

                    controlsContainer.appendChild(select);
                    this.sliders[param.name] = select;
                }
                // For continuous parameters
                else {
                    const slider = document.createElement('input');
                    slider.type = 'range';
                    slider.min = param.min;
                    slider.max = param.max;
                    slider.step = param.step;
                    slider.value = this.crtManager.crtPass.uniforms[param.name].value;
                    slider.style.flex = '1';

                    const valueDisplay = document.createElement('span');
                    valueDisplay.textContent = parseFloat(slider.value).toFixed(2);
                    valueDisplay.style.marginLeft = '10px';
                    valueDisplay.style.width = '40px';
                    valueDisplay.style.textAlign = 'right';

                    slider.addEventListener('input', () => {
                        this.crtManager.adjustParameter(param.name, parseFloat(slider.value));
                        valueDisplay.textContent = parseFloat(slider.value).toFixed(2);
                    });

                    controlsContainer.appendChild(slider);
                    controlsContainer.appendChild(valueDisplay);
                    this.sliders[param.name] = slider;
                }

                paramContainer.appendChild(controlsContainer);
                categoryContainer.appendChild(paramContainer);
            });

            slidersContainer.appendChild(categoryContainer);
        });

        slidersSection.appendChild(slidersContainer);
        this.container.appendChild(slidersSection);
    }

    /**
     * Create toggle button with retro styling
     */
    createToggleButton() {
        const toggleSection = document.createElement('div');
        toggleSection.className = 'section';

        const buttonsContainer = document.createElement('div');
        buttonsContainer.style.display = 'flex';
        buttonsContainer.style.justifyContent = 'space-between';
        buttonsContainer.style.marginBottom = '15px';

        const toggleButton = document.createElement('button');
        toggleButton.textContent = this.crtManager.enabled ? 'DISABLE EFFECT' : 'ENABLE EFFECT';
        toggleButton.style.flex = '1';
        toggleButton.style.marginRight = '10px';

        toggleButton.addEventListener('click', () => {
            const enabled = this.crtManager.toggle();
            toggleButton.textContent = enabled ? 'DISABLE EFFECT' : 'ENABLE EFFECT';
        });

        const closeButton = document.createElement('button');
        closeButton.textContent = 'CLOSE MENU';
        closeButton.style.flex = '1';
        closeButton.style.marginLeft = '10px';

        closeButton.addEventListener('click', () => {
            this.toggleVisibility();
        });

        buttonsContainer.appendChild(toggleButton);
        buttonsContainer.appendChild(closeButton);
        toggleSection.appendChild(buttonsContainer);

        // Add keyboard shortcut info
        const shortcutInfo = document.createElement('div');
        shortcutInfo.textContent = 'Press F1 to toggle this menu';
        shortcutInfo.style.textAlign = 'center';
        shortcutInfo.style.fontSize = '12px';
        shortcutInfo.style.opacity = '0.7';
        shortcutInfo.style.marginTop = '5px';
        toggleSection.appendChild(shortcutInfo);

        this.container.appendChild(toggleSection);
    }

    /**
     * Create refresh visualization controls with retro styling
     */
    createRefreshControls() {
        const refreshSection = document.createElement('div');
        refreshSection.className = 'section';

        const sectionTitle = document.createElement('div');
        sectionTitle.className = 'section-title';
        sectionTitle.textContent = 'REFRESH VISUALIZATION';
        refreshSection.appendChild(sectionTitle);

        const refreshContainer = document.createElement('div');
        refreshContainer.style.marginBottom = '15px';

        // Create checkbox container for better styling
        const checkboxContainer = document.createElement('div');
        checkboxContainer.style.display = 'flex';
        checkboxContainer.style.alignItems = 'center';
        checkboxContainer.style.marginBottom = '10px';

        const refreshCheckbox = document.createElement('input');
        refreshCheckbox.type = 'checkbox';
        refreshCheckbox.id = 'refresh-viz';
        // Set to checked by default for our custom arcade_monitor preset
        refreshCheckbox.checked = this.crtManager.refreshVisualizationEnabled ||
            this.crtManager.getCurrentPreset() === 'arcade_monitor';
        refreshCheckbox.style.marginRight = '10px';
        refreshCheckbox.style.width = '20px';
        refreshCheckbox.style.height = '20px';
        checkboxContainer.appendChild(refreshCheckbox);

        const refreshLabel = document.createElement('label');
        refreshLabel.htmlFor = 'refresh-viz';
        refreshLabel.textContent = 'SHOW LINE-BY-LINE REFRESH';
        refreshLabel.style.cursor = 'pointer';
        checkboxContainer.appendChild(refreshLabel);

        refreshContainer.appendChild(checkboxContainer);

        // Create speed control with better styling
        const speedContainer = document.createElement('div');
        speedContainer.style.marginTop = '10px';
        speedContainer.style.display = 'flex';
        speedContainer.style.alignItems = 'center';

        const speedLabel = document.createElement('div');
        speedLabel.textContent = 'REFRESH SPEED:';
        speedLabel.style.width = '120px';
        speedLabel.style.flexShrink = '0';
        speedContainer.appendChild(speedLabel);

        const controlsWrapper = document.createElement('div');
        controlsWrapper.style.flex = '1';
        controlsWrapper.style.display = 'flex';
        controlsWrapper.style.alignItems = 'center';

        const speedSlider = document.createElement('input');
        speedSlider.type = 'range';
        speedSlider.min = 0.01;
        speedSlider.max = 0.5;
        speedSlider.step = 0.01;
        // Set default value to 0.10 for our custom arcade_monitor preset
        speedSlider.value = this.crtManager.getCurrentPreset() === 'arcade_monitor' ?
            0.10 : this.crtManager.crtPass.uniforms.refreshSpeed.value;
        speedSlider.style.flex = '1';
        controlsWrapper.appendChild(speedSlider);

        const speedDisplay = document.createElement('span');
        speedDisplay.textContent = parseFloat(speedSlider.value).toFixed(2);
        speedDisplay.style.marginLeft = '10px';
        speedDisplay.style.width = '40px';
        speedDisplay.style.textAlign = 'right';
        controlsWrapper.appendChild(speedDisplay);

        speedContainer.appendChild(controlsWrapper);
        refreshContainer.appendChild(speedContainer);

        // Add description text
        const description = document.createElement('div');
        description.textContent = 'Simulates the authentic CRT refresh pattern where the screen is drawn line by line from top to bottom.';
        description.style.fontSize = '12px';
        description.style.marginTop = '10px';
        description.style.opacity = '0.8';
        description.style.lineHeight = '1.4';
        refreshContainer.appendChild(description);

        // Add event listeners
        refreshCheckbox.addEventListener('change', () => {
            this.crtManager.enableRefreshVisualization(refreshCheckbox.checked, parseFloat(speedSlider.value));
            speedContainer.style.opacity = refreshCheckbox.checked ? '1' : '0.5';
        });

        speedSlider.addEventListener('input', () => {
            this.crtManager.enableRefreshVisualization(refreshCheckbox.checked, parseFloat(speedSlider.value));
            speedDisplay.textContent = parseFloat(speedSlider.value).toFixed(2);
        });

        // Set initial state
        speedContainer.style.opacity = refreshCheckbox.checked ? '1' : '0.5';

        refreshSection.appendChild(refreshContainer);
        this.container.appendChild(refreshSection);
    }

    /**
     * Update sliders to match current preset
     */
    updateSliders() {
        let preset;
        const currentPreset = this.crtManager.getCurrentPreset();

        // Check if it's a saved preset
        if (currentPreset.startsWith('saved:')) {
            const presetName = currentPreset.substring(6);
            preset = this.savedPresets[presetName];
        } else {
            // Built-in preset
            preset = this.crtManager.presets[currentPreset];
        }

        if (!preset) return;

        for (const [param, control] of Object.entries(this.sliders)) {
            if (preset[param] !== undefined) {
                // Handle different control types
                if (control.tagName === 'SELECT') {
                    control.value = preset[param];
                } else {
                    control.value = preset[param];
                    // Update value display if it exists
                    if (control.nextElementSibling) {
                        control.nextElementSibling.textContent = parseFloat(preset[param]).toFixed(2);
                    }
                }
            }
        }
    }

    /**
     * Toggle control panel visibility with animation
     */
    toggleVisibility() {
        this.visible = !this.visible;

        if (this.visible) {
            // Show panel with fade-in effect
            this.container.style.display = 'block';
            this.container.style.opacity = '0';

            // Add a slight scale effect
            this.container.style.transform = 'translate(-50%, -50%) scale(0.95)';

            // Trigger animation
            setTimeout(() => {
                this.container.style.transition = 'opacity 0.2s ease-out, transform 0.2s ease-out';
                this.container.style.opacity = '1';
                this.container.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 10);
        } else {
            // Hide with fade-out effect
            this.container.style.transition = 'opacity 0.2s ease-in, transform 0.2s ease-in';
            this.container.style.opacity = '0';
            this.container.style.transform = 'translate(-50%, -50%) scale(0.95)';

            // Actually hide after animation completes
            setTimeout(() => {
                this.container.style.display = 'none';
            }, 200);
        }
    }
}

export { CRTControlPanel };
