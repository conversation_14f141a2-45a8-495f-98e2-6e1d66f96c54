/**
 * Control panel for HDR, framerate, and bit depth settings
 */
class HDRFramerateControlPanel {
    /**
     * Create a new HDR and Framerate Control Panel
     * @param {HDRFramerateManager} hdrManager - The HDR and Framerate Manager
     */
    constructor(hdrManager) {
        this.hdrManager = hdrManager;
        this.visible = false;
        this.sliders = {};
        this.savedPresets = this._loadSavedPresets();

        // Create container
        this.container = document.createElement('div');
        this.container.style.position = 'absolute';
        this.container.style.top = '50%';
        this.container.style.left = '50%';
        this.container.style.transform = 'translate(-50%, -50%)';
        this.container.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
        this.container.style.border = '2px solid #0f0';
        this.container.style.boxShadow = '0 0 20px #0f0';
        this.container.style.color = '#0f0';
        this.container.style.fontFamily = 'monospace';
        this.container.style.padding = '20px';
        this.container.style.zIndex = '1000';
        this.container.style.width = '500px';
        this.container.style.maxHeight = '80vh';
        this.container.style.overflowY = 'auto';
        this.container.style.display = 'none'; // Initially hidden

        // Create title
        const title = document.createElement('h2');
        title.textContent = 'HDR & FRAMERATE SETTINGS';
        title.style.textAlign = 'center';
        title.style.margin = '0 0 20px 0';
        title.style.textShadow = '0 0 5px #0f0';
        title.style.fontFamily = 'monospace';
        this.container.appendChild(title);

        // Create preset selector
        this.createPresetSelector();

        // Create parameter sliders
        this.createParameterSliders();

        // Create save preset controls
        this.createSavePresetControls();

        // Create toggle button
        this.createToggleButton();

        // Add to document
        document.body.appendChild(this.container);

        // Note: F2 keyboard shortcut is handled in HDRFramerateIntegration.js

        // Add custom CSS for retro styling
        this.addRetroStyles();
    }

    /**
     * Add retro styling CSS to document
     */
    addRetroStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .hdr-section {
                margin-bottom: 20px;
                border: 1px solid #0f0;
                padding: 15px;
                background-color: rgba(0, 40, 0, 0.3);
            }

            .hdr-section-title {
                font-size: 16px;
                margin-top: -25px;
                margin-bottom: 15px;
                background-color: black;
                display: inline-block;
                padding: 0 10px;
                color: #0f0;
                font-weight: bold;
                text-shadow: 0 0 5px #0f0;
            }

            .hdr-control-panel button {
                background-color: #000;
                color: #0f0;
                border: 1px solid #0f0;
                padding: 8px 12px;
                margin: 5px;
                font-family: monospace;
                cursor: pointer;
                transition: all 0.2s;
                text-shadow: 0 0 5px #0f0;
                box-shadow: 0 0 5px #0f0;
            }

            .hdr-control-panel button:hover {
                background-color: #030;
                box-shadow: 0 0 10px #0f0;
            }

            .hdr-control-panel select, .hdr-control-panel input[type="text"] {
                background-color: #000;
                color: #0f0;
                border: 1px solid #0f0;
                padding: 5px;
                font-family: monospace;
                box-shadow: 0 0 5px #0f0;
            }

            .hdr-control-panel select:focus, .hdr-control-panel input[type="text"]:focus {
                outline: none;
                box-shadow: 0 0 10px #0f0;
            }

            .hdr-control-panel input[type="range"] {
                -webkit-appearance: none;
                width: 100%;
                height: 8px;
                background: #000;
                border: 1px solid #0f0;
                box-shadow: 0 0 5px #0f0;
            }

            .hdr-control-panel input[type="range"]::-webkit-slider-thumb {
                -webkit-appearance: none;
                width: 15px;
                height: 15px;
                background: #0f0;
                cursor: pointer;
                box-shadow: 0 0 5px #0f0;
            }

            .hdr-control-panel input[type="checkbox"] {
                -webkit-appearance: none;
                width: 20px;
                height: 20px;
                background: #000;
                border: 1px solid #0f0;
                box-shadow: 0 0 5px #0f0;
                display: inline-block;
                position: relative;
                margin-right: 10px;
                vertical-align: middle;
            }

            .hdr-control-panel input[type="checkbox"]:checked::after {
                content: '';
                position: absolute;
                width: 12px;
                height: 12px;
                background-color: #0f0;
                top: 3px;
                left: 3px;
                box-shadow: 0 0 5px #0f0;
            }
        `;
        document.head.appendChild(style);

        // Add class to container
        this.container.classList.add('hdr-control-panel');
    }

    /**
     * Load saved presets from localStorage
     * @returns {Object} Saved presets
     * @private
     */
    _loadSavedPresets() {
        try {
            const savedPresets = localStorage.getItem('hdrFrameratePresets');
            return savedPresets ? JSON.parse(savedPresets) : {};
        } catch (error) {
            console.error('Error loading saved HDR presets:', error);
            return {};
        }
    }

    /**
     * Save presets to localStorage
     */
    savePresets() {
        try {
            localStorage.setItem('hdrFrameratePresets', JSON.stringify(this.savedPresets));
        } catch (error) {
            console.error('Error saving HDR presets:', error);
        }
    }

    /**
     * Create preset selector with built-in and saved presets
     */
    createPresetSelector() {
        const presetSection = document.createElement('div');
        presetSection.className = 'hdr-section';

        const sectionTitle = document.createElement('div');
        sectionTitle.className = 'hdr-section-title';
        sectionTitle.textContent = 'SELECT PRESET';
        presetSection.appendChild(sectionTitle);

        const presetContainer = document.createElement('div');
        presetContainer.style.display = 'flex';
        presetContainer.style.alignItems = 'center';
        presetContainer.style.marginBottom = '15px';

        const label = document.createElement('label');
        label.textContent = 'PRESET: ';
        label.style.marginRight = '10px';
        presetContainer.appendChild(label);

        const select = document.createElement('select');
        select.id = 'hdr-preset-select'; // Add an ID for easier access

        // Add built-in presets
        const builtInGroup = document.createElement('optgroup');
        builtInGroup.label = 'BUILT-IN PRESETS';

        const presets = this.hdrManager.getPresetList();
        presets.forEach(preset => {
            const option = document.createElement('option');
            option.value = preset;
            option.textContent = preset.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            // Mark the NES preset with a special attribute for easier selection
            if (preset === 'nes') {
                option.setAttribute('data-special-preset', 'true');
            }
            builtInGroup.appendChild(option);
        });

        select.appendChild(builtInGroup);

        // Add saved presets if any exist
        if (Object.keys(this.savedPresets).length > 0) {
            const savedGroup = document.createElement('optgroup');
            savedGroup.label = 'SAVED PRESETS';

            Object.keys(this.savedPresets).forEach(presetName => {
                const option = document.createElement('option');
                option.value = 'saved:' + presetName;
                option.textContent = presetName;
                savedGroup.appendChild(option);
            });

            select.appendChild(savedGroup);
        }

        // Set current preset
        select.value = this.hdrManager.getCurrentPreset();

        // Add change event
        select.addEventListener('change', () => {
            if (select.value.startsWith('saved:')) {
                // Apply saved preset
                const presetName = select.value.substring(6);
                const presetSettings = this.savedPresets[presetName];
                this.hdrManager.applyCustomSettings(presetSettings);
            } else {
                // Apply built-in preset
                this.hdrManager.applyPreset(select.value);
            }
            this.updateSliders();
        });

        presetContainer.appendChild(select);
        presetSection.appendChild(presetContainer);

        // Add buttons for quick presets
        const quickButtonsContainer = document.createElement('div');
        quickButtonsContainer.style.display = 'flex';
        quickButtonsContainer.style.justifyContent = 'center';
        quickButtonsContainer.style.flexWrap = 'wrap';

        // Add quick preset buttons for popular options
        const quickPresets = ['modern', 'sdr_tv', 'nes', 'ps1'];
        quickPresets.forEach(preset => {
            const button = document.createElement('button');
            button.textContent = preset.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            button.setAttribute('data-preset', preset); // Add data attribute for easier identification
            button.addEventListener('click', () => {
                this.hdrManager.applyPreset(preset);
                select.value = preset;
                this.updateSliders();
            });
            quickButtonsContainer.appendChild(button);
        });

        presetSection.appendChild(quickButtonsContainer);
        this.container.appendChild(presetSection);
        this.presetSelect = select;
    }

    /**
     * Create parameter sliders with categorized sections
     */
    createParameterSliders() {
        const slidersSection = document.createElement('div');
        slidersSection.className = 'hdr-section';

        const sectionTitle = document.createElement('div');
        sectionTitle.className = 'hdr-section-title';
        sectionTitle.textContent = 'ADJUST PARAMETERS';
        slidersSection.appendChild(sectionTitle);

        // Organize parameters into categories
        const categories = [
            {
                name: 'HDR SETTINGS',
                params: [
                    { name: 'peakBrightness', min: 80, max: 1000, step: 10, label: 'Peak Brightness (nits)' },
                    { name: 'toneMappingExposure', min: 0.1, max: 2.0, step: 0.1, label: 'Exposure' }
                ]
            },
            {
                name: 'FRAMERATE',
                params: [
                    { name: 'targetFramerate', min: 15, max: 120, step: 1, label: 'Target FPS' },
                    { name: 'framerateLocked', type: 'checkbox', label: 'Lock Framerate' }
                ]
            },
            {
                name: 'BIT DEPTH',
                params: [
                    { name: 'bitDepth', min: 1, max: 10, step: 1, label: 'Bit Depth' },
                    { name: 'ditherEnabled', type: 'checkbox', label: 'Enable Dithering' },
                    { name: 'ditherStrength', min: 0, max: 0.5, step: 0.01, label: 'Dither Strength' }
                ]
            }
        ];

        const slidersContainer = document.createElement('div');
        slidersContainer.style.maxHeight = '300px';
        slidersContainer.style.overflowY = 'auto';
        slidersContainer.style.marginBottom = '15px';
        slidersContainer.style.padding = '0 5px';

        this.sliders = {};

        // Create sliders for each category
        categories.forEach(category => {
            const categoryContainer = document.createElement('div');
            categoryContainer.style.marginBottom = '15px';

            const categoryTitle = document.createElement('div');
            categoryTitle.textContent = category.name;
            categoryTitle.style.borderBottom = '1px solid #0f0';
            categoryTitle.style.marginBottom = '8px';
            categoryTitle.style.paddingBottom = '3px';
            categoryTitle.style.fontSize = '14px';
            categoryContainer.appendChild(categoryTitle);

            category.params.forEach(param => {
                const paramContainer = document.createElement('div');
                paramContainer.style.marginBottom = '8px';
                paramContainer.style.display = 'flex';
                paramContainer.style.alignItems = 'center';

                const label = document.createElement('div');
                label.textContent = `${param.label}:`;
                label.style.width = '180px';
                label.style.flexShrink = '0';
                paramContainer.appendChild(label);

                const controlsContainer = document.createElement('div');
                controlsContainer.style.flex = '1';
                controlsContainer.style.display = 'flex';
                controlsContainer.style.alignItems = 'center';

                // For checkbox parameters
                if (param.type === 'checkbox') {
                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.checked = this.hdrManager[param.name];

                    checkbox.addEventListener('change', () => {
                        this.hdrManager.adjustParameter(param.name, checkbox.checked);
                    });

                    controlsContainer.appendChild(checkbox);
                    this.sliders[param.name] = checkbox;
                }
                // For continuous parameters
                else {
                    const slider = document.createElement('input');
                    slider.type = 'range';
                    slider.min = param.min;
                    slider.max = param.max;
                    slider.step = param.step;
                    slider.value = this.hdrManager[param.name];
                    slider.style.flex = '1';

                    const valueDisplay = document.createElement('span');
                    valueDisplay.textContent = parseFloat(slider.value).toFixed(2);
                    valueDisplay.style.marginLeft = '10px';
                    valueDisplay.style.width = '50px';
                    valueDisplay.style.textAlign = 'right';

                    slider.addEventListener('input', () => {
                        this.hdrManager.adjustParameter(param.name, parseFloat(slider.value));
                        valueDisplay.textContent = parseFloat(slider.value).toFixed(2);
                    });

                    controlsContainer.appendChild(slider);
                    controlsContainer.appendChild(valueDisplay);
                    this.sliders[param.name] = slider;
                }

                paramContainer.appendChild(controlsContainer);
                categoryContainer.appendChild(paramContainer);
            });

            slidersContainer.appendChild(categoryContainer);
        });

        slidersSection.appendChild(slidersContainer);
        this.container.appendChild(slidersSection);
    }

    /**
     * Create save preset controls
     */
    createSavePresetControls() {
        const saveSection = document.createElement('div');
        saveSection.className = 'hdr-section';

        const sectionTitle = document.createElement('div');
        sectionTitle.className = 'hdr-section-title';
        sectionTitle.textContent = 'SAVE CURRENT SETTINGS';
        saveSection.appendChild(sectionTitle);

        const saveContainer = document.createElement('div');
        saveContainer.style.display = 'flex';
        saveContainer.style.alignItems = 'center';
        saveContainer.style.marginBottom = '10px';

        const nameInput = document.createElement('input');
        nameInput.type = 'text';
        nameInput.placeholder = 'Preset Name';
        nameInput.style.flex = '1';
        saveContainer.appendChild(nameInput);

        const saveButton = document.createElement('button');
        saveButton.textContent = 'SAVE';
        saveButton.addEventListener('click', () => {
            const presetName = nameInput.value.trim();
            if (presetName) {
                // Get current settings from HDR manager
                const currentSettings = {
                    peakBrightness: this.hdrManager.peakBrightness,
                    toneMappingExposure: this.hdrManager.toneMappingExposure,
                    targetFramerate: this.hdrManager.targetFramerate,
                    framerateLocked: this.hdrManager.framerateLocked,
                    bitDepth: this.hdrManager.bitDepth,
                    ditherEnabled: this.hdrManager.ditherEnabled,
                    ditherStrength: this.hdrManager.ditherStrength
                };

                // Save to savedPresets
                this.savedPresets[presetName] = currentSettings;
                this.savePresets();

                // Update preset selector
                this.updatePresetSelector(presetName);

                // Clear input
                nameInput.value = '';

                // Show confirmation
                alert(`Preset "${presetName}" saved successfully!`);
            } else {
                alert('Please enter a name for your preset');
            }
        });
        saveContainer.appendChild(saveButton);

        saveSection.appendChild(saveContainer);

        // Add delete saved preset controls
        const deleteContainer = document.createElement('div');
        deleteContainer.style.display = 'flex';
        deleteContainer.style.alignItems = 'center';

        const deleteSelect = document.createElement('select');
        deleteSelect.style.flex = '1';
        this.updateDeleteSelector(deleteSelect);
        deleteContainer.appendChild(deleteSelect);

        const deleteButton = document.createElement('button');
        deleteButton.textContent = 'DELETE';
        deleteButton.addEventListener('click', () => {
            const presetName = deleteSelect.value;
            if (presetName && confirm(`Are you sure you want to delete the preset "${presetName}"?`)) {
                delete this.savedPresets[presetName];
                this.savePresets();
                this.updatePresetSelector();
                this.updateDeleteSelector(deleteSelect);
            }
        });
        deleteContainer.appendChild(deleteButton);

        saveSection.appendChild(deleteContainer);
        this.container.appendChild(saveSection);
    }

    /**
     * Create toggle button with retro styling
     */
    createToggleButton() {
        const toggleSection = document.createElement('div');
        toggleSection.className = 'hdr-section';

        const buttonsContainer = document.createElement('div');
        buttonsContainer.style.display = 'flex';
        buttonsContainer.style.justifyContent = 'space-between';
        buttonsContainer.style.marginBottom = '15px';

        const closeButton = document.createElement('button');
        closeButton.textContent = 'CLOSE MENU';
        closeButton.style.flex = '1';

        closeButton.addEventListener('click', () => {
            this.toggleVisibility();
        });

        buttonsContainer.appendChild(closeButton);
        toggleSection.appendChild(buttonsContainer);

        // Add keyboard shortcut info
        const shortcutInfo = document.createElement('div');
        shortcutInfo.textContent = 'Press F2 to toggle this menu';
        shortcutInfo.style.textAlign = 'center';
        shortcutInfo.style.fontSize = '12px';
        shortcutInfo.style.opacity = '0.7';
        shortcutInfo.style.marginTop = '5px';
        toggleSection.appendChild(shortcutInfo);

        this.container.appendChild(toggleSection);
    }

    /**
     * Update preset selector with new saved preset
     * @param {string} [selectedPreset] - Optional preset to select
     * @param {string} [forcePreset] - Optional preset to force select regardless of current preset
     */
    updatePresetSelector(selectedPreset = null, forcePreset = null) {
        // Clear existing options
        while (this.presetSelect.firstChild) {
            this.presetSelect.removeChild(this.presetSelect.firstChild);
        }

        // Add built-in presets
        const builtInGroup = document.createElement('optgroup');
        builtInGroup.label = 'BUILT-IN PRESETS';

        const presets = this.hdrManager.getPresetList();
        presets.forEach(preset => {
            const option = document.createElement('option');
            option.value = preset;
            option.textContent = preset.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            builtInGroup.appendChild(option);
        });

        this.presetSelect.appendChild(builtInGroup);

        // Add saved presets if any exist
        if (Object.keys(this.savedPresets).length > 0) {
            const savedGroup = document.createElement('optgroup');
            savedGroup.label = 'SAVED PRESETS';

            Object.keys(this.savedPresets).forEach(presetName => {
                const option = document.createElement('option');
                option.value = 'saved:' + presetName;
                option.textContent = presetName;
                savedGroup.appendChild(option);
            });

            this.presetSelect.appendChild(savedGroup);
        }

        // Set selected preset based on priority: forcePreset > selectedPreset > current preset
        if (forcePreset) {
            // Force the preset selection using a more reliable approach
            setTimeout(() => {
                this.presetSelect.value = forcePreset;
                // Manually trigger a change event to ensure UI consistency
                const event = new Event('change');
                this.presetSelect.dispatchEvent(event);
                console.log(`Forced preset selection to: ${forcePreset}`);
            }, 0);
        } else if (selectedPreset) {
            this.presetSelect.value = 'saved:' + selectedPreset;
        } else {
            this.presetSelect.value = this.hdrManager.getCurrentPreset();
        }
    }

    /**
     * Force select a specific preset in the dropdown and update UI
     * @param {string} presetName - The preset to force select
     */
    forceSelectPreset(presetName) {
        console.log(`Attempting to force select preset: ${presetName}`);

        // First make sure the manager has this preset set
        if (this.hdrManager.getCurrentPreset() !== presetName) {
            console.log(`Setting manager's currentPreset to ${presetName}`);
            this.hdrManager.currentPreset = presetName;
        }

        // Then update the dropdown using multiple approaches
        if (this.presetSelect) {
            // Approach 1: Direct value assignment
            this.presetSelect.value = presetName;
            console.log(`After direct assignment, dropdown value is: ${this.presetSelect.value}`);

            // Approach 2: Find and select by index
            let found = false;
            for (let i = 0; i < this.presetSelect.options.length; i++) {
                if (this.presetSelect.options[i].value === presetName) {
                    console.log(`Found option at index ${i}, selecting...`);
                    this.presetSelect.selectedIndex = i;
                    found = true;
                    break;
                }
            }

            // Approach 3: Find by data attribute (for NES preset)
            if (!found && presetName === 'nes') {
                console.log('Trying to find NES option by data attribute...');
                for (let i = 0; i < this.presetSelect.options.length; i++) {
                    if (this.presetSelect.options[i].getAttribute('data-special-preset') === 'true') {
                        console.log(`Found NES option by attribute at index ${i}`);
                        this.presetSelect.selectedIndex = i;
                        found = true;
                        break;
                    }
                }
            }

            // Approach 4: Extreme measure - replace the entire select element
            if (!found) {
                console.log('Rebuilding entire dropdown as last resort');
                // Create a new select element
                const newSelect = document.createElement('select');
                newSelect.id = 'hdr-preset-select';

                // Add the options
                const presets = this.hdrManager.getPresetList();
                presets.forEach(preset => {
                    const option = document.createElement('option');
                    option.value = preset;
                    option.textContent = preset.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    // Pre-select the target preset
                    if (preset === presetName) {
                        option.selected = true;
                    }
                    newSelect.appendChild(option);
                });

                // Copy event listeners
                newSelect.addEventListener('change', () => {
                    this.hdrManager.applyPreset(newSelect.value);
                    this.updateSliders();
                });

                // Replace the old select with the new one
                if (this.presetSelect.parentNode) {
                    this.presetSelect.parentNode.replaceChild(newSelect, this.presetSelect);
                    this.presetSelect = newSelect;
                }
            }

            // Trigger change event to ensure consistency
            const event = new Event('change');
            this.presetSelect.dispatchEvent(event);

            // Verify final state
            console.log(`Final dropdown value after all attempts: ${this.presetSelect.value}`);
            console.log(`Final manager preset: ${this.hdrManager.getCurrentPreset()}`);
        }
    }

    /**
     * Update delete selector with current saved presets
     * @param {HTMLSelectElement} deleteSelect - The select element to update
     */
    updateDeleteSelector(deleteSelect) {
        // Clear existing options
        while (deleteSelect.firstChild) {
            deleteSelect.removeChild(deleteSelect.firstChild);
        }

        // Add placeholder if no saved presets
        if (Object.keys(this.savedPresets).length === 0) {
            const placeholder = document.createElement('option');
            placeholder.value = '';
            placeholder.textContent = 'No saved presets';
            placeholder.disabled = true;
            placeholder.selected = true;
            deleteSelect.appendChild(placeholder);
            return;
        }

        // Add saved presets
        Object.keys(this.savedPresets).forEach(presetName => {
            const option = document.createElement('option');
            option.value = presetName;
            option.textContent = presetName;
            deleteSelect.appendChild(option);
        });
    }

    /**
     * Update sliders to match current preset
     */
    updateSliders() {
        // Get current settings
        const settings = {
            peakBrightness: this.hdrManager.peakBrightness,
            toneMappingExposure: this.hdrManager.toneMappingExposure,
            targetFramerate: this.hdrManager.targetFramerate,
            framerateLocked: this.hdrManager.framerateLocked,
            bitDepth: this.hdrManager.bitDepth,
            ditherEnabled: this.hdrManager.ditherEnabled,
            ditherStrength: this.hdrManager.ditherStrength
        };

        // Update sliders
        for (const [param, control] of Object.entries(this.sliders)) {
            if (settings[param] !== undefined) {
                // Handle different control types
                if (control.type === 'checkbox') {
                    control.checked = settings[param];
                } else {
                    control.value = settings[param];
                    // Update value display if it exists
                    if (control.nextElementSibling) {
                        control.nextElementSibling.textContent = parseFloat(settings[param]).toFixed(2);
                    }
                }
            }
        }
    }

    /**
     * Toggle control panel visibility with animation
     */
    toggleVisibility() {
        this.visible = !this.visible;

        if (this.visible) {
            // Show panel with fade-in effect
            this.container.style.display = 'block';
            this.container.style.opacity = '0';

            // Add a slight scale effect
            this.container.style.transform = 'translate(-50%, -50%) scale(0.95)';

            // Trigger animation
            setTimeout(() => {
                this.container.style.transition = 'opacity 0.2s ease-out, transform 0.2s ease-out';
                this.container.style.opacity = '1';
                this.container.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 10);
        } else {
            // Hide with fade-out effect
            this.container.style.transition = 'opacity 0.2s ease-in, transform 0.2s ease-in';
            this.container.style.opacity = '0';
            this.container.style.transform = 'translate(-50%, -50%) scale(0.95)';

            // Actually hide after animation completes
            setTimeout(() => {
                this.container.style.display = 'none';
            }, 200);
        }
    }
}

export { HDRFramerateControlPanel };
