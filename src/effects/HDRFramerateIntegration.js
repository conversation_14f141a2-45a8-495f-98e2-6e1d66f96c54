import { HDRFramerateManager } from './HDRFramerateManager.js';
import { HDRFramerateControlPanel } from '../ui/HDRFramerateControlPanel.js';

/**
 * Initialize HDR and Framerate effect
 * @param {THREE.WebGLRenderer} renderer - The WebGL renderer
 * @param {THREE.Scene} scene - The scene
 * @param {THREE.Camera} camera - The camera
 * @returns {Object} The HDR and Framerate manager and control panel
 */
export function initHDRFramerateEffect(renderer, scene, camera) {
    // Initialize HDR and Framerate manager
    const hdrManager = new HDRFramerateManager(renderer, scene, camera);

    // Ensure NES preset is selected
    if (hdrManager.getCurrentPreset() !== 'nes') {
        console.log('Forcing NES preset in HDRFramerateIntegration');
        hdrManager.currentPreset = 'nes';
        hdrManager.applyPreset('nes');
    }

    // Initialize control panel
    const controlPanel = new HDRFramerateControlPanel(hdrManager);

    // Force the control panel to show NES as selected after a delay
    setTimeout(() => {
        if (controlPanel.presetSelect) {
            console.log('Setting preset select to NES in HDRFramerateIntegration');
            // Try multiple approaches
            controlPanel.presetSelect.value = 'nes';

            // Find by index
            for (let i = 0; i < controlPanel.presetSelect.options.length; i++) {
                if (controlPanel.presetSelect.options[i].value === 'nes') {
                    controlPanel.presetSelect.selectedIndex = i;
                    break;
                }
            }

            // Trigger change event
            const event = new Event('change');
            controlPanel.presetSelect.dispatchEvent(event);
        }
    }, 100);

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Toggle HDR settings panel with F2 (only on keydown, not repeat)
        if (e.key === 'F2' && !e.repeat) {
            e.preventDefault(); // Prevent default browser behavior
            controlPanel.toggleVisibility();
        }

        // Toggle framerate lock with F4
        if (e.key === 'F4' && !e.repeat) {
            e.preventDefault(); // Prevent default browser behavior
            hdrManager.adjustParameter('framerateLocked', !hdrManager.framerateLocked);
            controlPanel.updateSliders();
            console.log(`Framerate lock ${hdrManager.framerateLocked ? 'enabled' : 'disabled'}`);
        }

        // Cycle through presets with F5
        if (e.key === 'F5' && !e.repeat) {
            e.preventDefault(); // Prevent default browser behavior
            const presets = hdrManager.getPresetList();
            const currentIndex = presets.indexOf(hdrManager.getCurrentPreset());
            const nextIndex = (currentIndex + 1) % presets.length;
            hdrManager.applyPreset(presets[nextIndex]);
            controlPanel.updateSliders();
            controlPanel.presetSelect.value = presets[nextIndex];
            console.log(`Applied preset: ${presets[nextIndex]}`);
        }
    });

    return {
        manager: hdrManager,
        controlPanel: controlPanel,

        // Expose all the methods needed for integration with other effects
        update: (time) => {
            return hdrManager.update(time);
        },

        // Expose framerate limiting method
        shouldRenderFrame: (time) => {
            return hdrManager.shouldRenderFrame(time);
        },

        // Expose HDR settings method
        applyHDRSettings: () => {
            hdrManager.applyHDRSettings();
        },

        // Expose bit depth post-processing method
        applyBitDepthPostProcessing: () => {
            hdrManager.applyBitDepthPostProcessing();
        },

        // Expose applyCustomSettings method
        applyCustomSettings: (settings) => {
            if (typeof hdrManager.applyCustomSettings === 'function') {
                hdrManager.applyCustomSettings(settings);
            } else {
                console.warn('HDR manager does not have applyCustomSettings method');
            }
        },

        // Expose applyPreset method
        applyPreset: (presetName) => {
            if (typeof hdrManager.applyPreset === 'function') {
                hdrManager.applyPreset(presetName);
            } else {
                console.warn('HDR manager does not have applyPreset method');
            }
        },

        // Expose properties for logging/debugging
        get peakBrightness() { return hdrManager.peakBrightness; },
        get bitDepth() { return hdrManager.bitDepth; },
        get framerateLocked() { return hdrManager.framerateLocked; },
        get targetFramerate() { return hdrManager.targetFramerate; }
    };
}
