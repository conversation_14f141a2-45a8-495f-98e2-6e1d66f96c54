import * as THREE from 'three';
import { ShaderPass } from 'three/addons/postprocessing/ShaderPass.js';
import { EffectComposer } from 'three/addons/postprocessing/EffectComposer.js';
import { RenderPass } from 'three/addons/postprocessing/RenderPass.js';
import { CodeVisualShader } from '../shaders/CodeVisualShader.js';

/**
 * Manages console visual presets and game FPS settings
 */
class ConsolePresetManager {
    /**
     * Create a new Console Preset Manager
     * @param {THREE.WebGLRenderer} renderer - The WebGL renderer
     * @param {THREE.Scene} scene - The scene
     * @param {THREE.Camera} camera - The camera
     * @param {Object} hdrManager - The HDR manager (optional)
     * @param {Object} crtManager - The CRT manager (optional)
     */
    constructor(renderer, scene, camera, hdrManager = null, crtManager = null) {
        this.renderer = renderer;
        this.scene = scene;
        this.camera = camera;
        this.hdrManager = hdrManager;
        this.crtManager = crtManager;
        this.clock = new THREE.Clock();

        // Console preset settings
        this.currentPreset = 'none';
        this.enabled = false;
        this.eightBitCodeEnabled = false;

        // Game FPS settings
        this.targetGameFPS = 60;
        this.gameFPSLocked = false;
        this.lastFrameTime = 0;
        this.frameTimeTarget = 1000 / this.targetGameFPS;

        // Initialize presets
        this.presets = this._initializePresets();

        // Initialize post-processing
        this._initializePostProcessing();
    }

    /**
     * Initialize presets for different consoles
     * @private
     */
    _initializePresets() {
        return {
            'none': {
                description: 'No console preset applied',
                hdrSettings: {
                    peakBrightness: 1000,
                    bitDepth: 10,
                    targetFramerate: 60,
                    ditherEnabled: false,
                    toneMappingExposure: 1.5
                },
                crtSettings: {
                    enabled: false,
                    preset: 'none'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 60
            },
            'nes': {
                description: 'Nintendo Entertainment System (1985)',
                hdrSettings: {
                    peakBrightness: 250,
                    bitDepth: 4.0, // Changed from 6 to 4.0 as requested
                    targetFramerate: 60,
                    ditherEnabled: true,
                    toneMappingExposure: 1.8
                },
                crtSettings: {
                    enabled: true,
                    preset: 'arcade_monitor'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 60
            },
            'snes': {
                description: 'Super Nintendo Entertainment System (1990)',
                hdrSettings: {
                    peakBrightness: 280,
                    bitDepth: 8,
                    targetFramerate: 60,
                    ditherEnabled: true,
                    toneMappingExposure: 1.7
                },
                crtSettings: {
                    enabled: true,
                    preset: 'sony_trinitron'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 60
            },
            'genesis': {
                description: 'Sega Genesis (1988)',
                hdrSettings: {
                    peakBrightness: 270,
                    bitDepth: 8,
                    targetFramerate: 60,
                    ditherEnabled: true,
                    toneMappingExposure: 1.7
                },
                crtSettings: {
                    enabled: true,
                    preset: 'arcade_monitor'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 60
            },
            'ps1': {
                description: 'PlayStation (1994)',
                hdrSettings: {
                    peakBrightness: 300,
                    bitDepth: 8,
                    targetFramerate: 30,
                    ditherEnabled: true,
                    toneMappingExposure: 1.6
                },
                crtSettings: {
                    enabled: true,
                    preset: 'sony_trinitron'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 30
            },
            'n64': {
                description: 'Nintendo 64 (1996)',
                hdrSettings: {
                    peakBrightness: 300,
                    bitDepth: 8,
                    targetFramerate: 30,
                    ditherEnabled: true,
                    toneMappingExposure: 1.6
                },
                crtSettings: {
                    enabled: true,
                    preset: 'sony_trinitron'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 30
            },
            'dreamcast': {
                description: 'Sega Dreamcast (1998)',
                hdrSettings: {
                    peakBrightness: 350,
                    bitDepth: 8,
                    targetFramerate: 60,
                    ditherEnabled: true,
                    toneMappingExposure: 1.6
                },
                crtSettings: {
                    enabled: true,
                    preset: 'arcade_monitor'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 60
            },
            'ps2': {
                description: 'PlayStation 2 (2000)',
                hdrSettings: {
                    peakBrightness: 400,
                    bitDepth: 8,
                    targetFramerate: 60,
                    ditherEnabled: true,
                    toneMappingExposure: 1.6
                },
                crtSettings: {
                    enabled: true,
                    preset: 'sony_trinitron'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 60
            },
            'xbox': {
                description: 'Xbox (2001)',
                hdrSettings: {
                    peakBrightness: 450,
                    bitDepth: 8,
                    targetFramerate: 60,
                    ditherEnabled: true,
                    toneMappingExposure: 1.6
                },
                crtSettings: {
                    enabled: false,
                    preset: 'none'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 60
            },
            'ps3': {
                description: 'PlayStation 3 (2006)',
                hdrSettings: {
                    peakBrightness: 600,
                    bitDepth: 8,
                    targetFramerate: 30,
                    ditherEnabled: false,
                    toneMappingExposure: 1.5
                },
                crtSettings: {
                    enabled: false,
                    preset: 'none'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 30
            },
            'xbox360': {
                description: 'Xbox 360 (2005)',
                hdrSettings: {
                    peakBrightness: 600,
                    bitDepth: 8,
                    targetFramerate: 30,
                    ditherEnabled: false,
                    toneMappingExposure: 1.5
                },
                crtSettings: {
                    enabled: false,
                    preset: 'none'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 30
            },
            'modern': {
                description: 'Modern Gaming PC',
                hdrSettings: {
                    peakBrightness: 1000,
                    bitDepth: 10,
                    targetFramerate: 60,
                    ditherEnabled: false,
                    toneMappingExposure: 1.5
                },
                crtSettings: {
                    enabled: false,
                    preset: 'none'
                },
                eightBitCodeSettings: {
                    enabled: false
                },
                gameFPS: 60
            },
            'matrix': {
                description: 'Matrix/Code Style (8-bit)',
                hdrSettings: {
                    peakBrightness: 800,
                    bitDepth: 8,
                    targetFramerate: 60,
                    ditherEnabled: true,
                    toneMappingExposure: 1.8
                },
                crtSettings: {
                    enabled: false,
                    preset: 'none'
                },
                codeVisualSettings: {
                    enabled: true,
                    is32Bit: false,
                    pixelSize: 4.0,
                    colorReduction: 8.0,
                    scanlineIntensity: 0.3,
                    colorTint: [0.0, 0.8, 0.0], // Green
                    characterDensity: 0.5,
                    glitchIntensity: 0.1,
                    glitchSpeed: 5.0,
                    matrixEffect: 0.8 // Strong matrix effect
                },
                gameFPS: 60
            },
            'retro_code': {
                description: 'Retro Code Terminal (8-bit)',
                hdrSettings: {
                    peakBrightness: 400,
                    bitDepth: 4,
                    targetFramerate: 30,
                    ditherEnabled: true,
                    toneMappingExposure: 1.6
                },
                crtSettings: {
                    enabled: true,
                    preset: 'arcade_monitor'
                },
                codeVisualSettings: {
                    enabled: true,
                    is32Bit: false,
                    pixelSize: 6.0,
                    colorReduction: 4.0,
                    scanlineIntensity: 0.5,
                    colorTint: [0.0, 0.9, 0.0], // Bright green
                    characterDensity: 0.7,
                    glitchIntensity: 0.2,
                    glitchSpeed: 3.0,
                    matrixEffect: 0.0 // No matrix effect
                },
                gameFPS: 30
            },
            'code_32bit': {
                description: 'Modern 32-bit Code Visualization',
                hdrSettings: {
                    peakBrightness: 1000,
                    bitDepth: 10,
                    targetFramerate: 60,
                    ditherEnabled: false,
                    toneMappingExposure: 1.5
                },
                crtSettings: {
                    enabled: false,
                    preset: 'none'
                },
                codeVisualSettings: {
                    enabled: true,
                    is32Bit: true, // 32-bit mode
                    pixelSize: 2.0, // Less pixelation
                    colorReduction: 10.0, // Higher color depth
                    scanlineIntensity: 0.1, // Subtle scanlines
                    colorTint: [0.0, 0.7, 0.3], // Teal-green
                    characterDensity: 0.4,
                    characterDetail: 1.5, // More detailed characters
                    glitchIntensity: 0.05, // Subtle glitches
                    glitchSpeed: 8.0,
                    colorfulText: 0.7, // More colorful
                    bloomStrength: 0.8, // Strong bloom
                    matrixEffect: 0.3 // Moderate matrix effect
                },
                gameFPS: 60
            },
            'cyberpunk': {
                description: 'Cyberpunk 32-bit Visualization',
                hdrSettings: {
                    peakBrightness: 1000,
                    bitDepth: 10,
                    targetFramerate: 60,
                    ditherEnabled: false,
                    toneMappingExposure: 1.7
                },
                crtSettings: {
                    enabled: false,
                    preset: 'none'
                },
                codeVisualSettings: {
                    enabled: true,
                    is32Bit: true, // 32-bit mode
                    pixelSize: 3.0,
                    colorReduction: 8.0,
                    scanlineIntensity: 0.2,
                    colorTint: [0.1, 0.0, 0.8], // Blue/purple tint
                    characterDensity: 0.6,
                    characterDetail: 1.2,
                    glitchIntensity: 0.15,
                    glitchSpeed: 10.0,
                    colorfulText: 0.9, // Very colorful
                    bloomStrength: 1.0, // Maximum bloom
                    matrixEffect: 0.5 // Medium matrix effect
                },
                gameFPS: 60
            }
        };
    }

    /**
     * Initialize post-processing for 8-bit code effect
     * @private
     */
    _initializePostProcessing() {
        // Create render targets with proper format
        const renderTargetParameters = {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            stencilBuffer: false
        };

        // Initialize composer with proper render target
        const renderTarget = new THREE.WebGLRenderTarget(
            Math.max(1, window.innerWidth),
            Math.max(1, window.innerHeight),
            renderTargetParameters
        );
        this.composer = new EffectComposer(this.renderer, renderTarget);

        // Create a dedicated render pass
        this.renderPass = new RenderPass(this.scene, this.camera);
        this.composer.addPass(this.renderPass);

        // Create code visualization pass
        this.codeVisualPass = new ShaderPass(CodeVisualShader);
        this.composer.addPass(this.codeVisualPass);

        // Add window resize listener
        window.addEventListener('resize', this.updateResolution.bind(this));
    }

    /**
     * Update resolution when window is resized
     */
    updateResolution() {
        const width = Math.max(1, window.innerWidth);
        const height = Math.max(1, window.innerHeight);

        // Update composer size
        this.composer.setSize(width, height);
    }

    /**
     * Apply a preset configuration
     * @param {string} presetName - Name of the preset to apply
     */
    applyPreset(presetName) {
        const preset = this.presets[presetName];
        if (!preset) {
            console.warn(`Preset '${presetName}' not found`);
            return;
        }

        this.currentPreset = presetName;

        // Apply HDR settings if HDR manager is available
        if (this.hdrManager && preset.hdrSettings) {
            // Check if applyCustomSettings exists, otherwise use applyPreset
            if (typeof this.hdrManager.applyCustomSettings === 'function') {
                this.hdrManager.applyCustomSettings(preset.hdrSettings);
            } else if (typeof this.hdrManager.applyPreset === 'function') {
                // If applyCustomSettings doesn't exist, try to use applyPreset
                console.log(`Using applyPreset fallback for HDR settings in preset: ${presetName}`);
                this.hdrManager.applyPreset(presetName);
            } else {
                console.warn(`Cannot apply HDR settings for preset ${presetName}: no compatible method found`);
            }
        }

        // Apply CRT settings if CRT manager is available
        if (this.crtManager && preset.crtSettings) {
            try {
                if (preset.crtSettings.enabled) {
                    // Check if enable method exists
                    if (typeof this.crtManager.enable === 'function') {
                        this.crtManager.enable();
                    } else if (typeof this.crtManager.toggle === 'function') {
                        // Try toggle method as fallback
                        this.crtManager.toggle(true);
                    } else {
                        console.warn('CRT manager does not have enable or toggle method');
                    }

                    // Apply preset if not 'none'
                    if (preset.crtSettings.preset !== 'none') {
                        if (typeof this.crtManager.applyPreset === 'function') {
                            this.crtManager.applyPreset(preset.crtSettings.preset);
                        } else {
                            console.warn(`Cannot apply CRT preset ${preset.crtSettings.preset}: no applyPreset method found`);
                        }
                    }
                } else {
                    // Check if disable method exists
                    if (typeof this.crtManager.disable === 'function') {
                        this.crtManager.disable();
                    } else if (typeof this.crtManager.toggle === 'function') {
                        // Try toggle method as fallback
                        this.crtManager.toggle(false);
                    } else {
                        console.warn('CRT manager does not have disable or toggle method');
                    }
                }
            } catch (error) {
                console.error('Error applying CRT settings:', error);
            }
        }

        // Apply code visualization settings
        if (preset.codeVisualSettings) {
            this.eightBitCodeEnabled = preset.codeVisualSettings.enabled;

            if (this.eightBitCodeEnabled) {
                // Apply specific settings to the shader
                const settings = preset.codeVisualSettings;

                // Set 32-bit mode flag
                this.codeVisualPass.uniforms.is32Bit.value = settings.is32Bit ? 1 : 0;

                // Basic settings
                this.codeVisualPass.uniforms.pixelSize.value = settings.pixelSize || 4.0;
                this.codeVisualPass.uniforms.colorReduction.value = settings.colorReduction || 8.0;
                this.codeVisualPass.uniforms.scanlineIntensity.value = settings.scanlineIntensity || 0.3;

                // Color tint (RGB vector)
                if (settings.colorTint) {
                    this.codeVisualPass.uniforms.colorTint.value.set(
                        settings.colorTint[0] || 0.0,
                        settings.colorTint[1] || 0.8,
                        settings.colorTint[2] || 0.0
                    );
                } else {
                    // Default green tint
                    this.codeVisualPass.uniforms.colorTint.value.set(0.0, 0.8, 0.0);
                }

                // Character settings
                this.codeVisualPass.uniforms.characterDensity.value = settings.characterDensity || 0.5;
                this.codeVisualPass.uniforms.characterDetail.value = settings.characterDetail || 1.0;

                // Glitch settings
                this.codeVisualPass.uniforms.glitchIntensity.value = settings.glitchIntensity || 0.1;
                this.codeVisualPass.uniforms.glitchSpeed.value = settings.glitchSpeed || 5.0;

                // 32-bit specific settings
                this.codeVisualPass.uniforms.colorfulText.value = settings.colorfulText || 0.0;
                this.codeVisualPass.uniforms.bloomStrength.value = settings.bloomStrength || 0.5;
                this.codeVisualPass.uniforms.matrixEffect.value = settings.matrixEffect || 0.0;
            }
        }

        // Apply game FPS settings
        if (preset.gameFPS) {
            this.targetGameFPS = preset.gameFPS;
            this.frameTimeTarget = 1000 / this.targetGameFPS;
        }

        console.log(`Applied console preset '${presetName}': ${preset.description}`);
    }

    /**
     * Toggle the 8-bit code effect on/off
     */
    toggleEightBitCode() {
        this.eightBitCodeEnabled = !this.eightBitCodeEnabled;
        console.log(`8-bit code effect ${this.eightBitCodeEnabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Set game FPS
     * @param {number} fps - Target FPS for the game
     */
    setGameFPS(fps) {
        this.targetGameFPS = fps;
        this.frameTimeTarget = 1000 / fps;
        console.log(`Game FPS set to ${fps}`);
    }

    /**
     * Toggle game FPS lock
     * @param {boolean} [locked] - If provided, set to this value, otherwise toggle
     */
    toggleGameFPSLock(locked = null) {
        if (locked !== null) {
            this.gameFPSLocked = locked;
        } else {
            this.gameFPSLocked = !this.gameFPSLocked;
        }
        console.log(`Game FPS lock ${this.gameFPSLocked ? 'enabled' : 'disabled'}`);
    }

    /**
     * Check if we should render this frame based on game FPS limiting
     * @param {number} time - Current time in milliseconds
     * @returns {boolean} - Whether to render this frame
     */
    shouldRenderGameFrame(time) {
        // Always render if FPS lock is disabled
        if (!this.gameFPSLocked) {
            return true;
        }

        // Check if enough time has passed since last frame
        const elapsed = time - this.lastFrameTime;
        if (elapsed < this.frameTimeTarget) {
            return false; // Skip this frame
        }

        // Update last frame time
        this.lastFrameTime = time;
        return true;
    }

    /**
     * Get the list of available presets
     * @returns {string[]} Array of preset names
     */
    getPresetList() {
        return Object.keys(this.presets);
    }

    /**
     * Get the current preset name
     * @returns {string} Current preset name
     */
    getCurrentPreset() {
        return this.currentPreset;
    }

    /**
     * Get preset description
     * @param {string} presetName - Name of the preset
     * @returns {string} Preset description
     */
    getPresetDescription(presetName) {
        const preset = this.presets[presetName];
        return preset ? preset.description : '';
    }

    /**
     * Update the effect (call this in your animation loop)
     * @param {number} time - Current time in milliseconds
     * @returns {boolean} - Whether rendering occurred
     */
    update(time) {
        try {
            // Update time uniform for animations
            this.codeVisualPass.uniforms.time.value = time * 0.001; // Convert to seconds

            // Skip if code effect is not enabled
            if (!this.eightBitCodeEnabled) {
                return true;
            }

            // Render scene with code visualization effect
            this.composer.render();

            return true;
        } catch (error) {
            console.error('Error in console preset update:', error);
            return true;
        }
    }
}

export { ConsolePresetManager };
