import { ConsolePresetManager } from './ConsolePresetManager.js';
import { ConsolePresetControlPanel } from '../ui/ConsolePresetControlPanel.js';

/**
 * Initialize Console Preset effect
 * @param {THREE.WebGLRenderer} renderer - The WebGL renderer
 * @param {THREE.Scene} scene - The scene
 * @param {THREE.Camera} camera - The camera
 * @param {Object} hdrManager - The HDR manager (optional)
 * @param {Object} crtManager - The CRT manager (optional)
 * @returns {Object} The Console Preset manager and control panel
 */
export function initConsolePresetEffect(renderer, scene, camera, hdrManager = null, crtManager = null) {
    // Initialize Console Preset manager
    const consoleManager = new ConsolePresetManager(renderer, scene, camera, hdrManager, crtManager);

    // Initialize control panel
    const controlPanel = new ConsolePresetControlPanel(consoleManager);

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Toggle Console settings panel with F3 (only on keydown, not repeat)
        if (e.key === 'F3' && !e.repeat) {
            e.preventDefault(); // Prevent default browser behavior
            controlPanel.toggleVisibility();
        }

        // Toggle 8-bit code effect with F7
        if (e.key === 'F7' && !e.repeat) {
            e.preventDefault(); // Prevent default browser behavior
            consoleManager.toggleEightBitCode();
            console.log(`8-bit code effect ${consoleManager.eightBitCodeEnabled ? 'enabled' : 'disabled'}`);
        }

        // Toggle game FPS lock with F8
        if (e.key === 'F8' && !e.repeat) {
            e.preventDefault(); // Prevent default browser behavior
            consoleManager.toggleGameFPSLock();
            console.log(`Game FPS lock ${consoleManager.gameFPSLocked ? 'enabled' : 'disabled'}`);
        }

        // Cycle through presets with F9
        if (e.key === 'F9' && !e.repeat) {
            e.preventDefault(); // Prevent default browser behavior
            const presets = consoleManager.getPresetList();
            const currentIndex = presets.indexOf(consoleManager.getCurrentPreset());
            const nextIndex = (currentIndex + 1) % presets.length;
            consoleManager.applyPreset(presets[nextIndex]);
            controlPanel.updateControls();
            console.log(`Applied console preset: ${presets[nextIndex]}`);
        }
    });

    return {
        manager: consoleManager,
        controlPanel: controlPanel,

        // Expose all the methods needed for integration with other effects
        update: (time) => {
            return consoleManager.update(time);
        },

        // Expose framerate limiting method
        shouldRenderGameFrame: (time) => {
            return consoleManager.shouldRenderGameFrame(time);
        },

        // Expose properties for logging/debugging
        get currentPreset() { return consoleManager.currentPreset; },
        get eightBitCodeEnabled() { return consoleManager.eightBitCodeEnabled; },
        get gameFPSLocked() { return consoleManager.gameFPSLocked; },
        get targetGameFPS() { return consoleManager.targetGameFPS; }
    };
}
