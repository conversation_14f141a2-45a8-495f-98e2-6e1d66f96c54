import * as THREE from 'three';

/**
 * Creates layered, semi-transparent planes to simulate floor mist.
 * 
 * @param {object} roomBounds - Object with minX, maxX, minZ, maxZ defining the floor area.
 * @param {number} [mistHeight=0.5] - The maximum height range for the mist planes.
 * @param {number} [layerCount=3] - How many layers of mist planes.
 * @param {number} [opacity=0.15] - Base opacity for the mist.
 * @param {Array} floorSegments - Array of floor segments defining actual walkable areas.
 * @returns {THREE.Group} A group containing the mist plane meshes.
 */
export function createFloorMistPlanes(roomBounds, mistHeight = 0.5, layerCount = 3, opacity = 0.15, floorSegments = []) {
    const mistGroup = new THREE.Group();
    mistGroup.name = "floorMist";

    // Constants for mist plane adjustments
    const WALL_BUFFER = 0.1; // Reduced buffer inward from walls (was 0.2)
    const SEGMENT_BLEND = 1.0; // How much to blend between segments

    // If no floor segments provided, create one for the entire room bounds (fallback)
    const segmentsToUse = floorSegments.length > 0 ? floorSegments : [{
        position: new THREE.Vector3(
            (roomBounds.minX + roomBounds.maxX) / 2,
            0,
            (roomBounds.minZ + roomBounds.maxZ) / 2
        ),
        width: roomBounds.maxX - roomBounds.minX,
        depth: roomBounds.maxZ - roomBounds.minZ
    }];

    // Create mist planes for each floor segment
    segmentsToUse.forEach(segment => {
        // Apply wall buffer to segment dimensions
        const mistWidth = segment.width - WALL_BUFFER * 2;
        const mistDepth = segment.depth - WALL_BUFFER * 2;

        // Create a plane geometry for this segment
        const planeGeo = new THREE.PlaneGeometry(mistWidth, mistDepth);

        // Create base layers
        for (let i = 0; i < layerCount; i++) {
            const baseOpacity = opacity / layerCount;
            const layerMat = new THREE.MeshBasicMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: baseOpacity + (Math.random() * baseOpacity * 0.2), // Reduced opacity variation
                depthWrite: false,
                blending: THREE.AdditiveBlending, // Changed to additive blending for smoother transitions
                side: THREE.DoubleSide,
            });

            const planeMesh = new THREE.Mesh(planeGeo, layerMat);
            
            // Position at the segment's location
            planeMesh.position.set(
                segment.position.x,
                (i + 1) * (mistHeight / layerCount) * 0.5,
                segment.position.z
            );
            
            // Rotate plane to be horizontal
            planeMesh.rotation.x = -Math.PI / 2;
            
            // Add very subtle random rotation
            planeMesh.rotation.z = (Math.random() - 0.5) * 0.05; // Reduced rotation variation

            mistGroup.add(planeMesh);
        }

        // If this is a multi-segment room, add blending planes between segments
        if (segmentsToUse.length > 1) {
            // Add larger, more transparent planes for smooth blending
            const blendPlaneGeo = new THREE.PlaneGeometry(
                mistWidth + SEGMENT_BLEND * 2,
                mistDepth + SEGMENT_BLEND * 2
            );

            for (let i = 0; i < layerCount; i++) {
                const blendMat = new THREE.MeshBasicMaterial({
                    color: 0xffffff,
                    transparent: true,
                    opacity: (opacity / layerCount) * 0.3, // Lower opacity for blend layers
                    depthWrite: false,
                    blending: THREE.AdditiveBlending,
                    side: THREE.DoubleSide,
                });

                const blendPlane = new THREE.Mesh(blendPlaneGeo, blendMat);
                blendPlane.position.set(
                    segment.position.x,
                    (i + 1) * (mistHeight / layerCount) * 0.5 + 0.01, // Slight Y offset
                    segment.position.z
                );
                blendPlane.rotation.x = -Math.PI / 2;

                mistGroup.add(blendPlane);
            }
        }
    });

    return mistGroup;
} 