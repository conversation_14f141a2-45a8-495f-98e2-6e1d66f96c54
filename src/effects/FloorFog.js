import * as THREE from 'three';

/**
 * Creates a particle system simulating low-lying fog.
 * 
 * @param {object} roomBounds - Object with minX, maxX, minZ, maxZ defining the floor area.
 * @param {number} particleCount - The number of fog particles to generate.
 * @param {number} [fogHeight=0.3] - The maximum height above the floor for the fog particles.
 * @param {number} [particleSize=0.5] - The base size of the fog particles.
 * @param {number} [wallBuffer=1.0] - Buffer distance from walls to prevent fog clipping.
 * @returns {THREE.Points} The particle system object.
 */
export function createFloorFogParticles(roomBounds, particleCount, fogHeight = 0.3, particleSize = 0.5, wallBuffer = 1.0) {
    const particlesGeo = new THREE.BufferGeometry();
    const positions = [];

    const { minX, maxX, minZ, maxZ } = roomBounds;
    const width = maxX - minX;
    const depth = maxZ - minZ;

    // Apply wall buffer to prevent fog from clipping through walls
    const safeMinX = minX + wallBuffer;
    const safeMaxX = maxX - wallBuffer;
    const safeMinZ = minZ + wallBuffer;
    const safeMaxZ = maxZ - wallBuffer;
    const safeWidth = safeMaxX - safeMinX;
    const safeDepth = safeMaxZ - safeMinZ;

    for (let i = 0; i < particleCount; i++) {
        const x = safeMinX + Math.random() * safeWidth;
        const y = Math.random() * fogHeight; // Random height within the fog layer
        const z = safeMinZ + Math.random() * safeDepth;
        positions.push(x, y, z);
    }

    particlesGeo.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));

    // --- Material --- 
    // TODO: Use a soft smoke/fog texture for better effect if available.
    // const fogTexture = new THREE.TextureLoader().load('path/to/fog_particle.png');
    const particlesMat = new THREE.PointsMaterial({
        color: 0xcccccc, // Greyish White
        size: 3.0,
        sizeAttenuation: false,
        transparent: true,
        opacity: 0.25,
        depthWrite: false, 
        blending: THREE.NormalBlending,
        // map: fogTexture // Uncomment if using a texture
    });

    const fogParticles = new THREE.Points(particlesGeo, particlesMat);
    fogParticles.name = "floorFog";

    return fogParticles;
} 