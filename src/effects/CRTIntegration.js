/**
 * CRT Integration
 * Integrates the CRT effect with the main game
 */
import { CRTEffectManager } from './CRTEffectManager.js';
import { CRTControlPanel } from '../ui/CRTControlPanel.js';

/**
 * Initialize CRT effect for the game
 * @param {THREE.WebGLRenderer} renderer - The renderer
 * @param {THREE.Scene} scene - The scene
 * @param {THREE.Camera} camera - The camera
 * @returns {Object} CRT manager and control panel
 */
function initCRTEffect(renderer, scene, camera) {
    // Create CRT manager
    const crtManager = new CRTEffectManager(renderer, scene, camera);

    // Apply arcade_monitor preset by default and enable it
    crtManager.applyPreset('arcade_monitor');
    crtManager.enabled = true;
    crtManager.enableRefreshVisualization(true, 0.10);

    // Create control panel
    const controlPanel = new CRTControlPanel(crtManager);
    controlPanel.init();

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Toggle CRT settings panel with F1 (only on keydown, not repeat)
        if (e.key === 'F1' && !e.repeat) {
            e.preventDefault(); // Prevent default browser behavior
            controlPanel.toggleVisibility();
        }

        // Toggle CRT effect on/off with F6 (changed from F2 to avoid conflict with HDR panel)
        if (e.key === 'F6') {
            e.preventDefault(); // Prevent default browser behavior
            crtManager.toggle();
        }

        // Cycle through presets with F3
        if (e.key === 'F3') {
            e.preventDefault(); // Prevent default browser behavior
            const presets = crtManager.getPresetList();
            // Filter out 'none' preset
            const filteredPresets = presets.filter(preset => preset !== 'none');

            if (filteredPresets.length > 0) {
                let currentIndex = filteredPresets.indexOf(crtManager.getCurrentPreset());
                if (currentIndex === -1) currentIndex = 0;

                const nextIndex = (currentIndex + 1) % filteredPresets.length;
                crtManager.applyPreset(filteredPresets[nextIndex]);
                controlPanel.updateSliders();
                controlPanel.presetSelect.value = filteredPresets[nextIndex];
            }
        }
    });

    // Return manager and control panel
    return {
        manager: crtManager,
        controlPanel: controlPanel,

        /**
         * Update CRT effect (call this in your animation loop)
         */
        update: function() {
            crtManager.update();
        }
    };
}

export { initCRTEffect };
