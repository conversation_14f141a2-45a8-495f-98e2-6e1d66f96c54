/**
 * Manages the item drop system, including drop generation, filtering, and spawning.
 */

import { getItemData, getItemsByRarity } from '../entities/ItemTypes.js';
import { 
    getRarityWeights, 
    getRandomRarity, 
    buildItemPool, 
    getRandomItemFromWeightedPool,
    SOUL_WEIGHT_MODIFIERS,
    RELIC_MODIFIERS
} from '../gameData/ItemPools.js';
import { mulberry32 } from '../generators/prefabs/shared.js';
import Item from '../entities/Item.js';

class ItemDropManager {
    constructor() {
        // Cache for biome item pools
        this.biomePoolCache = {};
        
        // Default soul weight (neutral)
        this.currentSoulWeight = 'balanced';
        
        // Active relics
        this.activeRelics = [];
        
        // Blocked items (e.g., by story progression)
        this.blockedItems = [];
        
        // Debug mode
        this.debugMode = false;
    }
    
    /**
     * Set the current soul weight
     * @param {string} soulWeight - The soul weight ('light', 'dark', or 'balanced')
     */
    setSoulWeight(soulWeight) {
        if (['light', 'dark', 'balanced'].includes(soulWeight)) {
            this.currentSoulWeight = soulWeight;
        } else {
            console.warn(`Invalid soul weight: ${soulWeight}`);
        }
    }
    
    /**
     * Add an active relic
     * @param {string} relicType - The type of relic
     */
    addActiveRelic(relicType) {
        if (!this.activeRelics.includes(relicType)) {
            this.activeRelics.push(relicType);
        }
    }
    
    /**
     * Remove an active relic
     * @param {string} relicType - The type of relic
     */
    removeActiveRelic(relicType) {
        const index = this.activeRelics.indexOf(relicType);
        if (index !== -1) {
            this.activeRelics.splice(index, 1);
        }
    }
    
    /**
     * Block an item from dropping
     * @param {string} itemType - The type of item to block
     */
    blockItem(itemType) {
        if (!this.blockedItems.includes(itemType)) {
            this.blockedItems.push(itemType);
        }
    }
    
    /**
     * Unblock an item
     * @param {string} itemType - The type of item to unblock
     */
    unblockItem(itemType) {
        const index = this.blockedItems.indexOf(itemType);
        if (index !== -1) {
            this.blockedItems.splice(index, 1);
        }
    }
    
    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode should be enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }
    
    /**
     * Get a random item drop based on the context
     * @param {string} enemyType - The type of enemy that dropped the item
     * @param {string} roomType - The type of room
     * @param {string} biome - The current biome
     * @param {number} seed - Random seed for deterministic drops
     * @returns {string|null} The selected item type, or null if no drop
     */
    getRandomDrop(enemyType, roomType, biome, seed = Date.now()) {
        // Create seeded random function
        const random = mulberry32(seed);
        
        // Determine if there should be a drop at all
        const dropChance = this._getDropChance(enemyType, roomType);
        if (random() > dropChance) {
            return null; // No drop
        }
        
        // Get rarity weights based on context
        const rarityWeights = getRarityWeights(enemyType, roomType);
        
        // Select a random rarity
        const rarity = getRandomRarity(rarityWeights, random);
        
        // Build the item pool based on context
        const itemPool = this._getFilteredItemPool(biome, roomType, enemyType, rarity);
        
        if (itemPool.length === 0) {
            console.warn(`No items available for drop in ${biome} (${roomType}, ${enemyType}, ${rarity})`);
            return null;
        }
        
        // Calculate item weights based on soul weight and active relics
        const itemWeights = this._calculateItemWeights(itemPool);
        
        // Select a random item from the weighted pool
        const selectedItem = getRandomItemFromWeightedPool(itemPool, itemWeights, random);
        
        if (this.debugMode) {
            console.log(`Selected item: ${selectedItem} (${rarity})`);
            console.log(`Item pool size: ${itemPool.length}`);
            console.log(`Soul weight: ${this.currentSoulWeight}`);
            console.log(`Active relics: ${this.activeRelics.join(', ') || 'None'}`);
        }
        
        return selectedItem;
    }
    
    /**
     * Spawn a voxel item in the world
     * @param {object} scene - The THREE.js scene
     * @param {string} itemType - The type of item to spawn
     * @param {THREE.Vector3} worldPosition - The position to spawn the item
     * @returns {Item|null} The spawned item, or null if failed
     */
    spawnVoxelItem(scene, itemType, worldPosition) {
        if (!scene || !itemType || !worldPosition) {
            console.error('Missing required parameters for spawnVoxelItem');
            return null;
        }
        
        // Create the item
        const item = new Item(scene, itemType, worldPosition);
        
        if (this.debugMode) {
            console.log(`Spawned item: ${itemType} at ${worldPosition.x.toFixed(2)}, ${worldPosition.y.toFixed(2)}, ${worldPosition.z.toFixed(2)}`);
        }
        
        return item;
    }
    
    /**
     * Collect an item and add it to the player's inventory
     * @param {Item} item - The item to collect
     * @param {object} player - The player object
     * @returns {object|null} The collected item data, or null if failed
     */
    collectItem(item, player) {
        if (!item || !player) {
            console.error('Missing required parameters for collectItem');
            return null;
        }
        
        // Collect the item
        const itemData = item.collect(player);
        
        if (this.debugMode && itemData) {
            console.log(`Collected item: ${itemData.name}`);
        }
        
        return itemData;
    }
    
    /**
     * Load the item pool for a specific biome
     * @param {string} biome - The biome to load
     * @returns {Array} The item pool for the biome
     */
    loadBiomeItemPool(biome) {
        // Check if we already have this biome's pool cached
        if (this.biomePoolCache[biome]) {
            return this.biomePoolCache[biome];
        }
        
        // Build the pool
        const pool = buildItemPool(biome, 'any', 'any');
        
        // Cache it
        this.biomePoolCache[biome] = pool;
        
        return pool;
    }
    
    /**
     * Get a preview of the drop table for debugging/balancing
     * @param {object} context - The context to preview
     * @returns {object} The drop table preview
     */
    getDropTablePreview(context) {
        const { enemyType, roomType, biome } = context;
        
        // Get rarity weights
        const rarityWeights = getRarityWeights(enemyType, roomType);
        
        // Build preview for each rarity
        const preview = {};
        
        for (const rarity in rarityWeights) {
            const itemPool = this._getFilteredItemPool(biome, roomType, enemyType, rarity);
            const itemWeights = this._calculateItemWeights(itemPool);
            
            preview[rarity] = {
                weight: rarityWeights[rarity],
                items: itemPool.map(itemType => {
                    const itemData = getItemData(itemType);
                    return {
                        type: itemType,
                        name: itemData.name,
                        weight: itemWeights[itemType] || 1
                    };
                })
            };
        }
        
        return preview;
    }
    
    /**
     * Get the chance of an enemy dropping an item
     * @param {string} enemyType - The type of enemy
     * @param {string} roomType - The type of room
     * @returns {number} The drop chance (0-1)
     * @private
     */
    _getDropChance(enemyType, roomType) {
        // Base drop chances
        const baseChances = {
            normal: 0.2,    // 20% for normal enemies
            elite: 0.5,     // 50% for elite enemies
            mini_boss: 0.8, // 80% for mini-bosses
            boss: 1.0,      // 100% for bosses
            destructible: 0.05 // 5% for destructible objects
        };
        
        // Room type modifiers
        const roomModifiers = {
            normal: 1.0,
            elite: 1.2,
            mini_boss: 1.2,
            boss: 1.5,
            secret: 1.5,
            soul_door: 1.3,
            trap: 0.8,
            shop: 0.0 // No drops in shops
        };
        
        // Get base chance based on enemy type
        let chance = baseChances[enemyType] || baseChances.normal;
        
        // Apply room modifier
        chance *= roomModifiers[roomType] || 1.0;
        
        // Clamp to 0-1 range
        return Math.min(1.0, Math.max(0.0, chance));
    }
    
    /**
     * Get a filtered item pool based on context
     * @param {string} biome - The current biome
     * @param {string} roomType - The type of room
     * @param {string} enemyType - The type of enemy
     * @param {string} rarity - The selected rarity
     * @returns {Array} The filtered item pool
     * @private
     */
    _getFilteredItemPool(biome, roomType, enemyType, rarity) {
        // Start with all items of the selected rarity
        let pool = getItemsByRarity(rarity);
        
        // Build the context-specific pool
        const contextPool = buildItemPool(biome, roomType, enemyType);
        
        // Filter by rarity and context
        pool = pool.filter(itemType => {
            // Check if item is in the context pool
            if (!contextPool.includes(itemType)) {
                return false;
            }
            
            // Check if item is blocked
            if (this.blockedItems.includes(itemType)) {
                return false;
            }
            
            // Get item data
            const itemData = getItemData(itemType);
            if (!itemData) {
                return false;
            }
            
            // Check if item is blocked by story progression
            if (itemData.blockedBy && itemData.blockedBy.some(block => this.blockedItems.includes(block))) {
                return false;
            }
            
            return true;
        });
        
        return pool;
    }
    
    /**
     * Calculate weights for items based on soul weight and active relics
     * @param {Array} itemPool - The pool of items
     * @returns {object} Object mapping item types to weights
     * @private
     */
    _calculateItemWeights(itemPool) {
        const weights = {};
        
        for (const itemType of itemPool) {
            const itemData = getItemData(itemType);
            if (!itemData) continue;
            
            // Start with base weight of 1
            let weight = 1.0;
            
            // Apply soul weight modifier
            if (itemData.soulWeightInfluence && SOUL_WEIGHT_MODIFIERS[this.currentSoulWeight]) {
                const modifier = SOUL_WEIGHT_MODIFIERS[this.currentSoulWeight][itemData.soulWeightInfluence] || 1.0;
                weight *= modifier;
            }
            
            // Apply relic modifiers
            for (const relicType of this.activeRelics) {
                const relicMod = RELIC_MODIFIERS[relicType];
                if (relicMod && relicMod.modifiedItems.includes(itemType)) {
                    weight *= relicMod.modifier;
                }
            }
            
            weights[itemType] = weight;
        }
        
        return weights;
    }
}

// Create and export a singleton instance
const itemDropManager = new ItemDropManager();
export default itemDropManager;
