/**
 * Manages the environment type system, including floor and wall type selection.
 * 
 * CURRENT IMPLEMENTATION NOTE:
 * This system is currently configured for the catacombs area, with support for:
 * - Floor Types: cave_floor
 * - Wall Types: stonebrick
 * - Door Types: stone_archway
 * 
 * Other environment types and biomes are defined but not actively used until they are fully implemented.
 */

import { mulberry32 } from '../generators/prefabs/shared.js';

// --- Environment Type Definitions ---
const ENVIRONMENT_TYPES = {
    FLOOR: 'floor',
    WALL: 'wall',
    DOOR: 'door',
    CEILING: 'ceiling'
};

// --- Biome-Specific Environment Type Pools ---
// These define which environment types can be used in each biome
const BIOME_ENVIRONMENT_POOLS = {
    catacombs: {
        [ENVIRONMENT_TYPES.FLOOR]: [
            { type: 'cave_floor', weight: 10 }
        ],
        [ENVIRONMENT_TYPES.WALL]: [
            { type: 'stonebrick', weight: 10 }
        ],
        [ENVIRONMENT_TYPES.DOOR]: [
            { type: 'stone_archway', weight: 10 }
        ]
    },
    
    fungal_caverns: {
        [ENVIRONMENT_TYPES.FLOOR]: [
            { type: 'mossy_earth', weight: 7 },
            { type: 'mushroom_patch', weight: 3 }
        ],
        [ENVIRONMENT_TYPES.WALL]: [
            { type: 'rough_stone', weight: 7 },
            { type: 'glowing_mushroom_wall', weight: 3 }
        ],
        [ENVIRONMENT_TYPES.DOOR]: [
            { type: 'mossy_archway', weight: 7 },
            { type: 'stone_archway', weight: 3 }
        ]
    },
    
    flooded_ruins: {
        [ENVIRONMENT_TYPES.FLOOR]: [
            { type: 'shallow_water', weight: 6 },
            { type: 'stone_debris', weight: 4 }
        ],
        [ENVIRONMENT_TYPES.WALL]: [
            { type: 'cracked_stone', weight: 6 },
            { type: 'waterlogged_brick', weight: 4 }
        ],
        [ENVIRONMENT_TYPES.DOOR]: [
            { type: 'stone_archway', weight: 6 },
            { type: 'cracked_archway', weight: 4 }
        ]
    },
    
    crystal_caves: {
        [ENVIRONMENT_TYPES.FLOOR]: [
            { type: 'crystal_floor', weight: 6 },
            { type: 'geode_floor', weight: 4 }
        ],
        [ENVIRONMENT_TYPES.WALL]: [
            { type: 'crystal_wall', weight: 6 },
            { type: 'rough_stone', weight: 4 }
        ],
        [ENVIRONMENT_TYPES.DOOR]: [
            { type: 'crystal_archway', weight: 10 }
        ]
    },
    
    ancient_library: {
        [ENVIRONMENT_TYPES.FLOOR]: [
            { type: 'library_floor', weight: 10 }
        ],
        [ENVIRONMENT_TYPES.WALL]: [
            { type: 'library_wall', weight: 10 }
        ],
        [ENVIRONMENT_TYPES.DOOR]: [
            { type: 'library_archway', weight: 10 }
        ]
    },
    
    lava_tubes: {
        [ENVIRONMENT_TYPES.FLOOR]: [
            { type: 'obsidian_floor', weight: 7 },
            { type: 'lava_river_edge', weight: 3 }
        ],
        [ENVIRONMENT_TYPES.WALL]: [
            { type: 'basalt_wall', weight: 7 },
            { type: 'cooling_lava', weight: 3 }
        ],
        [ENVIRONMENT_TYPES.DOOR]: [
            { type: 'obsidian_archway', weight: 10 }
        ]
    },
    
    obsidian_fortress: {
        [ENVIRONMENT_TYPES.FLOOR]: [
            { type: 'polished_obsidian', weight: 10 }
        ],
        [ENVIRONMENT_TYPES.WALL]: [
            { type: 'obsidian_wall', weight: 6 },
            { type: 'fortress_brick', weight: 4 }
        ],
        [ENVIRONMENT_TYPES.DOOR]: [
            { type: 'obsidian_archway', weight: 6 },
            { type: 'fortress_gate', weight: 4 }
        ]
    },
    
    astral_plane: {
        [ENVIRONMENT_TYPES.FLOOR]: [
            { type: 'crystal_platform', weight: 10 }
        ],
        [ENVIRONMENT_TYPES.WALL]: [
            { type: 'void_wall', weight: 6 },
            { type: 'starfield_backdrop', weight: 4 }
        ],
        [ENVIRONMENT_TYPES.DOOR]: [
            { type: 'void_portal', weight: 10 }
        ]
    },
    
    final_boss_arena: {
        [ENVIRONMENT_TYPES.FLOOR]: [
            { type: 'boss_arena_floor', weight: 10 }
        ],
        [ENVIRONMENT_TYPES.WALL]: [
            { type: 'boss_arena_wall', weight: 10 }
        ],
        [ENVIRONMENT_TYPES.DOOR]: [
            { type: 'boss_arena_gate', weight: 10 }
        ]
    }
};

// --- Room Type Variation Modifiers ---
// These define how room types affect environment type selection
const ROOM_TYPE_MODIFIERS = {
    start: {
        // Start rooms have special environment types
        floorModifier: (types) => {
            // Prioritize special floor types for start rooms
            return types.map(type => {
                if (type.type.includes('ritual') || type.type.includes('special')) {
                    return { ...type, weight: type.weight * 3 };
                }
                return type;
            });
        },
        wallModifier: (types) => {
            // No special modification for walls in start rooms
            return types;
        },
        doorModifier: (types) => {
            // No special modification for doors in start rooms
            return types;
        }
    },
    
    boss: {
        // Boss rooms have more impressive environment types
        floorModifier: (types) => {
            // Prioritize impressive floor types for boss rooms
            return types.map(type => {
                if (type.type.includes('grand') || type.type.includes('impressive')) {
                    return { ...type, weight: type.weight * 2 };
                }
                return type;
            });
        },
        wallModifier: (types) => {
            // Prioritize impressive wall types for boss rooms
            return types.map(type => {
                if (type.type.includes('grand') || type.type.includes('impressive')) {
                    return { ...type, weight: type.weight * 2 };
                }
                return type;
            });
        },
        doorModifier: (types) => {
            // Prioritize impressive door types for boss rooms
            return types.map(type => {
                if (type.type.includes('grand') || type.type.includes('impressive')) {
                    return { ...type, weight: type.weight * 2 };
                }
                return type;
            });
        }
    },
    
    elite: {
        // Elite rooms have slightly more impressive environment types
        floorModifier: (types) => {
            // Prioritize impressive floor types for elite rooms
            return types.map(type => {
                if (type.type.includes('grand') || type.type.includes('impressive')) {
                    return { ...type, weight: type.weight * 1.5 };
                }
                return type;
            });
        },
        wallModifier: (types) => {
            // Prioritize impressive wall types for elite rooms
            return types.map(type => {
                if (type.type.includes('grand') || type.type.includes('impressive')) {
                    return { ...type, weight: type.weight * 1.5 };
                }
                return type;
            });
        },
        doorModifier: (types) => {
            // Prioritize impressive door types for elite rooms
            return types.map(type => {
                if (type.type.includes('grand') || type.type.includes('impressive')) {
                    return { ...type, weight: type.weight * 1.5 };
                }
                return type;
            });
        }
    }
};

class EnvironmentTypeManager {
    constructor() {
        // Cache for biome environment pools
        this.biomePoolCache = {};
        
        // Debug mode
        this.debugMode = false;
    }
    
    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode should be enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }
    
    /**
     * Get the environment pool for a specific biome
     * @param {string} biome - The biome to get the environment pool for
     * @returns {object} The environment pool for the biome
     */
    getEnvironmentPool(biome) {
        // Check if we already have this biome's pool cached
        if (this.biomePoolCache[biome]) {
            return this.biomePoolCache[biome];
        }
        
        // Get the base environment pool for this biome
        const biomePool = BIOME_ENVIRONMENT_POOLS[biome] || BIOME_ENVIRONMENT_POOLS.catacombs;
        
        // Cache it
        this.biomePoolCache[biome] = biomePool;
        
        return biomePool;
    }
    
    /**
     * Select environment types for a room
     * @param {object} context - The context for environment type selection
     * @param {string} context.biome - The current biome
     * @param {string} context.roomType - The type of room
     * @param {number} context.seed - Random seed for deterministic selection
     * @returns {object} The selected environment types
     */
    selectEnvironmentTypes(context) {
        const { biome, roomType, seed = Date.now() } = context;
        
        // Create seeded random function
        const random = mulberry32(seed);
        
        // Get the environment pool for this biome
        const environmentPool = this.getEnvironmentPool(biome);
        
        // Get room type modifiers
        const roomModifiers = ROOM_TYPE_MODIFIERS[roomType.toLowerCase()] || {};
        
        // Select floor type
        const floorTypes = environmentPool[ENVIRONMENT_TYPES.FLOOR] || [];
        const modifiedFloorTypes = roomModifiers.floorModifier ? roomModifiers.floorModifier(floorTypes) : floorTypes;
        const floorType = this._selectWeightedType(modifiedFloorTypes, random);
        
        // Select wall type
        const wallTypes = environmentPool[ENVIRONMENT_TYPES.WALL] || [];
        const modifiedWallTypes = roomModifiers.wallModifier ? roomModifiers.wallModifier(wallTypes) : wallTypes;
        const wallType = this._selectWeightedType(modifiedWallTypes, random);
        
        // Select door type
        const doorTypes = environmentPool[ENVIRONMENT_TYPES.DOOR] || [];
        const modifiedDoorTypes = roomModifiers.doorModifier ? roomModifiers.doorModifier(doorTypes) : doorTypes;
        const doorType = this._selectWeightedType(modifiedDoorTypes, random);
        
        if (this.debugMode) {
            console.log(`Selected environment types for ${biome} (${roomType}):`);
            console.log(`Floor: ${floorType}, Wall: ${wallType}, Door: ${doorType}`);
        }
        
        return {
            floorType,
            wallType,
            doorType
        };
    }
    
    /**
     * Select a type from a weighted list
     * @param {Array} types - Array of types with weights
     * @param {function} random - Random number generator function
     * @returns {string} The selected type
     * @private
     */
    _selectWeightedType(types, random = Math.random) {
        if (!types || types.length === 0) {
            return null;
        }
        
        // Calculate total weight
        const totalWeight = types.reduce((sum, type) => sum + type.weight, 0);
        
        // Select random type based on weight
        let randomWeight = random() * totalWeight;
        for (const type of types) {
            randomWeight -= type.weight;
            if (randomWeight <= 0) {
                return type.type;
            }
        }
        
        // Fallback (should never reach here if weights are positive)
        return types[0].type;
    }
}

// Create and export a singleton instance
const environmentTypeManager = new EnvironmentTypeManager();
export default environmentTypeManager;
