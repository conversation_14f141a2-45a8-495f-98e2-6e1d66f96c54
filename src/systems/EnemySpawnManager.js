/**
 * Manages the enemy spawn system, including enemy group composition and difficulty scaling.
 *
 * CURRENT IMPLEMENTATION NOTE:
 * This system is configured to spawn all defined enemy types in the catacombs area,
 * including Skeleton Archers, Bat<PERSON>, Zombies, and Magma Golems.
 *
 * Skeleton Archer:
 * - Role: RANGED
 * - AI Type: RANGED
 * - Health: 30
 * - Speed: 1.5 (±0.2 variation)
 * - Damage: 5 (projectile)
 * - Attack Cooldown: 2.0 seconds
 * - Preferred Range: 8.0 units
 *
 * Bat:
 * - Role: SWARM
 * - AI Type: FLYING
 * - Health: 20
 * - Speed: 2.2 (±0.3 variation)
 * - Damage: 4 (melee)
 * - Attack Cooldown: 2.0 seconds
 * - Hover Height: 2.0-4.0 units
 *
 * Zombie:
 * - Role: MELEE
 * - AI Type: MELEE
 * - Health: 60
 * - Speed: 1.8 (±0.3 variation)
 * - Damage: 1 (melee)
 * - Attack Cooldown: 1.5 seconds
 *
 * Magma Golem:
 * - Role: HEAVY
 * - AI Type: MELEE
 * - Health: 80
 * - Speed: 1.5 (±0.2 variation)
 * - Damage: 1 (melee)
 * - Attack Cooldown: 2.0 seconds
 */

import { getEnemyData } from '../entities/EnemyTypes.js';
import { mulberry32 } from '../generators/prefabs/shared.js';

// --- Enemy Role Definitions ---
const ENEMY_ROLES = {
    SWARM: 'swarm',       // Weak but numerous
    HEAVY: 'heavy',       // Strong, slow enemies
    RANGED: 'ranged',     // Attacks from a distance
    MELEE: 'melee',       // Close combat
    ELITE: 'elite',       // Stronger versions of normal enemies
    MINI_BOSS: 'mini_boss', // Unique, powerful enemies
    BOSS: 'boss'          // Main boss enemies
};

// --- Enemy Role Assignments ---
const ENEMY_ROLE_MAP = {
    skeleton_archer: ENEMY_ROLES.RANGED,
    bat: ENEMY_ROLES.SWARM,
    zombie: ENEMY_ROLES.MELEE,
    magma_golem: ENEMY_ROLES.HEAVY
};

// --- Enemy Group Templates ---
const ENEMY_GROUP_TEMPLATES = {
    // Small groups (1-3 enemies)
    small: [
        { roles: [ENEMY_ROLES.RANGED], count: 1 },                      // Single skeleton archer
        { roles: [ENEMY_ROLES.SWARM, ENEMY_ROLES.SWARM], count: 2 },    // Two bats
        { roles: [ENEMY_ROLES.MELEE], count: 1 },                       // Single zombie
        { roles: [ENEMY_ROLES.HEAVY], count: 1 },                       // Single magma golem
        { roles: [ENEMY_ROLES.RANGED, ENEMY_ROLES.SWARM], count: 2 },    // One skeleton archer + one bat
        { roles: [ENEMY_ROLES.HEAVY, ENEMY_ROLES.SWARM], count: 2 }      // One magma golem + one bat
    ],

    // Medium groups (3-5 enemies)
    medium: [
        { roles: [ENEMY_ROLES.HEAVY, ENEMY_ROLES.RANGED], count: 2 },                                  // One magma golem + one skeleton archer
        { roles: [ENEMY_ROLES.HEAVY, ENEMY_ROLES.SWARM, ENEMY_ROLES.SWARM], count: 3 },                  // One magma golem + two bats
        { roles: [ENEMY_ROLES.RANGED, ENEMY_ROLES.RANGED, ENEMY_ROLES.RANGED], count: 3 },                // Three skeleton archers
        { roles: [ENEMY_ROLES.RANGED, ENEMY_ROLES.SWARM, ENEMY_ROLES.SWARM], count: 3 },                  // One skeleton archer + two bats
        { roles: [ENEMY_ROLES.MELEE, ENEMY_ROLES.MELEE, ENEMY_ROLES.SWARM], count: 3 },                  // Two zombies + one bat
        { roles: [ENEMY_ROLES.RANGED, ENEMY_ROLES.MELEE, ENEMY_ROLES.SWARM], count: 3 }                 // One skeleton archer + one zombie + one bat
    ],

    // Large groups (5-8 enemies)
    large: [
        { roles: [ENEMY_ROLES.RANGED, ENEMY_ROLES.RANGED, ENEMY_ROLES.RANGED, ENEMY_ROLES.SWARM, ENEMY_ROLES.SWARM], count: 5 }, // Three archers + two bats
        { roles: [ENEMY_ROLES.RANGED, ENEMY_ROLES.MELEE, ENEMY_ROLES.MELEE, ENEMY_ROLES.SWARM, ENEMY_ROLES.SWARM], count: 5 }, // One archer + two zombies + two bats
        { roles: [ENEMY_ROLES.RANGED, ENEMY_ROLES.RANGED, ENEMY_ROLES.SWARM, ENEMY_ROLES.SWARM, ENEMY_ROLES.SWARM, ENEMY_ROLES.SWARM], count: 6 } // Two archers + four bats
    ],

    // Elite groups
    elite: [
        { roles: [ENEMY_ROLES.HEAVY, ENEMY_ROLES.RANGED, ENEMY_ROLES.SWARM], count: 3 },                    // One magma golem + one archer + one bat
        { roles: [ENEMY_ROLES.HEAVY, ENEMY_ROLES.HEAVY], count: 2 }                                         // Two magma golems
    ],

    // Mini-boss groups
    mini_boss: [
        { roles: [ENEMY_ROLES.HEAVY], count: 1 },                                                      // One magma golem alone
        { roles: [ENEMY_ROLES.HEAVY, ENEMY_ROLES.SWARM, ENEMY_ROLES.SWARM], count: 3 }                   // One magma golem + two bats
    ]
};

// --- Room Type Difficulty Modifiers ---
const ROOM_TYPE_DIFFICULTY = {
    normal: 1.0,
    elite: 1.5,
    trap: 1.2,
    secret: 1.3,
    mini_boss: 2.0,
    boss: 3.0
};

// --- Biome Enemy Pools ---
const BIOME_ENEMY_POOLS = {
    catacombs: {
        [ENEMY_ROLES.MELEE]: ['zombie'],
        [ENEMY_ROLES.RANGED]: ['skeleton_archer'],
        [ENEMY_ROLES.HEAVY]: ['magma_golem'],
        [ENEMY_ROLES.SWARM]: ['bat'],
        [ENEMY_ROLES.ELITE]: ['magma_golem'],
        [ENEMY_ROLES.MINI_BOSS]: ['magma_golem'],
        [ENEMY_ROLES.BOSS]: ['catacombs_overlord']
    }
};

class EnemySpawnManager {
    constructor() {
        // Cache for biome enemy pools
        this.biomePoolCache = {};

        // Current floor level (1-based)
        this.currentFloorLevel = 1;

        // Maximum floor level
        this.maxFloorLevel = 10;

        // Debug mode
        this.debugMode = false;
    }

    /**
     * Set the current floor level
     * @param {number} level - The floor level (1-based)
     */
    setFloorLevel(level) {
        this.currentFloorLevel = Math.max(1, Math.min(this.maxFloorLevel, level));
    }

    /**
     * Set the maximum floor level
     * @param {number} level - The maximum floor level
     */
    setMaxFloorLevel(level) {
        this.maxFloorLevel = Math.max(1, level);
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode should be enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }

    /**
     * Get the enemy spawn table for a biome and floor level
     * @param {string} biome - The current biome
     * @param {number} floorLevel - The current floor level (optional)
     * @returns {object} The enemy spawn table
     */
    getEnemySpawnTable(biome, floorLevel = this.currentFloorLevel) {
        // Get the base enemy pool for this biome
        return BIOME_ENEMY_POOLS[biome] || BIOME_ENEMY_POOLS.catacombs;
    }

    /**
     * Choose a balanced enemy group for a room
     * @param {object} context - The context for enemy spawning
     * @param {string} context.biome - The current biome
     * @param {string} context.roomType - The type of room
     * @param {number} context.floorLevel - The current floor level (optional)
     * @param {number} context.seed - Random seed for deterministic spawning (optional)
     * @returns {Array} Array of enemy types to spawn
     */
    chooseEnemyGroup(context) {
        const { biome, roomType, floorLevel = this.currentFloorLevel, seed = Date.now() } = context;

        // Create seeded random function
        const random = mulberry32(seed);

        // Get the enemy spawn table for this biome and floor level
        const spawnTable = this.getEnemySpawnTable(biome, floorLevel);

        // Determine group size based on room type and floor level
        let groupSize;
        switch (roomType) {
            case 'elite':
                groupSize = 'elite';
                break;
            case 'mini_boss':
                groupSize = 'mini_boss';
                break;
            case 'boss':
                groupSize = 'boss';
                break;
            default:
                // Choose based on floor level
                if (floorLevel <= 3) {
                    groupSize = 'small';
                } else if (floorLevel <= 7) {
                    groupSize = 'medium';
                } else {
                    groupSize = 'large';
                }

                // Random chance to upgrade group size
                const upgradeChance = 0.2 + (floorLevel / this.maxFloorLevel) * 0.3; // 20-50% chance
                if (random() < upgradeChance) {
                    if (groupSize === 'small') {
                        groupSize = 'medium';
                    } else if (groupSize === 'medium') {
                        groupSize = 'large';
                    } else if (groupSize === 'large' && floorLevel >= 5) {
                        groupSize = 'elite';
                    }
                }
        }

        // Get the group templates for this size
        const templates = ENEMY_GROUP_TEMPLATES[groupSize] || ENEMY_GROUP_TEMPLATES.small;

        // Choose a random template
        const templateIndex = Math.floor(random() * templates.length);
        const template = templates[templateIndex];

        // Fill the template with actual enemy types
        const enemies = [];
        for (let i = 0; i < template.roles.length; i++) {
            const role = template.roles[i];
            const enemyPool = spawnTable[role] || [];

            if (enemyPool.length === 0) {
                // If no enemies available for this role, try to substitute
                const fallbackRole = this._getFallbackRole(role);
                const fallbackPool = spawnTable[fallbackRole] || [];

                if (fallbackPool.length === 0) {
                    // If still no enemies, skip this role
                    continue;
                }

                // Choose a random enemy from the fallback pool
                const enemyIndex = Math.floor(random() * fallbackPool.length);
                enemies.push(fallbackPool[enemyIndex]);
            } else {
                // Choose a random enemy from the pool
                const enemyIndex = Math.floor(random() * enemyPool.length);
                enemies.push(enemyPool[enemyIndex]);
            }
        }

        if (this.debugMode) {
            console.log(`Chose enemy group for ${biome} (${roomType}, floor ${floorLevel}):`);
            console.log(`Group size: ${groupSize}`);
            console.log(`Enemies: ${enemies.join(', ')}`);
        }

        return enemies;
    }

    /**
     * Get a fallback role when the primary role has no available enemies
     * @param {string} role - The original role
     * @returns {string} The fallback role
     * @private
     */
    _getFallbackRole(role) {
        switch (role) {
            case ENEMY_ROLES.HEAVY:
                return ENEMY_ROLES.MELEE;
            case ENEMY_ROLES.ELITE:
                return ENEMY_ROLES.HEAVY;
            case ENEMY_ROLES.MINI_BOSS:
                return ENEMY_ROLES.ELITE;
            case ENEMY_ROLES.BOSS:
                return ENEMY_ROLES.MINI_BOSS;
            default:
                return ENEMY_ROLES.SWARM;
        }
    }

    /**
     * Spawn an enemy in the world
     * @param {object} dungeonHandler - The dungeon handler
     * @param {string} enemyType - The type of enemy to spawn
     * @param {THREE.Vector3} worldPosition - The position to spawn the enemy (optional)
     * @returns {object|null} The spawned enemy, or null if failed
     */
    spawnEnemy(dungeonHandler, enemyType, worldPosition = null) {
        if (!dungeonHandler || !enemyType) {
            console.error('Missing required parameters for spawnEnemy');
            return null;
        }

        // Use the dungeon handler's spawn method
        const enemy = dungeonHandler._spawnEnemy(enemyType, worldPosition);

        if (this.debugMode && enemy) {
            console.log(`Spawned enemy: ${enemyType}`);
        }

        return enemy;
    }
}

// Create and export a singleton instance
const enemySpawnManager = new EnemySpawnManager();
export default enemySpawnManager;
