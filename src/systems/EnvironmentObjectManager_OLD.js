/**
 * Manages the environment object system, including floor and wall object generation and placement.
 *
 * CURRENT IMPLEMENTATION NOTE:
 * This system is currently configured for the catacombs area, with support for:
 * - Floor Objects: stone_vase, stone_pillar, stone_rubble
 * - Wall Objects: vine, torch
 *
 * Other object types and biomes are defined but not actively used until they are fully implemented.
 */

import { mulberry32 } from '../generators/prefabs/shared.js';
import { getPrefabFunction } from '../prefabs/prefabs.js';

// --- Object Role Definitions ---
const OBJECT_ROLES = {
    DECORATION: 'decoration',     // Visual elements with no gameplay impact
    LIGHT: 'light',               // Objects that provide light
    DESTRUCTIBLE: 'destructible', // Objects that can be destroyed
    INTERACTIVE: 'interactive',   // Objects that can be interacted with
    HAZARD: 'hazard',             // Objects that can harm the player
    SPECIAL: 'special'            // Special objects with unique behaviors
};

// --- Object Placement Types ---
const PLACEMENT_TYPES = {
    WALL: 'wall',       // Placed on walls
    FLOOR: 'floor',     // Placed on floors
    CEILING: 'ceiling', // Placed on ceilings
    CORNER: 'corner',   // Placed in corners
    CENTER: 'center'    // Placed in the center of the room
};

// --- Object Role Assignments ---
const OBJECT_ROLE_MAP = {
    // Wall objects
    vine: OBJECT_ROLES.DECORATION,
    torch: OBJECT_ROLES.LIGHT,

    // Floor objects
    stone_vase: OBJECT_ROLES.DESTRUCTIBLE,
    stone_pillar: OBJECT_ROLES.DESTRUCTIBLE,
    stone_rubble: OBJECT_ROLES.DESTRUCTIBLE,
    ritual_circle: OBJECT_ROLES.SPECIAL,
    aether_torch: OBJECT_ROLES.LIGHT
};

// --- Object Group Templates ---
// These define balanced object group compositions for different room types
const OBJECT_GROUP_TEMPLATES = {
    // Normal rooms
    normal: [
        // Light-focused room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 4 },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 2 },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 3 }
            ],
            probability: 0.3
        },
        // Decoration-focused room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 2 },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 4 },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 4 }
            ],
            probability: 0.3
        },
        // Destructible-focused room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 3 },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 2 },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 6 }
            ],
            probability: 0.3
        },
        // Minimal room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 2 },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 2 }
            ],
            probability: 0.1
        }
    ],

    // Elite rooms
    elite: [
        // Symmetrical elite room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 4 },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 4 },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 4, placementDetail: 'corners' }
            ],
            probability: 0.5
        },
        // Hazardous elite room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 6 },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 8 }
            ],
            probability: 0.5
        }
    ],

    // Boss rooms
    boss: [
        // Grand boss room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 8 },
                { type: OBJECT_ROLES.DECORATION, placement: PLACEMENT_TYPES.WALL, count: 4 },
                { type: OBJECT_ROLES.DESTRUCTIBLE, placement: PLACEMENT_TYPES.FLOOR, count: 4, placementDetail: 'corners' }
            ],
            probability: 1.0
        }
    ],

    // Start room
    start: [
        // Ritual circle room
        {
            roles: [
                { type: OBJECT_ROLES.LIGHT, placement: PLACEMENT_TYPES.WALL, count: 4 },
                { type: OBJECT_ROLES.SPECIAL, placement: PLACEMENT_TYPES.FLOOR, count: 1, placementDetail: 'center' }
            ],
            probability: 1.0
        }
    ]
};

// --- Biome-Specific Object Pools ---
// These define which objects can be placed in each biome
const BIOME_OBJECT_POOLS = {
    catacombs: {
        [OBJECT_ROLES.DECORATION]: {
            [PLACEMENT_TYPES.WALL]: ['vine'],
            [PLACEMENT_TYPES.FLOOR]: []
        },
        [OBJECT_ROLES.LIGHT]: {
            [PLACEMENT_TYPES.WALL]: ['torch'],
            [PLACEMENT_TYPES.FLOOR]: ['aether_torch']
        },
        [OBJECT_ROLES.DESTRUCTIBLE]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: ['stone_vase', 'stone_pillar', 'stone_rubble']
        },
        [OBJECT_ROLES.INTERACTIVE]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: []
        },
        [OBJECT_ROLES.HAZARD]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: []
        },
        [OBJECT_ROLES.SPECIAL]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: ['ritual_circle']
        }
    },

    // Other biomes would be defined here
    fungal_caverns: {
        [OBJECT_ROLES.DECORATION]: {
            [PLACEMENT_TYPES.WALL]: ['fungal_growth'],
            [PLACEMENT_TYPES.FLOOR]: ['small_mushroom']
        },
        [OBJECT_ROLES.LIGHT]: {
            [PLACEMENT_TYPES.WALL]: ['glowing_fungus'],
            [PLACEMENT_TYPES.FLOOR]: ['luminous_mushroom']
        },
        [OBJECT_ROLES.DESTRUCTIBLE]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: ['giant_mushroom', 'fungal_pod']
        },
        [OBJECT_ROLES.INTERACTIVE]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: ['spore_vent']
        },
        [OBJECT_ROLES.HAZARD]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: ['toxic_puddle']
        },
        [OBJECT_ROLES.SPECIAL]: {
            [PLACEMENT_TYPES.WALL]: [],
            [PLACEMENT_TYPES.FLOOR]: []
        }
    }
};

// --- Object Placement Details ---
// These define specific placement strategies for objects
const PLACEMENT_DETAILS = {
    random: (room, objectSize) => {
        // Return a random position within the room
        const x = (Math.random() - 0.5) * (room.width - objectSize * 2);
        const z = (Math.random() - 0.5) * (room.depth - objectSize * 2);
        return { x, y: 0, z };
    },
    corners: (room, objectSize, index) => {
        // Place objects in the corners of the room
        const cornerX = (index % 2 === 0) ? -1 : 1;
        const cornerZ = (index < 2) ? -1 : 1;
        const offset = objectSize * 1.5;
        const x = cornerX * (room.width / 2 - offset);
        const z = cornerZ * (room.depth / 2 - offset);
        return { x, y: 0, z };
    },
    center: (room) => {
        // Place object in the center of the room
        return { x: 0, y: 0, z: 0 };
    },
    walls: (room, objectSize, index, totalCount) => {
        // Distribute objects evenly along walls
        const wallIndex = index % 4; // 0: north, 1: east, 2: south, 3: west
        const positionAlongWall = (index / totalCount) * 2 - 1; // -1 to 1

        let x = 0, z = 0;
        switch (wallIndex) {
            case 0: // North wall
                x = positionAlongWall * (room.width / 2 - objectSize);
                z = -room.depth / 2 + objectSize / 2;
                break;
            case 1: // East wall
                x = room.width / 2 - objectSize / 2;
                z = positionAlongWall * (room.depth / 2 - objectSize);
                break;
            case 2: // South wall
                x = positionAlongWall * (room.width / 2 - objectSize);
                z = room.depth / 2 - objectSize / 2;
                break;
            case 3: // West wall
                x = -room.width / 2 + objectSize / 2;
                z = positionAlongWall * (room.depth / 2 - objectSize);
                break;
        }
        return { x, y: 0, z };
    }
};

class EnvironmentObjectManager {
    constructor() {
        // Cache for biome object pools
        this.biomePoolCache = {};

        // Debug mode
        this.debugMode = false;
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode should be enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }

    /**
     * Get the object pool for a specific biome
     * @param {string} biome - The biome to get the object pool for
     * @returns {object} The object pool for the biome
     */
    getObjectPool(biome) {
        // Check if we already have this biome's pool cached
        if (this.biomePoolCache[biome]) {
            return this.biomePoolCache[biome];
        }

        // Get the base object pool for this biome
        const biomePool = BIOME_OBJECT_POOLS[biome] || BIOME_OBJECT_POOLS.catacombs;

        // Cache it
        this.biomePoolCache[biome] = biomePool;

        return biomePool;
    }

    /**
     * Choose a balanced object group for a room
     * @param {object} context - The context for object placement
     * @param {string} context.biome - The current biome
     * @param {string} context.roomType - The type of room
     * @param {object} context.roomData - The room data
     * @param {number} context.seed - Random seed for deterministic placement
     * @returns {Array} Array of objects to place
     */
    chooseObjectGroup(context) {
        const { biome, roomType, roomData, seed = Date.now() } = context;

        // Create seeded random function
        const random = mulberry32(seed);

        // Get the object pool for this biome
        const objectPool = this.getObjectPool(biome);

        // Determine which template group to use based on room type
        let templateGroup;
        switch (roomType.toLowerCase()) {
            case 'start':
                templateGroup = OBJECT_GROUP_TEMPLATES.start;
                break;
            case 'boss':
                templateGroup = OBJECT_GROUP_TEMPLATES.boss;
                break;
            case 'elite':
                templateGroup = OBJECT_GROUP_TEMPLATES.elite;
                break;
            default:
                templateGroup = OBJECT_GROUP_TEMPLATES.normal;
        }

        // Choose a template based on probability
        let template = null;
        const roll = random();
        let cumulativeProbability = 0;

        for (const t of templateGroup) {
            cumulativeProbability += t.probability;
            if (roll < cumulativeProbability) {
                template = t;
                break;
            }
        }

        // If no template was selected, use the first one
        if (!template && templateGroup.length > 0) {
            template = templateGroup[0];
        }

        if (!template) {
            console.warn(`No object template found for room type: ${roomType}`);
            return [];
        }

        // Generate objects based on the template
        const objectsToPlace = [];

        for (const role of template.roles) {
            const { type, placement, count, placementDetail } = role;

            // Get available objects for this role and placement
            const availableObjects = objectPool[type]?.[placement] || [];

            if (availableObjects.length === 0) {
                if (this.debugMode) {
                    console.warn(`No objects available for role ${type} and placement ${placement} in biome ${biome}`);
                }
                continue;
            }

            // Place the specified number of objects
            for (let i = 0; i < count; i++) {
                // Choose a random object from the available ones
                const objectIndex = Math.floor(random() * availableObjects.length);
                const objectType = availableObjects[objectIndex];

                objectsToPlace.push({
                    type: objectType,
                    placement,
                    placementDetail: placementDetail || 'random',
                    index: i,
                    totalCount: count
                });
            }
        }

        if (this.debugMode) {
            console.log(`Chose object group for ${biome} (${roomType}):`);
            console.log(`Objects to place: ${objectsToPlace.map(o => o.type).join(', ')}`);
        }

        return objectsToPlace;
    }

    /**
     * Get placement positions for objects in a room
     * @param {Array} objectsToPlace - Array of objects to place
     * @param {object} roomDimensions - The dimensions of the room
     * @param {number} seed - Random seed for deterministic placement
     * @returns {Array} Array of objects with position information
     */
    getObjectPlacements(objectsToPlace, roomDimensions, seed = Date.now()) {
        // Create seeded random function
        const random = mulberry32(seed);

        // Default object size
        const defaultObjectSize = 1.0;

        // Process each object
        return objectsToPlace.map(object => {
            const { type, placement, placementDetail, index, totalCount } = object;

            // Get placement function based on placement detail
            const placementFunc = PLACEMENT_DETAILS[placementDetail] || PLACEMENT_DETAILS.random;

            // Calculate position
            const position = placementFunc(roomDimensions, defaultObjectSize, index, totalCount);

            // Add some randomness to position if not using specific placement
            if (placementDetail === 'random') {
                position.x += (random() - 0.5) * 0.5;
                position.z += (random() - 0.5) * 0.5;
            }

            // Add rotation
            const rotation = random() * Math.PI * 2;

            return {
                ...object,
                position,
                rotation
            };
        });
    }

    /**
     * Create object instances for placement in a room
     * @param {Array} objectPlacements - Array of objects with position information
     * @returns {Array} Array of object instances
     */
    createObjectInstances(objectPlacements) {
        console.log('Creating object instances for', objectPlacements.length, 'objects');

        return objectPlacements.map(object => {
            const { type, placement, position, rotation } = object;

            // Get the prefab function for this object type
            const prefabFunc = getPrefabFunction(type, 'interior');

            if (!prefabFunc) {
                console.warn(`No prefab function found for object type: ${type}`);
                return null;
            }

            console.log(`Found prefab function for ${type}:`, prefabFunc ? 'YES' : 'NO');

            // Create options for the prefab function
            const isDestructible = OBJECT_ROLE_MAP[type] === OBJECT_ROLES.DESTRUCTIBLE;
            console.log(`Object ${type} isDestructible: ${isDestructible}, role: ${OBJECT_ROLE_MAP[type]}`);

            const options = {
                position,
                rotation,
                placement,
                isDestructible: isDestructible,
                destructionEffect: 'collapse',
                health: 1
            };

            return {
                type,
                prefabFunc,
                options
            };
        }).filter(Boolean);
    }
}

// Create and export a singleton instance
const environmentObjectManager = new EnvironmentObjectManager();
export default environmentObjectManager;
