/**
 * Test script for the Catacomb Overlord Boss model and animations
 * This script creates a simple scene to visualize the boss model and animations
 */

import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { createCatacombOverlordModel } from '../generators/prefabs/catacombOverlordBoss.js';
import { CatacombOverlordAnimationHandler } from '../ai/animations/CatacombOverlordAnimationHandler.js';
import { AIStates } from '../ai/AIStates.js';

// Scene setup
const scene = new THREE.Scene();
scene.background = new THREE.Color(0x111111);

// Camera setup
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
camera.position.set(0, 5, 10);

// Renderer setup
const renderer = new THREE.WebGLRenderer({ antialias: true });
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.shadowMap.enabled = true;
document.body.appendChild(renderer.domElement);

// Controls
const controls = new OrbitControls(camera, renderer.domElement);
controls.target.set(0, 3, 0);
controls.update();

// Lights
const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
scene.add(ambientLight);

const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
directionalLight.position.set(5, 10, 5);
directionalLight.castShadow = true;
directionalLight.shadow.mapSize.width = 2048;
directionalLight.shadow.mapSize.height = 2048;
scene.add(directionalLight);

// Ground plane
const groundGeometry = new THREE.PlaneGeometry(20, 20);
const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
const ground = new THREE.Mesh(groundGeometry, groundMaterial);
ground.rotation.x = -Math.PI / 2;
ground.receiveShadow = true;
scene.add(ground);

// Grid helper
const gridHelper = new THREE.GridHelper(20, 20);
scene.add(gridHelper);

// Create the boss model
console.log("Creating Catacomb Overlord Boss model...");
const bossModel = createCatacombOverlordModel(1.0);
bossModel.position.set(0, 0, 0);
scene.add(bossModel);
console.log("Boss model created and added to scene");

// Create animation handler
console.log("Creating animation handler...");
const animationHandler = new CatacombOverlordAnimationHandler(bossModel);
console.log("Animation handler created");

// Animation state
let currentState = AIStates.IDLE;
let stateTimer = 0;
const stateDuration = 3; // seconds per state

// UI for state control
const stateUI = document.createElement('div');
stateUI.style.position = 'absolute';
stateUI.style.top = '10px';
stateUI.style.left = '10px';
stateUI.style.color = 'white';
stateUI.style.fontFamily = 'Arial, sans-serif';
stateUI.style.padding = '10px';
stateUI.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
stateUI.style.borderRadius = '5px';
document.body.appendChild(stateUI);

// Create buttons for each state
const states = [
    { name: 'IDLE', value: AIStates.IDLE },
    { name: 'MOVING', value: AIStates.MOVING },
    { name: 'ATTACKING', value: AIStates.ATTACKING },
    { name: 'HIT_REACTING', value: AIStates.HIT_REACTING }
];

states.forEach(state => {
    const button = document.createElement('button');
    button.textContent = state.name;
    button.style.margin = '5px';
    button.style.padding = '5px 10px';
    button.addEventListener('click', () => {
        currentState = state.value;
        stateTimer = 0;
        updateStateUI();
    });
    stateUI.appendChild(button);
});

// Add a label to show current state
const stateLabel = document.createElement('div');
stateLabel.style.marginTop = '10px';
stateUI.appendChild(stateLabel);

function updateStateUI() {
    const stateName = states.find(s => s.value === currentState)?.name || 'UNKNOWN';
    stateLabel.textContent = `Current State: ${stateName}`;
}

// Animation loop
const clock = new THREE.Clock();
let globalTime = 0;

function animate() {
    requestAnimationFrame(animate);
    
    const deltaTime = clock.getDelta();
    globalTime += deltaTime;
    
    // Update state timer
    stateTimer += deltaTime;
    if (stateTimer >= stateDuration && currentState !== AIStates.ATTACKING) {
        // Cycle through states automatically (except when manually set)
        const currentIndex = states.findIndex(s => s.value === currentState);
        const nextIndex = (currentIndex + 1) % states.length;
        currentState = states[nextIndex].value;
        stateTimer = 0;
        updateStateUI();
    }
    
    // Update animation
    animationHandler.update(currentState, deltaTime, globalTime);
    
    // Render
    renderer.render(scene, camera);
}

// Handle window resize
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
});

// Initialize UI
updateStateUI();

// Start animation loop
animate();

console.log("Test script running. Use the buttons to change animation states.");
