/**
 * bossTimelines.js
 *
 * Contains timeline configurations for boss battles.
 * Each timeline defines which patterns and projectiles should play at specific time points in the music.
 */

export const bossTimelines = {
    // Catacomb Overlord timeline - Updated based on music rhythm and intensity changes
    "catacomb_overlord": [
        {
            startTime: 0,
            endTime: 4,
            patterns: ["spiral_wave"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.0,
            triggerIntervalMax: 2.0,
            description: "Shots in 360 degree - spiral pattern"
        },
        {
            startTime: 4,
            endTime: 7,
            patterns: ["lightning_chain"],
            projectileTypes: ["blood_threads"],
            intensity: 0.6,
            speedMultiplier: 1.1,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.5,
            description: "Faster shots in player direction at 90 degree"
        },
        {
            startTime: 7,
            endTime: 9,
            patterns: ["petal_spread"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.0,
            triggerIntervalMax: 2.0,
            description: "Shots in 360 degree - flower pattern"
        },
        {
            startTime: 9,
            endTime: 13,
            patterns: ["inhale_exhale"],
            projectileTypes: ["blood_threads"],
            intensity: 0.6,
            speedMultiplier: 1.1,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.5,
            description: "Faster shots in player direction at 90 degree - push/pull pattern"
        },
        {
            startTime: 13,
            endTime: 16,
            patterns: ["boomerang_arcs"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.0,
            triggerIntervalMax: 2.0,
            description: "Shots in 360 degree - arcing pattern"
        },
        {
            startTime: 16,
            endTime: 20,
            patterns: ["shifting_polarity"],
            projectileTypes: ["lava_droplets"],
            intensity: 0.6,
            speedMultiplier: 1.1,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.5,
            description: "Faster shots in player direction at 90 degree - changing behavior"
        },
        {
            startTime: 20,
            endTime: 24,
            patterns: ["alchemical_chain"],
            projectileTypes: ["lava_droplets"],
            intensity: 0.7,
            speedMultiplier: 1.2,
            triggerIntervalMin: 0.7,
            triggerIntervalMax: 1.3,
            description: "Fast arpeggio shots - reactive chain pattern"
        },
        {
            startTime: 24,
            endTime: 37,
            patterns: ["rapid_fire"],
            projectileTypes: ["blood_threads"],
            intensity: 0.65,
            speedMultiplier: 1.0,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.4,
            description: "Fast single shots - repetitive staccato notes"
        },
        {
            startTime: 37,
            endTime: 41,
            patterns: ["spiral_rain"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.0,
            triggerIntervalMax: 2.0,
            description: "Shots in 360 degree - falling spiral pattern"
        },
        {
            startTime: 42,
            endTime: 45,
            patterns: ["fire_kraken"],
            projectileTypes: ["lava_droplets"],
            intensity: 0.6,
            speedMultiplier: 1.1,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.5,
            description: "Faster shots in player direction at 90 degree - tentacle pattern"
        },
        {
            startTime: 45,
            endTime: 48,
            patterns: ["divine_divide"],
            projectileTypes: ["blood_threads"],
            intensity: 0.7,
            speedMultiplier: 1.0,
            triggerIntervalMin: 0.7,
            triggerIntervalMax: 1.2,
            description: "Waves of shots main melody stacked with arp in 360 degree - divided pattern"
        },
        {
            startTime: 48,
            endTime: 51,
            patterns: ["soul_magnetism"],
            projectileTypes: ["blood_threads"],
            intensity: 0.6,
            speedMultiplier: 1.1,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.5,
            description: "Faster shots in player direction at 90 degree - magnetic pattern"
        },
        {
            startTime: 51,
            endTime: 64,
            patterns: ["time_freeze"],
            projectileTypes: ["ash_cinders"],
            intensity: 0.3,
            speedMultiplier: 0.7,
            triggerIntervalMin: 2.0,
            triggerIntervalMax: 3.5,
            description: "Slower phase, lower tempo, less shots - break phase for player with time freeze effect"
        },
        {
            startTime: 64,
            endTime: 70,
            patterns: ["spiral_wave"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.0,
            triggerIntervalMax: 2.0,
            description: "Shots in 360 degree - elegant spiral pattern"
        },
        {
            startTime: 70,
            endTime: 76,
            patterns: ["alchemical_chain"],
            projectileTypes: ["lava_droplets"],
            intensity: 0.7,
            speedMultiplier: 1.2,
            triggerIntervalMin: 0.7,
            triggerIntervalMax: 1.3,
            description: "Fast arpeggios pick interesting pattern for come back energy - reactive chain"
        },
        {
            startTime: 76,
            endTime: 83,
            patterns: ["breath_trails"],
            projectileTypes: ["blood_threads"],
            intensity: 0.65,
            speedMultiplier: 1.1,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.5,
            description: "Fast repeating notes, faster shots in player direction at 90 degree - trailing pattern"
        },
        {
            startTime: 83,
            endTime: 90,
            patterns: ["laser_grid"],
            projectileTypes: ["blood_threads"],
            intensity: 0.7,
            speedMultiplier: 1.0,
            triggerIntervalMin: 1.0,
            triggerIntervalMax: 1.8,
            description: "Same tempo but different pattern - laser pattern covering the whole room with gaps"
        },
        {
            startTime: 90,
            endTime: 96,
            patterns: ["vortex_wings"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.0,
            triggerIntervalMax: 2.0,
            description: "Shots in 360 degree - wing-like vortex pattern"
        },
        {
            startTime: 96,
            endTime: 103,
            patterns: ["fake_out_pulse"],
            projectileTypes: ["lava_droplets"],
            intensity: 0.75,
            speedMultiplier: 1.1,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.4,
            description: "Dubstep like sound - pulsing fake-out pattern"
        },
        {
            startTime: 103,
            endTime: 106,
            patterns: ["time_freeze"],
            projectileTypes: ["ash_cinders"],
            intensity: 0.3,
            speedMultiplier: 0.7,
            triggerIntervalMin: 2.0,
            triggerIntervalMax: 3.0,
            description: "Small break - time freeze effect"
        },
        {
            startTime: 106,
            endTime: 109,
            patterns: ["delay_accelerate"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.2,
            triggerIntervalMax: 2.0,
            description: "Medium shots - delayed acceleration pattern"
        },
        {
            startTime: 109,
            endTime: 118,
            patterns: ["rapid_fire"],
            projectileTypes: ["blood_threads"],
            intensity: 0.65,
            speedMultiplier: 1.0,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.4,
            description: "Fast repeating notes - rapid fire pattern"
        },
        {
            startTime: 118,
            endTime: 131,
            patterns: ["boomerang_arcs"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.2,
            triggerIntervalMax: 2.0,
            description: "Medium shots - arcing boomerang pattern"
        },
        {
            startTime: 131,
            endTime: 135,
            patterns: ["alchemical_chain"],
            projectileTypes: ["lava_droplets"],
            intensity: 0.7,
            speedMultiplier: 1.2,
            triggerIntervalMin: 0.7,
            triggerIntervalMax: 1.3,
            description: "Arpeggios - reactive chain pattern"
        },
        {
            startTime: 135,
            endTime: 138,
            patterns: ["fire_kraken"],
            projectileTypes: ["blood_threads"],
            intensity: 0.6,
            speedMultiplier: 1.0,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.5,
            description: "Bursts of short sounds - tentacle pattern"
        },
        {
            startTime: 138,
            endTime: 140,
            patterns: ["spiral_rain"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.0,
            triggerIntervalMax: 2.0,
            description: "Shots in 360 degree - falling spiral pattern"
        },
        {
            startTime: 140,
            endTime: 144,
            patterns: ["inhale_exhale"],
            projectileTypes: ["fireball"],
            intensity: 0.6,
            speedMultiplier: 1.1,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.5,
            description: "Faster shots in player direction at 90 degree - push/pull pattern"
        },
        {
            startTime: 144,
            endTime: 150,
            patterns: ["shifting_polarity"],
            projectileTypes: ["blood_threads"],
            intensity: 0.65,
            speedMultiplier: 1.0,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.5,
            description: "Bursts and repeating notes - changing behavior pattern"
        },
        {
            startTime: 150,
            endTime: 158,
            patterns: ["delay_accelerate"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.2,
            triggerIntervalMax: 2.0,
            description: "Medium shots - delayed acceleration pattern"
        },
        {
            startTime: 158,
            endTime: 164,
            patterns: ["divine_divide"],
            projectileTypes: ["fireball"],
            intensity: 0.7,
            speedMultiplier: 1.0,
            triggerIntervalMin: 0.8,
            triggerIntervalMax: 1.5,
            description: "360 degree intense - divided pattern"
        },
        {
            startTime: 163,
            endTime: 170,
            patterns: ["breath_trails"],
            projectileTypes: ["fireball"],
            intensity: 0.7,
            speedMultiplier: 1.2,
            triggerIntervalMin: 0.7,
            triggerIntervalMax: 1.3,
            description: "Faster shots in player direction at 90 degree - trailing pattern"
        },
        {
            startTime: 170,
            endTime: 173,
            patterns: ["alchemical_chain"],
            projectileTypes: ["lava_droplets"],
            intensity: 0.7,
            speedMultiplier: 1.2,
            triggerIntervalMin: 0.7,
            triggerIntervalMax: 1.3,
            description: "Arpeggios - reactive chain pattern"
        },
        {
            startTime: 173,
            endTime: 176,
            patterns: ["fake_out_pulse"],
            projectileTypes: ["blood_threads"],
            intensity: 0.75,
            speedMultiplier: 1.1,
            triggerIntervalMin: 0.6,
            triggerIntervalMax: 1.2,
            description: "Fast bursts along with arp - pulsing fake-out pattern"
        },
        {
            startTime: 176,
            endTime: 189,
            patterns: ["boomerang_arcs"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.2,
            triggerIntervalMax: 2.0,
            description: "Medium shots - arcing boomerang pattern"
        },
        {
            startTime: 189,
            endTime: 192,
            patterns: ["vortex_wings"],
            projectileTypes: ["fireball"],
            intensity: 0.7,
            speedMultiplier: 1.2,
            triggerIntervalMin: 0.7,
            triggerIntervalMax: 1.3,
            description: "Faster shots in player direction at 360 degree - wing-like vortex pattern"
        },
        {
            startTime: 192,
            endTime: 198,
            patterns: ["spiral_rain"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.2,
            triggerIntervalMax: 2.0,
            description: "Medium shots - falling spiral pattern"
        },
        {
            startTime: 198,
            endTime: 202,
            patterns: ["soul_magnetism"],
            projectileTypes: ["fireball"],
            intensity: 0.7,
            speedMultiplier: 1.2,
            triggerIntervalMin: 0.7,
            triggerIntervalMax: 1.3,
            description: "Faster shots - magnetic pattern"
        },
        {
            startTime: 202,
            endTime: 208,
            patterns: ["alchemical_chain"],
            projectileTypes: ["lava_droplets"],
            intensity: 0.8,
            speedMultiplier: 1.3,
            triggerIntervalMin: 0.5,
            triggerIntervalMax: 1.0,
            description: "Very fast burst and arpeggios - reactive chain pattern"
        },
        {
            startTime: 208,
            endTime: 211,
            patterns: ["shifting_polarity"],
            projectileTypes: ["blood_threads"],
            intensity: 0.7,
            speedMultiplier: 1.1,
            triggerIntervalMin: 0.7,
            triggerIntervalMax: 1.3,
            description: "Triplet shots - changing behavior pattern"
        },
        {
            startTime: 211,
            endTime: 220,
            patterns: ["alchemical_chain"],
            projectileTypes: ["lava_droplets"],
            intensity: 0.7,
            speedMultiplier: 1.2,
            triggerIntervalMin: 0.7,
            triggerIntervalMax: 1.3,
            description: "Arpeggios - reactive chain pattern"
        },
        {
            startTime: 220,
            endTime: 224,
            patterns: ["delay_accelerate"],
            projectileTypes: ["fireball"],
            intensity: 0.5,
            speedMultiplier: 0.9,
            triggerIntervalMin: 1.2,
            triggerIntervalMax: 2.0,
            description: "Medium shots - delayed acceleration pattern"
        }
    ],

    // Default timeline for testing
    "default": [
        {
            startTime: 0,
            endTime: 30,
            patterns: ["petal_spread"],
            projectileTypes: ["shadow_bolt"],
            intensity: 0.3,
            speedMultiplier: 0.8,
            triggerIntervalMin: 5.0,
            triggerIntervalMax: 7.0,
            description: "Gentle intro with petal patterns"
        },
        {
            startTime: 30,
            endTime: 60,
            patterns: ["laser_grid"],
            projectileTypes: ["lightning_bolt"],
            intensity: 0.5,
            speedMultiplier: 1.0,
            triggerIntervalMin: 4.5,
            triggerIntervalMax: 6.0,
            description: "Building up with laser grid"
        },
        {
            startTime: 60,
            endTime: 90,
            patterns: ["spiral_wave"],
            projectileTypes: ["fireball"],
            intensity: 0.7,
            speedMultiplier: 1.1,
            triggerIntervalMin: 4.0,
            triggerIntervalMax: 5.5,
            description: "First mini-climax with spiral waves"
        },
        {
            startTime: 90,
            endTime: 120,
            patterns: ["hellburst"],
            projectileTypes: ["lightning_bolt"],
            intensity: 0.8,
            speedMultiplier: 1.2,
            triggerIntervalMin: 3.5,
            triggerIntervalMax: 5.0,
            description: "Intense section with hellburst"
        },
        {
            startTime: 120,
            endTime: 150,
            patterns: ["inhale_exhale"],
            projectileTypes: ["poison_cloud"],
            intensity: 0.6,
            speedMultiplier: 1.0,
            triggerIntervalMin: 4.5,
            triggerIntervalMax: 6.0,
            description: "Breakdown with inhale-exhale"
        },
        {
            startTime: 150,
            endTime: 180,
            patterns: ["circle_ripple"],
            projectileTypes: ["shadow_bolt"],
            intensity: 0.4,
            speedMultiplier: 0.9,
            triggerIntervalMin: 5.0,
            triggerIntervalMax: 7.0,
            description: "Outro with circle ripples"
        }
    ],

    // Epic boss battle timeline
    "epic_battle": [
        {
            startTime: 0,
            endTime: 20,
            patterns: ["petal_spread"],
            projectileTypes: ["shadow_bolt"],
            intensity: 0.3,
            speedMultiplier: 0.8,
            triggerIntervalMin: 5.0,
            triggerIntervalMax: 7.0,
            description: "Mysterious intro"
        },
        {
            startTime: 20,
            endTime: 40,
            patterns: ["circle_ripple"],
            projectileTypes: ["shadow_bolt"],
            intensity: 0.4,
            speedMultiplier: 0.9,
            triggerIntervalMin: 4.5,
            triggerIntervalMax: 6.5,
            description: "Building tension"
        },
        {
            startTime: 40,
            endTime: 60,
            patterns: ["laser_grid"],
            projectileTypes: ["lightning_bolt"],
            intensity: 0.5,
            speedMultiplier: 1.0,
            triggerIntervalMin: 4.0,
            triggerIntervalMax: 5.5,
            description: "First attack phase"
        },
        {
            startTime: 60,
            endTime: 80,
            patterns: ["spiral_wave"],
            projectileTypes: ["fireball"],
            intensity: 0.6,
            speedMultiplier: 1.1,
            triggerIntervalMin: 3.8,
            triggerIntervalMax: 5.2,
            description: "Increasing intensity"
        },
        {
            startTime: 80,
            endTime: 100,
            patterns: ["boomerang_arcs"],
            projectileTypes: ["fireball"],
            intensity: 0.7,
            speedMultiplier: 1.2,
            triggerIntervalMin: 3.5,
            triggerIntervalMax: 5.0,
            description: "Complex attack patterns"
        },
        {
            startTime: 100,
            endTime: 120,
            patterns: ["lightning_chain"],
            projectileTypes: ["lightning_bolt"],
            intensity: 0.8,
            speedMultiplier: 1.3,
            triggerIntervalMin: 3.2,
            triggerIntervalMax: 4.5,
            description: "Fast chain attacks"
        },
        {
            startTime: 120,
            endTime: 140,
            patterns: ["hellburst"],
            projectileTypes: ["lightning_bolt"],
            intensity: 0.9,
            speedMultiplier: 1.4,
            triggerIntervalMin: 3.0,
            triggerIntervalMax: 4.0,
            description: "Climax - all out attack"
        },
        {
            startTime: 140,
            endTime: 160,
            patterns: ["inhale_exhale"],
            projectileTypes: ["poison_cloud"],
            intensity: 0.7,
            speedMultiplier: 1.2,
            triggerIntervalMin: 3.5,
            triggerIntervalMax: 4.8,
            description: "Breathing phase"
        },
        {
            startTime: 160,
            endTime: 180,
            patterns: ["spiral_wave"],
            projectileTypes: ["fireball"],
            intensity: 0.6,
            speedMultiplier: 1.1,
            triggerIntervalMin: 3.8,
            triggerIntervalMax: 5.2,
            description: "Second wave"
        },
        {
            startTime: 180,
            endTime: 200,
            patterns: ["circle_ripple"],
            projectileTypes: ["shadow_bolt"],
            intensity: 0.5,
            speedMultiplier: 1.0,
            triggerIntervalMin: 4.0,
            triggerIntervalMax: 5.5,
            description: "Winding down"
        },
        {
            startTime: 200,
            endTime: 220,
            patterns: ["petal_spread"],
            projectileTypes: ["shadow_bolt"],
            intensity: 0.4,
            speedMultiplier: 0.9,
            triggerIntervalMin: 4.5,
            triggerIntervalMax: 6.0,
            description: "Outro - gentle finish"
        }
    ],

    // Custom timeline - add your own based on specific music tracks
    "custom": [
        // Add your custom timeline entries here
        // Example:
        // {
        //     startTime: 0,
        //     endTime: 30,
        //     patterns: ["petal_spread"],
        //     projectileTypes: ["shadow_bolt"],
        //     intensity: 0.4,
        //     speedMultiplier: 0.9,
        //     triggerIntervalMin: 1.5,
        //     triggerIntervalMax: 2.5,
        //     description: "Custom section 1"
        // },
    ],

    // Showcase timeline for new projectile types and patterns
    "new_showcase": [
        {
            startTime: 0,
            endTime: 30,
            patterns: ["alchemical_circle", "petal_spread", "circle_ripple"],
            projectileTypes: ["alchemical_symbols", "ash_cinders", "eye_tears"],
            intensity: 0.4,
            speedMultiplier: 0.8,
            triggerIntervalMin: 8.0,
            triggerIntervalMax: 12.0,
            description: "Mystical intro with alchemical symbols and other projectiles"
        },
        {
            startTime: 30,
            endTime: 60,
            patterns: ["sigil_array", "spiral_wave", "laser_grid"],
            projectileTypes: ["rotating_sigils", "blood_threads", "gold_coins"],
            intensity: 0.5,
            speedMultiplier: 0.7,
            triggerIntervalMin: 9.0,
            triggerIntervalMax: 13.0,
            description: "Arcane sigils and spiral patterns"
        },
        {
            startTime: 60,
            endTime: 90,
            patterns: ["sacred_formation", "boomerang_arcs", "inhale_exhale"],
            projectileTypes: ["sacred_geometry", "wax_drips", "thorn_seeds"],
            intensity: 0.6,
            speedMultiplier: 0.6,
            triggerIntervalMin: 10.0,
            triggerIntervalMax: 14.0,
            description: "Sacred geometry and complex patterns"
        },
        {
            startTime: 90,
            endTime: 120,
            patterns: ["alchemical_circle", "sigil_array", "hellburst"],
            projectileTypes: ["alchemical_symbols", "rotating_sigils", "sacred_geometry"],
            intensity: 0.7,
            speedMultiplier: 0.7,
            triggerIntervalMin: 9.0,
            triggerIntervalMax: 13.0,
            description: "Combined alchemical and sigil summoning with hellbursts"
        },
        {
            startTime: 120,
            endTime: 150,
            patterns: ["sacred_formation", "alchemical_circle", "soul_magnetism"],
            projectileTypes: ["sacred_geometry", "alchemical_symbols", "blood_threads"],
            intensity: 0.8,
            speedMultiplier: 0.6,
            triggerIntervalMin: 8.0,
            triggerIntervalMax: 12.0,
            description: "Sacred geometry with alchemical symbols and blood threads"
        },
        {
            startTime: 150,
            endTime: 180,
            patterns: ["sigil_array", "sacred_formation", "spiral_bloom"],
            projectileTypes: ["rotating_sigils", "sacred_geometry", "wax_drips"],
            intensity: 0.9,
            speedMultiplier: 0.7,
            triggerIntervalMin: 7.0,
            triggerIntervalMax: 11.0,
            description: "Sigils and sacred geometry with spiral bloom effects"
        },
        {
            startTime: 180,
            endTime: 210,
            patterns: ["alchemical_circle", "sigil_array", "sacred_formation"],
            projectileTypes: ["alchemical_symbols", "rotating_sigils", "sacred_geometry"],
            intensity: 1.0,
            speedMultiplier: 0.6,
            triggerIntervalMin: 7.0,
            triggerIntervalMax: 11.0,
            description: "Grand finale with all summoning patterns"
        },
        {
            startTime: 210,
            endTime: 240,
            patterns: ["fire_kraken"],
            projectileTypes: ["lava_droplets"],
            intensity: 0.8,
            speedMultiplier: 0.7,
            triggerIntervalMin: 9.0,
            triggerIntervalMax: 13.0,
            description: "Fire Kraken with tentacles reaching for the player"
        }
    ]
};
