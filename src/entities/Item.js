/**
 * Represents an item in the game world that can be picked up by the player.
 */

import * as THREE from 'three';
import { getItemData } from './ItemTypes.js';
import { createVoxelItemModel } from '../generators/prefabs/voxelItemModels.js';

class Item {
    /**
     * Create a new item
     * @param {object} scene - The THREE.js scene
     * @param {string} itemType - The type of item
     * @param {THREE.Vector3} position - The position in the world
     */
    constructor(scene, itemType, position) {
        this.scene = scene;
        this.itemType = itemType;
        this.itemData = getItemData(itemType);
        
        if (!this.itemData) {
            console.error(`Failed to create item: Type "${itemType}" data not found.`);
            return;
        }
        
        // Create the visual representation
        this.createVisual(position);
        
        // Set up interaction properties
        this.isCollected = false;
        this.interactionRadius = 0.8; // How close the player needs to be to pick up
        
        // Set up animation properties
        this.bobHeight = 0.2; // How high the item bobs up and down
        this.bobSpeed = 1.5;  // Speed of bobbing animation
        this.rotationSpeed = 0.5; // Speed of rotation animation
        this.creationTime = Date.now() / 1000; // For animation timing
        
        // Create bounding box for collision detection
        this.boundingBox = new THREE.Box3().setFromObject(this.mesh);
    }
    
    /**
     * Create the visual representation of the item
     * @param {THREE.Vector3} position - The position in the world
     */
    createVisual(position) {
        // Use the voxel model specified in the item data
        const modelName = this.itemData.voxelModel;
        
        // Create the mesh using the voxel item model generator
        this.mesh = createVoxelItemModel(modelName, this.itemData.rarity);
        
        if (!this.mesh) {
            console.error(`Failed to create item mesh for "${this.itemType}"`);
            // Create a fallback mesh
            const geometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
            const material = new THREE.MeshStandardMaterial({ color: 0xffffff });
            this.mesh = new THREE.Mesh(geometry, material);
        }
        
        // Position the mesh
        this.mesh.position.copy(position);
        
        // Add glow effect if specified
        if (this.itemData.glow) {
            this.addGlowEffect();
        }
        
        // Add to scene
        this.scene.add(this.mesh);
        
        // Store original position for bobbing animation
        this.originalY = position.y;
    }
    
    /**
     * Add a glow effect to the item
     */
    addGlowEffect() {
        const { color, intensity } = this.itemData.glow;
        
        // Create a point light for the glow
        this.light = new THREE.PointLight(color, intensity, 2.0);
        this.light.position.copy(this.mesh.position);
        this.scene.add(this.light);
        
        // Make the mesh material emissive
        this.mesh.traverse(child => {
            if (child.isMesh && child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(mat => {
                        mat.emissive = new THREE.Color(color);
                        mat.emissiveIntensity = intensity * 0.3; // Reduce intensity for material
                    });
                } else {
                    child.material.emissive = new THREE.Color(color);
                    child.material.emissiveIntensity = intensity * 0.3; // Reduce intensity for material
                }
            }
        });
    }
    
    /**
     * Update the item's animation
     * @param {number} deltaTime - Time since last update
     */
    update(deltaTime) {
        if (this.isCollected) return;
        
        const time = Date.now() / 1000 - this.creationTime;
        
        // Bob up and down
        const bobOffset = Math.sin(time * this.bobSpeed) * this.bobHeight;
        this.mesh.position.y = this.originalY + bobOffset;
        
        // Rotate
        this.mesh.rotation.y += this.rotationSpeed * deltaTime;
        
        // Update light position if it exists
        if (this.light) {
            this.light.position.copy(this.mesh.position);
        }
        
        // Update bounding box
        this.boundingBox.setFromObject(this.mesh);
    }
    
    /**
     * Check if the player is close enough to collect this item
     * @param {THREE.Vector3} playerPosition - The player's position
     * @returns {boolean} True if the player can collect this item
     */
    canCollect(playerPosition) {
        if (this.isCollected) return false;
        
        const distance = this.mesh.position.distanceTo(playerPosition);
        return distance <= this.interactionRadius;
    }
    
    /**
     * Collect the item and apply its effect
     * @param {object} player - The player object
     * @returns {object} The item data for the collected item
     */
    collect(player) {
        if (this.isCollected) return null;
        
        this.isCollected = true;
        
        // Apply the item's effect to the player
        this.applyEffect(player);
        
        // Remove the item from the scene
        this.remove();
        
        // Return the item data for UI updates or inventory
        return this.itemData;
    }
    
    /**
     * Apply the item's effect to the player
     * @param {object} player - The player object
     */
    applyEffect(player) {
        if (!player) return;
        
        const { effect, effectValue } = this.itemData;
        
        switch (effect) {
            case 'heal':
                player.heal(effectValue);
                break;
            case 'add_soul_heart':
                // TODO: Implement soul hearts
                player.heal(effectValue);
                break;
            case 'increase_max_health':
                player.increaseMaxHealth(effectValue);
                break;
            case 'add_key':
                // TODO: Implement key inventory
                console.log(`Added ${effectValue} key(s) to inventory`);
                break;
            case 'add_bomb':
                // TODO: Implement bomb inventory
                console.log(`Added ${effectValue} bomb(s) to inventory`);
                break;
            case 'reveal_map':
                // TODO: Implement map reveal
                console.log('Revealed map');
                break;
            case 'increase_damage':
                player.increaseProjectileDamage(effectValue);
                break;
            case 'increase_range':
                player.increaseProjectileRange(effectValue);
                break;
            case 'increase_size':
                player.increaseProjectileSize(effectValue);
                break;
            case 'slow_time':
                // TODO: Implement time slowing
                console.log('Activated time slowing effect');
                break;
            case 'reflect_projectiles':
                // TODO: Implement projectile reflection
                console.log('Activated projectile reflection');
                break;
            case 'danger_boost':
                // TODO: Implement danger boost
                console.log('Activated danger boost');
                break;
            case 'shift_soul_weight':
                // TODO: Implement soul weight shifting
                console.log(`Shifted soul weight toward ${effectValue}`);
                break;
            case 'balance_soul_weight':
                // TODO: Implement soul weight balancing
                console.log('Balanced soul weight');
                break;
            default:
                console.warn(`Unknown effect: ${effect}`);
        }
    }
    
    /**
     * Remove the item from the scene
     */
    remove() {
        if (this.mesh) {
            this.scene.remove(this.mesh);
        }
        
        if (this.light) {
            this.scene.remove(this.light);
        }
    }
}

export default Item;
