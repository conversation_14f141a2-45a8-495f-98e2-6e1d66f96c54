import * as THREE from 'three';
import { getEnemyData } from './EnemyTypes.js';

const ENEMY_STATE = {
    IDLE: 0,
    CHASING: 1,
    ATTACKING: 2,
    FLEEING: 3,
    KNOCKBACK: 4
};

class Enemy {
    constructor(dungeonHandler, playerMesh, scene, definition) {
        this.dungeonHandler = dungeonHandler;
        this.playerMesh = playerMesh;
        this.scene = scene;
        this.definition = definition;

        // Initialize the mesh
        const geometry = new THREE.BoxGeometry(definition.size, definition.size, definition.size);
        const material = new THREE.MeshStandardMaterial({ color: definition.color || 0xff0000 });
        this.mesh = new THREE.Mesh(geometry, material);

        // Set up userData on the mesh itself
        this.mesh.userData = {
            type: 'enemy',
            takeDamage: (damage) => this.takeHit(damage, this.playerMesh.position, 6) // Increased for better visual impact
        };

        // Also set up userData on the Enemy instance for consistency
        this.userData = this.mesh.userData;

        this.scene.add(this.mesh);

        // Store mesh reference on the Enemy instance
        this.mesh.enemy = this;

        // Store Enemy instance reference on the mesh
        this.mesh.parent = this;

        this.state = ENEMY_STATE.IDLE;
        this.timeInState = 0;
        this.timeSinceLastShot = 0;

        // Initialize health
        this.health = this.definition.health;

        this.boundingBox = new THREE.Box3();
        this.boundingBox.setFromObject(this.mesh);

        this.boxHelper = new THREE.Box3Helper(this.boundingBox, 0xff0000);
        this.scene.add(this.boxHelper);
        this.boxHelper.visible = false;

        this.velocity = new THREE.Vector3();
        this.knockbackVelocity = new THREE.Vector3();
        this.knockbackDuration = 0;
        this.speed = this.definition.baseSpeed + (Math.random() - 0.5) * 2 * (this.definition.speedVariation || 0) * this.definition.baseSpeed;

        this.targetPosition = new THREE.Vector3();
        this.currentPath = [];
        this.currentWaypointIndex = 0;

        this.healthBarCanvas = null;
        this.healthBarTexture = null;
        this.healthBarMaterial = null;
        this.healthBarMesh = null;
    }

    _calculateBoundingBox() {
        this.boundingBox.setFromObject(this.mesh);
    }

    _createHealthBar() {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        const barWidthPx = 64;
        const barHeightPx = 8;
        const padding = 2;
        canvas.width = barWidthPx + padding * 2;
        canvas.height = barHeightPx + padding * 2;

        this.healthBarCanvas = canvas;
        this.healthBarTexture = new THREE.CanvasTexture(this.healthBarCanvas);
        this.healthBarMaterial = new THREE.MeshBasicMaterial({
            map: this.healthBarTexture,
            transparent: true,
            depthWrite: false,
            side: THREE.DoubleSide
        });

        const healthBarHeight = 0.15;
        const healthBarWidth = healthBarHeight * (canvas.width / canvas.height);
        const geometry = new THREE.PlaneGeometry(healthBarWidth, healthBarHeight);
        this.healthBarMesh = new THREE.Mesh(geometry, this.healthBarMaterial);

        this.healthBarMesh.position.y = this.definition.size * 1.1;
        this.mesh.add(this.healthBarMesh);

        this.updateHealthBar();
    }

    updateHealthBar() {
        if (!this.healthBarCanvas) return;

        const context = this.healthBarCanvas.getContext('2d');
        const width = this.healthBarCanvas.width;
        const height = this.healthBarCanvas.height;
        const barWidthPx = width - 4;
        const barHeightPx = height - 4;
        const healthRatio = this.health / this.definition.health;

        context.clearRect(0, 0, width, height);

        context.fillStyle = 'rgba(80, 0, 0, 0.7)';
        context.fillRect(2, 2, barWidthPx, barHeightPx);

        if (healthRatio > 0.5) {
            context.fillStyle = 'rgba(0, 200, 0, 0.9)';
        } else if (healthRatio > 0.2) {
            context.fillStyle = 'rgba(200, 200, 0, 0.9)';
        } else {
            context.fillStyle = 'rgba(200, 0, 0, 0.9)';
        }
        context.fillRect(2, 2, barWidthPx * healthRatio, barHeightPx);

        if (this.healthBarTexture) {
             this.healthBarTexture.needsUpdate = true;
        }
    }

    setState(newState) {
        if (this.state !== newState) {
            this.state = newState;
            this.timeInState = 0;
            this.velocity.set(0, 0, 0);
        }
    }

    takeHit(damage, sourcePosition, knockbackForce = 6) {
        console.log("Enemy taking hit! Damage:", damage, "Force:", knockbackForce); // Debug log

        // Apply damage
        if (this.health) {
            this.health -= damage;
            this.updateHealthBar();
        }

        // Calculate knockback direction and distance
        const knockbackDirection = this.mesh.position.clone().sub(sourcePosition).normalize();
        const knockbackDistance = knockbackDirection.multiplyScalar(knockbackForce); // Direct distance to move

        // Try to move the full distance
        const targetPosition = this.mesh.position.clone().add(knockbackDistance);
        if (!this._checkCollisionAtPosition(targetPosition, [], [])) {
            this.mesh.position.copy(targetPosition);
        } else {
            // If full distance not possible, try moving along individual axes
            const xMove = this.mesh.position.clone();
            xMove.x += knockbackDistance.x;
            if (!this._checkCollisionAtPosition(xMove, [], [])) {
                this.mesh.position.x = xMove.x;
            }

            const zMove = this.mesh.position.clone();
            zMove.z += knockbackDistance.z;
            if (!this._checkCollisionAtPosition(zMove, [], [])) {
                this.mesh.position.z = zMove.z;
            }
        }

        // Update bounding box after movement
        this.boundingBox.setFromObject(this.mesh);
    }

    update(deltaTime, playerPosition, collisionObjects, floorBounds, isEspEnabled) {
        this.timeInState += deltaTime;
        this.timeSinceLastShot += deltaTime;

        const distanceToPlayer = this.mesh.position.distanceTo(playerPosition);

        switch (this.state) {
            case ENEMY_STATE.IDLE:
                break;

            case ENEMY_STATE.CHASING:
                this.targetPosition.copy(playerPosition);
                this._moveTowardsTarget(deltaTime, playerPosition, collisionObjects, floorBounds);

                if (distanceToPlayer < this.definition.preferredRange) {
                    this.setState(ENEMY_STATE.ATTACKING);
                } else if (distanceToPlayer > this.definition.preferredRange * 1.5) {
                    this.setState(ENEMY_STATE.IDLE);
                }
                break;

            case ENEMY_STATE.ATTACKING:
                this.velocity.set(0, 0, 0);
                this._faceTarget(playerPosition);

                if (this.timeSinceLastShot >= this.definition.shootCooldown) {
                     this._shootProjectile(playerPosition);
                     this.timeSinceLastShot = 0; // Reset timer
                }

                if (distanceToPlayer < this.definition.moveAwayRange) {
                    this.setState(ENEMY_STATE.FLEEING);
                } else if (distanceToPlayer > this.definition.preferredRange) {
                    this.setState(ENEMY_STATE.IDLE);
                }
                break;

            case ENEMY_STATE.FLEEING:
                const fleeDirection = this.mesh.position.clone().sub(playerPosition).normalize();
                this.targetPosition.copy(this.mesh.position).addScaledVector(fleeDirection, 5);
                this._moveTowardsTarget(deltaTime, this.targetPosition, collisionObjects, floorBounds);

                if (distanceToPlayer > this.definition.preferredRange * 1.1) {
                    this.setState(ENEMY_STATE.IDLE);
                }
                break;
        }

        // Handle normal movement
        if (this.velocity.lengthSq() > 0.001) {
            const moveVector = this.velocity.clone().multiplyScalar(deltaTime);
            const originalPosition = this.mesh.position.clone();
            const finalMove = new THREE.Vector3(0, 0, 0);

            // Try X movement
            const potentialPosX = originalPosition.clone();
            potentialPosX.x += moveVector.x;
            if (!this._checkCollisionAtPosition(potentialPosX, collisionObjects, floorBounds)) {
                finalMove.x = moveVector.x;
            }

            // Try Z movement
            const potentialPosZ = originalPosition.clone().add(new THREE.Vector3(finalMove.x, 0, 0));
            potentialPosZ.z += moveVector.z;
            if (!this._checkCollisionAtPosition(potentialPosZ, collisionObjects, floorBounds)) {
                finalMove.z = moveVector.z;
            }

            this.mesh.position.add(finalMove);
        }

        this.boundingBox.setFromObject(this.mesh);
    }

    _moveTowardsTarget(deltaTime, targetPos, collisionObjects, floorBounds) {
        const direction = targetPos.clone().sub(this.mesh.position).normalize();
        this.velocity.copy(direction).multiplyScalar(this.speed);
        this._faceTarget(targetPos);
    }

    _faceTarget(targetPosition) {
    }

    _checkCollisionAtPosition(position, collisionObjects, floorBounds) {
        // Implementation of _checkCollisionAtPosition method
        return false; // Placeholder return, actual implementation needed
    }
}

export default Enemy;