/**
 * Defines all item types, rarities, and properties for the game.
 * This serves as the master definition for all items that can be found in the game.
 */

// --- Item Rarity Definitions ---
export const ITEM_RARITY = {
    COMMON: 'common',
    RARE: 'rare',
    EPIC: 'epic',
    LEGENDARY: 'legendary'
};

// --- Item Category Definitions ---
export const ITEM_CATEGORY = {
    WEAPON: 'weapon',       // Modifies player's attack
    CONSUMABLE: 'consumable', // One-time use items
    RELIC: 'relic',         // Passive effects
    UTILITY: 'utility',     // Keys, bombs, etc.
    SOUL: 'soul',           // Soul-related items
    HEALTH: 'health'        // Health-related items
};

// --- Item Type Definitions ---
export const ITEM_TYPES = {
    // Health Items
    HEALTH_POTION: 'health_potion',
    SOUL_HEART: 'soul_heart',
    HEART_CONTAINER: 'heart_container',
    
    // Utility Items
    KEY: 'key',
    SOUL_BOMB: 'soul_bomb',
    TREASURE_MAP: 'treasure_map',
    
    // Weapons/Attack Modifiers
    SOUL_BLADE: 'soul_blade',
    SPECTRAL_BOW: 'spectral_bow',
    FROST_WAND: 'frost_wand',
    
    // Relics
    ZEITUHR: 'zeituhr',
    GOLDEN_MIRROR: 'golden_mirror',
    FAMILY_PHOTO: 'family_photo',
    
    // Soul Items
    LIGHT_ESSENCE: 'light_essence',
    DARK_ESSENCE: 'dark_essence',
    BALANCED_ESSENCE: 'balanced_essence'
};

// --- Item Data Definitions ---
const ITEM_DATA = {
    // --- Health Items ---
    health_potion: {
        name: 'Health Potion',
        description: 'Restores 2 health points',
        category: ITEM_CATEGORY.HEALTH,
        rarity: ITEM_RARITY.COMMON,
        effect: 'heal',
        effectValue: 2,
        voxelModel: 'health_potion',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xff0000,
            intensity: 1.0
        },
        blockedBy: [], // No restrictions
        soulWeightInfluence: 'neutral' // Not affected by soul weight
    },
    
    soul_heart: {
        name: 'Soul Heart',
        description: 'Adds a temporary heart that disappears when damaged',
        category: ITEM_CATEGORY.HEALTH,
        rarity: ITEM_RARITY.RARE,
        effect: 'add_soul_heart',
        effectValue: 1,
        voxelModel: 'soul_heart',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any', 'soul_door'],
        glow: {
            color: 0x00ffff,
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced' // More likely with balanced soul weight
    },
    
    heart_container: {
        name: 'Heart Container',
        description: 'Permanently increases maximum health by 1',
        category: ITEM_CATEGORY.HEALTH,
        rarity: ITEM_RARITY.EPIC,
        effect: 'increase_max_health',
        effectValue: 1,
        voxelModel: 'heart_container',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['boss', 'secret'],
        glow: {
            color: 0xff00ff,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral'
    },
    
    // --- Utility Items ---
    key: {
        name: 'Key',
        description: 'Opens locked doors and chests',
        category: ITEM_CATEGORY.UTILITY,
        rarity: ITEM_RARITY.COMMON,
        effect: 'add_key',
        effectValue: 1,
        voxelModel: 'key',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xffff00,
            intensity: 0.8
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral'
    },
    
    soul_bomb: {
        name: 'Soul Bomb',
        description: 'Explosive that damages enemies and can destroy certain obstacles',
        category: ITEM_CATEGORY.UTILITY,
        rarity: ITEM_RARITY.COMMON,
        effect: 'add_bomb',
        effectValue: 1,
        voxelModel: 'soul_bomb',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xff6600,
            intensity: 0.8
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral'
    },
    
    treasure_map: {
        name: 'Treasure Map',
        description: 'Reveals the entire floor layout',
        category: ITEM_CATEGORY.UTILITY,
        rarity: ITEM_RARITY.RARE,
        effect: 'reveal_map',
        effectValue: 1,
        voxelModel: 'treasure_map',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['secret', 'shop'],
        glow: {
            color: 0xaa7722,
            intensity: 0.5
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral'
    },
    
    // --- Weapons/Attack Modifiers ---
    soul_blade: {
        name: 'Soul Blade',
        description: 'Increases projectile damage by 5',
        category: ITEM_CATEGORY.WEAPON,
        rarity: ITEM_RARITY.RARE,
        effect: 'increase_damage',
        effectValue: 5,
        voxelModel: 'soul_blade',
        allowedBiomes: ['catacombs', 'ancient_library'],
        allowedRoomTypes: ['normal', 'elite', 'boss'],
        glow: {
            color: 0x0066ff,
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'dark' // More likely with dark soul weight
    },
    
    spectral_bow: {
        name: 'Spectral Bow',
        description: 'Increases projectile range by 5',
        category: ITEM_CATEGORY.WEAPON,
        rarity: ITEM_RARITY.RARE,
        effect: 'increase_range',
        effectValue: 5,
        voxelModel: 'spectral_bow',
        allowedBiomes: ['crystal_caves', 'flooded_ruins'],
        allowedRoomTypes: ['normal', 'elite', 'boss'],
        glow: {
            color: 0x66ffaa,
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'light' // More likely with light soul weight
    },
    
    frost_wand: {
        name: 'Frost Wand',
        description: 'Increases projectile size by 0.1',
        category: ITEM_CATEGORY.WEAPON,
        rarity: ITEM_RARITY.RARE,
        effect: 'increase_size',
        effectValue: 0.1,
        voxelModel: 'frost_wand',
        allowedBiomes: ['crystal_caves', 'lava_tubes'],
        allowedRoomTypes: ['normal', 'elite', 'boss'],
        glow: {
            color: 0x00ffff,
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced'
    },
    
    // --- Relics ---
    zeituhr: {
        name: 'Zeituhr',
        description: 'Slows down enemies when your health is low',
        category: ITEM_CATEGORY.RELIC,
        rarity: ITEM_RARITY.EPIC,
        effect: 'slow_time',
        effectValue: 0.5, // 50% speed reduction
        voxelModel: 'zeituhr',
        allowedBiomes: ['ancient_library', 'obsidian_fortress'],
        allowedRoomTypes: ['boss', 'secret'],
        glow: {
            color: 0xffcc00,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced'
    },
    
    golden_mirror: {
        name: 'Golden Mirror',
        description: 'Chance to reflect enemy projectiles',
        category: ITEM_CATEGORY.RELIC,
        rarity: ITEM_RARITY.EPIC,
        effect: 'reflect_projectiles',
        effectValue: 0.3, // 30% chance to reflect
        voxelModel: 'golden_mirror',
        allowedBiomes: ['crystal_caves', 'astral_plane'],
        allowedRoomTypes: ['boss', 'secret'],
        glow: {
            color: 0xffff00,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'light'
    },
    
    family_photo: {
        name: 'Family Photo',
        description: 'Increases all stats when in danger',
        category: ITEM_CATEGORY.RELIC,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'danger_boost',
        effectValue: 1.5, // 50% boost to all stats
        voxelModel: 'family_photo',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['boss', 'secret'],
        glow: {
            color: 0xffffff,
            intensity: 2.5
        },
        blockedBy: ['story_progression_1'], // Requires story progression
        soulWeightInfluence: 'neutral'
    },
    
    // --- Soul Items ---
    light_essence: {
        name: 'Light Essence',
        description: 'Shifts your soul weight toward light',
        category: ITEM_CATEGORY.SOUL,
        rarity: ITEM_RARITY.RARE,
        effect: 'shift_soul_weight',
        effectValue: 'light',
        voxelModel: 'light_essence',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['soul_door', 'secret'],
        glow: {
            color: 0xffffff,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'light'
    },
    
    dark_essence: {
        name: 'Dark Essence',
        description: 'Shifts your soul weight toward dark',
        category: ITEM_CATEGORY.SOUL,
        rarity: ITEM_RARITY.RARE,
        effect: 'shift_soul_weight',
        effectValue: 'dark',
        voxelModel: 'dark_essence',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['soul_door', 'secret'],
        glow: {
            color: 0x330066,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'dark'
    },
    
    balanced_essence: {
        name: 'Balanced Essence',
        description: 'Balances your soul weight',
        category: ITEM_CATEGORY.SOUL,
        rarity: ITEM_RARITY.EPIC,
        effect: 'balance_soul_weight',
        effectValue: 'balanced',
        voxelModel: 'balanced_essence',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['soul_door', 'secret'],
        glow: {
            color: 0x9966ff,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced'
    }
};

// --- Helper Functions ---

/**
 * Get item data by type
 * @param {string} itemType - The type of item to get data for
 * @returns {object|null} The item data or null if not found
 */
export function getItemData(itemType) {
    const data = ITEM_DATA[itemType];
    if (!data) {
        console.warn(`Item type "${itemType}" not found!`);
        return null;
    }
    // Return a copy to prevent accidental modification
    return { ...data };
}

/**
 * Get all items of a specific rarity
 * @param {string} rarity - The rarity to filter by
 * @returns {Array} Array of item types matching the rarity
 */
export function getItemsByRarity(rarity) {
    return Object.entries(ITEM_DATA)
        .filter(([_, data]) => data.rarity === rarity)
        .map(([type, _]) => type);
}

/**
 * Get all items of a specific category
 * @param {string} category - The category to filter by
 * @returns {Array} Array of item types matching the category
 */
export function getItemsByCategory(category) {
    return Object.entries(ITEM_DATA)
        .filter(([_, data]) => data.category === category)
        .map(([type, _]) => type);
}

/**
 * Get all items allowed in a specific biome
 * @param {string} biome - The biome to filter by
 * @returns {Array} Array of item types allowed in the biome
 */
export function getItemsByBiome(biome) {
    return Object.entries(ITEM_DATA)
        .filter(([_, data]) => 
            data.allowedBiomes.includes(biome) || 
            data.allowedBiomes.includes('any')
        )
        .map(([type, _]) => type);
}

/**
 * Get all items allowed in a specific room type
 * @param {string} roomType - The room type to filter by
 * @returns {Array} Array of item types allowed in the room type
 */
export function getItemsByRoomType(roomType) {
    return Object.entries(ITEM_DATA)
        .filter(([_, data]) => 
            data.allowedRoomTypes.includes(roomType) || 
            data.allowedRoomTypes.includes('any')
        )
        .map(([type, _]) => type);
}

// Make the main definitions read-only
Object.freeze(ITEM_DATA);
Object.freeze(ITEM_RARITY);
Object.freeze(ITEM_CATEGORY);
Object.freeze(ITEM_TYPES);
