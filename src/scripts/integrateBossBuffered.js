/**
 * integrateBossBuffered.js
 *
 * This script integrates the buffered music controller with the boss controller.
 * Add this to your game's initialization code.
 */

import { integrateBufferedController } from '../ai/brains/BossControllerBufferedIntegration.js';

/**
 * Integrate buffered music controller with the catacomb boss
 * @param {Object} game - Your game instance
 * @param {Object} options - Additional options
 */
export async function integrateCatacombBossBuffered(game, options = {}) {
  // Get the boss controller
  const boss = game.getBoss ? game.getBoss() : null;
  if (!boss || !boss.userData || !boss.userData.bossController) {
    console.error("[integrateBossBuffered] Cannot find boss controller");
    return false;
  }

  const bossController = boss.userData.bossController;

  // Path to the comprehensive data with updated timing information
  const dataPath = 'assets/music/catacombs/catacomb_boss_comprehensive_updated.json';

  // Default options
  const defaultOptions = {
    bufferTimeMs: 200, // Default 200ms buffer
    speedMultiplier: 1.2 // Default 1.2x speed
  };

  // Merge with provided options
  const mergedOptions = {
    ...defaultOptions,
    ...options
  };

  // Integrate buffered controller
  const result = await integrateBufferedController(bossController, dataPath, mergedOptions);

  if (result) {
    console.log(`[integrateBossBuffered] Successfully integrated buffered music controller with ${mergedOptions.bufferTimeMs}ms buffer and ${mergedOptions.speedMultiplier}x speed`);
  } else {
    console.error("[integrateBossBuffered] Failed to integrate buffered music controller");
  }

  return result;
}

// Example usage:
/*
import { integrateCatacombBossBuffered } from './scripts/integrateBossBuffered.js';

// In your game initialization:
async function initGame() {
  // ... other initialization code ...

  // Integrate buffered music controller with custom options
  await integrateCatacombBossBuffered(game, {
    bufferTimeMs: 250, // 250ms buffer
    speedMultiplier: 1.3 // 1.3x speed
  });

  // ... continue with game initialization ...
}
*/
