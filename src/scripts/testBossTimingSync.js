/**
 * testBossTimingSync.js
 * 
 * This script tests the timing synchronization between music and projectiles.
 * It can be used to fine-tune the buffer time and speed multiplier.
 */

import { integrateCatacombBossBuffered } from './integrateBossBuffered.js';

/**
 * Test different timing configurations for the catacomb boss
 * @param {Object} game - Your game instance
 */
export async function testBossTimingSync(game) {
  // Get the boss controller
  const boss = game.getBoss ? game.getBoss() : null;
  if (!boss || !boss.userData || !boss.userData.bossController) {
    console.error("[testBossTimingSync] Cannot find boss controller");
    return false;
  }
  
  const bossController = boss.userData.bossController;
  
  // Create a simple UI for adjusting timing parameters
  const container = document.createElement('div');
  container.style.position = 'fixed';
  container.style.top = '10px';
  container.style.right = '10px';
  container.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  container.style.padding = '10px';
  container.style.borderRadius = '5px';
  container.style.color = 'white';
  container.style.fontFamily = 'Arial, sans-serif';
  container.style.zIndex = '1000';
  
  // Add title
  const title = document.createElement('h3');
  title.textContent = 'Boss Timing Sync';
  title.style.margin = '0 0 10px 0';
  container.appendChild(title);
  
  // Add buffer time slider
  const bufferLabel = document.createElement('div');
  bufferLabel.textContent = 'Buffer Time: 200ms';
  container.appendChild(bufferLabel);
  
  const bufferSlider = document.createElement('input');
  bufferSlider.type = 'range';
  bufferSlider.min = '0';
  bufferSlider.max = '500';
  bufferSlider.value = '200';
  bufferSlider.step = '10';
  bufferSlider.style.width = '100%';
  container.appendChild(bufferSlider);
  
  // Add speed multiplier slider
  const speedLabel = document.createElement('div');
  speedLabel.textContent = 'Speed Multiplier: 1.2x';
  container.appendChild(speedLabel);
  
  const speedSlider = document.createElement('input');
  speedSlider.type = 'range';
  speedSlider.min = '0.5';
  speedSlider.max = '2.0';
  speedSlider.value = '1.2';
  speedSlider.step = '0.1';
  speedSlider.style.width = '100%';
  container.appendChild(speedSlider);
  
  // Add apply button
  const applyButton = document.createElement('button');
  applyButton.textContent = 'Apply Settings';
  applyButton.style.marginTop = '10px';
  applyButton.style.padding = '5px 10px';
  applyButton.style.width = '100%';
  container.appendChild(applyButton);
  
  // Add status text
  const statusText = document.createElement('div');
  statusText.textContent = 'Ready to test';
  statusText.style.marginTop = '10px';
  statusText.style.fontSize = '12px';
  container.appendChild(statusText);
  
  // Add to document
  document.body.appendChild(container);
  
  // Update labels when sliders change
  bufferSlider.addEventListener('input', () => {
    bufferLabel.textContent = `Buffer Time: ${bufferSlider.value}ms`;
  });
  
  speedSlider.addEventListener('input', () => {
    speedLabel.textContent = `Speed Multiplier: ${speedSlider.value}x`;
  });
  
  // Apply button click handler
  applyButton.addEventListener('click', async () => {
    // Get current values
    const bufferTimeMs = parseInt(bufferSlider.value);
    const speedMultiplier = parseFloat(speedSlider.value);
    
    // Update status
    statusText.textContent = `Applying: Buffer=${bufferTimeMs}ms, Speed=${speedMultiplier}x...`;
    
    // Stop the current controller if active
    if (bossController.active && bossController.bufferedController) {
      bossController.stop();
    }
    
    // Apply new settings
    const result = await integrateCatacombBossBuffered(game, {
      bufferTimeMs,
      speedMultiplier
    });
    
    // Start the boss controller
    if (result) {
      bossController.start();
      statusText.textContent = `Applied: Buffer=${bufferTimeMs}ms, Speed=${speedMultiplier}x`;
    } else {
      statusText.textContent = 'Failed to apply settings';
    }
  });
  
  // Initial integration with default settings
  const initialResult = await integrateCatacombBossBuffered(game);
  
  if (initialResult) {
    statusText.textContent = 'Initial integration successful';
  } else {
    statusText.textContent = 'Initial integration failed';
  }
  
  return true;
}

// Example usage:
/*
import { testBossTimingSync } from './scripts/testBossTimingSync.js';

// In your game:
testBossTimingSync(game);
*/
