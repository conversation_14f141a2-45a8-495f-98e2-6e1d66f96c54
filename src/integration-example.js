/**
 * Example of integrating the CRT effect with your game
 * This is just an example - you'll need to adapt it to your game's structure
 */
import * as THREE from 'three';
import { initCRTEffect } from './effects/CRTIntegration.js';

// Your existing game initialization code
function initGame() {
    // Create renderer, scene, camera, etc.
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.body.appendChild(renderer.domElement);
    
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 5;
    
    // Initialize CRT effect
    const crtEffect = initCRTEffect(renderer, scene, camera);
    
    // Your game objects, lights, etc.
    // ...
    
    // Animation loop
    function animate() {
        requestAnimationFrame(animate);
        
        // Update your game logic
        // ...
        
        // Update CRT effect (this replaces your normal renderer.render call)
        crtEffect.update();
    }
    
    // Start animation loop
    animate();
    
    // Return game objects for external access
    return {
        scene,
        camera,
        renderer,
        crtEffect
    };
}

// Initialize game
const game = initGame();

// Example of how to access CRT effect from elsewhere in your code
function toggleCRTEffect() {
    game.crtEffect.manager.toggle();
}

function applySonyPreset() {
    game.crtEffect.manager.applyPreset('sony_trinitron');
    game.crtEffect.controlPanel.updateSliders();
}

// Export for use in other modules
export { game, toggleCRTEffect, applySonyPreset };
