/**
 * Projectile class
 * Handles projectile behavior, movement, and collision
 */
import * as THREE from 'three';
// Removed import for createDamageNumber
import { getProjectileType } from './ProjectileTypes.js';

export class Projectile {
    /**
     * Constructor for Projectile
     * @param {Object} scene - The scene object
     * @param {THREE.Vector3} startPosition - Initial position
     * @param {THREE.Vector3} velocity - Initial velocity
     * @param {Number} damage - Damage amount
     * @param {Number} range - Maximum range
     * @param {Number|Object} size - Size of projectile (number or object with dimensions)
     * @param {Boolean} isEnemyProjectile - Whether this is an enemy projectile
     * @param {String} type - Projectile type name
     */
    constructor(scene, startPosition, velocity, damage, range, size, isEnemyProjectile = false, type = 'default_soul_orb', customProps = {}) {
        this.scene = scene;
        this.damage = damage;
        this.range = range;
        this.isEnemyProjectile = isEnemyProjectile;
        this.type = type;

        // Get projectile type data
        this.typeData = getProjectileType(type);

        // Movement
        // CRITICAL FIX: Ensure velocity is valid, use default if not
        if (velocity && velocity.isVector3) {
            this.velocity = velocity.clone();
        } else {
            console.error('[Projectile] Invalid velocity provided, using default');
            this.velocity = new THREE.Vector3(0, 0, -1); // Default direction
        }
        this.gravity = this.typeData.gravity || -4.9;
        this.distanceTraveled = 0;

        // Timing
        this.lifeTime = 0;
        // Use custom lifetime if provided, otherwise use default 3-second lifetime
        this.maxLifeTime = customProps.lifetime || 3.0; // Use custom lifetime if specified

        // Log custom lifetime for debugging
        if (customProps.lifetime) {
            console.log(`[Projectile] Using custom lifetime: ${customProps.lifetime}s for ${type} projectile`);
        }

        // Owner
        this.owner = isEnemyProjectile ? 'enemy' : 'player';

        // Custom properties
        this.customProps = customProps || {};

        // Handle special projectile types
        if (this.customProps.isBigFireball) {
            console.log("[Projectile] Creating big fireball with scale:", this.customProps.scale);
            this.scale = this.customProps.scale || 2.0;
        }

        if (this.customProps.isSkeleton) {
            console.log("[Projectile] Creating skeleton minion with health:", this.customProps.health);
            this.health = this.customProps.health || 1;
        }

        if (this.customProps.isFireCircle) {
            console.log("[Projectile] Creating fire circle projectile");
            this.isFireCircle = true;
        }

        // Trail effect
        // Check if trails should be disabled for this projectile (e.g., boss projectiles)
        if (this.customProps.disableTrails) {
            this.hasTrail = false;
            console.log("[Projectile] Trails disabled for this projectile");
        } else {
            this.hasTrail = this.typeData.trailEffect || false;
        }
        this.trailPoints = [];
        this.trailMaxLength = this.typeData.trailLength || 2; // Drastically reduced from 3 for performance
        this.trailType = 'line'; // Always use simple line trails for performance
        this.trailWidth = this.typeData.trailWidth || 1; // Trail width
        this.trailFade = this.typeData.trailFade || false; // Whether trail fades out

        // PERFORMANCE: Adjust trail based on global performance mode
        if (window.gamePerformanceMode === 'low') {
            // Low performance mode: disable trails completely
            this.hasTrail = false;
        } else if (window.gamePerformanceMode === 'medium') {
            // Medium performance mode: use minimal trails
            this.trailMaxLength = 2;
        }

        // Additional FPS-based adjustments
        if (window.lastFps && window.lastFps < 30) {
            // If FPS is very low, consider disabling trails completely
            if (window.lastFps < 20) {
                this.hasTrail = false;
            } else {
                // Otherwise use minimal trails
                this.trailMaxLength = Math.min(this.trailMaxLength, 2);
            }
        }

        // Bounding box for collision
        this.boundingBox = new THREE.Box3();

        // Mesh reference (set by DungeonHandler)
        this.mesh = null;
    }

    /**
     * Update projectile
     * @param {Number} deltaTime - Time since last update
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} playerMesh - Player mesh
     * @param {Array} activeEnemies - Active enemies
     * @returns {Boolean} - Whether projectile should be removed
     */
    update(deltaTime, collisionObjects, playerMesh, activeEnemies) {
        this.lifeTime += deltaTime;

        // Store previous position for distance calculation
        const previousPosition = this.mesh ? this.mesh.position.clone() : null;

        // Apply gravity
        if (this.velocity) {
            this.velocity.y += this.gravity * deltaTime;
        } else {
            console.error('[Projectile] Velocity is null in update method');
            this.velocity = new THREE.Vector3(0, 0, -1); // Create a default velocity
            this.velocity.y += this.gravity * deltaTime;
        }

        // Update position
        if (this.mesh) {
            // CRITICAL FIX: Ensure velocity is valid before using it
            if (this.velocity && this.velocity.isVector3) {
                this.mesh.position.addScaledVector(this.velocity, deltaTime);
            } else {
                console.error('[Projectile] Cannot update position: velocity is invalid');
                // Create a default velocity and use it
                this.velocity = new THREE.Vector3(0, 0, -1);
                this.mesh.position.addScaledVector(this.velocity, deltaTime);
            }

            // Run custom animation function if it exists
            if (this.mesh.userData && this.mesh.userData.animate) {
                this.mesh.userData.animate(deltaTime);
            }

            // Animate any children with animation functions
            if (this.mesh.children && this.mesh.children.length > 0) {
                this._animateChildren(this.mesh, deltaTime);
            }

            // PERFORMANCE: Update trail less frequently on low-end devices
            // Skip trail updates more aggressively
            const shouldUpdateTrail = !window.lastFps ||
                                     window.lastFps >= 40 ||
                                     (window.lastFps >= 30 && Math.random() < 0.3) ||
                                     Math.random() < 0.15;

            // Update trail (with aggressive performance optimization)
            if (this.hasTrail && shouldUpdateTrail) {
                this._updateTrail();
            }

            // Orient projectile in direction of travel (for arrows, etc.)
            if (this.type === 'arrow' || this.typeData.orientWithVelocity) {
                this._orientWithVelocity();
            }

            // Update bounding box
            this.boundingBox.setFromObject(this.mesh);

            // Calculate distance traveled
            if (previousPosition) {
                this.distanceTraveled += previousPosition.distanceTo(this.mesh.position);
            }
        }

        // Check lifetime
        if (this.lifeTime >= this.maxLifeTime || this.distanceTraveled >= this.range) {
            this.destroy();
            return true; // Indicate projectile should be removed
        }

        // Check collision
        if (this._checkCollision(collisionObjects, playerMesh, activeEnemies)) {
            this.destroy();
            return true; // Indicate projectile should be removed
        }

        return false; // Projectile still active
    }

    /**
     * Check collision with objects, player, and enemies
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} playerMesh - Player mesh
     * @param {Array} activeEnemies - Active enemies
     * @returns {Boolean} - Whether collision occurred
     * @private
     */
    _checkCollision(collisionObjects, playerMesh, activeEnemies) {
        // FIXED: Check DESTRUCTIBLE objects first (only for player projectiles)
        if (!this.isEnemyProjectile) {
            console.log(`[Projectile] Checking ${collisionObjects.length} collision objects for destructible targets`);

            for (const obj of collisionObjects) {
                // Skip objects without geometry or children
                if (!obj || (!obj.geometry && (!obj.children || obj.children.length === 0))) {
                    continue;
                }

                // FIXED: Check both object and parent for destructible userData
                let isDestructible = obj.userData?.isDestructible;
                let destructibleObject = obj;

                // If the object itself isn't marked destructible, check its parent
                if (!isDestructible && obj.parent && obj.parent.userData?.isDestructible) {
                    isDestructible = true;
                    destructibleObject = obj.parent; // Use parent for userData
                    console.log(`[Projectile] Object ${obj.name} inherits destructible from parent: ${obj.parent.name}`);
                }

                console.log(`[Projectile] Checking object: ${obj.name || 'unnamed'}, isDestructible: ${!!isDestructible}, objectType: ${destructibleObject.userData?.objectType}`);

                // Check if this is a destructible object (either directly or via parent)
                if (isDestructible) {
                    const objectBox = new THREE.Box3().setFromObject(obj);
                    const intersects = this.boundingBox.intersectsBox(objectBox);
                    console.log(`[Projectile] Destructible object collision check - intersects: ${intersects}`);

                    if (intersects) {
                        console.log(`[Projectile] HIT DESTRUCTIBLE OBJECT: ${destructibleObject.name || destructibleObject.userData.objectType || destructibleObject.uuid}`);
                        console.log(`[Projectile] Object userData:`, destructibleObject.userData);

                        // Trigger impact effect at the hit location
                        this._triggerImpactEffect(obj.position.clone(), 'destructible');

                        // Apply damage to destructible object (use parent object for health tracking)
                        const currentHealth = destructibleObject.userData.health || 1;
                        destructibleObject.userData.health = currentHealth - this.damage;
                        console.log(`[Projectile] Object health: ${currentHealth} -> ${destructibleObject.userData.health} (damage: ${this.damage})`);

                        // Check if object should be destroyed
                        if (destructibleObject.userData.health <= 0) {
                            console.log(`[Projectile] DESTRUCTIBLE OBJECT DESTROYED: ${destructibleObject.name || destructibleObject.userData.objectType}`);

                            // Trigger destruction through global event system (destroy the parent object)
                            const destructionEvent = new CustomEvent('objectDestroyed', {
                                detail: {
                                    object: destructibleObject, // Use the parent object for destruction
                                    pointOfImpact: this.mesh.position.clone(),
                                    projectileVelocity: this.velocity.clone()
                                }
                            });
                            console.log(`[Projectile] Dispatching objectDestroyed event:`, destructionEvent.detail);
                            window.dispatchEvent(destructionEvent);
                        }

                        return true; // Hit destructible object
                    }
                }
            }

            console.log(`[Projectile] No destructible objects hit`);
        }

        // Check against NON-DESTRUCTIBLE collision objects (walls, etc.)
        for (const obj of collisionObjects) {
            // Skip objects without geometry or children
            if (!obj || (!obj.geometry && (!obj.children || obj.children.length === 0))) {
                continue;
            }

            // Skip destructible objects (already handled above)
            if (obj.userData?.isDestructible) {
                continue;
            }

            const objectBox = new THREE.Box3().setFromObject(obj);
            if (this.boundingBox.intersectsBox(objectBox)) {
                // Trigger impact effect
                this._triggerImpactEffect(obj.position.clone(), 'environment');
                return true; // Hit wall/non-destructible object
            }
        }

        if (this.isEnemyProjectile) {
            // Enemy projectile checking against Player
            if (playerMesh) {
                const playerBox = new THREE.Box3().setFromObject(playerMesh);
                if (this.boundingBox.intersectsBox(playerBox)) {
                    // Trigger impact effect
                    this._triggerImpactEffect(playerMesh.position.clone(), 'player');

                    // Deal damage to player (always 1 damage)
                    if (playerMesh.userData && playerMesh.userData.takeDamage) {
                        playerMesh.userData.takeDamage(1); // Always use 1 damage
                    }

                    return true;
                }
            }
        } else {
            // Player projectile checking against Enemies
            for (const enemy of activeEnemies) {
                // Create bounding box for enemy
                const enemyBox = new THREE.Box3().setFromObject(enemy);

                // Expand the enemy bounding box slightly to make hits more reliable
                const expansionAmount = 0.2; // Expand by 0.2 units in all directions
                enemyBox.expandByScalar(expansionAmount);

                // Check for intersection with expanded box
                if (this.boundingBox.intersectsBox(enemyBox)) {
                    // Trigger impact effect
                    this._triggerImpactEffect(enemy.position.clone(), 'enemy');

                    // Determine if this is a critical hit (random chance)
                    const isCritical = Math.random() < 0.2; // 20% chance for critical hit
                    const damageAmount = isCritical ? this.damage * 1.5 : this.damage;

                    // Apply damage to enemy
                    if (enemy.userData && typeof enemy.userData.health === 'number') {
                        // Reduce enemy health
                        enemy.userData.health -= damageAmount;

                        // Apply immediate AI brain knockback for smooth animation (only if enemy survives)
                        if (enemy.userData.health > 0 && enemy.userData.aiBrain) {
                            const knockbackDirection = enemy.position.clone().sub(this.mesh.position).normalize();
                            const knockbackStrength = 20.0; // Use same strength as DungeonHandler
                            enemy.userData.aiBrain.applyKnockback(knockbackDirection, knockbackStrength);
                        }
                        // Fallback: Check if enemy has a takeDamage method in userData (for old system compatibility)
                        else if (enemy.userData.health > 0 && typeof enemy.userData.takeDamage === 'function') {
                            enemy.userData.takeDamage(damageAmount);
                        }

                        // Check if enemy is defeated - let DungeonHandler handle death properly
                        if (enemy.userData.health <= 0) {
                            enemy.userData.health = 0; // Ensure health doesn't go negative
                            // Mark enemy for death processing in next DungeonHandler update
                            enemy.userData.markedForDeath = true;
                        }
                    } else if (typeof enemy.takeHit === 'function') {
                        // Fallback: try calling takeHit directly on enemy (for Enemy class instances)
                        enemy.takeHit(damageAmount, this.mesh.position, 6); // Increased for better visual impact
                    }

                    return true;
                }
            }
        }

        return false; // No collision detected
    }

    /**
     * Trigger impact effect
     * @param {THREE.Vector3} position - Impact position
     * @param {String} targetType - Type of target hit
     * @param {THREE.Vector3} [velocity] - Projectile velocity at impact
     * @private
     */
    _triggerImpactEffect(position, targetType, velocity) {
        // Get impact effect type
        const impactEffect = this.typeData.impactEffect;

        // Emit impact event (to be handled by DungeonHandler)
        if (this.mesh && this.mesh.userData && this.mesh.userData.onImpact) {
            // Pass velocity if available, otherwise pass the projectile's current velocity
            const impactVelocity = velocity || this.velocity;
            this.mesh.userData.onImpact(position, impactEffect, targetType, impactVelocity);
        }
    }

    /**
     * Update trail effect
     * @private
     */
    _updateTrail() {
        if (!this.mesh || this.customProps.disableTrails) return;

        // Add current position to trail
        this.trailPoints.push(this.mesh.position.clone());

        // Limit trail length
        if (this.trailPoints.length > this.trailMaxLength) {
            this.trailPoints.shift();
        }

        // Update trail geometry if it exists
        if (this.trailMesh) {
            // Remove old trail
            this.scene.remove(this.trailMesh);
            this.trailMesh.geometry.dispose();
            this.trailMesh.material.dispose();

            // Create new trail
            this._createTrailMesh();
        } else {
            // Create trail mesh for the first time
            this._createTrailMesh();
        }
    }

    /**
     * Create trail mesh
     * @private
     */
    _createTrailMesh() {
        if (this.trailPoints.length < 2 || this.customProps.disableTrails) return;

        // Get trail properties
        const trailColor = this.typeData.trailColor || this.typeData.color || 0xFFFFFF;
        const trailType = this.trailType;
        const trailWidth = this.trailWidth;

        // Create different trail types based on the projectile type
        switch (trailType) {
            case 'ribbon':
                this._createRibbonTrail(trailColor);
                break;

            case 'particles':
                this._createParticleTrail(trailColor);
                break;

            case 'flame':
                this._createFlameTrail(trailColor);
                break;

            case 'energy':
                this._createEnergyTrail(trailColor);
                break;

            case 'water':
                this._createWaterTrail(trailColor);
                break;

            case 'shadow':
                this._createShadowTrail(trailColor);
                break;

            case 'line':
            default:
                this._createLineTrail(trailColor, trailWidth);
                break;
        }
    }

    /**
     * Create a basic line trail
     * @param {Number} color - Trail color
     * @param {Number} width - Trail width
     * @private
     */
    _createLineTrail(color, width = 1) {
        // Create geometry from points
        const geometry = new THREE.BufferGeometry().setFromPoints(this.trailPoints);

        // Create material with specified width and lower opacity for faster visual fading
        const material = new THREE.LineBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.5, // Reduced from 0.7 for faster visual fading
            linewidth: width // Note: linewidth may not work in all browsers due to WebGL limitations
        });

        // Create line
        this.trailMesh = new THREE.Line(geometry, material);

        // PERFORMANCE: Add timestamp for lifetime tracking
        this.trailMesh.userData = this.trailMesh.userData || {};
        this.trailMesh.userData.createdAt = Date.now();

        this.scene.add(this.trailMesh);
    }

    /**
     * Create a ribbon-like trail with width
     * @param {Number} color - Trail color
     * @private
     */
    _createRibbonTrail(color) {
        if (this.trailPoints.length < 2) return;

        // Create a ribbon by generating a plane along the trail path
        const points = this.trailPoints;
        const width = this.trailWidth * 0.1; // Scale down for reasonable width

        // Create vertices for the ribbon
        const vertices = [];
        const uvs = [];

        // Create a ribbon by generating points perpendicular to the path
        for (let i = 0; i < points.length; i++) {
            // Calculate direction vector between points
            const direction = new THREE.Vector3();

            if (i < points.length - 1) {
                direction.subVectors(points[i + 1], points[i]).normalize();
            } else if (i > 0) {
                direction.subVectors(points[i], points[i - 1]).normalize();
            }

            // Calculate perpendicular vector (in world space)
            const perpendicular = new THREE.Vector3(direction.z, 0, -direction.x).normalize();

            // Create two vertices for the ribbon width
            const v1 = points[i].clone().add(perpendicular.clone().multiplyScalar(width));
            const v2 = points[i].clone().add(perpendicular.clone().multiplyScalar(-width));

            vertices.push(v1.x, v1.y, v1.z);
            vertices.push(v2.x, v2.y, v2.z);

            // Add UV coordinates
            const t = i / (points.length - 1);
            uvs.push(0, t);
            uvs.push(1, t);
        }

        // Create faces (triangles) for the ribbon
        const indices = [];
        for (let i = 0; i < points.length - 1; i++) {
            const v1 = i * 2;
            const v2 = v1 + 1;
            const v3 = v1 + 2;
            const v4 = v1 + 3;

            // Create two triangles for each segment
            indices.push(v1, v2, v3);
            indices.push(v2, v4, v3);
        }

        // Create geometry
        const geometry = new THREE.BufferGeometry();
        geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
        geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
        geometry.setIndex(indices);

        // Create material with opacity gradient
        const material = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide
        });

        // Apply opacity gradient if trail should fade
        if (this.trailFade) {
            // Create a gradient texture
            const canvas = document.createElement('canvas');
            canvas.width = 256;
            canvas.height = 1;
            const context = canvas.getContext('2d');

            // Create gradient from opaque to transparent
            const gradient = context.createLinearGradient(0, 0, 256, 0);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
            gradient.addColorStop(1, 'rgba(255, 255, 255, 1)');

            context.fillStyle = gradient;
            context.fillRect(0, 0, 256, 1);

            const texture = new THREE.CanvasTexture(canvas);
            material.map = texture;
            material.transparent = true;
        }

        // Create mesh
        this.trailMesh = new THREE.Mesh(geometry, material);

        // PERFORMANCE: Add timestamp for lifetime tracking
        this.trailMesh.userData = this.trailMesh.userData || {};
        this.trailMesh.userData.createdAt = Date.now();

        this.scene.add(this.trailMesh);
    }

    /**
     * Create a particle-based trail
     * @param {Number} color - Trail color
     * @private
     */
    _createParticleTrail(color) {
        // Create a particle system for the trail
        const particleCount = this.trailPoints.length * 2;
        const particles = new THREE.BufferGeometry();

        const positions = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const colors = new Float32Array(particleCount * 3);

        // Convert hex color to RGB
        const r = (color >> 16 & 255) / 255;
        const g = (color >> 8 & 255) / 255;
        const b = (color & 255) / 255;

        // Create particles along the trail
        for (let i = 0; i < this.trailPoints.length; i++) {
            const i3 = i * 6; // Two particles per point, 3 values per particle
            const point = this.trailPoints[i];

            // First particle at the point
            positions[i3] = point.x;
            positions[i3 + 1] = point.y;
            positions[i3 + 2] = point.z;

            // Second particle with slight offset
            positions[i3 + 3] = point.x + (Math.random() - 0.5) * 0.1;
            positions[i3 + 4] = point.y + (Math.random() - 0.5) * 0.1;
            positions[i3 + 5] = point.z + (Math.random() - 0.5) * 0.1;

            // Particle sizes - smaller at the end of the trail
            const sizeFactor = i / this.trailPoints.length;
            sizes[i * 2] = 0.05 + sizeFactor * 0.1;
            sizes[i * 2 + 1] = 0.03 + sizeFactor * 0.08;

            // Particle colors - fade out at the end of the trail
            const opacity = this.trailFade ? sizeFactor : 1.0;

            // First particle color
            colors[i3] = r;
            colors[i3 + 1] = g;
            colors[i3 + 2] = b;

            // Second particle color (slightly different)
            colors[i3 + 3] = r * 0.9;
            colors[i3 + 4] = g * 0.9;
            colors[i3 + 5] = b * 0.9;
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        // Create particle material with lower opacity for faster visual fading
        const particleMaterial = new THREE.PointsMaterial({
            color: 0xFFFFFF, // Use white as base, actual colors come from the attribute
            size: 0.1,
            transparent: true,
            opacity: 0.5, // Reduced from 0.8 for faster visual fading
            vertexColors: true,
            blending: THREE.AdditiveBlending,
            sizeAttenuation: true
        });

        // Create particle system
        this.trailMesh = new THREE.Points(particles, particleMaterial);

        // PERFORMANCE: Add timestamp for lifetime tracking
        this.trailMesh.userData = this.trailMesh.userData || {};
        this.trailMesh.userData.createdAt = Date.now();

        this.scene.add(this.trailMesh);
    }

    /**
     * Create a flame-like trail (for fire projectiles)
     * @param {Number} color - Trail color
     * @private
     */
    _createFlameTrail(color) {
        // Similar to particle trail but with flame-like appearance
        const particleCount = this.trailPoints.length * 3; // More particles for flames
        const particles = new THREE.BufferGeometry();

        const positions = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const colors = new Float32Array(particleCount * 3);

        // Convert hex color to RGB
        const r = (color >> 16 & 255) / 255;
        const g = (color >> 8 & 255) / 255;
        const b = (color & 255) / 255;

        // Create flame particles along the trail
        for (let i = 0; i < this.trailPoints.length; i++) {
            const point = this.trailPoints[i];
            const normalizedPos = i / this.trailPoints.length;

            // Create multiple particles per point for a fuller flame
            for (let j = 0; j < 3; j++) {
                const idx = (i * 3 + j) * 3;

                // Add random offsets for flame-like appearance
                positions[idx] = point.x + (Math.random() - 0.5) * 0.15;
                positions[idx + 1] = point.y + (Math.random() - 0.5) * 0.15;
                positions[idx + 2] = point.z + (Math.random() - 0.5) * 0.15;

                // Larger particles at the start of the trail
                const sizeFactor = 1 - normalizedPos;
                sizes[i * 3 + j] = 0.1 + sizeFactor * 0.2;

                // Color gradient from yellow/white to red/orange
                const colorIdx = (i * 3 + j) * 3;
                if (normalizedPos < 0.3) {
                    // Start of trail: yellow/white
                    colors[colorIdx] = 1.0; // Red
                    colors[colorIdx + 1] = 0.9; // Green
                    colors[colorIdx + 2] = 0.5; // Blue
                } else if (normalizedPos < 0.7) {
                    // Middle of trail: orange
                    colors[colorIdx] = 1.0; // Red
                    colors[colorIdx + 1] = 0.5 - (normalizedPos - 0.3) * 0.5; // Green (fading)
                    colors[colorIdx + 2] = 0.0; // Blue
                } else {
                    // End of trail: red/dark
                    colors[colorIdx] = 1.0 - (normalizedPos - 0.7) * 2; // Red (fading)
                    colors[colorIdx + 1] = 0.0; // Green
                    colors[colorIdx + 2] = 0.0; // Blue
                }
            }
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        // Create particle material with flame texture and lower opacity
        const particleMaterial = new THREE.PointsMaterial({
            color: 0xFFFFFF,
            size: 0.15,
            transparent: true,
            opacity: 0.5, // Reduced from 0.8 for faster visual fading
            vertexColors: true,
            blending: THREE.AdditiveBlending,
            sizeAttenuation: true
        });

        // Create particle system
        this.trailMesh = new THREE.Points(particles, particleMaterial);

        // PERFORMANCE: Add timestamp for lifetime tracking
        this.trailMesh.userData = this.trailMesh.userData || {};
        this.trailMesh.userData.createdAt = Date.now();

        this.scene.add(this.trailMesh);
    }

    /**
     * Create an energy-like trail (for lightning/magic projectiles)
     * @param {Number} color - Trail color
     * @private
     */
    _createEnergyTrail(color) {
        // Create a line with energy-like appearance
        const points = this.trailPoints;

        // Create a slightly jagged path for energy effect
        const energyPoints = [];

        for (let i = 0; i < points.length; i++) {
            const point = points[i].clone();
            energyPoints.push(point);

            // Add extra points with slight offsets for jagged energy effect
            if (i < points.length - 1) {
                const nextPoint = points[i + 1];
                const midPoint = point.clone().lerp(nextPoint, 0.5);

                // Add random offset to midpoint
                midPoint.x += (Math.random() - 0.5) * 0.1;
                midPoint.y += (Math.random() - 0.5) * 0.1;
                midPoint.z += (Math.random() - 0.5) * 0.1;

                energyPoints.push(midPoint);
            }
        }

        // Create geometry from jagged points
        const geometry = new THREE.BufferGeometry().setFromPoints(energyPoints);

        // Create material with glow effect and lower opacity
        const material = new THREE.LineBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.5, // Reduced from 0.8 for faster visual fading
            linewidth: 2
        });

        // Create line
        this.trailMesh = new THREE.Line(geometry, material);

        // PERFORMANCE: Add timestamp for lifetime tracking
        this.trailMesh.userData = this.trailMesh.userData || {};
        this.trailMesh.userData.createdAt = Date.now();

        // Add secondary glow line for energy effect
        const glowGeometry = new THREE.BufferGeometry().setFromPoints(energyPoints);
        const glowMaterial = new THREE.LineBasicMaterial({
            color: 0xFFFFFF, // White core
            transparent: true,
            opacity: 0.3, // Reduced from 0.4 for faster visual fading
            linewidth: 1
        });

        const glowLine = new THREE.Line(glowGeometry, glowMaterial);

        // Also add timestamp to the glow line
        glowLine.userData = { createdAt: Date.now() };

        this.trailMesh.add(glowLine);

        this.scene.add(this.trailMesh);
    }

    /**
     * Create a water-like trail (for water/ice projectiles)
     * @param {Number} color - Trail color
     * @private
     */
    _createWaterTrail(color) {
        // Create a ribbon with droplet particles
        this._createRibbonTrail(color);

        // Add water droplet particles
        const particleCount = Math.min(this.trailPoints.length * 2, 20); // Limit particle count
        const particles = new THREE.BufferGeometry();

        const positions = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);

        // Create droplet particles along the trail
        for (let i = 0; i < particleCount; i++) {
            // Select a random point along the trail
            const pointIndex = Math.floor(Math.random() * this.trailPoints.length);
            const point = this.trailPoints[pointIndex];

            // Add random offsets for droplet effect
            positions[i * 3] = point.x + (Math.random() - 0.5) * 0.15;
            positions[i * 3 + 1] = point.y + (Math.random() - 0.5) * 0.15;
            positions[i * 3 + 2] = point.z + (Math.random() - 0.5) * 0.15;

            // Random sizes for droplets
            sizes[i] = 0.03 + Math.random() * 0.05;
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        // Create droplet material with lower opacity
        const dropletMaterial = new THREE.PointsMaterial({
            color: color,
            transparent: true,
            opacity: 0.4, // Reduced from 0.6 for faster visual fading
            size: 0.05,
            sizeAttenuation: true
        });

        // Create droplet system
        const droplets = new THREE.Points(particles, dropletMaterial);
        this.trailMesh.add(droplets);
    }

    /**
     * Create a shadow-like trail (for shadow/dark projectiles)
     * @param {Number} color - Trail color
     * @private
     */
    _createShadowTrail(color) {
        // Create a fading ribbon with smoky effect
        this._createRibbonTrail(color);

        // Make the ribbon fade out
        this.trailMesh.material.opacity = 0.5;

        // Add smoky particles
        const particleCount = Math.min(this.trailPoints.length * 2, 15);
        const particles = new THREE.BufferGeometry();

        const positions = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);

        // Create smoke particles along the trail
        for (let i = 0; i < particleCount; i++) {
            // Select a random point along the trail
            const pointIndex = Math.floor(Math.random() * this.trailPoints.length);
            const point = this.trailPoints[pointIndex];

            // Add random offsets for smoke effect
            positions[i * 3] = point.x + (Math.random() - 0.5) * 0.2;
            positions[i * 3 + 1] = point.y + (Math.random() - 0.5) * 0.2;
            positions[i * 3 + 2] = point.z + (Math.random() - 0.5) * 0.2;

            // Random sizes for smoke particles
            sizes[i] = 0.08 + Math.random() * 0.1;
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        // Create smoke material
        const smokeMaterial = new THREE.PointsMaterial({
            color: color,
            transparent: true,
            opacity: 0.3,
            size: 0.1,
            sizeAttenuation: true,
            blending: THREE.AdditiveBlending
        });

        // Create smoke system
        const smoke = new THREE.Points(particles, smokeMaterial);
        this.trailMesh.add(smoke);
    }

    /**
     * Orient projectile in direction of travel
     * @private
     */
    _orientWithVelocity() {
        if (!this.mesh || this.velocity.lengthSq() < 0.001) return;

        // Create a direction vector from velocity
        const direction = this.velocity.clone().normalize();

        // Create a quaternion that rotates from (0,0,1) to the direction vector
        const quaternion = new THREE.Quaternion();
        quaternion.setFromUnitVectors(new THREE.Vector3(0, 0, 1), direction);

        // Apply rotation
        this.mesh.quaternion.copy(quaternion);
    }

    /**
     * Recursively animate all children with animation functions
     * @param {THREE.Object3D} object - The object to animate children of
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _animateChildren(object, deltaTime) {
        if (!object || !object.children) return;

        // Process each child
        for (let i = 0; i < object.children.length; i++) {
            const child = object.children[i];

            // Run animation function if it exists
            if (child.userData && child.userData.animate) {
                const result = child.userData.animate(deltaTime);

                // If animation returns false, remove the child
                // This is used for temporary effects that should be removed after animation
                if (result === false) {
                    object.remove(child);
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(m => m.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                    i--; // Adjust index since we removed an item
                    continue;
                }
            }

            // Recursively animate grandchildren
            if (child.children && child.children.length > 0) {
                this._animateChildren(child, deltaTime);
            }
        }
    }

    /**
     * Destroy projectile
     */
    destroy() {
        // PERFORMANCE: Enhanced trail cleanup to prevent memory leaks
        if (this.trailMesh) {
            // Remove from scene
            this.scene.remove(this.trailMesh);

            // Dispose of geometry
            if (this.trailMesh.geometry) {
                this.trailMesh.geometry.dispose();
            }

            // Dispose of materials (handle both single and array materials)
            if (this.trailMesh.material) {
                if (Array.isArray(this.trailMesh.material)) {
                    this.trailMesh.material.forEach(material => {
                        if (material.map) material.map.dispose();
                        // Dispose of shader uniforms if they exist
                        if (material.uniforms) {
                            Object.keys(material.uniforms).forEach(key => {
                                const uniform = material.uniforms[key];
                                if (uniform.value && uniform.value.isTexture) {
                                    uniform.value.dispose();
                                }
                            });
                        }
                        material.dispose();
                    });
                } else {
                    if (this.trailMesh.material.map) this.trailMesh.material.map.dispose();
                    // Dispose of shader uniforms if they exist
                    if (this.trailMesh.material.uniforms) {
                        Object.keys(this.trailMesh.material.uniforms).forEach(key => {
                            const uniform = this.trailMesh.material.uniforms[key];
                            if (uniform.value && uniform.value.isTexture) {
                                uniform.value.dispose();
                            }
                        });
                    }
                    this.trailMesh.material.dispose();
                }
            }

            // Clean up any child objects (like particles in complex trails)
            if (this.trailMesh.children && this.trailMesh.children.length > 0) {
                for (let i = this.trailMesh.children.length - 1; i >= 0; i--) {
                    const child = this.trailMesh.children[i];

                    // Dispose of child geometries and materials
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => {
                                if (material.map) material.map.dispose();
                                // Dispose of shader uniforms if they exist
                                if (material.uniforms) {
                                    Object.keys(material.uniforms).forEach(key => {
                                        const uniform = material.uniforms[key];
                                        if (uniform.value && uniform.value.isTexture) {
                                            uniform.value.dispose();
                                        }
                                    });
                                }
                                material.dispose();
                            });
                        } else {
                            if (child.material.map) child.material.map.dispose();
                            // Dispose of shader uniforms if they exist
                            if (child.material.uniforms) {
                                Object.keys(child.material.uniforms).forEach(key => {
                                    const uniform = child.material.uniforms[key];
                                    if (uniform.value && uniform.value.isTexture) {
                                        uniform.value.dispose();
                                    }
                                });
                            }
                            child.material.dispose();
                        }
                    }

                    // Remove child from parent
                    this.trailMesh.remove(child);
                }
            }

            // Ensure the trail is completely removed from the scene
            if (this.scene && this.trailMesh && this.scene.getObjectById && this.scene.getObjectById(this.trailMesh.id)) {
                console.warn('Trail mesh still in scene after removal attempt, forcing removal');
                this.scene.remove(this.trailMesh);
            }

            // Clear reference
            this.trailMesh = null;
        }

        // Clear trail points array
        this.trailPoints = [];

        // Remove mesh and clean up procedural effects
        if (this.mesh) {
            // Recursively dispose of all children
            this._disposeObject(this.mesh);

            // Ensure the mesh is completely removed from the scene
            if (this.scene && this.mesh && this.scene.getObjectById && this.scene.getObjectById(this.mesh.id)) {
                console.warn('Projectile mesh still in scene, but should be removed by DungeonHandler');
                // Force removal as a safety measure
                this.scene.remove(this.mesh);
            }
            this.mesh = null;
        }

        // PERFORMANCE: Force garbage collection hint
        if (window.gc) {
            try {
                window.gc();
            } catch (e) {
                // Ignore if gc is not available
            }
        }
    }

    /**
     * Recursively dispose of an object and all its children
     * @param {THREE.Object3D} object - The object to dispose
     * @private
     */
    _disposeObject(object) {
        if (!object) return;

        // First, recursively dispose all children
        if (object.children && object.children.length > 0) {
            // Use a reverse loop since we're removing items
            for (let i = object.children.length - 1; i >= 0; i--) {
                this._disposeObject(object.children[i]);
                object.remove(object.children[i]);
            }
        }

        // Dispose of geometry
        if (object.geometry) {
            object.geometry.dispose();
        }

        // Dispose of materials
        if (object.material) {
            if (Array.isArray(object.material)) {
                object.material.forEach(material => {
                    this._disposeMaterial(material);
                });
            } else {
                this._disposeMaterial(object.material);
            }
        }

        // Remove any references in userData to help garbage collection
        if (object.userData) {
            // Clear animation functions
            if (object.userData.animate) {
                object.userData.animate = null;
            }

            // Clear any other references that might prevent garbage collection
            Object.keys(object.userData).forEach(key => {
                object.userData[key] = null;
            });
        }
    }

    /**
     * Dispose of a material and its resources
     * @param {THREE.Material} material - The material to dispose
     * @private
     */
    _disposeMaterial(material) {
        if (!material) return;

        // Dispose of textures
        if (material.map) material.map.dispose();
        if (material.normalMap) material.normalMap.dispose();
        if (material.specularMap) material.specularMap.dispose();
        if (material.emissiveMap) material.emissiveMap.dispose();
        if (material.alphaMap) material.alphaMap.dispose();
        if (material.aoMap) material.aoMap.dispose();
        if (material.displacementMap) material.displacementMap.dispose();
        if (material.metalnessMap) material.metalnessMap.dispose();
        if (material.roughnessMap) material.roughnessMap.dispose();
        if (material.bumpMap) material.bumpMap.dispose();
        if (material.envMap) material.envMap.dispose();
        if (material.lightMap) material.lightMap.dispose();

        // Dispose of shader uniforms if they exist
        if (material.uniforms) {
            Object.keys(material.uniforms).forEach(key => {
                const uniform = material.uniforms[key];
                if (uniform.value && uniform.value.isTexture) {
                    uniform.value.dispose();
                }
            });
        }

        // Finally dispose the material itself
        material.dispose();
    }
}
