/**
 * ProceduralEffects.js
 *
 * Provides procedural visual effects for projectiles without requiring image files.
 * All effects are generated programmatically using THREE.js capabilities.
 */

import * as THREE from 'three';

/**
 * Creates a procedural texture using a canvas
 * @param {Function} drawFunction - Function that draws on the canvas context
 * @param {Number} width - Canvas width
 * @param {Number} height - Canvas height
 * @returns {THREE.Texture} - The generated texture
 */
function createProceduralTexture(drawFunction, width = 128, height = 128) {
    // Create a canvas element
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;

    // Get the 2D context
    const context = canvas.getContext('2d');

    // Call the provided drawing function
    drawFunction(context, width, height);

    // Create a texture from the canvas
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    return texture;
}

/**
 * Creates a gradient texture
 * @param {Array} colorStops - Array of {offset, color} objects
 * @param {String} type - 'radial' or 'linear'
 * @returns {THREE.Texture} - The generated texture
 */
function createGradientTexture(colorStops, type = 'radial') {
    return createProceduralTexture((ctx, width, height) => {
        let gradient;

        if (type === 'radial') {
            gradient = ctx.createRadialGradient(
                width / 2, height / 2, 0,
                width / 2, height / 2, width / 2
            );
        } else {
            gradient = ctx.createLinearGradient(0, 0, width, 0);
        }

        // Add color stops
        colorStops.forEach(stop => {
            gradient.addColorStop(stop.offset, stop.color);
        });

        // Fill with gradient
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
    });
}

/**
 * Creates a noise texture
 * @param {Number} scale - Scale of the noise
 * @param {String} color - Base color
 * @returns {THREE.Texture} - The generated texture
 */
function createNoiseTexture(scale = 10, color = '#ffffff') {
    return createProceduralTexture((ctx, width, height) => {
        // Fill with base color
        ctx.fillStyle = color;
        ctx.fillRect(0, 0, width, height);

        // Create noise pattern
        const imageData = ctx.getImageData(0, 0, width, height);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
            const noise = Math.random() * scale - scale / 2;

            data[i] = Math.max(0, Math.min(255, data[i] + noise));     // R
            data[i+1] = Math.max(0, Math.min(255, data[i+1] + noise)); // G
            data[i+2] = Math.max(0, Math.min(255, data[i+2] + noise)); // B
        }

        ctx.putImageData(imageData, 0, 0);
    });
}

/**
 * Creates a particle texture
 * @param {String} shape - 'circle', 'square', 'star'
 * @param {String} color - Particle color
 * @returns {THREE.Texture} - The generated texture
 */
function createParticleTexture(shape = 'circle', color = '#ffffff') {
    return createProceduralTexture((ctx, width, height) => {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = width / 2;

        // Clear canvas
        ctx.clearRect(0, 0, width, height);

        // Set fill style
        ctx.fillStyle = color;

        // Draw shape
        switch (shape) {
            case 'square':
                ctx.fillRect(width * 0.2, height * 0.2, width * 0.6, height * 0.6);
                break;

            case 'star':
                const outerRadius = radius;
                const innerRadius = radius * 0.4;
                const spikes = 5;

                ctx.beginPath();
                for (let i = 0; i < spikes * 2; i++) {
                    const r = i % 2 === 0 ? outerRadius : innerRadius;
                    const angle = (Math.PI * 2 * i) / (spikes * 2);
                    const x = centerX + Math.cos(angle) * r;
                    const y = centerY + Math.sin(angle) * r;

                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.closePath();
                ctx.fill();
                break;

            case 'circle':
            default:
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.fill();
                break;
        }

        // Add gradient for softer edges
        const gradient = ctx.createRadialGradient(
            centerX, centerY, 0,
            centerX, centerY, radius
        );
        gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
        gradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.8)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

        ctx.globalCompositeOperation = 'source-in';
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
    });
}

/**
 * Creates a custom shader material with animated effects
 * @param {Object} options - Configuration options
 * @returns {THREE.ShaderMaterial} - The generated shader material
 */
function createAnimatedShaderMaterial(options = {}) {
    const {
        baseColor = new THREE.Color(0xffffff),
        pulseColor = new THREE.Color(0xffffff),
        pulseRate = 1.0,
        noiseStrength = 0.1,
        glowStrength = 0.5
    } = options;

    return new THREE.ShaderMaterial({
        uniforms: {
            time: { value: 0 },
            baseColor: { value: baseColor },
            pulseColor: { value: pulseColor },
            pulseRate: { value: pulseRate },
            noiseStrength: { value: noiseStrength },
            glowStrength: { value: glowStrength }
        },
        vertexShader: `
            varying vec2 vUv;
            varying vec3 vPosition;

            void main() {
                vUv = uv;
                vPosition = position;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `,
        fragmentShader: `
            uniform float time;
            uniform vec3 baseColor;
            uniform vec3 pulseColor;
            uniform float pulseRate;
            uniform float noiseStrength;
            uniform float glowStrength;

            varying vec2 vUv;
            varying vec3 vPosition;

            // Simple noise function
            float noise(vec2 p) {
                return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
            }

            void main() {
                // Calculate distance from center for radial effect
                float dist = length(vUv - vec2(0.5));

                // Create pulse effect
                float pulse = 0.5 + 0.5 * sin(time * pulseRate);

                // Add noise
                float noiseVal = noise(vUv * 10.0 + time) * noiseStrength;

                // Calculate alpha for edge glow
                float alpha = smoothstep(0.5, 0.0, dist + noiseVal);

                // Mix colors based on pulse
                vec3 finalColor = mix(baseColor, pulseColor, pulse * glowStrength);

                // Add noise to color
                finalColor += noiseVal;

                gl_FragColor = vec4(finalColor, alpha);
            }
        `,
        transparent: true,
        depthWrite: false
    });
}

/**
 * Creates a volumetric effect using layered meshes
 * @param {THREE.Color} coreColor - Color at the core
 * @param {THREE.Color} outerColor - Color at the outer edge
 * @param {Number} layers - Number of layers
 * @param {Number} size - Base size
 * @returns {THREE.Group} - Group containing the volumetric effect
 */
function createVolumetricEffect(coreColor, outerColor, layers = 5, size = 0.2) {
    const group = new THREE.Group();

    for (let i = 0; i < layers; i++) {
        const t = i / (layers - 1);
        const layerSize = size * (1.0 - t * 0.3);

        // Interpolate color
        const color = new THREE.Color().lerpColors(coreColor, outerColor, t);

        // Create geometry and material
        const geometry = new THREE.SphereGeometry(layerSize, 8, 8);
        const material = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.7 * (1.0 - t * 0.5),
            depthWrite: false
        });

        const layer = new THREE.Mesh(geometry, material);
        group.add(layer);
    }

    // Add animation function
    group.userData.animate = (delta) => {
        group.userData.time = (group.userData.time || 0) + delta;
        const time = group.userData.time;

        // Animate each layer
        group.children.forEach((layer, index) => {
            const t = index / (group.children.length - 1);
            const pulseFactor = 0.1 * Math.sin(time * 2 + t * Math.PI);

            layer.scale.set(
                1.0 + pulseFactor,
                1.0 + pulseFactor,
                1.0 + pulseFactor
            );
        });
    };

    return group;
}

/**
 * Creates a dynamic trail effect
 * @param {Array} points - Array of points for the trail
 * @param {THREE.Color} color - Trail color
 * @param {Number} width - Trail width
 * @returns {THREE.Mesh} - Mesh representing the trail
 */
function createDynamicTrail(points, color, width = 0.1) {
    if (points.length < 2) return null;

    // Create a curve from the points
    const curve = new THREE.CatmullRomCurve3(points);

    // Create tube geometry along the curve
    const geometry = new THREE.TubeGeometry(
        curve,
        points.length * 2, // tubular segments
        width,            // radius
        8,                // radial segments
        false             // closed
    );

    // Create gradient material
    const material = new THREE.ShaderMaterial({
        uniforms: {
            color: { value: color },
            time: { value: 0 }
        },
        vertexShader: `
            varying vec2 vUv;

            void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `,
        fragmentShader: `
            uniform vec3 color;
            uniform float time;
            varying vec2 vUv;

            void main() {
                // Create gradient along the trail
                float alpha = smoothstep(0.0, 0.2, vUv.x) * smoothstep(1.0, 0.8, vUv.x);

                // Add pulse effect
                alpha *= 0.7 + 0.3 * sin(vUv.x * 10.0 + time * 5.0);

                gl_FragColor = vec4(color, alpha);
            }
        `,
        transparent: true,
        depthWrite: false,
        side: THREE.DoubleSide
    });

    const mesh = new THREE.Mesh(geometry, material);

    // Add animation function
    mesh.userData.animate = (delta) => {
        mesh.material.uniforms.time.value += delta;
    };

    return mesh;
}

/**
 * Creates a lightning effect
 * @param {THREE.Vector3} start - Start position
 * @param {THREE.Vector3} end - End position
 * @param {Number} segments - Number of segments
 * @param {Number} jitter - Amount of randomness
 * @returns {THREE.Line} - Line representing the lightning
 */
function createLightningEffect(start, end, segments = 10, jitter = 0.2) {
    // Create points for the lightning bolt
    const points = [];
    for (let i = 0; i <= segments; i++) {
        const t = i / segments;

        // Interpolate between start and end
        const pos = new THREE.Vector3().lerpVectors(start, end, t);

        // Add randomness (except for start and end points)
        if (i > 0 && i < segments) {
            const perpendicular = new THREE.Vector3().crossVectors(
                new THREE.Vector3(0, 1, 0),
                new THREE.Vector3().subVectors(end, start).normalize()
            ).normalize();

            const binormal = new THREE.Vector3().crossVectors(
                perpendicular,
                new THREE.Vector3().subVectors(end, start).normalize()
            ).normalize();

            // Add random offset in perpendicular directions
            pos.add(perpendicular.multiplyScalar((Math.random() - 0.5) * jitter));
            pos.add(binormal.multiplyScalar((Math.random() - 0.5) * jitter));
        }

        points.push(pos);
    }

    // Create geometry from points
    const geometry = new THREE.BufferGeometry().setFromPoints(points);

    // Create material with glow effect
    const material = new THREE.LineBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.8
    });

    const lightning = new THREE.Line(geometry, material);

    // Add animation function to randomize the lightning
    lightning.userData.animate = (delta) => {
        lightning.userData.time = (lightning.userData.time || 0) + delta;

        // Every few frames, regenerate the lightning
        if (lightning.userData.time > 0.05) {
            lightning.userData.time = 0;

            const positions = lightning.geometry.attributes.position.array;

            // Update all points except start and end
            for (let i = 1; i < segments; i++) {
                const t = i / segments;

                // Interpolate between start and end
                const pos = new THREE.Vector3().lerpVectors(start, end, t);

                // Add randomness
                const perpendicular = new THREE.Vector3().crossVectors(
                    new THREE.Vector3(0, 1, 0),
                    new THREE.Vector3().subVectors(end, start).normalize()
                ).normalize();

                const binormal = new THREE.Vector3().crossVectors(
                    perpendicular,
                    new THREE.Vector3().subVectors(end, start).normalize()
                ).normalize();

                pos.add(perpendicular.multiplyScalar((Math.random() - 0.5) * jitter));
                pos.add(binormal.multiplyScalar((Math.random() - 0.5) * jitter));

                // Update position
                positions[i * 3] = pos.x;
                positions[i * 3 + 1] = pos.y;
                positions[i * 3 + 2] = pos.z;
            }

            lightning.geometry.attributes.position.needsUpdate = true;
        }
    };

    return lightning;
}

/**
 * Creates a shockwave effect
 * @param {THREE.Vector3} position - Center position
 * @param {THREE.Color} color - Shockwave color
 * @returns {THREE.Mesh} - Mesh representing the shockwave
 */
function createShockwaveEffect(position, color) {
    // Create ring geometry
    const geometry = new THREE.RingGeometry(0.1, 0.2, 32);

    // Create material with fade-out effect
    const material = new THREE.ShaderMaterial({
        uniforms: {
            color: { value: color },
            time: { value: 0 },
            maxTime: { value: 1.0 }
        },
        vertexShader: `
            varying vec2 vUv;

            void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `,
        fragmentShader: `
            uniform vec3 color;
            uniform float time;
            uniform float maxTime;
            varying vec2 vUv;

            void main() {
                float progress = time / maxTime;
                float alpha = (1.0 - progress) * smoothstep(0.0, 0.1, progress);
                gl_FragColor = vec4(color, alpha);
            }
        `,
        transparent: true,
        depthWrite: false,
        side: THREE.DoubleSide
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.copy(position);
    mesh.rotation.x = Math.PI / 2; // Make it horizontal

    // Add animation function
    mesh.userData.time = 0;
    mesh.userData.maxTime = 1.0;
    mesh.userData.animate = (delta) => {
        mesh.userData.time += delta;

        // Update shader time
        mesh.material.uniforms.time.value = mesh.userData.time;

        // Scale up the ring over time
        const progress = mesh.userData.time / mesh.userData.maxTime;
        const scale = 0.5 + progress * 3.0;
        mesh.scale.set(scale, scale, scale);

        // Return false when animation is complete to remove the mesh
        return mesh.userData.time < mesh.userData.maxTime;
    };

    return mesh;
}

/**
 * Creates a particle burst effect
 * @param {THREE.Vector3} position - Center position
 * @param {THREE.Color} color - Particle color
 * @param {Number} count - Number of particles
 * @returns {THREE.Points} - Points representing the particles
 */
function createParticleBurst(position, color, count = 20) {
    // Create geometry for particles
    const geometry = new THREE.BufferGeometry();

    // Create positions for each particle
    const positions = new Float32Array(count * 3);
    const velocities = [];

    for (let i = 0; i < count; i++) {
        // Random position around center
        const angle = Math.random() * Math.PI * 2;
        const radius = Math.random() * 0.1;

        positions[i * 3] = position.x;
        positions[i * 3 + 1] = position.y;
        positions[i * 3 + 2] = position.z;

        // Random velocity
        velocities.push(new THREE.Vector3(
            Math.cos(angle) * (0.5 + Math.random() * 0.5),
            Math.random() * 0.5,
            Math.sin(angle) * (0.5 + Math.random() * 0.5)
        ));
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

    // Create material
    const material = new THREE.PointsMaterial({
        color: color,
        size: 0.1,
        transparent: true,
        opacity: 0.8,
        depthWrite: false
    });

    const particles = new THREE.Points(geometry, material);

    // Add animation function
    particles.userData.time = 0;
    particles.userData.maxTime = 1.0;
    particles.userData.velocities = velocities;
    particles.userData.animate = (delta) => {
        particles.userData.time += delta;

        // Update particle positions
        const positions = particles.geometry.attributes.position.array;

        for (let i = 0; i < count; i++) {
            const velocity = particles.userData.velocities[i];

            // Apply velocity
            positions[i * 3] += velocity.x * delta;
            positions[i * 3 + 1] += velocity.y * delta;
            positions[i * 3 + 2] += velocity.z * delta;

            // Apply gravity
            velocity.y -= delta * 2.0;

            // Slow down over time
            velocity.multiplyScalar(0.98);
        }

        particles.geometry.attributes.position.needsUpdate = true;

        // Fade out
        const progress = particles.userData.time / particles.userData.maxTime;
        material.opacity = 0.8 * (1.0 - progress);

        // Return false when animation is complete to remove the particles
        return particles.userData.time < particles.userData.maxTime;
    };

    return particles;
}

// Export all functions
export {
    createProceduralTexture,
    createGradientTexture,
    createNoiseTexture,
    createParticleTexture,
    createAnimatedShaderMaterial,
    createVolumetricEffect,
    createDynamicTrail,
    createLightningEffect,
    createShockwaveEffect,
    createParticleBurst
};
