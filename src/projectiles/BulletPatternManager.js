/**
 * BulletPatternManager.js
 *
 * Defines and manages bullet patterns for the boss battle system.
 * Patterns are selected based on music intensity and spawned with parameters
 * that adapt to the music's energy.
 */

import * as THREE from 'three';
import { getProjectileType } from './ProjectileTypes.js';

export class BulletPatternManager {
    /**
     * Constructor for BulletPatternManager
     * @param {Object} dungeonHandler - Reference to the DungeonHandler
     * @param {Object} boss - Reference to the boss entity
     * @param {Object} options - Configuration options
     */
    constructor(dungeonHandler, boss, options = {}) {
        console.log("[BulletPatternManager] constructor called");
        console.log("[BulletPatternManager] DungeonHandler parameter:", dungeonHandler ? "exists" : "null");
        console.log("[BulletPatternManager] Boss parameter:", boss ? "exists" : "null");

        this.dungeonHandler = dungeonHandler;
        this.boss = boss;

        if (boss) {
            console.log("[BulletPatternManager] Boss position:", boss.position ? "exists" : "null");
            if (boss.position) {
                console.log(`[BulletPatternManager] Boss position: ${boss.position.x.toFixed(2)}, ${boss.position.y.toFixed(2)}, ${boss.position.z.toFixed(2)}`);
            }
        }

        // Configuration
        this.minIntensity = options.minIntensity || 0;
        this.maxIntensity = options.maxIntensity || 100;
        this.patternCooldown = options.patternCooldown || 3.0; // Seconds between pattern spawns (drastically increased from 2.0)
        this.currentCooldown = 0;
        this.lastPatternTime = 0;
        this.lastPatternType = null;

        // Timeline control
        this.useOnlyTimelinePatterns = options.useOnlyTimelinePatterns || false; // When true, only patterns from timeline will be used
        this.currentTimelinePattern = null; // Current pattern from timeline
        this.currentTimelineProjectileType = null; // Current projectile type from timeline

        // Enhanced pattern definitions with musical feature associations
        this.patterns = [
            // Catacomb Boss Patterns
            {
                name: "lantern_flail",
                minIntensity: 20,
                maxIntensity: 100,
                cooldown: 2.0, // Moderate cooldown for regular use
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: true,
                bestForDrop: true,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.6,
                speed: 0.7,
                complexity: 0.7
            },
            {
                name: "soul_fire_toss",
                minIntensity: 40,
                maxIntensity: 100,
                cooldown: 3.0, // Longer cooldown for more impactful attack
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: true,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.5,
                speed: 0.8,
                complexity: 0.8
            },
            {
                name: "big_fireball_charge",
                minIntensity: 60,
                maxIntensity: 100,
                cooldown: 5.0, // Long cooldown for this powerful attack
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: true,
                bestForDrop: true,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.3, // Low density (only one big projectile)
                speed: 0.6,  // Slower speed but more damage
                complexity: 0.9 // High complexity due to charging mechanic
            },
            {
                name: "sarcophagus_minions",
                minIntensity: 50,
                maxIntensity: 100,
                cooldown: 8.0, // Very long cooldown for summoning
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: true,
                bestForDrop: false,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.4, // Medium density
                speed: 0.5,  // Slow speed for summons
                complexity: 0.8 // High complexity for summoning mechanic
            },
            {
                name: "fire_circle_closure",
                minIntensity: 70,
                maxIntensity: 100,
                cooldown: 10.0, // Very long cooldown for this arena-wide effect
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: true,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.7, // High density for ring of fire
                speed: 0.4,  // Slow closing speed
                complexity: 1.0 // Maximum complexity for this special attack
            },
            {
                name: "grave_summon",
                minIntensity: 60,
                maxIntensity: 100,
                cooldown: 4.0, // Long cooldown for powerful summon
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: true,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.8,
                speed: 0.4,
                complexity: 0.9
            },
            {
                name: "chained_realm",
                minIntensity: 50,
                maxIntensity: 100,
                cooldown: 5.0, // Long cooldown for powerful effect
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: true,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.4,
                speed: 0.6,
                complexity: 1.0
            },
            // New FireKraken pattern
            {
                name: "fire_kraken",
                minIntensity: 40,
                maxIntensity: 100,
                cooldown: 2.5, // Longer cooldown for more impactful summons
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: true,
                bestForDrop: true,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.7,
                speed: 0.5,
                complexity: 0.9
            },
            // New summoning patterns
            {
                name: "alchemical_circle",
                minIntensity: 30,
                maxIntensity: 90,
                cooldown: 2.5, // Longer cooldown for more impactful summons
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: true,
                bestForDrop: true,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.6,
                speed: 0.4,
                complexity: 0.8
            },
            {
                name: "sigil_array",
                minIntensity: 40,
                maxIntensity: 100,
                cooldown: 3.0, // Longer cooldown for more impactful summons
                // Musical associations
                bestForArpeggio: true,
                bestForBuildUp: false,
                bestForDrop: true,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.5,
                speed: 0.3,
                complexity: 0.9
            },
            {
                name: "sacred_formation",
                minIntensity: 50,
                maxIntensity: 100,
                cooldown: 3.5, // Longer cooldown for more impactful summons
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: true,
                bestForDrop: true,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.4,
                speed: 0.2,
                complexity: 1.0
            },

            // Original patterns
            {
                name: "rapid_fire", // NEW PATTERN specifically for fast notes
                minIntensity: 30,
                maxIntensity: 100,
                cooldown: 0.4, // Fast cooldown for rapid firing (increased from 0.2)
                // Musical associations
                bestForArpeggio: true,   // Perfect for fast arpeggios
                bestForBuildUp: false,
                bestForDrop: false,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.9,     // Very high density (0-1)
                speed: 1.0,      // High speed (0-1)
                complexity: 0.5  // Medium complexity (0-1)
            },

            // New patterns
            {
                name: "spiral_bloom", // Bullets spiral out in a flower shape, then collapse inward
                minIntensity: 20,
                maxIntensity: 80,
                cooldown: 0.8,
                // Musical associations
                bestForArpeggio: true,
                bestForBuildUp: true,
                bestForDrop: false,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.7,
                speed: 0.6,
                complexity: 0.7
            },
            {
                name: "delay_accelerate", // Projectiles float harmlessly, then suddenly rocket forward
                minIntensity: 40,
                maxIntensity: 90,
                cooldown: 0.6,
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: true,
                bestForDrop: true,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.6,
                speed: 0.8,
                complexity: 0.7
            },
            {
                name: "fake_out_pulse", // Bullets pulse in and out, blinking off-screen
                minIntensity: 50,
                maxIntensity: 100,
                cooldown: 0.7,
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: true,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.7,
                speed: 0.9,
                complexity: 0.8
            },
            {
                name: "soul_magnetism", // Bullets home toward where player was a second ago
                minIntensity: 30,
                maxIntensity: 90,
                cooldown: 0.5,
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: false,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.5,
                speed: 0.7,
                complexity: 0.8
            },
            {
                name: "alchemical_chain", // Bullets with different elements that react with each other
                minIntensity: 60,
                maxIntensity: 100,
                cooldown: 0.6,
                // Musical associations
                bestForArpeggio: true,
                bestForBuildUp: true,
                bestForDrop: true,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.8,
                speed: 0.7,
                complexity: 0.9
            },
            {
                name: "time_freeze", // Slow orbs that freeze nearby bullets when they explode
                minIntensity: 40,
                maxIntensity: 90,
                cooldown: 1.0,
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: false,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.4,
                speed: 0.5,
                complexity: 0.8
            },
            {
                name: "spiral_rain", // Bullets fall from above but spiral sideways
                minIntensity: 30,
                maxIntensity: 80,
                cooldown: 0.7,
                // Musical associations
                bestForArpeggio: true,
                bestForBuildUp: true,
                bestForDrop: false,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.7,
                speed: 0.6,
                complexity: 0.7
            },
            {
                name: "shifting_polarity", // Bullets change color and behavior periodically
                minIntensity: 50,
                maxIntensity: 100,
                cooldown: 0.8,
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: true,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.6,
                speed: 0.7,
                complexity: 0.9
            },
            {
                name: "breath_trails", // Foggy trails that crystallize into bullets
                minIntensity: 20,
                maxIntensity: 70,
                cooldown: 0.9,
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: true,
                bestForDrop: false,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.5,
                speed: 0.4,
                complexity: 0.6
            },
            {
                name: "vortex_wings", // Butterfly-shaped patterns that flutter outward
                minIntensity: 40,
                maxIntensity: 90,
                cooldown: 0.7,
                // Musical associations
                bestForArpeggio: true,
                bestForBuildUp: false,
                bestForDrop: false,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.7,
                speed: 0.8,
                complexity: 0.8
            },
            {
                name: "divine_divide", // Wall of light that splits bullets in half
                minIntensity: 70,
                maxIntensity: 100,
                cooldown: 1.2,
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: true,
                bestForTransition: true,
                // Pattern characteristics
                density: 0.9,
                speed: 0.6,
                complexity: 1.0
            },
            {
                name: "petal_spread",
                minIntensity: 0,
                maxIntensity: 25,
                cooldown: 1.0, // BALANCED: Restored to original cooldown
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: false,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.3,     // Low density (0-1)
                speed: 0.4,      // Slow speed (0-1)
                complexity: 0.2  // Low complexity (0-1)
            },
            {
                name: "circle_ripple",
                minIntensity: 15,
                maxIntensity: 40,
                cooldown: 0.8, // BALANCED: Restored to original cooldown
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: true,   // Good for build-ups
                bestForDrop: false,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.5,     // Medium density
                speed: 0.5,      // Medium speed
                complexity: 0.4  // Medium-low complexity
            },
            {
                name: "spiral_wave",
                minIntensity: 25,
                maxIntensity: 60,
                cooldown: 0.7, // BALANCED: Restored to original cooldown
                // Musical associations
                bestForArpeggio: true,   // Perfect for arpeggios
                bestForBuildUp: false,
                bestForDrop: false,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.6,     // Medium-high density
                speed: 0.7,      // Medium-high speed
                complexity: 0.6  // Medium-high complexity
            },
            {
                name: "laser_grid",
                minIntensity: 50,
                maxIntensity: 75,
                cooldown: 0.6, // BALANCED: Restored to original cooldown
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: false,
                bestForTransition: true,  // Good for transitions
                // Pattern characteristics
                density: 0.7,     // High density
                speed: 0.6,      // Medium-high speed
                complexity: 0.7  // High complexity
            },
            {
                name: "boomerang_arcs",
                minIntensity: 40,
                maxIntensity: 70,
                cooldown: 0.7, // BALANCED: Restored to original cooldown
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: false,
                bestForTransition: true,  // Good for transitions
                // Pattern characteristics
                density: 0.5,     // Medium density
                speed: 0.8,      // High speed
                complexity: 0.8  // High complexity
            },
            {
                name: "inhale_exhale",
                minIntensity: 30,
                maxIntensity: 80,
                cooldown: 0.8, // BALANCED: Restored to original cooldown
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: true,   // Good for build-ups
                bestForDrop: true,      // Also good for drops
                bestForTransition: false,
                // Pattern characteristics
                density: 0.8,     // High density
                speed: 0.5,      // Medium speed
                complexity: 0.6  // Medium-high complexity
            },
            {
                name: "hellburst",
                minIntensity: 60,
                maxIntensity: 100,
                cooldown: 0.5, // BALANCED: Restored to original cooldown
                // Musical associations
                bestForArpeggio: false,
                bestForBuildUp: false,
                bestForDrop: true,      // Perfect for drops
                bestForTransition: false,
                // Pattern characteristics
                density: 1.0,     // Maximum density
                speed: 1.0,      // Maximum speed
                complexity: 0.9  // Very high complexity
            },
            {
                name: "lightning_chain",
                minIntensity: 40,
                maxIntensity: 100,
                cooldown: 0.3, // Fast cooldown for continuous chains
                // Musical associations
                bestForArpeggio: true,   // Perfect for fast arpeggios
                bestForBuildUp: true,    // Good for build-ups
                bestForDrop: false,
                bestForTransition: false,
                // Pattern characteristics
                density: 0.8,     // High density
                speed: 1.0,      // High speed
                complexity: 0.7  // Medium-high complexity
            }
        ];

        // Active bullets
        this.activeBullets = [];

        // Projectile types to use
        this.projectileTypes = [
            // Original projectiles
            'shadow_bolt',
            'fireball',
            'lightning_bolt',
            'poison_cloud',

            // New projectile types
            'ash_cinders',
            'eye_tears',
            'blood_threads',
            'wax_drips',
            'gold_coins',
            'thorn_seeds',
            'wind_spirits',
            'rust_flakes',
            'teeth',
            'halo_fragments',
            'screamer_skulls',
            'iron_chains',
            'lava_droplets',
            'shatter_spikes',
            'memory_echoes',
            'hex_strings',
            'eclipsed_suns'
        ];

        console.log("[BulletPatternManager] Created");
    }

    /**
     * Update the pattern manager
     * @param {Number} deltaTime - Time since last update
     * @param {Number} intensity - Current music intensity (0-100)
     * @param {Object} musicalFeatures - Detected musical features
     */
    update(deltaTime, intensity, musicalFeatures = {}) {
        // If we're using timeline patterns, we'll still update cooldowns but won't auto-select patterns
        // This allows the music sync to trigger patterns, but only those specified in the timeline

        // Update cooldown
        if (this.currentCooldown > 0) {
            this.currentCooldown -= deltaTime;
        }

        // Select and spawn pattern if cooldown is ready
        if (this.currentCooldown <= 0) {
            // PERFORMANCE: Randomly skip many pattern spawns to further reduce density
            // Higher chance to skip when FPS is low
            const skipChance = window.lastFps && window.lastFps < 30 ? 0.7 : 0.5;
            if (Math.random() < skipChance) {
                // Skip this pattern spawn and reset cooldown
                this.currentCooldown = this.patternCooldown * (0.5 + Math.random() * 0.5);
                return;
            }
            // ULTRA OPTIMIZATION: Severely limit projectiles to prevent lag
            const activeProjectiles = this.dungeonHandler.activeProjectiles ? this.dungeonHandler.activeProjectiles.length : 0;
            const maxProjectiles = 4; // BALANCED: Maximum number of projectiles to allow (drastically reduced from 8)

            // Get current FPS if available
            let currentFps = 60; // Default assumption
            if (window.lastFps) {
                currentFps = window.lastFps;
            }

            // Adjust max projectiles based on FPS
            const fpsAdjustedMax = currentFps < 30 ? 15 : maxProjectiles;

            if (activeProjectiles < fpsAdjustedMax) {
                // If using timeline patterns, only use the current timeline pattern
                let pattern;
                if (this.useOnlyTimelinePatterns && this.currentTimelinePattern) {
                    pattern = this.patterns.find(p => p.name === this.currentTimelinePattern);
                    if (!pattern) {
                        console.warn(`[BulletPatternManager] Timeline pattern not found: ${this.currentTimelinePattern}`);
                        return;
                    }
                } else {
                    // Pass musical features to pattern selection
                    // Note: Even when using auto-selected patterns, the projectile type will still
                    // be determined by the timeline's currentTimelineProjectileType if available
                    // (see _selectProjectileType method)
                    pattern = this._selectPatternBasedOnIntensity(intensity, musicalFeatures);
                }
                if (pattern) {
                    // Scale down the pattern size based on active projectiles and FPS
                    const projectileScaleFactor = Math.max(0.2, 1 - (activeProjectiles / maxProjectiles));
                    const fpsScaleFactor = Math.max(0.2, currentFps / 60);
                    let scaleFactor = Math.min(projectileScaleFactor, fpsScaleFactor);

                    // Adjust pattern parameters based on note change speed
                    let speedMultiplier = 1.0;
                    let densityMultiplier = 1.0;

                    // Extract note pattern information from musical features
                    const { noteChangeRate, noteChangeSpeed, patternComplexity } = musicalFeatures;

                    if (noteChangeSpeed > 0) {
                        // Significantly increase speed multiplier for fast notes
                        // Increase the multiplier range for more dramatic speed changes with fast notes
                        speedMultiplier = 1.0 + (noteChangeSpeed * 1.5); // 1.0-7.0 range for extreme responsiveness

                        // ENHANCED: Increase base density for fast notes to create more bullets
                        // Base density multiplier increased from 0.5 to 0.7 for more projectiles
                        densityMultiplier = 0.7;

                        // Adjust pattern density based on note change rate and complexity
                        // with enhanced density for fast notes to create more bullets
                        if (patternComplexity > 0.7) {
                            // Complex patterns: significant increase for complex note patterns
                            densityMultiplier = 0.7 + (noteChangeRate * 0.3); // 0.7-1.0 range
                        } else if (noteChangeRate > 0.8) {
                            // Very fast notes: increase density for more bullets
                            densityMultiplier = 0.9;
                        } else if (noteChangeRate > 0.5) {
                            // Moderately fast notes: moderate increase
                            densityMultiplier = 0.8;
                        }

                        // Check for specific musical features to further adjust density
                        if (musicalFeatures.isDrop) {
                            // Increase density during drops for more impact
                            densityMultiplier *= 1.2;
                            console.log(`[BulletPatternManager] Increasing density for drop: ${densityMultiplier.toFixed(2)}x`);
                        } else if (musicalFeatures.isBuildUp) {
                            // Gradually increase density during build-ups
                            const buildUpFactor = Math.min(1.0, intensity / 80); // 0-1 range based on intensity
                            densityMultiplier *= (1.0 + buildUpFactor * 0.3); // 1.0-1.3x multiplier
                            console.log(`[BulletPatternManager] Adjusting density for build-up: ${densityMultiplier.toFixed(2)}x`);
                        }

                        // For very fast notes, add an additional speed boost
                        if (noteChangeSpeed > 1.2) {
                            // Enhanced fast note bonus with lower threshold and higher multiplier
                            const fastNoteBonus = (noteChangeSpeed - 1.2) * 0.8; // Additional 0-2.4x for very fast notes
                            speedMultiplier += fastNoteBonus;
                            console.log(`[BulletPatternManager] Fast note bonus speed: +${fastNoteBonus.toFixed(2)}x`);

                            // For extremely fast notes, add even more bullets by increasing density
                            if (noteChangeSpeed > 2.5) {
                                const extremeSpeedBonus = Math.min(0.3, (noteChangeSpeed - 2.5) * 0.15); // Up to 0.3 additional density
                                densityMultiplier += extremeSpeedBonus;
                                console.log(`[BulletPatternManager] Extreme speed density bonus: +${extremeSpeedBonus.toFixed(2)}x`);
                            }
                        }

                        // Log adjustments
                        console.log(`[BulletPatternManager] Adjusting pattern for note speed: ${noteChangeSpeed.toFixed(2)}, ` +
                                   `Speed: ${speedMultiplier.toFixed(2)}x, Density: ${densityMultiplier.toFixed(2)}x`);
                    }

                    // Apply density multiplier to scale factor
                    scaleFactor *= densityMultiplier;

                    // BALANCED COOLDOWN: Set cooldown between patterns for a good balance
                    // Base cooldown multiplier set to 1.0 (normal cooldown timing)
                    const baseCooldownMultiplier = 1.0;

                    // Adjust for note speed with significantly more responsive cooldowns
                    // Enhanced cooldown reduction for fast notes to spawn patterns more frequently
                    const noteSpeedCooldownFactor = noteChangeSpeed > 0.8 ?
                        Math.max(0.4, 1.0 - (noteChangeSpeed - 0.8) * 0.5) : 1.0; // 0.4-1.0 range with lower threshold

                    const baseCooldown = pattern.cooldown * baseCooldownMultiplier * (1 - (intensity / 200)) * noteSpeedCooldownFactor;
                    const fpsCooldownMultiplier = currentFps < 30 ? 3 : (currentFps < 45 ? 2 : 1); // Normal multipliers
                    this.currentCooldown = baseCooldown * fpsCooldownMultiplier;

                    // Only spawn pattern if we're not lagging too much
                    if (currentFps > 15) {
                        this._spawnPattern(pattern, intensity, scaleFactor, speedMultiplier);
                        this.lastPatternTime = Date.now();
                        this.lastPatternType = pattern.name;

                        // Log pattern spawn for debugging
                        console.log(`[BulletPatternManager] Spawned pattern: ${pattern.name}, Intensity: ${Math.round(intensity)}, Active: ${activeProjectiles}, FPS: ${Math.round(currentFps)}`);
                    } else {
                        // FPS too low, skip pattern and increase cooldown
                        this.currentCooldown = 2.0;
                        console.warn(`[BulletPatternManager] FPS too low (${Math.round(currentFps)}), skipping pattern`);
                    }
                }
            } else {
                // Too many projectiles, increase cooldown to prevent lag
                this.currentCooldown = 2.0; // Increased from 1.0
                console.warn(`[BulletPatternManager] Too many active projectiles (${activeProjectiles}), delaying pattern spawn`);

                // ULTRA OPTIMIZATION: Clear some projectiles if we have too many
                if (activeProjectiles > maxProjectiles * 1.5 && this.dungeonHandler.activeProjectiles) {
                    // Remove oldest projectiles to reduce lag
                    const removeCount = Math.min(10, Math.floor(activeProjectiles - maxProjectiles));
                    if (removeCount > 0) {
                        console.warn(`[BulletPatternManager] Removing ${removeCount} oldest projectiles to reduce lag`);

                        // Get the oldest projectiles (first in the array)
                        const projectilesToRemove = this.dungeonHandler.activeProjectiles.slice(0, removeCount);

                        // Remove them from the scene
                        projectilesToRemove.forEach(projectileMesh => {
                            if (projectileMesh) {
                                // Call the projectile's destroy method to clean up trails
                                const projectile = projectileMesh.userData;
                                if (projectile && typeof projectile.destroy === 'function') {
                                    projectile.destroy();
                                }

                                // Remove from scene
                                if (projectileMesh.parent) {
                                    projectileMesh.parent.remove(projectileMesh);
                                }
                            }
                        });

                        // Remove them from the active projectiles array
                        this.dungeonHandler.activeProjectiles = this.dungeonHandler.activeProjectiles.slice(removeCount);
                    }
                }
            }
        }
    }

    /**
     * Select a pattern based on current intensity and musical features
     * @param {Number} intensity - Current music intensity (0-100)
     * @param {Object} musicalFeatures - Detected musical features
     * @returns {Object} Selected pattern or null
     * @private
     */
    _selectPatternBasedOnIntensity(intensity, musicalFeatures = {}) {
        // Extract all musical features for enhanced pattern selection
        const {
            isArpeggio, isBuildUp, isDrop, isTransition,
            noteChangeRate, noteChangeSpeed, patternComplexity,
            isStaccato, isLegato, isTremolo, isTrill
        } = musicalFeatures;

        // Expand intensity range slightly to ensure we always have patterns to choose from
        const expandedIntensity = {
            min: Math.max(0, intensity - 10),
            max: Math.min(100, intensity + 10)
        };

        // Filter patterns that match the current intensity (with expanded range)
        let matchingPatterns = this.patterns.filter(pattern =>
            expandedIntensity.min <= pattern.maxIntensity && expandedIntensity.max >= pattern.minIntensity
        );

        if (matchingPatterns.length === 0) {
            // Fallback to any pattern if we still don't have matches
            matchingPatterns = this.patterns;
            console.warn(`[BulletPatternManager] No patterns match intensity ${intensity}, using all patterns`);
        }

        // Enhanced scoring system that considers all musical features
        matchingPatterns = matchingPatterns.map(pattern => {
            let score = 0;

            // Base score from intensity match (0-10 points)
            const intensityMidpoint = (pattern.minIntensity + pattern.maxIntensity) / 2;
            const intensityMatchScore = 10 - Math.min(10, Math.abs(intensity - intensityMidpoint) / 5);
            score += intensityMatchScore;

            // Add score for primary musical features (0-15 points each)
            if (isArpeggio) {
                score += pattern.bestForArpeggio ? 15 : 0;

                // For arpeggios, also consider pattern complexity and speed
                if (pattern.complexity > 0.5 && noteChangeSpeed > 1.0) {
                    score += 5; // Bonus for complex patterns during fast arpeggios
                }
            }

            if (isBuildUp) {
                score += pattern.bestForBuildUp ? 15 : 0;

                // For build-ups, prefer patterns with increasing density
                if (pattern.density > 0.5) {
                    score += 5; // Bonus for dense patterns during build-ups
                }
            }

            if (isDrop) {
                score += pattern.bestForDrop ? 15 : 0;

                // For drops, prefer faster patterns
                if (pattern.speed > 0.7) {
                    score += 5; // Bonus for fast patterns during drops
                }
            }

            if (isTransition) {
                score += pattern.bestForTransition ? 15 : 0;
            }

            // Add score for secondary musical features (0-8 points each)
            if (isStaccato && pattern.speed > 0.6) score += 8;
            if (isLegato && pattern.speed < 0.5) score += 8;
            if (isTremolo && pattern.name.includes('ripple')) score += 8;
            if (isTrill && pattern.name.includes('spiral')) score += 8;

            // Add score for pattern characteristics matching note patterns
            if (noteChangeRate > 0.7 && pattern.complexity > 0.6) score += 5;
            if (noteChangeSpeed > 1.5 && pattern.speed > 0.7) score += 5;
            if (patternComplexity > 0.7 && pattern.complexity > 0.7) score += 5;

            // Slightly randomize score to prevent always choosing the same pattern
            // but keep the randomization small enough to not override important features
            score += Math.random() * 3;

            return { ...pattern, score };
        });

        // Sort by score (highest first)
        matchingPatterns.sort((a, b) => b.score - a.score);

        // Log top 3 patterns for debugging
        const topPatterns = matchingPatterns.slice(0, 3);
        console.log(`[BulletPatternManager] Top pattern scores:`,
            topPatterns.map(p => `${p.name}: ${p.score.toFixed(1)}`).join(', '));

        // If we have a high-scoring pattern, use it
        if (matchingPatterns[0].score > 10) {
            console.log(`[BulletPatternManager] Selected pattern ${matchingPatterns[0].name} based on musical features (score: ${matchingPatterns[0].score.toFixed(1)})`);
            return matchingPatterns[0];
        }

        // Fall back to intensity-based selection if no musical features detected
        // or if no patterns scored highly enough

        // Prefer patterns that match the intensity more closely
        matchingPatterns.sort((a, b) => {
            const aMidpoint = (a.minIntensity + a.maxIntensity) / 2;
            const bMidpoint = (b.minIntensity + b.maxIntensity) / 2;
            return Math.abs(intensity - aMidpoint) - Math.abs(intensity - bMidpoint);
        });

        // Avoid repeating the same pattern twice in a row
        if (matchingPatterns.length > 1 && matchingPatterns[0].name === this.lastPatternType) {
            return matchingPatterns[1];
        }

        return matchingPatterns[0];
    }

    /**
     * Spawn a bullet pattern
     * @param {Object} pattern - Pattern definition
     * @param {Number} intensity - Current music intensity (0-100)
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles (1.0 = normal)
     * @private
     */
    _spawnPattern(pattern, intensity, scaleFactor = 1.0, speedMultiplier = 1.0) {
        console.log(`[BulletPatternManager] _spawnPattern called with: pattern=${pattern ? pattern.name : 'null'}, intensity=${intensity}, scaleFactor=${scaleFactor}, speedMultiplier=${speedMultiplier}`);

        if (!this.boss || !this.dungeonHandler) {
            console.warn("[BulletPatternManager] Cannot spawn pattern: boss or dungeonHandler not available");
            return;
        }

        // Get boss position
        if (!this.boss) {
            console.error("[BulletPatternManager] Boss is null in _spawnPattern, trying to find it from dungeonHandler");
            // Try to get the boss from dungeonHandler
            if (this.dungeonHandler && this.dungeonHandler.activeBosses && this.dungeonHandler.activeBosses.length > 0) {
                this.boss = this.dungeonHandler.activeBosses[0];
                console.log("[BulletPatternManager] Found boss from dungeonHandler");
            } else {
                console.error("[BulletPatternManager] Could not find boss from dungeonHandler");
                return;
            }
        }

        if (!this.boss.position) {
            console.error("[BulletPatternManager] Boss position is undefined in _spawnPattern");
            return;
        }

        console.log(`[BulletPatternManager] Boss object: ${this.boss ? 'exists' : 'null'}, position: ${this.boss.position ? 'exists' : 'null'}`);
        const bossPosition = this.boss.position.clone();
        console.log(`[BulletPatternManager] Boss position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);

        // Normalize intensity to 0-1 for pattern parameters
        const normalizedIntensity = intensity / 100;

        // Select projectile type based on pattern and intensity
        const projectileType = this._selectProjectileType(pattern.name, intensity);

        // Get projectile data
        const projectileData = getProjectileType(projectileType);
        if (!projectileData) {
            console.error(`[BulletPatternManager] Projectile type '${projectileType}' not found`);
            return;
        }

        // Add anticipation effect before spawning pattern
        this._addPatternAnticipationEffect(pattern.name, bossPosition);

        // BALANCED OPTIMIZATION: Extremely limit the number of projectiles to prevent lag
        // BALANCED: Cap at 5% for maximum performance
        const performanceScaleFactor = Math.min(scaleFactor, 0.05); // Cap at 5% for performance (drastically reduced from 8%)

        try {
            // ULTRA OPTIMIZATION: Randomly select only one pattern type to spawn based on intensity
            // This prevents spawning multiple pattern types at once which can cause lag
            const rand = Math.random();

            // Get pattern name from pattern object
            const patternName = pattern.name;

            // Handle specific pattern types
            // Catacomb Boss patterns
            console.log(`[BulletPatternManager] Handling pattern: ${patternName}`);
            if (patternName === "lantern_flail") {
                // For lantern flail, check if we're in high intensity music
                // If so, we'll add additional cooldown to prevent too many projectiles
                if (intensity > 80) { // High intensity music (>80%)
                    console.log(`[BulletPatternManager] High intensity detected (${intensity.toFixed(1)}%) - Adding extra cooldown for lantern flail`);
                    // Add extra cooldown to prevent spawning too many projectiles
                    this.currentCooldown += 1.0; // Add 1 second to cooldown
                }

                this._spawnLanternFlail(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "soul_fire_toss") {
                this._spawnSoulFireToss(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "grave_summon") {
                this._spawnGraveSummon(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "chained_realm") {
                this._spawnChainedRealm(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "big_fireball_charge") {
                this._spawnBigFireballCharge(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "sarcophagus_minions") {
                this._spawnSarcophagusMinions(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "fire_circle_closure") {
                this._spawnFireCircleClosure(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "fire_kraken") {
                this._spawnFireKraken(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "spiral_bloom") {
                this._spawnSpiralBloom(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "delay_accelerate") {
                this._spawnDelayAccelerate(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "fake_out_pulse") {
                this._spawnFakeOutPulse(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "soul_magnetism") {
                this._spawnSoulMagnetism(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "alchemical_chain") {
                this._spawnAlchemicalChain(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "time_freeze") {
                this._spawnTimeFreeze(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "spiral_rain") {
                this._spawnSpiralRain(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "shifting_polarity") {
                // For now, use spiral wave as a substitute
                this._spawnSpiralWave(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, Date.now() / 1000, speedMultiplier);
                return;
            } else if (patternName === "breath_trails") {
                // For now, use inhale exhale as a substitute
                this._spawnInhaleExhale(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "vortex_wings") {
                // For now, use boomerang arcs as a substitute
                this._spawnBoomerangArcs(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, Date.now() / 1000, speedMultiplier);
                return;
            } else if (patternName === "divine_divide") {
                this._spawnDivineDivide(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "alchemical_circle") {
                this._spawnAlchemicalCircle(bossPosition, normalizedIntensity, projectileType || 'alchemical_symbols', performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "sigil_array") {
                this._spawnSigilArray(bossPosition, normalizedIntensity, projectileType || 'rotating_sigils', performanceScaleFactor, speedMultiplier);
                return;
            } else if (patternName === "sacred_formation") {
                this._spawnSacredFormation(bossPosition, normalizedIntensity, projectileType || 'sacred_geometry', performanceScaleFactor, speedMultiplier);
                return;
            }

            // Choose pattern based on intensity and randomness for original patterns
            if (intensity < 30) {
                // Low intensity - simple patterns
                // MODIFIED: Added laser_grid to ensure white projectiles appear
                if (rand < 0.4) { // Reduced from 0.5
                    this._spawnPetalSpread(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.6) { // Added laser_grid with 20% chance
                    this._spawnLaserGrid(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.8) {
                    this._spawnCircleRipple(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else {
                    // Add a chance for new patterns even at low intensity
                    this._spawnSpiralBloom(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                }
            } else if (intensity < 60) {
                // Medium intensity - moderate patterns
                // MODIFIED: Added lightning_chain for fast continuous parts
                if (rand < 0.15) {
                    this._spawnCircleRipple(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.3) { // Reduced laser_grid chance to 15%
                    this._spawnLaserGrid(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.45) { // Added lightning_chain with 15% chance
                    this._spawnLightningChain(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.6) {
                    this._spawnSpiralWave(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, Date.now() / 1000, speedMultiplier);
                } else if (rand < 0.75) {
                    this._spawnBoomerangArcs(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, Date.now() / 1000, speedMultiplier);
                } else if (rand < 0.85) {
                    this._spawnSpiralRain(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.95) {
                    this._spawnDelayAccelerate(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else {
                    this._spawnSoulMagnetism(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                }
            } else {
                // High intensity - complex patterns
                // MODIFIED: Added rapid_fire and lightning_chain patterns for fast continuous parts
                if (rand < 0.1) { // Reduced from 0.2
                    this._spawnLaserGrid(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.2) { // Added rapid_fire with 10% chance
                    this._spawnRapidFire(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.3) { // Lightning chain with 10% chance
                    this._spawnLightningChain(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.4) { // Adjusted to 10% chance
                    this._spawnSpiralWave(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, Date.now() / 1000, speedMultiplier);
                } else if (rand < 0.5) { // Adjusted to 10% chance
                    this._spawnInhaleExhale(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.6) { // 10% chance
                    this._spawnHellburst(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.7) { // 10% chance
                    this._spawnFakeOutPulse(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.8) { // 10% chance
                    this._spawnAlchemicalChain(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.85) { // 5% chance
                    this._spawnTimeFreeze(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else if (rand < 0.9) { // 5% chance
                    this._spawnDivineDivide(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                } else { // 10% chance
                    this._spawnFireKraken(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor, speedMultiplier);
                }
            }
        } catch (error) {
            console.error(`[BulletPatternManager] Error spawning pattern ${pattern.name}:`, error);
        }
    }

    /**
     * Select a projectile type based on pattern and intensity
     * @param {String} patternName - Pattern name
     * @param {Number} intensity - Current music intensity (0-100)
     * @returns {String} Projectile type
     * @private
     */
    _selectProjectileType(patternName, intensity) {
        // FIXED: Always use the timeline projectile type if available, regardless of useOnlyTimelinePatterns setting
        // This ensures consistent projectile types within each timeline section
        if (this.currentTimelineProjectileType) {
            console.log(`[BulletPatternManager] Using timeline projectile type: ${this.currentTimelineProjectileType}`);
            return this.currentTimelineProjectileType;
        }

        // Map patterns to preferred projectile types (only used if no timeline projectile is set)
        const patternToTypeMap = {
            // New summoning patterns
            "alchemical_circle": 'alchemical_symbols',
            "sigil_array": 'rotating_sigils',
            "sacred_formation": 'sacred_geometry',

            // Original patterns
            "rapid_fire": 'lightning_bolt', // Always use lightning bolt for rapid fire pattern
            "petal_spread": intensity < 20 ? 'shadow_bolt' : 'poison_cloud',
            "circle_ripple": 'shadow_bolt',
            "spiral_wave": intensity > 40 ? 'fireball' : 'shadow_bolt',
            "laser_grid": 'lightning_bolt',
            "boomerang_arcs": 'fireball',
            "inhale_exhale": intensity > 50 ? 'lightning_bolt' : 'shadow_bolt',
            "hellburst": intensity > 80 ? 'lightning_bolt' : 'fireball',
            "lightning_chain": 'lightning_bolt', // Always use lightning bolt for chain pattern

            // Catacomb Boss patterns with recommended projectile types
            "lantern_flail": 'fireball', // Use fireballs for the lantern flail
            "soul_fire_toss": 'lava_droplets', // Use lava droplets for soul fire
            "grave_summon": 'screamer_skulls', // Use screamer skulls for grave summon
            "chained_realm": 'iron_chains', // Use iron chains for the soul tether

            // New patterns with recommended projectile types
            "fire_kraken": 'lava_droplets', // Use lava droplets for fire tentacles
            "spiral_bloom": 'ash_cinders',
            "delay_accelerate": 'gold_coins',
            "fake_out_pulse": 'memory_echoes',
            "soul_magnetism": 'blood_threads',
            "alchemical_chain": 'fireball', // Will use different projectiles internally
            "time_freeze": 'memory_echoes',
            "spiral_rain": 'wax_drips',
            "shifting_polarity": 'lightning_bolt',
            "breath_trails": 'ash_cinders',
            "vortex_wings": 'wind_spirits',
            "divine_divide": 'halo_fragments'
        };

        const selectedType = patternToTypeMap[patternName] || this.projectileTypes[0];
        console.log(`[BulletPatternManager] Using default projectile type: ${selectedType} for pattern: ${patternName}`);
        return selectedType;
    }

    /**
     * Spawn a projectile with the given parameters
     * @param {THREE.Vector3} position - Start position
     * @param {THREE.Vector3} direction - Direction vector
     * @param {String} projectileType - Projectile type
     * @param {Number} speedMultiplier - Speed multiplier
     * @param {Object} [customProps] - Custom properties for the projectile
     * @returns {THREE.Object3D} The spawned projectile mesh, or null if spawn failed
     * @private
     */
    _spawnProjectile(position, direction, projectileType, speedMultiplier = 1.0, customProps = {}) {
        if (!this.boss || !this.boss.userData) {
            console.warn("[BulletPatternManager] Cannot spawn projectile: boss data not available");
            return null;
        }

        // Create a copy of boss userData for projectile spawning
        const projectileData = {
            ...this.boss.userData,
            projectileType: projectileType,
            projectileSpeed: (this.boss.userData.projectileSpeed || 8.0) * speedMultiplier,
            disableTrails: true // Always disable trails for boss projectiles
        };

        // Add any custom properties
        Object.keys(customProps).forEach(key => {
            projectileData[key] = customProps[key];
        });

        // Ensure disableTrails is set even if overridden by customProps
        projectileData.disableTrails = true;

        // Use dungeonHandler to spawn the projectile
        const projectile = this.dungeonHandler.spawnProjectile(
            position,
            direction,
            projectileData
        );

        return projectile;
    }

    /**
     * Spawn a directional petal spread pattern (ambient, peaceful in a 90-degree arc)
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnPetalSpread(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // BALANCED: Use 1-2 petals for a directional spread
        const basePetalCount = 1 + Math.floor(intensity * 1); // 1-2 petals for directional spread
        const petalCount = Math.max(1, Math.floor(basePetalCount * scaleFactor));

        // Get boss forward direction
        const bossForward = this._getBossForwardDirection();

        // Spawn petals in a 90-degree arc in front of the boss with better spacing
        for (let i = 0; i < petalCount; i++) {
            // Calculate angle within 90-degree arc (-45 to +45 degrees from forward)
            // For 2 projectiles, use wider spacing (-30 and +30 degrees)
            let angle;
            if (petalCount === 1) {
                angle = 0; // Single projectile goes straight forward
            } else if (petalCount === 2) {
                // Two projectiles: one at -30 degrees, one at +30 degrees
                angle = (i === 0) ? -Math.PI / 6 : Math.PI / 6; // +/- 30 degrees
            } else {
                // More than 2 projectiles: spread evenly across 90-degree arc
                angle = ((i / (petalCount - 1)) - 0.5) * Math.PI / 2;
            }

            // Calculate direction by rotating forward around up axis
            const direction = bossForward.clone();
            direction.applyAxisAngle(new THREE.Vector3(0, 1, 0), angle);

            // Add slight upward curve for petal effect
            direction.y = 0.1 * Math.sin(intensity * Math.PI); // Slight up/down based on intensity
            direction.normalize();

            // Add offset to starting position based on angle for better spacing
            const offsetDistance = 0.5 + i * 0.5; // Increasing distance for each projectile
            const startPos = position.clone().add(direction.clone().multiplyScalar(offsetDistance));

            // Spawn projectile with enhanced speed scaling
            this._spawnProjectile(
                startPos,
                direction,
                projectileType,
                (0.6 + (intensity * 0.5)) * speedMultiplier // Speed scales with intensity and multiplier
            );
        }
    }

    /**
     * Spawn a directional ripple pattern (pulsing beat in a 90-degree arc)
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles (1.0 = normal)
     * @private
     */
    _spawnCircleRipple(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // BALANCED: Absolute minimum number of projectiles to prevent lag
        const baseProjectileCount = 1; // Fixed at 1 projectile (reduced from 1-2)
        const projectileCount = 1; // Always use exactly 1 projectile

        // Get boss forward direction
        const bossForward = this._getBossForwardDirection();

        // Calculate right vector (perpendicular to forward)
        const right = new THREE.Vector3(1, 0, 0);
        if (Math.abs(bossForward.x) > 0.9) {
            // If forward is along X axis, use Z as right
            right.set(0, 0, 1);
        }
        right.crossVectors(bossForward, new THREE.Vector3(0, 1, 0)).normalize();

        // With only one projectile, always shoot straight forward
        for (let i = 0; i < projectileCount; i++) {
            // Calculate direction - straight forward
            const direction = bossForward.clone();

            // Spawn projectile with enhanced speed scaling
            this._spawnProjectile(
                position.clone().add(direction.clone().multiplyScalar(0.5)), // Start slightly in front
                direction,
                projectileType,
                (0.8 + (intensity * 0.7)) * speedMultiplier // Speed scales with intensity and multiplier
            );
        }
    }

    /**
     * Spawn a spiral wave pattern (melodic, evolving)
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnSpiralWave(position, intensity, projectileType, scaleFactor = 1.0, time = 0, speedMultiplier = 1.0) {
        // BALANCED: Absolute minimum number of arms and projectiles to prevent lag
        const baseArmCount = 1; // Fixed at 1 arm
        const baseProjectilesPerArm = 1; // Fixed at 1 projectile per arm

        const armCount = Math.max(1, Math.floor(baseArmCount * scaleFactor));
        const projectilesPerArm = Math.max(1, Math.floor(baseProjectilesPerArm * scaleFactor));

        // Use provided time or current time for spiral animation
        const currentTime = time || Date.now() / 1000;

        // Spawn spiral arms
        for (let arm = 0; arm < armCount; arm++) {
            const armOffset = (arm / armCount) * Math.PI * 2;

            for (let i = 0; i < projectilesPerArm; i++) {
                // Calculate spiral angle
                const distance = 0.5 + (i / projectilesPerArm) * 2.0; // Distance from center
                const angle = armOffset + (i / projectilesPerArm) * Math.PI * 2 * (1 + intensity); // Spiral angle

                // Calculate direction with spiral motion
                const direction = new THREE.Vector3(
                    Math.cos(angle + currentTime * 2),
                    0.05 * Math.sin(currentTime * 3), // Slight up/down wave
                    Math.sin(angle + currentTime * 2)
                ).normalize();

                // Spawn projectile with enhanced speed scaling
                this._spawnProjectile(
                    position.clone().add(direction.clone().multiplyScalar(0.2 * i)), // Start in spiral formation
                    direction,
                    projectileType,
                    (0.6 + (intensity * 0.8)) * speedMultiplier // Speed scales with intensity and multiplier
                );
            }
        }
    }

    /**
     * Spawn a laser grid pattern (harsh high-frequency)
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnLaserGrid(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // BALANCED: Reduced grid size
        const baseGridSize = 2; // Fixed at 2x2 grid (reduced from 2-3)
        const gridSize = Math.max(2, Math.floor(baseGridSize * scaleFactor));

        // Spawn grid of projectiles
        for (let x = 0; x < gridSize; x++) {
            for (let z = 0; z < gridSize; z++) {
                // BALANCED: Skip more projectiles for a less dense pattern
                // Skip more when scaleFactor is low to reduce lag
                if ((x + z) % 2 === 0 || Math.random() > (0.5 * scaleFactor)) continue;

                // Calculate normalized position in grid (-1 to 1)
                const nx = (x / (gridSize - 1)) * 2 - 1;
                const nz = (z / (gridSize - 1)) * 2 - 1;

                // Calculate direction (away from center)
                const direction = new THREE.Vector3(nx, 0, nz).normalize();

                // Spawn projectile with enhanced speed scaling
                this._spawnProjectile(
                    position.clone(),
                    direction,
                    projectileType,
                    (1.0 + (intensity * 1.0)) * speedMultiplier // Speed scales with intensity and multiplier
                );
            }
        }
    }

    /**
     * Spawn a lightning chain pattern (for fast continuous parts)
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnLightningChain(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Number of chains and projectiles per chain - reduced by half for performance
        const chainCount = Math.max(1, Math.floor((1 + Math.floor(intensity * 2)) / 2)); // 1-2 chains (reduced from 1-3)
        const projectilesPerChain = Math.max(2, Math.floor((3 + Math.floor(intensity * 5)) / 2)); // 2-4 projectiles per chain (reduced from 3-8)

        // Time offset for animation
        const now = Date.now() / 1000;

        // Spawn multiple chains
        for (let c = 0; c < chainCount; c++) {
            // Calculate base angle for this chain
            const baseAngle = (c / chainCount) * Math.PI * 2 + (now % 1.0) * Math.PI;

            // Calculate chain parameters
            const chainLength = 3.0 + intensity * 4.0; // 3.0-7.0 units long
            const chainSpread = 0.2 + intensity * 0.3; // 0.2-0.5 units between zigzags

            // Previous position for chaining
            let prevPos = position.clone();

            // Spawn projectiles in a chain
            for (let i = 0; i < projectilesPerChain; i++) {
                // Calculate position along chain with zigzag pattern
                const distance = (i / projectilesPerChain) * chainLength;
                const zigzag = Math.sin(i * 2) * chainSpread;

                // Calculate direction with zigzag
                const angle = baseAngle + (zigzag * Math.PI / 8);
                const direction = new THREE.Vector3(
                    Math.cos(angle),
                    0.1 * Math.sin(i * 0.5), // Slight up/down movement
                    Math.sin(angle)
                ).normalize();

                // Calculate position
                const newPos = position.clone().add(
                    direction.clone().multiplyScalar(distance)
                );

                // Calculate direction from previous position to new position
                const chainDirection = newPos.clone().sub(prevPos).normalize();

                // Spawn projectile with enhanced speed scaling
                this._spawnProjectile(
                    prevPos.clone(),
                    chainDirection,
                    projectileType,
                    (1.0 + (intensity * 1.5)) * speedMultiplier // Speed scales with intensity and multiplier
                );

                // Update previous position
                prevPos = newPos;
            }
        }
    }

    /**
     * Spawn boomerang arcs pattern (dancey curved bullets)
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @private
     */
    _spawnBoomerangArcs(position, intensity, projectileType, scaleFactor = 1.0, time = 0, speedMultiplier = 1.0) {
        // BALANCED: Moderate number of arcs and projectiles
        const arcCount = 1 + Math.floor(intensity * 1); // 1-2 arcs (was 1, originally 2-6)
        const projectilesPerArc = 3 + Math.floor(intensity * 2); // 3-5 projectiles per arc (was 1-2, originally 5-12)

        // Use provided time or current time for animation
        const currentTime = time || Date.now() / 1000;

        // Spawn arcs
        for (let arc = 0; arc < arcCount; arc++) {
            const arcOffset = (arc / arcCount) * Math.PI * 2;

            for (let i = 0; i < projectilesPerArc; i++) {
                // Calculate arc angle
                const arcProgress = i / (projectilesPerArc - 1); // 0 to 1
                const angle = arcOffset + arcProgress * Math.PI * 0.5; // 90-degree arc

                // Calculate direction with curved motion
                const direction = new THREE.Vector3(
                    Math.cos(angle + currentTime),
                    0.1 * Math.sin(arcProgress * Math.PI), // Arc upward
                    Math.sin(angle + currentTime)
                ).normalize();

                // Spawn projectile with enhanced speed scaling
                this._spawnProjectile(
                    position.clone(),
                    direction,
                    projectileType,
                    (0.8 + (intensity * 0.7)) * speedMultiplier // Speed scales with intensity and multiplier
                );
            }
        }
    }

    /**
     * Spawn a rapid fire pattern (fast stream of bullets in multiple directions)
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnRapidFire(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Number of streams and projectiles per stream - reduced by half for performance
        const streamCount = Math.max(1, Math.floor((2 + Math.floor(intensity * 3)) / 2)); // 1-3 streams (reduced from 2-5)
        const projectilesPerStream = Math.max(2, Math.floor((3 + Math.floor(intensity * 4)) / 2)); // 2-4 projectiles per stream (reduced from 3-7)

        // Time offset for animation
        const now = Date.now() / 1000;

        // Spawn multiple streams
        for (let s = 0; s < streamCount; s++) {
            // Calculate base angle for this stream with time-based rotation
            const baseAngle = (s / streamCount) * Math.PI * 2 + (now % 1.0) * Math.PI * 2;

            // Calculate stream parameters
            const streamLength = 4.0 + intensity * 3.0; // 4.0-7.0 units long
            const streamSpread = 0.1 + intensity * 0.1; // 0.1-0.2 units between projectiles

            // Spawn projectiles in a stream with increasing speed
            for (let i = 0; i < projectilesPerStream; i++) {
                // Calculate position along stream
                const distance = (i / projectilesPerStream) * streamLength;

                // Add slight randomness to angle for more natural look
                const angleVariation = (Math.random() - 0.5) * streamSpread;
                const angle = baseAngle + angleVariation;

                // Calculate direction
                const direction = new THREE.Vector3(
                    Math.cos(angle),
                    0.05 * Math.sin(now * 3 + i), // Slight up/down movement
                    Math.sin(angle)
                ).normalize();

                // Calculate position
                const spawnPos = position.clone().add(
                    direction.clone().multiplyScalar(distance * 0.2)
                );

                // Increase speed for projectiles further along the stream
                const positionBasedSpeed = 1.0 + (i / projectilesPerStream) * 1.5; // 1.0-2.5x speed increase

                // Spawn projectile with enhanced speed scaling
                this._spawnProjectile(
                    spawnPos,
                    direction,
                    projectileType,
                    (1.2 + (intensity * 1.8)) * speedMultiplier * positionBasedSpeed // Very high speed scaling
                );
            }
        }

        // Create visual effect
        if (this.dungeonHandler.createParticleEffect) {
            this.dungeonHandler.createParticleEffect('electric_spark', position, 0.7, 150);
        }
    }

    /**
     * Spawn inhale-exhale pattern (bullets pulse inward/outward)
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @private
     */
    _spawnInhaleExhale(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // BALANCED: Moderate number of projectiles
        const projectileCount = 4 + Math.floor(intensity * 4); // 4-8 projectiles (was 2-3, originally 12-32)

        // Use current time to determine inhale/exhale
        const currentTime = Date.now() / 1000;
        const isExhale = Math.sin(currentTime * 2) > 0;

        // Spawn projectiles in a sphere
        for (let i = 0; i < projectileCount; i++) {
            // Calculate spherical coordinates
            const theta = Math.random() * Math.PI * 2; // Horizontal angle
            const phi = Math.acos(2 * Math.random() - 1); // Vertical angle

            // Convert to Cartesian coordinates
            const direction = new THREE.Vector3(
                Math.sin(phi) * Math.cos(theta),
                Math.sin(phi) * Math.sin(theta),
                Math.cos(phi)
            ).normalize();

            // Reverse direction for inhale
            if (!isExhale) {
                direction.negate();
            }

            // Spawn projectile with enhanced speed scaling
            this._spawnProjectile(
                isExhale ? position.clone() : position.clone().add(direction.clone().multiplyScalar(5)),
                direction,
                projectileType,
                (0.7 + (intensity * 0.9)) * speedMultiplier // Speed scales with intensity and multiplier
            );
        }
    }

    /**
     * Spawn hellburst pattern (full chaos mode)
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnHellburst(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // BALANCED: Use 1-2 projectiles for a directional hellburst
        const baseProjectileCount = 1 + Math.floor(intensity * 1); // 1-2 projectiles for directional burst
        const projectileCount = Math.max(1, Math.floor(baseProjectileCount * scaleFactor));

        // Get boss forward direction
        const bossForward = this._getBossForwardDirection();

        // Calculate right vector (perpendicular to forward)
        const right = new THREE.Vector3();
        right.crossVectors(bossForward, new THREE.Vector3(0, 1, 0)).normalize();

        // Calculate up vector
        const up = new THREE.Vector3(0, 1, 0);

        // Spawn projectiles in a 90-degree cone in front of the boss with better spacing
        for (let i = 0; i < projectileCount; i++) {
            let direction;

            if (projectileCount === 1 || i === 0) {
                // Single projectile or center projectile - straight forward
                direction = bossForward.clone();
            } else if (projectileCount === 2 && i === 1) {
                // For 2 projectiles, second one goes to the right
                direction = bossForward.clone();
                direction.applyAxisAngle(up, -Math.PI / 6); // -30 degrees
            } else if (i === 1) {
                // Left projectile - 30 degrees to the left
                direction = bossForward.clone();
                direction.applyAxisAngle(up, Math.PI / 6); // 30 degrees
            } else {
                // Right projectile - 30 degrees to the right
                direction = bossForward.clone();
                direction.applyAxisAngle(up, -Math.PI / 6); // -30 degrees
            }

            // Add slight random variation
            direction.x += (Math.random() * 0.2 - 0.1);
            direction.y += (Math.random() * 0.2 - 0.1) * 0.5; // Less vertical spread
            direction.z += (Math.random() * 0.2 - 0.1);
            direction.normalize();

            // Add offset to starting position based on index for better spacing
            // Each projectile starts at a different distance from the boss
            const offsetDistance = 0.5 + i * 1.5; // Significant distance between projectiles
            const startPos = position.clone().add(direction.clone().multiplyScalar(offsetDistance));

            // Spawn projectile with enhanced speed scaling
            this._spawnProjectile(
                startPos,
                direction,
                projectileType,
                (1.2 + (intensity * 1.5)) * speedMultiplier // Speed scales with intensity and multiplier
            );
        }
    }

    /**
     * Spawn an alchemical circle pattern
     * Summons alchemical symbols in a circle around the boss
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnAlchemicalCircle(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Number of symbols to summon
        const symbolCount = 2 + Math.floor(intensity * 2); // 2-4 symbols (reduced from 4-8)

        // Radius of the circle
        const radius = 3.0 + intensity * 2.0; // 3-5 units radius

        // Create symbols in a circle around the boss with better spacing
        for (let i = 0; i < symbolCount; i++) {
            // Calculate position in circle with varied radius for each symbol
            // This ensures they don't all appear at the same distance
            const individualRadius = radius * (0.8 + 0.4 * Math.random()); // 80-120% of base radius
            const angle = (i / symbolCount) * Math.PI * 2;

            // Add height variation for each symbol
            const heightVariation = 0.5 + i * 0.3; // Each symbol floats at a different height

            const symbolPosition = new THREE.Vector3(
                position.x + Math.cos(angle) * individualRadius,
                position.y + heightVariation, // Varied height above ground
                position.z + Math.sin(angle) * individualRadius
            );

            // Calculate direction (toward center with variation)
            // Add slight sideways component for more interesting movement
            const sidewaysAngle = angle + Math.PI / 4; // 45-degree offset for sideways movement
            const sidewaysStrength = 0.3; // How much sideways movement to add

            const direction = new THREE.Vector3()
                .subVectors(position, symbolPosition)
                .normalize();

            // Add sideways component
            direction.x += Math.cos(sidewaysAngle) * sidewaysStrength;
            direction.z += Math.sin(sidewaysAngle) * sidewaysStrength;

            // Add upward component
            direction.y = 0.2 + 0.1 * i; // Different upward component for each symbol
            direction.normalize();

            // Spawn the alchemical symbol
            this._spawnProjectile(
                symbolPosition,
                direction,
                projectileType,
                speedMultiplier * (0.3 + intensity * 0.3) // Slower speed for summoned objects
            );
        }
    }

    /**
     * Spawn a sigil array pattern
     * Summons rotating sigils in a geometric pattern
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnSigilArray(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Number of sigils to summon
        const sigilCount = 1 + Math.floor(intensity * 2); // 1-3 sigils (reduced from 3-6)

        // Get boss forward direction
        const bossForward = this._getBossForwardDirection();

        // Create a triangular or pentagram formation
        const isTriangle = Math.random() < 0.5;
        const points = [];

        if (isTriangle) {
            // Triangular formation
            const triangleRadius = 4.0;
            for (let i = 0; i < 3; i++) {
                const angle = (i / 3) * Math.PI * 2;
                points.push(new THREE.Vector3(
                    position.x + Math.cos(angle) * triangleRadius,
                    position.y + 0.5, // Slightly above ground
                    position.z + Math.sin(angle) * triangleRadius
                ));
            }

            // Add center point
            points.push(position.clone().add(new THREE.Vector3(0, 0.5, 0)));
        } else {
            // Pentagram formation
            const pentagramRadius = 4.0;
            for (let i = 0; i < 5; i++) {
                const angle = (i / 5) * Math.PI * 2;
                points.push(new THREE.Vector3(
                    position.x + Math.cos(angle) * pentagramRadius,
                    position.y + 0.5, // Slightly above ground
                    position.z + Math.sin(angle) * pentagramRadius
                ));
            }

            // Add center point
            points.push(position.clone().add(new THREE.Vector3(0, 0.5, 0)));
        }

        // Spawn sigils at the calculated points with better spacing
        for (let i = 0; i < Math.min(sigilCount, points.length); i++) {
            // Add height variation to each sigil
            const heightOffset = i * 0.4; // Each sigil floats at a different height
            const sigilPosition = points[i].clone();
            sigilPosition.y += heightOffset;

            // Add slight position variation to avoid perfect alignment
            const posVariation = 0.3;
            sigilPosition.x += (Math.random() - 0.5) * posVariation;
            sigilPosition.z += (Math.random() - 0.5) * posVariation;

            // Calculate direction with more variation between sigils
            // Each sigil moves in a slightly different direction
            const directionVariation = i * 0.2; // Increasing variation for each sigil
            const direction = new THREE.Vector3(
                (Math.random() - 0.5) * (0.5 + directionVariation), // Varied x component
                0.8 + (i * 0.1),   // Different upward component for each sigil
                (Math.random() - 0.5) * (0.5 + directionVariation)  // Varied z component
            ).normalize();

            // Spawn the sigil with varied speed
            this._spawnProjectile(
                sigilPosition,
                direction,
                projectileType,
                speedMultiplier * (0.2 + intensity * 0.2 + (i * 0.05)) // Slightly different speed for each sigil
            );
        }
    }

    /**
     * Spawn a sacred formation pattern
     * Summons sacred geometry objects in a complex formation
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnSacredFormation(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Number of geometry objects to summon
        const objectCount = 1 + Math.floor(intensity * 2); // 1-3 objects (reduced from 3-6)

        // Create a complex 3D formation with better spacing
        // First, create a central object at a higher position
        const centerHeight = 1.5 + intensity * 0.5; // Higher central object based on intensity
        const centerPosition = position.clone().add(new THREE.Vector3(0, centerHeight, 0));

        // Spawn the central object with slight movement
        this._spawnProjectile(
            centerPosition,
            new THREE.Vector3(
                (Math.random() - 0.5) * 0.1, // Slight random horizontal drift
                0.1 + Math.random() * 0.1,   // Slight upward movement
                (Math.random() - 0.5) * 0.1  // Slight random horizontal drift
            ).normalize(),
            projectileType,
            speedMultiplier * 0.1 // Extremely slow for central object
        );

        // Create orbiting objects with varied distances
        // Each orbiting object will have a unique radius
        const baseOrbitRadius = 2.5 + intensity * 1.5; // 2.5-4.0 units base radius

        for (let i = 1; i < objectCount; i++) {
            // Each object has a different orbit radius
            const individualRadius = baseOrbitRadius * (0.7 + (i * 0.3)); // Increasing radius for each object

            // Calculate position in 3D space around center
            // Use golden ratio for more aesthetic distribution
            const phi = Math.acos(1 - 2 * (i / (objectCount - 1)));
            const theta = Math.PI * (1 + Math.sqrt(5)) * i;

            // Add height variation
            const heightVariation = (i % 2 === 0) ? 0.5 : -0.5; // Alternate above/below center

            const orbitPosition = new THREE.Vector3(
                centerPosition.x + individualRadius * Math.sin(phi) * Math.cos(theta),
                centerPosition.y + heightVariation, // Varied height
                centerPosition.z + individualRadius * Math.sin(phi) * Math.sin(theta)
            );

            // Calculate direction (toward center with orbital component)
            const toCenter = new THREE.Vector3()
                .subVectors(centerPosition, orbitPosition)
                .normalize();

            // Add orbital component perpendicular to toCenter
            // Each object has a different orbital vs. center ratio
            const orbitalRatio = 0.6 + (i * 0.1); // Increasing orbital component for each object
            const orbital = new THREE.Vector3()
                .crossVectors(toCenter, new THREE.Vector3(0, 1, 0))
                .normalize()
                .multiplyScalar(orbitalRatio);

            const direction = new THREE.Vector3()
                .addVectors(toCenter.multiplyScalar(1 - orbitalRatio), orbital)
                .normalize();

            // Spawn the orbiting object with varied speed
            this._spawnProjectile(
                orbitPosition,
                direction,
                projectileType,
                speedMultiplier * (0.3 + intensity * 0.2 + (i * 0.05)) // Slightly different speed for each object
            );
        }
    }

    /**
     * Get the boss forward direction vector
     * @returns {THREE.Vector3} Forward direction vector
     * @private
     */
    _getBossForwardDirection() {
        // Default direction (negative Z - toward player spawn)
        const defaultDirection = new THREE.Vector3(0, 0, -1);

        // Try to get actual boss direction if available
        if (this.dungeonHandler && this.dungeonHandler.activeBosses && this.dungeonHandler.activeBosses.length > 0) {
            const boss = this.dungeonHandler.activeBosses[0];
            if (boss && boss.userData && boss.userData.forward) {
                return boss.userData.forward.clone();
            }

            // If no forward vector but boss has rotation, calculate forward from rotation
            if (boss && boss.rotation) {
                // Create a forward vector (negative Z) and apply the boss's rotation
                const forward = new THREE.Vector3(0, 0, -1);
                forward.applyQuaternion(boss.quaternion);
                return forward;
            }
        }

        // If no boss or no direction information, return default
        return defaultDirection;
    }

    /**
     * Add visual anticipation effect before spawning a pattern
     * @param {String} patternName - Pattern name
     * @param {THREE.Vector3} position - Boss position
     * @private
     */
    _addPatternAnticipationEffect(patternName, position) {
        // Skip if no dungeonHandler available
        if (!this.dungeonHandler) return;

        try {
            // Create a temporary visual effect based on pattern type
            switch (patternName) {
                case 'laser_grid':
                    // Create a brief flash of light
                    if (this.dungeonHandler.createLightFlash) {
                        this.dungeonHandler.createLightFlash(position, 0.5, 0x88ffff, 200);
                    }
                    break;

                case 'hellburst':
                    // Create a more intense flash for hellburst
                    if (this.dungeonHandler.createLightFlash) {
                        this.dungeonHandler.createLightFlash(position, 1.0, 0xff3300, 300);
                    }
                    break;

                // Catacomb Boss patterns
                case 'lantern_flail':
                    // Create a fire effect for the lantern flail aimed at the player
                    if (this.dungeonHandler.createLightFlash) {
                        // Get player position for targeting
                        const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
                            this.dungeonHandler.playerController.playerMesh.position.clone() :
                            new THREE.Vector3(0, 0, 0);

                        // Calculate direction to player
                        const toPlayer = new THREE.Vector3()
                            .subVectors(playerPosition, position)
                            .normalize();

                        // Keep it horizontal
                        toPlayer.y = 0;
                        toPlayer.normalize();

                        // Create the flash in front of the boss in the direction of the player
                        const flashPosition = position.clone().add(toPlayer.clone().multiplyScalar(1.0));
                        this.dungeonHandler.createLightFlash(flashPosition, 0.8, 0xff6600, 300);

                        // Create the effect in front of the boss in the direction of the player
                        if (this.dungeonHandler.createParticleEffect) {
                            this.dungeonHandler.createParticleEffect('fire_burst', flashPosition, 0.5, 200);
                        }
                    }
                    // Add a camera shake effect when pattern changes
                    if (this.dungeonHandler.cameraShake) {
                        console.log("[BulletPatternManager] Lantern Flail pattern change - triggering camera shake");
                        this.dungeonHandler.cameraShake(0.15, 250);
                    }
                    break;

                case 'soul_fire_toss':
                    // Create a fire effect for the soul fire toss
                    if (this.dungeonHandler.createLightFlash) {
                        this.dungeonHandler.createLightFlash(position, 1.0, 0xff3300, 350);
                    }
                    if (this.dungeonHandler.createParticleEffect) {
                        this.dungeonHandler.createParticleEffect('fire_burst', position, 0.6, 250);
                    }
                    // Add a camera shake effect when pattern changes
                    if (this.dungeonHandler.cameraShake) {
                        console.log("[BulletPatternManager] Soul Fire Toss pattern change - triggering camera shake");
                        this.dungeonHandler.cameraShake(0.15, 250);
                    }
                    break;

                case 'grave_summon':
                    // Create a ghostly effect for the grave summon
                    if (this.dungeonHandler.createLightFlash) {
                        this.dungeonHandler.createLightFlash(position, 0.7, 0x33ff33, 400);
                    }
                    if (this.dungeonHandler.createParticleEffect) {
                        this.dungeonHandler.createParticleEffect('dust_puff', position, 0.8, 300);
                    }
                    // Add a camera shake effect when pattern changes
                    if (this.dungeonHandler.cameraShake) {
                        console.log("[BulletPatternManager] Grave Summon pattern change - triggering camera shake");
                        this.dungeonHandler.cameraShake(0.15, 300);
                    }
                    break;

                case 'chained_realm':
                    // Create a soul effect for the chained realm
                    if (this.dungeonHandler.createLightFlash) {
                        this.dungeonHandler.createLightFlash(position, 0.6, 0x8800ff, 300);
                    }
                    if (this.dungeonHandler.createParticleEffect) {
                        this.dungeonHandler.createParticleEffect('soul_wisp', position, 0.7, 300);
                    }
                    // Add a camera shake effect when pattern changes
                    if (this.dungeonHandler.cameraShake) {
                        console.log("[BulletPatternManager] Chained Realm pattern change - triggering camera shake");
                        this.dungeonHandler.cameraShake(0.15, 300);
                    }
                    break;

                case 'fire_kraken':
                    // Create a fire burst effect for the kraken
                    if (this.dungeonHandler.createLightFlash) {
                        this.dungeonHandler.createLightFlash(position, 1.2, 0xff2200, 400);
                    }
                    if (this.dungeonHandler.createParticleEffect) {
                        this.dungeonHandler.createParticleEffect('fire_burst', position, 0.7, 200);
                    }
                    // Add a camera shake effect ONLY when pattern changes (not during continuous firing)
                    // This creates a more impactful moment when the pattern changes
                    if (this.dungeonHandler.cameraShake) {
                        console.log("[BulletPatternManager] FireKraken pattern change - triggering camera shake");
                        this.dungeonHandler.cameraShake(0.15, 300); // Reduced from 0.3 to 0.15 (half intensity)
                    }
                    break;

                case 'lightning_chain':
                    // Create electric sparks effect
                    if (this.dungeonHandler.createParticleEffect) {
                        this.dungeonHandler.createParticleEffect('electric_spark', position, 0.5, 200);
                    }
                    break;

                case 'spiral_wave':
                    // Create a subtle swirl effect
                    if (this.dungeonHandler.createParticleEffect) {
                        this.dungeonHandler.createParticleEffect('swirl', position, 0.7, 300);
                    }
                    break;

                case 'big_fireball_charge':
                    // Create a growing red-orange flash for the big fireball charge
                    if (this.dungeonHandler.createLightFlash) {
                        // Initial flash
                        this.dungeonHandler.createLightFlash(position, 0.8, 0xff3300, 300);

                        // Schedule growing flashes to indicate charging
                        setTimeout(() => {
                            if (this.dungeonHandler.createLightFlash) {
                                this.dungeonHandler.createLightFlash(position, 1.0, 0xff4400, 300);
                            }
                        }, 500);

                        setTimeout(() => {
                            if (this.dungeonHandler.createLightFlash) {
                                this.dungeonHandler.createLightFlash(position, 1.2, 0xff5500, 300);
                            }
                        }, 1000);

                        setTimeout(() => {
                            if (this.dungeonHandler.createLightFlash) {
                                this.dungeonHandler.createLightFlash(position, 1.5, 0xff6600, 300);
                            }
                        }, 1500);
                    }

                    // Create fire particle effects that grow in intensity
                    if (this.dungeonHandler.createParticleEffect) {
                        this.dungeonHandler.createParticleEffect('fire_burst', position, 0.5, 200);

                        setTimeout(() => {
                            if (this.dungeonHandler.createParticleEffect) {
                                this.dungeonHandler.createParticleEffect('fire_burst', position, 0.7, 250);
                            }
                        }, 750);

                        setTimeout(() => {
                            if (this.dungeonHandler.createParticleEffect) {
                                this.dungeonHandler.createParticleEffect('fire_burst', position, 1.0, 300);
                            }
                        }, 1500);
                    }

                    // Add a camera shake effect that intensifies
                    if (this.dungeonHandler.cameraShake) {
                        console.log("[BulletPatternManager] BigFireballCharge pattern - starting camera shake sequence");
                        this.dungeonHandler.cameraShake(0.05, 200);

                        setTimeout(() => {
                            if (this.dungeonHandler.cameraShake) {
                                this.dungeonHandler.cameraShake(0.1, 200);
                            }
                        }, 750);

                        setTimeout(() => {
                            if (this.dungeonHandler.cameraShake) {
                                this.dungeonHandler.cameraShake(0.15, 300);
                            }
                        }, 1500);
                    }
                    break;

                case 'sarcophagus_minions':
                    // Create a green-tinted flash for the sarcophagus summon
                    if (this.dungeonHandler.createLightFlash) {
                        this.dungeonHandler.createLightFlash(position, 0.7, 0x33ff33, 400);
                    }

                    // Add a camera shake effect
                    if (this.dungeonHandler.cameraShake) {
                        console.log("[BulletPatternManager] SarcophagusMinions pattern change - triggering camera shake");
                        this.dungeonHandler.cameraShake(0.15, 250);
                    }
                    break;

                case 'fire_circle_closure':
                    // Create an intense orange flash for the fire circle
                    if (this.dungeonHandler.createLightFlash) {
                        this.dungeonHandler.createLightFlash(position, 1.2, 0xff6600, 500);
                    }

                    // Create fire particle effects in a circle
                    if (this.dungeonHandler.createParticleEffect) {
                        // Create particles in a circle around the boss
                        const particleCount = 8;
                        const radius = 5.0;

                        for (let i = 0; i < particleCount; i++) {
                            const angle = (i / particleCount) * Math.PI * 2;
                            const x = position.x + Math.cos(angle) * radius;
                            const z = position.z + Math.sin(angle) * radius;
                            const particlePos = new THREE.Vector3(x, position.y, z);

                            this.dungeonHandler.createParticleEffect('fire_burst', particlePos, 0.8, 200);
                        }
                    }

                    // Add a stronger camera shake effect
                    if (this.dungeonHandler.cameraShake) {
                        console.log("[BulletPatternManager] FireCircleClosure pattern change - triggering camera shake");
                        this.dungeonHandler.cameraShake(0.2, 300);
                    }
                    break;

                // Add more pattern-specific anticipation effects as needed
            }
        } catch (error) {
            console.warn('[BulletPatternManager] Error creating anticipation effect:', error);
        }
    }

    /**
     * Trigger a specific pattern regardless of intensity
     * @param {String} patternName - Pattern name
     * @param {Number} intensity - Intensity override (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles (1.0 = normal)
     * @param {String} [projectileTypeOverride] - Optional override for projectile type
     */
    triggerPattern(patternName, intensity = 0.5, speedMultiplier = 1.0, projectileTypeOverride = null) {
        console.log(`[BulletPatternManager] triggerPattern called with: patternName=${patternName}, intensity=${intensity}, speedMultiplier=${speedMultiplier}, projectileTypeOverride=${projectileTypeOverride}`);
        console.log(`[BulletPatternManager] Current state: boss=${this.boss ? 'exists' : 'null'}, dungeonHandler=${this.dungeonHandler ? 'exists' : 'null'}`);
        console.log(`[BulletPatternManager] Patterns array length: ${this.patterns ? this.patterns.length : 'null'}`);

        const pattern = this.patterns.find(p => p.name === patternName);
        console.log(`[BulletPatternManager] Pattern found: ${pattern ? 'Yes' : 'No'}`);
        if (pattern) {
            // Store the current pattern and projectile type for timeline mode
            this.currentTimelinePattern = patternName;
            if (projectileTypeOverride) {
                this.currentTimelineProjectileType = projectileTypeOverride;
            }

            // Calculate scale factor based on intensity
            const scaleFactor = 0.7 + (intensity * 0.6); // 0.7-1.3 range

            // If a projectile type is specified, override the normal selection
            if (projectileTypeOverride) {
                // Store the original _selectProjectileType method
                const originalSelectMethod = this._selectProjectileType;

                // Override the method temporarily to return the specified projectile type
                this._selectProjectileType = () => projectileTypeOverride;

                // Get boss position
                const bossPosition = this.boss ? this.boss.position.clone() : new THREE.Vector3(0, 0, 0);
                console.log(`[BulletPatternManager] Boss position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);

                // Spawn the pattern with the overridden method
                this._spawnPattern(pattern, intensity * 100, scaleFactor, speedMultiplier);

                // Restore the original method
                this._selectProjectileType = originalSelectMethod;
            } else {
                // Get boss position
                const bossPosition = this.boss ? this.boss.position.clone() : new THREE.Vector3(0, 0, 0);
                console.log(`[BulletPatternManager] Boss position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);

                // Use normal projectile type selection
                this._spawnPattern(pattern, intensity * 100, scaleFactor, speedMultiplier);
            }

            // Update lastPatternType for debug display
            this.lastPatternTime = Date.now();
            this.lastPatternType = patternName;
            console.log(`[BulletPatternManager] Triggered pattern: ${patternName}, ` +
                       `Intensity: ${Math.round(intensity * 100)}, ` +
                       `Speed: ${speedMultiplier.toFixed(2)}x, ` +
                       `Scale: ${scaleFactor.toFixed(2)}x` +
                       `${projectileTypeOverride ? `, Projectile: ${projectileTypeOverride}` : ''}` +
                       `${this.useOnlyTimelinePatterns ? ' (Timeline Mode)' : ''}`);
        } else {
            console.warn(`[BulletPatternManager] Pattern not found: ${patternName}`);
        }
    }

    /**
     * Spawn a spiral bloom pattern
     * Bullets spiral out in a flower shape, then collapse inward
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnSpiralBloom(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Reduced projectile counts by half for better performance
        const petalCount = Math.max(3, Math.floor((5 + Math.floor(intensity * 3)) / 2)); // 3-4 petals (reduced from 5-8)
        const bulletsPerPetal = Math.max(2, Math.floor((4 + Math.floor(intensity * 4)) / 2)); // 2-4 bullets per petal (reduced from 4-8)
        const radius = 0.5 + intensity * 1.5; // 0.5-2.0 radius
        const scaledRadius = radius * scaleFactor;

        // Spawn petals
        for (let i = 0; i < petalCount; i++) {
            const petalAngle = (i / petalCount) * Math.PI * 2;

            // Spawn bullets in each petal
            for (let j = 0; j < bulletsPerPetal; j++) {
                const bulletRadius = (j / bulletsPerPetal) * scaledRadius;
                const bulletAngle = petalAngle + Math.sin(j / bulletsPerPetal * Math.PI) * 0.5;

                // Calculate position with spiral effect
                const x = position.x + Math.cos(bulletAngle) * bulletRadius;
                const y = position.y;
                const z = position.z + Math.sin(bulletAngle) * bulletRadius;
                const bulletPosition = new THREE.Vector3(x, y, z);

                // Calculate direction (initially outward, will curve inward)
                const dirX = Math.cos(bulletAngle);
                const dirZ = Math.sin(bulletAngle);
                const direction = new THREE.Vector3(dirX, 0, dirZ).normalize();

                // Add bloom behavior
                const userData = {
                    bloomPhase: 'expand', // 'expand' or 'collapse'
                    bloomTimer: 0,
                    bloomDuration: 1.5 + Math.random() * 0.5,
                    originalDirection: direction.clone(),
                    centerPosition: position.clone()
                };

                // Spawn the projectile
                this._spawnProjectile(bulletPosition, direction, projectileType, speedMultiplier * 0.7);
            }
        }
    }

    /**
     * Spawn a delay-accelerate pattern
     * Projectiles float harmlessly, then suddenly rocket forward
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnDelayAccelerate(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Reduced projectile count by half for better performance
        const bulletCount = Math.max(4, Math.floor((8 + Math.floor(intensity * 8)) / 2)); // 4-8 bullets (reduced from 8-16)
        const delayTime = 1.0 - intensity * 0.5; // 0.5-1.0 second delay

        // Spawn bullets in a circle
        for (let i = 0; i < bulletCount; i++) {
            const angle = (i / bulletCount) * Math.PI * 2;

            // Calculate position in a circle
            const radius = (0.8 + Math.random() * 0.4) * scaleFactor;
            const x = position.x + Math.cos(angle) * radius;
            const y = position.y;
            const z = position.z + Math.sin(angle) * radius;
            const bulletPosition = new THREE.Vector3(x, y, z);

            // Calculate initial direction (very slow)
            const dirX = Math.cos(angle);
            const dirZ = Math.sin(angle);
            const direction = new THREE.Vector3(dirX, 0, dirZ).normalize();

            // Add delay-accelerate behavior
            const userData = {
                delayPhase: 'float', // 'float' or 'accelerate'
                delayTimer: 0,
                delayDuration: delayTime + Math.random() * 0.3,
                originalDirection: direction.clone(),
                accelerationFactor: 1.0 + intensity * 2.0 // 1.0-3.0x acceleration
            };

            // Spawn the projectile with very low initial speed
            this._spawnProjectile(bulletPosition, direction, projectileType, speedMultiplier * 0.2);
        }
    }

    /**
     * Spawn a fake-out pulse pattern
     * Bullets pulse in and out, blinking off-screen before teleporting
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnFakeOutPulse(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Reduced projectile counts by half for better performance
        const ringCount = Math.max(1, Math.floor((2 + Math.floor(intensity * 2)) / 2)); // 1-2 rings (reduced from 2-4)
        const bulletsPerRing = Math.max(3, Math.floor((6 + Math.floor(intensity * 6)) / 2)); // 3-6 bullets per ring (reduced from 6-12)

        // Spawn rings of bullets
        for (let ring = 0; ring < ringCount; ring++) {
            const ringRadius = (0.5 + ring * 0.5) * scaleFactor;

            // Spawn bullets in a ring
            for (let i = 0; i < bulletsPerRing; i++) {
                const angle = (i / bulletsPerRing) * Math.PI * 2;

                // Calculate position in a ring
                const x = position.x + Math.cos(angle) * ringRadius;
                const y = position.y;
                const z = position.z + Math.sin(angle) * ringRadius;
                const bulletPosition = new THREE.Vector3(x, y, z);

                // Calculate direction
                const dirX = Math.cos(angle);
                const dirZ = Math.sin(angle);
                const direction = new THREE.Vector3(dirX, 0, dirZ).normalize();

                // Add pulse behavior
                const userData = {
                    pulsePhase: 'out', // 'out', 'in', or 'teleport'
                    pulseTimer: 0,
                    pulseDuration: 0.8 + Math.random() * 0.4,
                    pulseCount: 0,
                    maxPulses: 1 + Math.floor(intensity * 2), // 1-3 pulses
                    originalPosition: bulletPosition.clone(),
                    originalDirection: direction.clone(),
                    teleportDistance: 1.0 + intensity * 1.0 // 1.0-2.0 teleport distance
                };

                // Spawn the projectile
                this._spawnProjectile(bulletPosition, direction, projectileType, speedMultiplier * 0.6);
            }
        }
    }

    /**
     * Spawn a soul magnetism pattern
     * Bullets home toward where player was a second ago
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnSoulMagnetism(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Reduced projectile count by half for better performance
        const bulletCount = Math.max(5, Math.floor((10 + Math.floor(intensity * 10)) / 2)); // 5-10 bullets (reduced from 10-20)
        const trackingDelay = 1.0 - intensity * 0.5; // 0.5-1.0 second delay

        // Get player position if available
        const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
            this.dungeonHandler.playerController.playerMesh.position.clone() :
            new THREE.Vector3(0, 0, 0);

        // Store player position as the "soul echo"
        const soulEchoPosition = playerPosition.clone();

        // Spawn bullets in a sphere around the boss
        for (let i = 0; i < bulletCount; i++) {
            // Calculate random position around the boss
            const phi = Math.acos(2 * Math.random() - 1);
            const theta = Math.random() * Math.PI * 2;
            const radius = (1.0 + Math.random() * 0.5) * scaleFactor;

            const x = position.x + radius * Math.sin(phi) * Math.cos(theta);
            const y = position.y + radius * Math.sin(phi) * Math.sin(theta);
            const z = position.z + radius * Math.cos(phi);
            const bulletPosition = new THREE.Vector3(x, y, z);

            // Calculate initial random direction
            const direction = new THREE.Vector3(
                Math.random() * 2 - 1,
                Math.random() * 2 - 1,
                Math.random() * 2 - 1
            ).normalize();

            // Add soul magnetism behavior
            const userData = {
                magnetismPhase: 'wander', // 'wander' or 'track'
                magnetismTimer: 0,
                magnetismDelay: trackingDelay + Math.random() * 0.3,
                soulEchoPosition: soulEchoPosition,
                trackingStrength: 0.5 + intensity * 0.5 // 0.5-1.0 tracking strength
            };

            // Spawn the projectile
            this._spawnProjectile(bulletPosition, direction, projectileType, speedMultiplier * 0.8);
        }
    }

    /**
     * Spawn an alchemical chain reaction pattern
     * Bullets with different elements that react with each other
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnAlchemicalChain(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Reduced projectile counts by half for better performance
        const elementCount = Math.max(2, Math.floor((3 + Math.floor(intensity * 2)) / 2)); // 2-3 elements (reduced from 3-5)
        const bulletsPerElement = Math.max(2, Math.floor((3 + Math.floor(intensity * 3)) / 2)); // 2-3 bullets per element (reduced from 3-6)

        // Define element types
        const elements = ['fire', 'water', 'earth', 'air', 'aether'];

        // Spawn element clusters
        for (let e = 0; e < elementCount; e++) {
            const element = elements[e % elements.length];
            const elementAngle = (e / elementCount) * Math.PI * 2;
            const elementRadius = 1.0 * scaleFactor;

            // Calculate element cluster center
            const clusterX = position.x + Math.cos(elementAngle) * elementRadius;
            const clusterY = position.y;
            const clusterZ = position.z + Math.sin(elementAngle) * elementRadius;
            const clusterPosition = new THREE.Vector3(clusterX, clusterY, clusterZ);

            // Spawn bullets in the element cluster
            for (let i = 0; i < bulletsPerElement; i++) {
                const bulletAngle = (i / bulletsPerElement) * Math.PI * 2;
                const bulletRadius = 0.3 * scaleFactor;

                // Calculate bullet position
                const x = clusterPosition.x + Math.cos(bulletAngle) * bulletRadius;
                const y = clusterPosition.y;
                const z = clusterPosition.z + Math.sin(bulletAngle) * bulletRadius;
                const bulletPosition = new THREE.Vector3(x, y, z);

                // Calculate direction toward center
                const direction = new THREE.Vector3()
                    .subVectors(position, bulletPosition)
                    .normalize();

                // Add alchemical behavior
                const userData = {
                    element: element,
                    reactionTimer: 0,
                    reactionThreshold: 1.0 + Math.random() * 1.0,
                    hasReacted: false,
                    reactionRadius: 0.5 + intensity * 0.5 // 0.5-1.0 reaction radius
                };

                // Select projectile type based on element
                let elementProjectileType = projectileType;
                if (element === 'fire') elementProjectileType = 'fireball';
                if (element === 'water') elementProjectileType = 'eye_tears';
                if (element === 'earth') elementProjectileType = 'thorn_seeds';
                if (element === 'air') elementProjectileType = 'wind_spirits';
                if (element === 'aether') elementProjectileType = 'halo_fragments';

                // Spawn the projectile
                this._spawnProjectile(bulletPosition, direction, elementProjectileType, speedMultiplier * 0.7);
            }
        }
    }

    /**
     * Spawn a time freeze orb pattern
     * Slow orbs that freeze nearby bullets when they explode
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnTimeFreeze(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Reduced projectile count by half for better performance
        const orbCount = Math.max(1, Math.floor((2 + Math.floor(intensity * 3)) / 2)); // 1-3 orbs (reduced from 2-5)
        const freezeDuration = 1.0 + intensity * 1.0; // 1.0-2.0 seconds freeze
        const freezeRadius = 1.0 + intensity * 1.0; // 1.0-2.0 freeze radius

        // Spawn time freeze orbs
        for (let i = 0; i < orbCount; i++) {
            const angle = (i / orbCount) * Math.PI * 2;

            // Calculate position in a circle
            const radius = (1.0 + Math.random() * 0.5) * scaleFactor;
            const x = position.x + Math.cos(angle) * radius;
            const y = position.y;
            const z = position.z + Math.sin(angle) * radius;
            const orbPosition = new THREE.Vector3(x, y, z);

            // Calculate direction (slowly toward player)
            const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
                this.dungeonHandler.playerController.playerMesh.position.clone() :
                new THREE.Vector3(0, 0, 0);

            const direction = new THREE.Vector3()
                .subVectors(playerPosition, orbPosition)
                .normalize();

            // Add time freeze behavior
            const userData = {
                timePhase: 'slow', // 'slow' or 'explode'
                timeTimer: 0,
                explosionDelay: 2.0 + Math.random() * 1.0,
                freezeDuration: freezeDuration,
                freezeRadius: freezeRadius
            };

            // Spawn the projectile (very slow)
            this._spawnProjectile(orbPosition, direction, 'memory_echoes', speedMultiplier * 0.3);
        }
    }

    /**
     * Spawn a spiral rain pattern
     * Bullets fall from above but spiral sideways as they descend
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnSpiralRain(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        const rowCount = 3 + Math.floor(intensity * 3); // 3-6 rows
        const bulletsPerRow = 4 + Math.floor(intensity * 4); // 4-8 bullets per row
        const spiralFactor = 0.5 + intensity * 0.5; // 0.5-1.0 spiral strength

        // Spawn rows of bullets
        for (let row = 0; row < rowCount; row++) {
            const rowHeight = 2.0 + row * 0.5;

            // Spawn bullets in a row
            for (let i = 0; i < bulletsPerRow; i++) {
                const angle = (i / bulletsPerRow) * Math.PI * 2;

                // Calculate position in a circle above
                const radius = (0.5 + Math.random() * 0.5) * scaleFactor;
                const x = position.x + Math.cos(angle) * radius;
                const y = position.y + rowHeight;
                const z = position.z + Math.sin(angle) * radius;
                const bulletPosition = new THREE.Vector3(x, y, z);

                // Calculate direction (downward with spiral)
                const spiralX = Math.cos(angle) * spiralFactor;
                const spiralZ = Math.sin(angle) * spiralFactor;
                const direction = new THREE.Vector3(spiralX, -1, spiralZ).normalize();

                // Add spiral rain behavior
                const userData = {
                    spiralPhase: 'descend',
                    spiralTimer: 0,
                    spiralFactor: spiralFactor,
                    originalDirection: direction.clone(),
                    spiralAngle: angle
                };

                // Spawn the projectile
                this._spawnProjectile(bulletPosition, direction, projectileType, speedMultiplier * 0.9);
            }
        }
    }

    /**
     * Spawn a divine divide pattern
     * Wall of light that splits bullets in half and alters their paths
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @private
     */
    _spawnDivineDivide(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        const wallWidth = 3.0 * scaleFactor;
        const wallHeight = 2.0;
        const segmentCount = 10 + Math.floor(intensity * 10); // 10-20 segments

        // Create a wall of light segments
        for (let i = 0; i < segmentCount; i++) {
            const t = i / (segmentCount - 1); // 0 to 1

            // Calculate position along the wall
            const x = position.x + (t - 0.5) * wallWidth;
            const y = position.y + wallHeight / 2; // Start at top
            const z = position.z;
            const segmentPosition = new THREE.Vector3(x, y, z);

            // Calculate direction (downward)
            const direction = new THREE.Vector3(0, -1, 0);

            // Add divine divide behavior
            const userData = {
                divinePhase: 'descend',
                divineTimer: 0,
                descentSpeed: 0.5 + intensity * 0.5, // 0.5-1.0 descent speed
                splitFactor: 0.5 + intensity * 0.5, // 0.5-1.0 split strength
                isWallSegment: true
            };

            // Spawn the wall segment
            this._spawnProjectile(segmentPosition, direction, 'halo_fragments', speedMultiplier * 0.4);
        }

        // Also spawn some regular bullets that will be split by the wall
        const bulletCount = 5 + Math.floor(intensity * 5); // 5-10 bullets

        for (let i = 0; i < bulletCount; i++) {
            // Calculate random position behind the wall
            const x = position.x + (Math.random() * 2 - 1) * wallWidth * 0.8;
            const y = position.y;
            const z = position.z - wallWidth / 2; // Behind the wall
            const bulletPosition = new THREE.Vector3(x, y, z);

            // Calculate direction (toward player)
            const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
                this.dungeonHandler.playerController.playerMesh.position.clone() :
                new THREE.Vector3(0, 0, 0);

            const direction = new THREE.Vector3()
                .subVectors(playerPosition, bulletPosition)
                .normalize();

            // Add bullet behavior
            const userData = {
                divineSplit: false, // Will be set to true when split by the wall
                originalDirection: direction.clone()
            };

            // Spawn the bullet
            this._spawnProjectile(bulletPosition, direction, projectileType, speedMultiplier);
        }
    }

    /**
     * Spawn a Lantern Flail pattern
     * Wide swing with knockback and burn effect
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnLanternFlail(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Get player position for targeting
        const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
            this.dungeonHandler.playerController.playerMesh.position.clone() :
            new THREE.Vector3(0, 0, 0);

        // Calculate direction to player (this will be the center of our arc)
        const toPlayer = new THREE.Vector3()
            .subVectors(playerPosition, position)
            .normalize();

        // We want to keep the y-component minimal to ensure the arc is mostly horizontal
        toPlayer.y = 0;
        toPlayer.normalize();

        // Number of projectiles in the flail - significantly reduced for better performance
        // Apply a hard cap to prevent too many projectiles during high intensity music
        const baseCount = 5 + Math.floor(intensity * 5); // Original calculation (5-10)
        const reducedCount = Math.floor(baseCount / 2); // First reduction (3-5)
        const cappedCount = Math.min(reducedCount, 3); // Hard cap at 3 projectiles
        const projectileCount = Math.max(2, cappedCount); // Ensure at least 2 projectiles

        // Swing arc (90 degrees centered on the player direction)
        const arcAngle = Math.PI / 2; // 90 degrees

        // Create a visual effect for the flail
        if (this.dungeonHandler.createLightFlash) {
            // Create the flash in front of the boss in the direction of the player
            const flashPosition = position.clone().add(toPlayer.clone().multiplyScalar(1.0));
            this.dungeonHandler.createLightFlash(flashPosition, 0.8, 0xff6600, 300);
        }

        // Spawn projectiles in an arc aimed at the player
        for (let i = 0; i < projectileCount; i++) {
            // Calculate angle within the arc
            const t = i / (projectileCount - 1);
            const angle = -arcAngle / 2 + arcAngle * t;

            // Calculate direction by rotating around the up axis
            const direction = toPlayer.clone();
            direction.applyAxisAngle(new THREE.Vector3(0, 1, 0), angle);

            // Calculate position offset (further out for later projectiles in the sequence)
            const distanceFromCenter = 0.5 + (i / projectileCount) * 1.5;

            // IMPORTANT: Start position is in front of the boss in the direction of the player
            // Move the starting position forward by 1.0 units in the player direction
            const startPosition = position.clone().add(toPlayer.clone().multiplyScalar(1.0));

            // Calculate speed (faster for outer projectiles)
            const projectileSpeed = speedMultiplier * (0.8 + (i / projectileCount) * 0.7);

            // Spawn the projectile from the forward position
            this._spawnProjectile(
                startPosition.clone().add(direction.clone().multiplyScalar(distanceFromCenter)),
                direction,
                projectileType,
                projectileSpeed
            );
        }

        // Create fire particle effect in front of the boss in the direction of the player
        if (this.dungeonHandler.createParticleEffect) {
            const effectPosition = position.clone().add(toPlayer.clone().multiplyScalar(1.0));
            this.dungeonHandler.createParticleEffect('fire_burst', effectPosition, 0.7, 200);
        }
    }

    /**
     * Spawn a Soul Fire Toss pattern
     * Throws the lantern in a chain arc that explodes on impact
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnSoulFireToss(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Get player position for targeting
        const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
            this.dungeonHandler.playerController.playerMesh.position.clone() :
            new THREE.Vector3(0, 0, 0);

        // Calculate direction to player
        const toPlayer = new THREE.Vector3()
            .subVectors(playerPosition, position)
            .normalize();

        // Add a slight arc to the throw
        toPlayer.y = 0.3 + (intensity * 0.3); // 0.3-0.6 upward component
        toPlayer.normalize();

        // Number of projectiles in the chain
        const chainLength = 3 + Math.floor(intensity * 4); // 3-7 projectiles

        // Create a visual effect for the toss
        if (this.dungeonHandler.createLightFlash) {
            this.dungeonHandler.createLightFlash(position, 1.0, 0xff3300, 300);
        }

        // Spawn the main lantern projectile
        this._spawnProjectile(
            position.clone().add(new THREE.Vector3(0, 0.5, 0)), // Start slightly above
            toPlayer,
            'fireball', // Use fireball for the main lantern
            speedMultiplier * 1.2 // Faster than normal
        );

        // Spawn the chain of fire behind it
        for (let i = 1; i < chainLength; i++) {
            // Calculate position behind the main projectile
            const distance = i * 0.3; // Spacing between chain links
            const chainPos = position.clone().add(toPlayer.clone().multiplyScalar(-distance));

            // Add some randomness to the chain
            const randomOffset = new THREE.Vector3(
                (Math.random() - 0.5) * 0.2,
                (Math.random() - 0.5) * 0.2,
                (Math.random() - 0.5) * 0.2
            );
            chainPos.add(randomOffset);

            // Spawn the chain projectile
            this._spawnProjectile(
                chainPos,
                toPlayer.clone(),
                projectileType, // Use the specified projectile type for the chain
                speedMultiplier * (1.0 - (i / chainLength) * 0.3) // Slightly slower for trailing projectiles
            );
        }

        // Create fire particle effect
        if (this.dungeonHandler.createParticleEffect) {
            this.dungeonHandler.createParticleEffect('fire_burst', position, 0.8, 250);
        }
    }

    /**
     * Spawn a Grave Summon pattern
     * Summons skeletal warriors from nearby graves
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnGraveSummon(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Number of graves to summon from
        const graveCount = 2 + Math.floor(intensity * 3); // 2-5 graves

        // Number of projectiles per grave
        const projectilesPerGrave = 2 + Math.floor(intensity * 2); // 2-4 projectiles per grave

        // Create a visual effect for the summon
        if (this.dungeonHandler.createLightFlash) {
            this.dungeonHandler.createLightFlash(position, 0.7, 0x33ff33, 400);
        }

        // Spawn graves around the boss
        for (let g = 0; g < graveCount; g++) {
            // Calculate grave position (random position around the boss)
            const angle = (g / graveCount) * Math.PI * 2;
            const distance = 3.0 + Math.random() * 2.0; // 3-5 units away

            const gravePos = new THREE.Vector3(
                position.x + Math.cos(angle) * distance,
                position.y,
                position.z + Math.sin(angle) * distance
            );

            // Create a visual effect for the grave
            if (this.dungeonHandler.createParticleEffect) {
                this.dungeonHandler.createParticleEffect('dust_puff', gravePos, 0.5, 200);
            }

            // Get player position for targeting
            const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
                this.dungeonHandler.playerController.playerMesh.position.clone() :
                new THREE.Vector3(0, 0, 0);

            // Spawn projectiles from the grave
            for (let i = 0; i < projectilesPerGrave; i++) {
                // Delay each projectile
                setTimeout(() => {
                    // Calculate direction toward player with some randomness
                    const toPlayer = new THREE.Vector3()
                        .subVectors(playerPosition, gravePos)
                        .normalize();

                    // Add randomness to direction
                    toPlayer.x += (Math.random() - 0.5) * 0.3;
                    toPlayer.z += (Math.random() - 0.5) * 0.3;
                    toPlayer.normalize();

                    // Spawn the projectile
                    this._spawnProjectile(
                        gravePos.clone().add(new THREE.Vector3(0, 0.5, 0)), // Start slightly above ground
                        toPlayer,
                        projectileType,
                        speedMultiplier * (0.7 + Math.random() * 0.6) // Varied speed
                    );
                }, i * 200); // 200ms delay between projectiles from the same grave
            }
        }
    }

    /**
     * Spawn a Chained Realm pattern
     * If the player stays too far, the boss pulls them closer with a soul tether
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnChainedRealm(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Get player position
        const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
            this.dungeonHandler.playerController.playerMesh.position.clone() :
            new THREE.Vector3(0, 0, 0);

        // Calculate distance to player
        const distanceToPlayer = position.distanceTo(playerPosition);

        // Only activate if player is far enough away
        const minDistance = 5.0; // Minimum distance to activate
        if (distanceToPlayer < minDistance) {
            // Player is close enough, don't activate
            return;
        }

        // Calculate direction to player
        const toPlayer = new THREE.Vector3()
            .subVectors(playerPosition, position)
            .normalize();

        // Number of chain links
        const chainLength = 5 + Math.floor(intensity * 5); // 5-10 chain links

        // Create a visual effect for the chain
        if (this.dungeonHandler.createLightFlash) {
            this.dungeonHandler.createLightFlash(position, 0.6, 0x8800ff, 300);
        }

        // Spawn the chain links
        for (let i = 0; i < chainLength; i++) {
            // Calculate position along the chain
            const t = i / (chainLength - 1);
            const chainPos = new THREE.Vector3().lerpVectors(position, playerPosition, t);

            // Add some vertical oscillation
            chainPos.y += Math.sin(t * Math.PI) * 0.5;

            // Spawn the chain link
            this._spawnProjectile(
                chainPos,
                toPlayer.clone(),
                projectileType,
                speedMultiplier * (0.5 + t * 1.0) // Faster toward the player
            );
        }

        // Create soul particle effect
        if (this.dungeonHandler.createParticleEffect) {
            this.dungeonHandler.createParticleEffect('soul_wisp', position, 0.7, 300);
            this.dungeonHandler.createParticleEffect('soul_wisp', playerPosition, 0.5, 200);
        }
    }

    /**
     * Spawn a FireKraken pattern
     * Fire tentacles that reach out toward the player
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnFireKraken(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        // Number of tentacles to spawn - reduced by half for better performance
        const tentacleCount = Math.max(1, Math.floor((2 + Math.floor(intensity * 3)) / 2)); // 1-3 tentacles (reduced from 2-5)

        // Number of segments per tentacle - reduced by half for better performance
        const segmentsPerTentacle = Math.max(2, Math.floor((4 + Math.floor(intensity * 4)) / 2)); // 2-4 segments (reduced from 4-8)

        // Get player position for targeting
        const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
            this.dungeonHandler.playerController.playerMesh.position.clone() :
            new THREE.Vector3(0, 0, 0);

        // Create a visual effect for the kraken's core
        if (this.dungeonHandler.createLightFlash) {
            this.dungeonHandler.createLightFlash(position, 1.0, 0xff3300, 300);
        }

        // Spawn tentacles
        for (let t = 0; t < tentacleCount; t++) {
            // Calculate base angle for this tentacle
            const baseAngle = (t / tentacleCount) * Math.PI * 2;

            // Calculate initial direction
            const initialDirection = new THREE.Vector3(
                Math.cos(baseAngle),
                0.1, // Slight upward component
                Math.sin(baseAngle)
            ).normalize();

            // Calculate target direction (toward player with some randomness)
            const toPlayer = new THREE.Vector3()
                .subVectors(playerPosition, position)
                .normalize();

            // Blend between initial direction and player direction
            const targetDirection = new THREE.Vector3()
                .addVectors(
                    initialDirection.clone().multiplyScalar(0.3),
                    toPlayer.multiplyScalar(0.7)
                )
                .normalize();

            // Previous position for chaining
            let prevPos = position.clone();

            // Spawn segments in a tentacle
            for (let i = 0; i < segmentsPerTentacle; i++) {
                // Calculate progress along tentacle (0-1)
                const progress = i / (segmentsPerTentacle - 1);

                // Blend between initial and target direction based on progress
                const blendedDirection = new THREE.Vector3()
                    .addVectors(
                        initialDirection.clone().multiplyScalar(1 - progress),
                        targetDirection.clone().multiplyScalar(progress)
                    )
                    .normalize();

                // Add sinusoidal movement for tentacle-like motion
                const waveAmplitude = 0.3 * (1 - progress); // Stronger at the base, weaker at the tip
                const waveFrequency = 2.0 + intensity * 2.0; // 2-4 waves
                const wavePhase = Date.now() / 1000 * 2.0; // Time-based phase

                // Calculate wave offset perpendicular to direction
                const perpendicular = new THREE.Vector3(blendedDirection.z, 0, -blendedDirection.x).normalize();
                const waveOffset = perpendicular.clone().multiplyScalar(
                    waveAmplitude * Math.sin(progress * waveFrequency * Math.PI + wavePhase + t)
                );

                // Calculate segment position with wave motion
                const segmentDistance = 0.5 + (0.3 * i); // Increasing distance between segments
                const segmentDirection = blendedDirection.clone().add(waveOffset).normalize();
                const newPos = prevPos.clone().add(segmentDirection.clone().multiplyScalar(segmentDistance));

                // Calculate size based on position in tentacle (larger at base, smaller at tip)
                const segmentSize = 0.3 - (0.2 * progress);

                // Calculate speed based on position in tentacle (slower at base, faster at tip)
                const segmentSpeed = 0.3 + (0.7 * progress);

                // Spawn the segment
                this._spawnProjectile(
                    newPos,
                    segmentDirection,
                    i === segmentsPerTentacle - 1 ? 'fireball' : projectileType, // Use fireball for the tip
                    speedMultiplier * segmentSpeed
                );

                // Update previous position for next segment
                prevPos = newPos;
            }
        }

        // Create fire particle effect
        if (this.dungeonHandler.createParticleEffect) {
            this.dungeonHandler.createParticleEffect('fire_burst', position, 1.0, 300);
        }

        // No camera shake here - only shake when pattern changes in the anticipation effect
    }

    /**
     * Spawn a Big Fireball Charge pattern
     * Boss takes a few seconds to charge a large fireball, then releases it toward the player
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnBigFireballCharge(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        console.log("[BulletPatternManager] Spawning Big Fireball Charge pattern");
        // Get player position for targeting
        const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
            this.dungeonHandler.playerController.playerMesh.position.clone() :
            new THREE.Vector3(0, 0, 0);

        // Calculate direction to player
        const toPlayer = new THREE.Vector3()
            .subVectors(playerPosition, position)
            .normalize();

        // Add a slight upward arc
        toPlayer.y = 0.2;
        toPlayer.normalize();

        // Create a charging effect that grows over time
        const chargeTime = 2.0; // 2 seconds to charge
        let chargeSize = 0.5;
        let chargeIntensity = 0.3;

        // Create initial small fireball
        if (this.dungeonHandler.createParticleEffect) {
            this.dungeonHandler.createParticleEffect('fire_burst', position.clone().add(new THREE.Vector3(0, 1.0, 0)), chargeSize, 100);
        }

        // Schedule growing fireballs to indicate charging
        const chargeInterval = setInterval(() => {
            chargeSize += 0.2;
            chargeIntensity += 0.15;

            if (this.dungeonHandler && this.dungeonHandler.createParticleEffect) {
                this.dungeonHandler.createParticleEffect(
                    'fire_burst',
                    position.clone().add(new THREE.Vector3(0, 1.0, 0)),
                    chargeSize,
                    100 + chargeSize * 100
                );
            }
        }, 400);

        // Log the start of charging
        console.log(`[BulletPatternManager] Starting big fireball charge, will release in ${chargeTime} seconds`);

        // After charge time, release the big fireball
        setTimeout(() => {
            console.log("[BulletPatternManager] Releasing big fireball!");
            // Clear the charging interval
            clearInterval(chargeInterval);

            // Create a final flash effect
            if (this.dungeonHandler.createLightFlash) {
                this.dungeonHandler.createLightFlash(position, 1.5, 0xff6600, 500);
            }

            // Create a strong camera shake
            if (this.dungeonHandler.cameraShake) {
                this.dungeonHandler.cameraShake(0.2, 300);
            }

            // Calculate fireball size based on intensity
            const fireballSize = 1.5 + (intensity * 1.5); // 1.5-3.0x normal size

            // Spawn the big fireball
            // We'll use a custom projectile with special properties
            const startPos = position.clone().add(new THREE.Vector3(0, 1.0, 0));
            const projectileSpeed = speedMultiplier * 0.7; // Slower than normal projectiles

            // Create the big fireball with custom properties
            this._spawnProjectile(
                startPos,
                toPlayer,
                'fireball', // Always use fireball for this pattern
                projectileSpeed,
                {
                    isBigFireball: true,
                    scale: fireballSize,
                    damage: 2, // More damage than regular projectiles
                    lifetime: 5.0 // Longer lifetime
                }
            );

            // Spawn some smaller fireballs around the main one for visual effect - reduced count for better performance
            const smallFireballCount = Math.max(2, Math.floor((3 + Math.floor(intensity * 4)) / 2)); // 2-4 small fireballs (reduced from 3-7)
            for (let i = 0; i < smallFireballCount; i++) {
                // Calculate angle within a cone
                const angle = (i / (smallFireballCount - 1) - 0.5) * Math.PI / 4; // +/- 22.5 degrees

                // Calculate direction by rotating the player direction
                const direction = toPlayer.clone();
                direction.applyAxisAngle(new THREE.Vector3(0, 1, 0), angle);

                // Add slight random variation to speed
                const smallSpeed = projectileSpeed * (0.8 + Math.random() * 0.4);

                // Spawn the small fireball
                this._spawnProjectile(
                    startPos.clone().add(new THREE.Vector3(Math.random() - 0.5, Math.random() - 0.5, Math.random() - 0.5).multiplyScalar(0.5)),
                    direction,
                    'fireball',
                    smallSpeed
                );
            }
        }, chargeTime * 1000);
    }

    /**
     * Spawn Sarcophagus Minions pattern
     * Boss summons sarcophagi around the room that spawn skeleton minions
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnSarcophagusMinions(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        console.log("[BulletPatternManager] Spawning Sarcophagus Minions pattern");
        // Get player position for targeting
        const playerPosition = this.dungeonHandler && this.dungeonHandler.playerController ?
            this.dungeonHandler.playerController.playerMesh.position.clone() :
            new THREE.Vector3(0, 0, 0);

        // Number of sarcophagi to spawn - reduced by half for better performance
        const sarcophagusCount = Math.max(1, Math.floor((2 + Math.floor(intensity * 3)) / 2)); // 1-3 sarcophagi (reduced from 2-5)

        // Create a visual effect for the summon
        if (this.dungeonHandler.createLightFlash) {
            this.dungeonHandler.createLightFlash(position, 0.7, 0x33ff33, 400);
        }

        // Log the number of sarcophagi
        console.log(`[BulletPatternManager] Spawning ${sarcophagusCount} sarcophagi around the room`);

        // Spawn sarcophagi around the room
        for (let i = 0; i < sarcophagusCount; i++) {
            // Calculate position in a circle around the boss
            const angle = (i / sarcophagusCount) * Math.PI * 2;
            const distance = 3.0 + Math.random() * 2.0; // 3-5 units away
            const x = position.x + Math.cos(angle) * distance;
            const z = position.z + Math.sin(angle) * distance;
            const sarcophagusPos = new THREE.Vector3(x, position.y, z);

            // Create a visual effect for the sarcophagus
            if (this.dungeonHandler.createParticleEffect) {
                this.dungeonHandler.createParticleEffect('dust_puff', sarcophagusPos, 0.8, 150);
            }

            // Spawn skeletons immediately instead of using setTimeout
            // Number of skeletons to spawn from this sarcophagus - reduced by half for better performance
            const skeletonCount = 1 + Math.floor(Math.random() * 2); // 1-2 skeletons (reduced from 2-4)

            // Log the number of skeletons
            console.log(`[BulletPatternManager] Spawning ${skeletonCount} skeletons from sarcophagus ${i+1}/${sarcophagusCount}`);

            // Create a visual effect for the sarcophagus opening
            if (this.dungeonHandler.createLightFlash) {
                this.dungeonHandler.createLightFlash(sarcophagusPos, 0.5, 0x33ff33, 300);
            }

            // Spawn skeletons in different directions
            for (let j = 0; j < skeletonCount; j++) {
                // Calculate direction toward the player with variation
                const spreadAngle = ((j / skeletonCount) * Math.PI) - (Math.PI / 2); // Spread them out in a semicircle
                const direction = new THREE.Vector3(
                    Math.cos(spreadAngle),
                    0,
                    Math.sin(spreadAngle)
                ).normalize();

                // Create a visual effect for the skeleton spawn
                if (this.dungeonHandler.createParticleEffect) {
                    this.dungeonHandler.createParticleEffect('bone_fragments', sarcophagusPos, 0.6, 100);
                }

                // Spawn a projectile representing the skeleton
                this._spawnProjectile(
                    sarcophagusPos,
                    direction,
                    'screamer_skulls', // Use screamer skulls for skeletons
                    speedMultiplier * 0.6, // Slower than normal projectiles
                    {
                        isSkeleton: true,
                        health: 1, // 1 HP as requested
                        damage: 1
                    }
                );
            }
        }
    }

    /**
     * Spawn Fire Circle Closure pattern
     * Boss creates a ring of fire that gradually closes in
     * @param {THREE.Vector3} position - Center position
     * @param {Number} intensity - Normalized intensity (0-1)
     * @param {String} projectileType - Projectile type
     * @param {Number} scaleFactor - Scale factor for pattern size (0-1)
     * @param {Number} speedMultiplier - Speed multiplier for projectiles
     * @private
     */
    _spawnFireCircleClosure(position, intensity, projectileType, scaleFactor = 1.0, speedMultiplier = 1.0) {
        console.log("[BulletPatternManager] Spawning Fire Circle Closure pattern");

        // Initial radius of the fire circle - much larger to allow player to be inside
        const initialRadius = 15.0; // Increased from 8.0 to 15.0

        // Final radius when fully closed - still leaves space in the center
        const finalRadius = 5.0; // The circle will close to this radius, not completely

        // Number of fire points around the circle - drastically reduced for better performance
        const firePointCount = Math.max(6, Math.floor((24 + Math.floor(intensity * 16)) / 4)); // 6-10 points (drastically reduced from 24-40)

        // Duration of the closing effect - longer to give player more time to react
        const closeDuration = 8.0 + (intensity * 4.0); // 8-12 seconds

        // Create a visual effect for the initial circle
        if (this.dungeonHandler.createLightFlash) {
            this.dungeonHandler.createLightFlash(position, 1.2, 0xff3300, 500); // More red
        }

        // Create the initial fire circle
        const firePoints = [];
        for (let i = 0; i < firePointCount; i++) {
            const angle = (i / firePointCount) * Math.PI * 2;
            const x = position.x + Math.cos(angle) * initialRadius;
            const z = position.z + Math.sin(angle) * initialRadius;
            const firePos = new THREE.Vector3(x, position.y, z);

            // Create a fire effect at this position
            if (this.dungeonHandler.createParticleEffect) {
                this.dungeonHandler.createParticleEffect('fire_burst', firePos, 0.8, 150);
            }

            // Store the fire point data
            firePoints.push({
                angle: angle,
                position: firePos,
                lastProjectileTime: 0, // Track when we last spawned a projectile here
                activeProjectiles: [] // Track active projectiles for this fire point
            });
        }

        // Start time for the closing effect
        const startTime = Date.now();

        // Create an interval to update the fire circle
        const updateInterval = setInterval(() => {
            // Calculate elapsed time
            const elapsedTime = (Date.now() - startTime) / 1000; // in seconds

            // Calculate current radius based on elapsed time
            const progress = Math.min(1.0, elapsedTime / closeDuration);

            // Linear interpolation from initial to final radius
            const currentRadius = initialRadius - (progress * (initialRadius - finalRadius));

            // Log progress every second
            if (Math.floor(elapsedTime) % 1 === 0 && Math.floor(elapsedTime) !== this._lastLoggedFireCircleTime) {
                this._lastLoggedFireCircleTime = Math.floor(elapsedTime);
                console.log(`[BulletPatternManager] Fire circle closing: ${Math.round(progress * 100)}%, radius: ${currentRadius.toFixed(1)}`);
            }

            // Track if we need to remove old projectiles
            const shouldRemoveOldProjectiles = Math.floor(progress * 10) > Math.floor((elapsedTime - 0.1) / closeDuration * 10);

            // Update each fire point
            for (let i = 0; i < firePoints.length; i++) {
                const firePoint = firePoints[i];

                // Calculate new position
                const x = position.x + Math.cos(firePoint.angle) * currentRadius;
                const z = position.z + Math.sin(firePoint.angle) * currentRadius;
                firePoint.position.set(x, position.y, z);

                // Remove the oldest projectile when the circle closes in to prevent too many particles
                if (shouldRemoveOldProjectiles && firePoint.activeProjectiles.length > 0) {
                    const oldestProjectile = firePoint.activeProjectiles.shift(); // Remove oldest projectile
                    if (oldestProjectile && oldestProjectile.parent) {
                        oldestProjectile.parent.remove(oldestProjectile);
                        console.log("[BulletPatternManager] Removed old fire circle projectile to prevent particle buildup");
                    }
                }

                // Create a fire effect at the new position - only at very long intervals to avoid overwhelming effects
                const currentTime = Date.now();
                if (currentTime - firePoint.lastProjectileTime > 3000) { // Increased from 500ms to 3000ms (3 seconds) between projectiles at each point
                    firePoint.lastProjectileTime = currentTime;

                    // Create fire effect
                    if (this.dungeonHandler.createParticleEffect) {
                        this.dungeonHandler.createParticleEffect('fire_burst', firePoint.position, 0.6, 100);
                    }

                    // Spawn a fire projectile at the new position - only at the fire points, not filling the entire area
                    // Calculate direction perpendicular to the radius (tangent to the circle)
                    // This makes projectiles move along the circle outline rather than toward the center
                    const tangentDirection = new THREE.Vector3(
                        -Math.sin(firePoint.angle), // Perpendicular to radius
                        0,
                        Math.cos(firePoint.angle)
                    ).normalize();

                    // Randomly reverse direction for some projectiles
                    if (Math.random() < 0.5) {
                        tangentDirection.multiplyScalar(-1);
                    }

                    // Spawn the projectile and track it
                    const projectile = this._spawnProjectile(
                        firePoint.position,
                        tangentDirection,
                        'fireball', // Always use fireball for this pattern
                        speedMultiplier * 0.3, // Very slow speed
                        {
                            isFireCircle: true,
                            damage: 1,
                            lifetime: 0.5 // Drastically reduced from 1.5 to 0.5 seconds for much faster cleanup
                        }
                    );

                    // Track the projectile for later removal
                    if (projectile) {
                        firePoint.activeProjectiles.push(projectile);

                        // Limit the number of tracked projectiles to prevent memory issues
                        // Keep only 2 projectiles maximum per fire point
                        if (firePoint.activeProjectiles.length > 2) {
                            const oldestProjectile = firePoint.activeProjectiles.shift();
                            if (oldestProjectile && oldestProjectile.parent) {
                                oldestProjectile.parent.remove(oldestProjectile);
                                console.log("[BulletPatternManager] Removed excess fire circle projectile");
                            }
                        }
                    }
                }
            }

            // If the circle has closed to the final radius, clear the interval
            if (progress >= 1.0) {
                clearInterval(updateInterval);

                // Create a final effect
                if (this.dungeonHandler.createLightFlash) {
                    this.dungeonHandler.createLightFlash(position, 1.2, 0xff3300, 400);
                }

                if (this.dungeonHandler.createParticleEffect) {
                    // Create a ring of fire effects at the final radius
                    for (let i = 0; i < firePointCount; i++) {
                        const angle = (i / firePointCount) * Math.PI * 2;
                        const x = position.x + Math.cos(angle) * finalRadius;
                        const z = position.z + Math.sin(angle) * finalRadius;
                        const finalPos = new THREE.Vector3(x, position.y, z);

                        this.dungeonHandler.createParticleEffect('fire_burst', finalPos, 1.0, 200);
                    }
                }

                // Create a camera shake effect
                if (this.dungeonHandler.cameraShake) {
                    this.dungeonHandler.cameraShake(0.15, 300); // Reduced intensity
                }

                // Clean up all remaining projectiles to prevent memory leaks and particle buildup
                console.log("[BulletPatternManager] Fire circle complete, cleaning up all projectiles");
                firePoints.forEach(firePoint => {
                    // Remove all projectiles for this fire point
                    while (firePoint.activeProjectiles.length > 0) {
                        const projectile = firePoint.activeProjectiles.pop();
                        if (projectile && projectile.parent) {
                            projectile.parent.remove(projectile);
                        }
                    }
                });
            }
        }, 100); // Update more frequently (100ms) for smoother movement

        // Clear the interval after the duration to prevent memory leaks
        setTimeout(() => {
            console.log("[BulletPatternManager] Fire circle closure complete, cleaning up interval");
            clearInterval(updateInterval);
        }, (closeDuration + 1) * 1000);
    }

    /**
     * Clean up resources
     */
    dispose() {
        this.activeBullets = [];
        this.boss = null;
        this.dungeonHandler = null;
        console.log("[BulletPatternManager] Disposed");
    }
}
