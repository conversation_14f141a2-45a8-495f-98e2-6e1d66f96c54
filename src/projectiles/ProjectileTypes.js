/**
 * Projectile type definitions
 * Contains configuration for different projectile types
 */
import * as THREE from 'three';
import {
    createAnimatedShaderMaterial,
    createVolumetricEffect,
    createDynamicTrail,
    createLightningEffect,
    createParticleBurst
} from './ProceduralEffects.js';

// Helper function to create particle system for complex projectiles
function createParticleSystem(position, color, size, count, spread) {
    const particles = new THREE.Group();
    particles.position.copy(position);

    // PERFORMANCE: Reduce particle count on low-end devices
    if (window.lastFps && window.lastFps < 30) {
        count = Math.max(2, Math.floor(count * 0.5)); // Reduce particle count by 50%
        size *= 1.2; // Make particles slightly larger to compensate
    }

    // PERFORMANCE: Use simpler geometry for particles
    const geometry = new THREE.SphereGeometry(size * 0.2, 4, 4);
    const material = new THREE.MeshBasicMaterial({
        color: color,
        transparent: true,
        opacity: 0.8
    });

    for (let i = 0; i < count; i++) {
        const particle = new THREE.Mesh(geometry, material);
        particle.position.set(
            (Math.random() - 0.5) * spread,
            (Math.random() - 0.5) * spread,
            (Math.random() - 0.5) * spread
        );
        particle.userData.offset = {
            x: (Math.random() - 0.5) * 0.02,
            y: (Math.random() - 0.5) * 0.02,
            z: (Math.random() - 0.5) * 0.02
        };
        particles.add(particle);
    }

    // Add animation function with performance optimization
    particles.userData.animate = (delta) => {
        // PERFORMANCE: Skip animation on low-end devices sometimes
        if (window.lastFps && window.lastFps < 30 && Math.random() < 0.3) {
            return; // Skip this animation frame 30% of the time on low-FPS devices
        }

        // PERFORMANCE: Optimize animation by using direct array access
        const children = particles.children;
        const len = children.length;

        for (let i = 0; i < len; i++) {
            const particle = children[i];
            const offset = particle.userData.offset;
            const pos = particle.position;

            // Update position
            pos.x += offset.x;
            pos.y += offset.y;
            pos.z += offset.z;

            // Bring particles back toward center to maintain shape
            pos.x *= 0.98;
            pos.y *= 0.98;
            pos.z *= 0.98;
        }
    };

    return particles;
}

/**
 * Projectile type definitions
 * Each type has properties for appearance, behavior, and effects
 */
export const ProjectileTypes = {
    // Default player projectile
    default_soul_orb: {
        name: 'Soul Orb',
        damage: 10, // Restored to original value
        speed: 12.0,
        range: 50.0, // Increased from 15.0 for much longer travel distance
        size: 0.2,
        gravity: -4.9,
        color: 0x00ffff,
        trailEffect: true,
        trailColor: 0x00ffff,
        trailLength: 10,
        impactEffect: 'soul_impact',
        createMesh: (position) => {
            // Create a group for the soul orb
            const group = new THREE.Group();
            group.position.copy(position);

            // Use imported procedural effects

            // Create the core of the soul orb with animated shader
            const coreGeometry = new THREE.SphereGeometry(0.15, 16, 16);
            const coreMaterial = createAnimatedShaderMaterial({
                baseColor: new THREE.Color(0x00ffff),
                pulseColor: new THREE.Color(0x80ffff),
                pulseRate: 2.0,
                noiseStrength: 0.05,
                glowStrength: 0.7
            });

            const core = new THREE.Mesh(coreGeometry, coreMaterial);
            group.add(core);

            // Add volumetric outer glow
            const outerGlow = createVolumetricEffect(
                new THREE.Color(0x00ffff),
                new THREE.Color(0x0088aa),
                4, // layers
                0.25 // size
            );
            group.add(outerGlow);

            // Add orbiting soul particles
            const particleCount = 3;
            const particles = [];

            for (let i = 0; i < particleCount; i++) {
                const angle = (i / particleCount) * Math.PI * 2;
                const radius = 0.3;

                const particleGeometry = new THREE.SphereGeometry(0.04, 8, 8);
                const particleMaterial = new THREE.MeshBasicMaterial({
                    color: 0x80ffff,
                    transparent: true,
                    opacity: 0.7
                });

                const particle = new THREE.Mesh(particleGeometry, particleMaterial);
                particle.position.set(
                    Math.cos(angle) * radius,
                    Math.sin(angle) * radius,
                    0
                );

                // Store initial angle for animation
                particle.userData.angle = angle;
                particle.userData.radius = radius;
                particle.userData.speed = 1.0 + Math.random() * 0.5;

                group.add(particle);
                particles.push(particle);
            }

            // Add point light with pulsing effect
            const light = new THREE.PointLight(0x00ffff, 1.2, 3);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation function
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniform
                if (core.material.uniforms) {
                    core.material.uniforms.time.value = time;
                }

                // Animate volumetric effect
                if (outerGlow.userData.animate) {
                    outerGlow.userData.animate(delta);
                }

                // Animate orbiting particles
                particles.forEach(particle => {
                    particle.userData.angle += delta * particle.userData.speed;

                    // Update position in orbit
                    particle.position.x = Math.cos(particle.userData.angle) * particle.userData.radius;
                    particle.position.z = Math.sin(particle.userData.angle) * particle.userData.radius;

                    // Add subtle bobbing motion
                    particle.position.y = Math.sin(time * 2 + particle.userData.angle) * 0.05;

                    // Pulse size
                    const pulse = 0.8 + Math.sin(time * 3 + particle.userData.angle * 2) * 0.2;
                    particle.scale.set(pulse, pulse, pulse);
                });

                // Pulse the light
                light.intensity = 1.0 + Math.sin(time * 4) * 0.3;
            };

            return group;
        }
    },

    // Skeleton arrow
    arrow: {
        name: 'Arrow',
        damage: 1, // Reduced from 5 to 1
        speed: 7.0, // Reduced from 12.0 to make arrows slower and more dodgeable
        range: 60.0, // Increased from 25.0 for much longer arrow flight
        size: { width: 0.05, height: 0.5, depth: 0.05 },
        gravity: -9.8, // Increased from -6.0 to give arrows a more noticeable arc
        color: 0x8B4513, // Brown
        trailEffect: false,
        impactEffect: 'arrow_impact',
        createMesh: (position) => {
            // Create arrow group
            const arrowGroup = new THREE.Group();
            arrowGroup.position.copy(position);

            // Arrow shaft
            const shaftGeometry = new THREE.CylinderGeometry(0.025, 0.025, 0.5, 8);
            const shaftMaterial = new THREE.MeshBasicMaterial({ color: 0x8B4513 });
            const shaft = new THREE.Mesh(shaftGeometry, shaftMaterial);
            shaft.rotation.x = Math.PI / 2;
            arrowGroup.add(shaft);

            // Arrow head
            const headGeometry = new THREE.ConeGeometry(0.05, 0.1, 8);
            const headMaterial = new THREE.MeshBasicMaterial({ color: 0x808080 });
            const head = new THREE.Mesh(headGeometry, headMaterial);
            head.position.z = -0.3;
            head.rotation.x = Math.PI / 2;
            arrowGroup.add(head);

            // Arrow fletching
            const fletchingGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.01);
            const fletchingMaterial = new THREE.MeshBasicMaterial({ color: 0xA52A2A });
            const fletching = new THREE.Mesh(fletchingGeometry, fletchingMaterial);
            fletching.position.z = 0.2;
            arrowGroup.add(fletching);

            return arrowGroup;
        }
    },

    // Fireball
    fireball: {
        name: 'Fireball',
        damage: 1, // Reduced from 8 to 1
        speed: 6.0,
        range: 40.0, // Increased from 10.0 for much longer travel distance
        size: 0.3,
        gravity: -2.0,
        color: 0xFF4500, // Orange-red
        trailEffect: true,
        trailColor: 0xFF8C00,
        trailLength: 15,
        trailType: 'flame',
        trailWidth: 2,
        trailFade: true,
        impactEffect: 'fire_impact',
        createMesh: (position) => {
            // Use imported procedural effects

            // Create a group for the fireball
            const group = new THREE.Group();
            group.position.copy(position);

            // Create a more realistic, skull-shaped fireball core
            // This fits the catacomb theme with a burning skull shape

            // Create a group for the skull shape
            const skullGroup = new THREE.Group();

            // Create the main skull dome with custom shader material
            const skullDomeGeometry = new THREE.SphereGeometry(0.15, 16, 16);
            // Flatten the bottom slightly
            for (let i = 0; i < skullDomeGeometry.attributes.position.count; i++) {
                const y = skullDomeGeometry.attributes.position.getY(i);
                if (y < 0) {
                    skullDomeGeometry.attributes.position.setY(i, y * 0.8);
                }
            }
            skullDomeGeometry.computeVertexNormals();

            // Create the animated shader material - more red
            const coreMaterial = createAnimatedShaderMaterial({
                baseColor: new THREE.Color(0xFF3300), // Deep red-orange core
                pulseColor: new THREE.Color(0xFF0000), // Bright red pulse
                pulseRate: 8.0, // Fast pulsing for fire effect
                noiseStrength: 0.2, // More noise for flickering
                glowStrength: 0.8
            });

            // Create the skull dome
            const skullDome = new THREE.Mesh(skullDomeGeometry, coreMaterial);
            skullGroup.add(skullDome);

            // Add eye sockets using separate meshes with dark material
            const eyeSocketGeometry = new THREE.SphereGeometry(0.05, 8, 8);
            const eyeSocketMaterial = new THREE.MeshBasicMaterial({
                color: 0x330000, // Very dark red
                transparent: true,
                opacity: 0.7
            });

            const leftEye = new THREE.Mesh(eyeSocketGeometry, eyeSocketMaterial);
            leftEye.position.set(-0.06, 0, 0.09);
            skullGroup.add(leftEye);

            const rightEye = new THREE.Mesh(eyeSocketGeometry, eyeSocketMaterial);
            rightEye.position.set(0.06, 0, 0.09);
            skullGroup.add(rightEye);

            // Add nasal cavity
            const nasalGeometry = new THREE.ConeGeometry(0.03, 0.06, 4);
            const nasalMaterial = new THREE.MeshBasicMaterial({
                color: 0x330000, // Very dark red
                transparent: true,
                opacity: 0.7
            });

            const nasal = new THREE.Mesh(nasalGeometry, nasalMaterial);
            nasal.position.set(0, -0.03, 0.12);
            nasal.rotation.x = Math.PI / 2;
            skullGroup.add(nasal);

            // Add jaw
            const jawGeometry = new THREE.BoxGeometry(0.12, 0.04, 0.08);
            const jawMaterial = createAnimatedShaderMaterial({
                baseColor: new THREE.Color(0xFF2200), // Slightly different red
                pulseColor: new THREE.Color(0xFF0000), // Bright red pulse
                pulseRate: 7.0, // Slightly different pulse rate
                noiseStrength: 0.3, // More noise
                glowStrength: 0.7
            });

            const jaw = new THREE.Mesh(jawGeometry, jawMaterial);
            jaw.position.set(0, -0.1, 0.06);
            skullGroup.add(jaw);

            // Use the skull group as the core
            const core = skullGroup;
            group.add(core);

            // Add volumetric fire effect with multiple layers - more red
            const fireEffect = createVolumetricEffect(
                new THREE.Color(0xFF2200), // Red-orange core
                new THREE.Color(0xCC0000), // Deep red outer
                6, // More layers for more detailed fire
                0.3 // Size
            );
            group.add(fireEffect);

            // Create ember particles that orbit and detach
            const emberCount = 12;
            const embers = [];

            for (let i = 0; i < emberCount; i++) {
                // Random position around the fireball
                const angle = Math.random() * Math.PI * 2;
                const radius = 0.1 + Math.random() * 0.2;
                const height = (Math.random() - 0.5) * 0.2;

                const emberGeometry = new THREE.SphereGeometry(0.02 + Math.random() * 0.03, 4, 4);
                const emberMaterial = new THREE.MeshBasicMaterial({
                    color: new THREE.Color().lerpColors(
                        new THREE.Color(0xFF3300), // Red-orange
                        new THREE.Color(0xCC0000), // Deep red
                        Math.random()
                    ),
                    transparent: true,
                    opacity: 0.6 + Math.random() * 0.4
                });

                const ember = new THREE.Mesh(emberGeometry, emberMaterial);
                ember.position.set(
                    Math.cos(angle) * radius,
                    height,
                    Math.sin(angle) * radius
                );

                // Store properties for animation
                ember.userData.angle = angle;
                ember.userData.radius = radius;
                ember.userData.height = height;
                ember.userData.speed = 0.5 + Math.random() * 2.0;
                ember.userData.detachTime = 0.5 + Math.random() * 1.5; // Random time to detach
                ember.userData.detached = false;
                ember.userData.velocity = new THREE.Vector3(
                    (Math.random() - 0.5) * 0.3,
                    Math.random() * 0.2,
                    (Math.random() - 0.5) * 0.3
                );

                group.add(ember);
                embers.push(ember);
            }

            // Add flickering point light - more red
            const light = new THREE.PointLight(0xFF0000, 1.5, 3.0);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add heat distortion effect (simulated with a transparent sphere)
            const distortionGeometry = new THREE.SphereGeometry(0.4, 16, 16);
            const distortionMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    intensity: { value: 0.02 }
                },
                vertexShader: `
                    varying vec2 vUv;
                    varying vec3 vPosition;

                    void main() {
                        vUv = uv;
                        vPosition = position;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform float intensity;
                    varying vec2 vUv;
                    varying vec3 vPosition;

                    float noise(vec3 p) {
                        return fract(sin(dot(p, vec3(12.9898, 78.233, 45.164))) * 43758.5453);
                    }

                    void main() {
                        float dist = length(vPosition);
                        float alpha = smoothstep(0.4, 0.0, dist) * 0.2;

                        // Add noise for heat distortion effect
                        float noiseVal = noise(vPosition * 10.0 + time * 5.0) * intensity;

                        gl_FragColor = vec4(1.0, 0.6, 0.2, alpha * noiseVal);
                    }
                `,
                transparent: true,
                depthWrite: false,
                blending: THREE.AdditiveBlending
            });

            const distortion = new THREE.Mesh(distortionGeometry, distortionMaterial);
            group.add(distortion);

            // Add animation function
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniforms
                if (core.material.uniforms) {
                    core.material.uniforms.time.value = time;
                }

                if (distortion.material.uniforms) {
                    distortion.material.uniforms.time.value = time;
                }

                // Animate volumetric fire effect
                if (fireEffect.userData.animate) {
                    fireEffect.userData.animate(delta);
                }

                // Animate ember particles
                embers.forEach((ember, index) => {
                    if (!ember.userData.detached) {
                        // Update ember orbit position
                        ember.userData.angle += delta * ember.userData.speed;
                        ember.position.x = Math.cos(ember.userData.angle) * ember.userData.radius;
                        ember.position.z = Math.sin(ember.userData.angle) * ember.userData.radius;

                        // Check if it's time to detach
                        if (time > ember.userData.detachTime) {
                            ember.userData.detached = true;
                        }
                    } else {
                        // Move detached ember with velocity
                        ember.position.add(ember.userData.velocity.clone().multiplyScalar(delta));

                        // Apply gravity and drag
                        ember.userData.velocity.y -= delta * 0.5; // Gravity
                        ember.userData.velocity.multiplyScalar(0.98); // Drag

                        // Fade out
                        ember.material.opacity *= 0.95;

                        // Remove if too far or too faded
                        if (ember.position.length() > 1.0 || ember.material.opacity < 0.1) {
                            group.remove(ember);

                            // Create a new ember to replace it
                            if (Math.random() < 0.5) {
                                const newAngle = Math.random() * Math.PI * 2;
                                const newRadius = 0.1 + Math.random() * 0.2;
                                const newHeight = (Math.random() - 0.5) * 0.2;

                                const newEmberGeometry = new THREE.SphereGeometry(0.02 + Math.random() * 0.03, 4, 4);
                                const newEmberMaterial = new THREE.MeshBasicMaterial({
                                    color: new THREE.Color().lerpColors(
                                        new THREE.Color(0xFFFF00),
                                        new THREE.Color(0xFF4500),
                                        Math.random()
                                    ),
                                    transparent: true,
                                    opacity: 0.6 + Math.random() * 0.4
                                });

                                const newEmber = new THREE.Mesh(newEmberGeometry, newEmberMaterial);
                                newEmber.position.set(
                                    Math.cos(newAngle) * newRadius,
                                    newHeight,
                                    Math.sin(newAngle) * newRadius
                                );

                                newEmber.userData.angle = newAngle;
                                newEmber.userData.radius = newRadius;
                                newEmber.userData.height = newHeight;
                                newEmber.userData.speed = 0.5 + Math.random() * 2.0;
                                newEmber.userData.detachTime = time + 0.5 + Math.random() * 1.5;
                                newEmber.userData.detached = false;
                                newEmber.userData.velocity = new THREE.Vector3(
                                    (Math.random() - 0.5) * 0.3,
                                    Math.random() * 0.2,
                                    (Math.random() - 0.5) * 0.3
                                );

                                group.add(newEmber);
                                embers[index] = newEmber;
                            }
                        }
                    }
                });

                // Flicker the light
                const flicker = 0.8 + Math.random() * 0.4;
                light.intensity = 1.5 * flicker;

                // Randomly emit small particle bursts
                if (Math.random() < 0.05) {
                    const burstPosition = new THREE.Vector3(
                        (Math.random() - 0.5) * 0.2,
                        (Math.random() - 0.5) * 0.2,
                        (Math.random() - 0.5) * 0.2
                    );

                    const burst = createParticleBurst(
                        burstPosition,
                        new THREE.Color(0xFF0000), // Bright red
                        5 // Small number of particles
                    );

                    group.add(burst);
                }
            };

            return group;
        }
    },

    // Ice spike
    ice_spike: {
        name: 'Ice Spike',
        damage: 1, // Reduced from 6 to 1
        speed: 8.0,
        range: 45.0, // Increased from 14.0 for much longer travel distance
        size: 0.25,
        gravity: -3.0,
        color: 0xADD8E6, // Light blue
        trailEffect: true,
        trailColor: 0xE0FFFF,
        trailLength: 8,
        impactEffect: 'ice_impact',
        createMesh: (position) => {
            // Create an ice spike
            const geometry = new THREE.ConeGeometry(0.15, 0.5, 8);
            const material = new THREE.MeshBasicMaterial({
                color: 0xADD8E6,
                transparent: true,
                opacity: 0.7
            });
            const mesh = new THREE.Mesh(geometry, material);
            mesh.rotation.x = Math.PI / 2;
            mesh.position.copy(position);

            return mesh;
        }
    },

    // Lightning bolt
    lightning_bolt: {
        name: 'Lightning Bolt',
        damage: 1, // Reduced from 12 to 1
        speed: 15.0,
        range: 60.0, // Increased from 20.0 for much longer travel distance
        size: 0.15,
        gravity: 0, // No gravity
        color: 0xFFFF00, // Yellow
        trailEffect: true,
        trailColor: 0xFFFFFF,
        trailLength: 20,
        trailType: 'energy',
        trailWidth: 2,
        trailFade: true,
        impactEffect: 'lightning_impact',
        createMesh: (position) => {
            // Use imported procedural effects

            // Create a more impressive lightning bolt with branching and glow
            const group = new THREE.Group();
            group.position.copy(position);

            // Create main lightning bolt using procedural effect
            const mainBoltEnd = new THREE.Vector3(0, 0, -0.8);
            const mainBolt = createLightningEffect(
                new THREE.Vector3(0, 0, 0),
                mainBoltEnd,
                12, // More segments for more detailed zigzag
                0.15 // Higher jitter for more dramatic effect
            );
            group.add(mainBolt);

            // Add 3-5 smaller branches
            const branchCount = 3 + Math.floor(Math.random() * 3);
            const branches = [];

            for (let i = 0; i < branchCount; i++) {
                // Calculate branch start position along main bolt
                const branchStartPercent = 0.2 + Math.random() * 0.6; // Start branch 20-80% along main bolt
                const branchStart = new THREE.Vector3().lerpVectors(
                    new THREE.Vector3(0, 0, 0),
                    mainBoltEnd,
                    branchStartPercent
                );

                // Calculate branch end position
                const branchLength = 0.3 + Math.random() * 0.3;
                const branchAngle = (Math.random() * 0.8 + 0.4) * (Math.random() > 0.5 ? 1 : -1);

                // Create perpendicular vector for branch direction
                const mainDirection = new THREE.Vector3().subVectors(mainBoltEnd, new THREE.Vector3(0, 0, 0)).normalize();
                const perpendicular = new THREE.Vector3(1, 0, 0);
                if (Math.abs(mainDirection.dot(perpendicular)) > 0.9) {
                    perpendicular.set(0, 1, 0); // Use a different perpendicular if needed
                }

                const branchDirection = new THREE.Vector3().crossVectors(mainDirection, perpendicular).normalize();
                branchDirection.multiplyScalar(branchAngle);

                // Add some forward component to the branch
                branchDirection.add(mainDirection.clone().multiplyScalar(0.5));
                branchDirection.normalize().multiplyScalar(branchLength);

                const branchEnd = new THREE.Vector3().addVectors(branchStart, branchDirection);

                // Create the branch
                const branch = createLightningEffect(
                    branchStart,
                    branchEnd,
                    6, // Fewer segments for branches
                    0.08 // Less jitter for branches
                );

                group.add(branch);
                branches.push(branch);
            }

            // Create a glowing core for the lightning using shader material
            const coreGeometry = new THREE.CylinderGeometry(0.03, 0.03, 0.8, 12);
            const coreMaterial = createAnimatedShaderMaterial({
                baseColor: new THREE.Color(0xCCFFFF), // Light blue-white
                pulseColor: new THREE.Color(0xFFFFFF), // Pure white pulse
                pulseRate: 15.0, // Fast pulsing for electric effect
                noiseStrength: 0.1,
                glowStrength: 0.7
            });

            const core = new THREE.Mesh(coreGeometry, coreMaterial);
            core.rotation.x = Math.PI / 2;
            core.position.z = -0.4; // Center along the bolt
            group.add(core);

            // Add electric glow effect with shader
            const glowGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.8, 12);
            const glowMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    color: { value: new THREE.Color(0x88AAFF) },
                    intensity: { value: 0.3 }
                },
                vertexShader: `
                    varying vec2 vUv;
                    varying vec3 vPosition;

                    void main() {
                        vUv = uv;
                        vPosition = position;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform vec3 color;
                    uniform float intensity;
                    varying vec2 vUv;
                    varying vec3 vPosition;

                    // Simple noise function
                    float noise(vec2 p) {
                        return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
                    }

                    void main() {
                        // Calculate distance from center axis
                        float dist = length(vec2(vPosition.x, vPosition.y));

                        // Create electric arcs effect
                        float noiseVal = noise(vec2(vUv.x * 10.0, time * 20.0)) * 0.1;
                        float arcEffect = smoothstep(0.07 + noiseVal, 0.0, dist);

                        // Add flickering
                        float flicker = 0.8 + 0.2 * noise(vec2(time * 30.0, 0.0));

                        // Add pulsing along the length
                        float pulse = 0.8 + 0.3 * sin(vUv.y * 20.0 + time * 15.0);

                        // Combine effects
                        float alpha = arcEffect * intensity * flicker * pulse;

                        // Add color variation
                        vec3 finalColor = color * (0.8 + 0.2 * sin(time * 10.0 + vUv.y * 5.0));

                        gl_FragColor = vec4(finalColor, alpha);
                    }
                `,
                transparent: true,
                depthWrite: false,
                blending: THREE.AdditiveBlending
            });

            const glow = new THREE.Mesh(glowGeometry, glowMaterial);
            glow.rotation.x = Math.PI / 2;
            glow.position.z = -0.4;
            group.add(glow);

            // Add electric arc particles
            const arcCount = 8;
            const arcs = [];

            for (let i = 0; i < arcCount; i++) {
                // Create small electric arcs that jump between points on the main bolt
                const arcGeometry = new THREE.BufferGeometry();

                // Create initial arc points
                const arcPoints = [];
                const arcSegments = 4 + Math.floor(Math.random() * 3);

                // Start and end points along the main bolt
                const startPercent = Math.random() * 0.8;
                const endPercent = startPercent + 0.1 + Math.random() * 0.1;

                const arcStart = new THREE.Vector3().lerpVectors(
                    new THREE.Vector3(0, 0, 0),
                    mainBoltEnd,
                    startPercent
                );

                const arcEnd = new THREE.Vector3().lerpVectors(
                    new THREE.Vector3(0, 0, 0),
                    mainBoltEnd,
                    endPercent
                );

                // Add some offset to make it jump away from the main bolt
                const offset = new THREE.Vector3(
                    (Math.random() - 0.5) * 0.1,
                    (Math.random() - 0.5) * 0.1,
                    0
                );

                // Generate arc points
                for (let j = 0; j <= arcSegments; j++) {
                    const t = j / arcSegments;
                    const point = new THREE.Vector3().lerpVectors(arcStart, arcEnd, t);

                    // Add some randomness to middle points
                    if (j > 0 && j < arcSegments) {
                        point.add(new THREE.Vector3(
                            (Math.random() - 0.5) * 0.05,
                            (Math.random() - 0.5) * 0.05,
                            (Math.random() - 0.5) * 0.05
                        ));
                    }

                    // Add the offset, scaled by a bell curve to make middle points jump more
                    const bellCurve = 4 * t * (1 - t);
                    point.add(offset.clone().multiplyScalar(bellCurve));

                    arcPoints.push(point);
                }

                arcGeometry.setFromPoints(arcPoints);

                const arcMaterial = new THREE.LineBasicMaterial({
                    color: 0xAAFFFF,
                    transparent: true,
                    opacity: 0.7
                });

                const arc = new THREE.Line(arcGeometry, arcMaterial);
                arc.userData.lifetime = Math.random() * 0.2; // Random initial lifetime
                arc.userData.maxLifetime = 0.2 + Math.random() * 0.1;
                arc.userData.arcStart = arcStart.clone();
                arc.userData.arcEnd = arcEnd.clone();
                arc.userData.offset = offset.clone();
                arc.userData.arcSegments = arcSegments;

                group.add(arc);
                arcs.push(arc);
            }

            // Add point lights along the bolt
            const mainLight = new THREE.PointLight(0xFFFFFF, 1.5, 3);
            mainLight.position.set(0, 0, -0.3);
            group.add(mainLight);

            const tipLight = new THREE.PointLight(0xAAFFFF, 1, 2);
            tipLight.position.set(0, 0, -0.8);
            group.add(tipLight);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniforms
                if (core.material.uniforms) {
                    core.material.uniforms.time.value = time;
                }

                if (glow.material.uniforms) {
                    glow.material.uniforms.time.value = time;
                }

                // Animate main lightning bolt
                if (mainBolt.userData.animate) {
                    mainBolt.userData.animate(delta);
                }

                // Animate branches
                branches.forEach(branch => {
                    if (branch.userData.animate) {
                        branch.userData.animate(delta);
                    }
                });

                // Animate electric arcs
                arcs.forEach((arc, index) => {
                    arc.userData.lifetime += delta;

                    // If arc lifetime exceeded, regenerate it
                    if (arc.userData.lifetime >= arc.userData.maxLifetime) {
                        arc.userData.lifetime = 0;

                        // New start and end points
                        const startPercent = Math.random() * 0.8;
                        const endPercent = startPercent + 0.1 + Math.random() * 0.1;

                        arc.userData.arcStart.copy(new THREE.Vector3().lerpVectors(
                            new THREE.Vector3(0, 0, 0),
                            mainBoltEnd,
                            startPercent
                        ));

                        arc.userData.arcEnd.copy(new THREE.Vector3().lerpVectors(
                            new THREE.Vector3(0, 0, 0),
                            mainBoltEnd,
                            endPercent
                        ));

                        // New offset
                        arc.userData.offset.set(
                            (Math.random() - 0.5) * 0.1,
                            (Math.random() - 0.5) * 0.1,
                            0
                        );
                    }

                    // Update arc points
                    const arcPoints = [];

                    for (let j = 0; j <= arc.userData.arcSegments; j++) {
                        const t = j / arc.userData.arcSegments;
                        const point = new THREE.Vector3().lerpVectors(arc.userData.arcStart, arc.userData.arcEnd, t);

                        // Add some randomness to middle points
                        if (j > 0 && j < arc.userData.arcSegments) {
                            point.add(new THREE.Vector3(
                                (Math.random() - 0.5) * 0.05,
                                (Math.random() - 0.5) * 0.05,
                                (Math.random() - 0.5) * 0.05
                            ));
                        }

                        // Add the offset, scaled by a bell curve
                        const bellCurve = 4 * t * (1 - t);
                        point.add(arc.userData.offset.clone().multiplyScalar(bellCurve));

                        arcPoints.push(point);
                    }

                    // Update geometry
                    arc.geometry.setFromPoints(arcPoints);

                    // Fade opacity based on lifetime
                    const lifetimeRatio = arc.userData.lifetime / arc.userData.maxLifetime;
                    arc.material.opacity = 0.7 * (1 - Math.pow(lifetimeRatio, 2));
                });

                // Flicker the lights with more natural randomness
                const flicker = 0.7 + 0.3 * (0.5 + 0.5 * Math.sin(time * 30)) * (0.8 + 0.2 * Math.random());
                mainLight.intensity = 1.5 * flicker;
                tipLight.intensity = 1.0 * flicker;
            };

            return group;
        }
    },

    // Poison cloud
    poison_cloud: {
        name: 'Poison Cloud',
        damage: 1, // Reduced from 4 to 1
        speed: 4.0,
        range: 35.0, // Increased from 8.0 for much longer travel distance
        size: 0.4,
        gravity: -1.0,
        color: 0x00FF00, // Green
        trailEffect: true,
        trailColor: 0x008000,
        trailLength: 5,
        trailType: 'particles',
        trailWidth: 1,
        trailFade: true,
        impactEffect: 'poison_impact',
        createMesh: (position) => {
            // Use imported procedural effects

            // Create a more realistic poison cloud with particles and effects
            const group = new THREE.Group();
            group.position.copy(position);

            // Create a central toxic core with animated shader
            const coreGeometry = new THREE.SphereGeometry(0.15, 16, 16);
            const coreMaterial = createAnimatedShaderMaterial({
                baseColor: new THREE.Color(0x009900), // Dark green
                pulseColor: new THREE.Color(0x00FF00), // Bright green pulse
                pulseRate: 2.0, // Slow, ominous pulsing
                noiseStrength: 0.2, // More noise for toxic cloud effect
                glowStrength: 0.6
            });

            const core = new THREE.Mesh(coreGeometry, coreMaterial);
            group.add(core);

            // Create multiple cloud puffs with custom shader for more volumetric look
            const puffCount = 8; // Increased from 5
            const puffs = [];

            for (let i = 0; i < puffCount; i++) {
                // Create irregular cloud puff with custom shader
                const puffSize = 0.15 + Math.random() * 0.15;
                const puffGeometry = new THREE.SphereGeometry(puffSize, 8, 8);

                // Create a custom shader for the toxic cloud effect
                const puffMaterial = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        baseColor: { value: new THREE.Color(i % 2 === 0 ? 0x00FF00 : 0x33CC33) },
                        noiseScale: { value: 10.0 + Math.random() * 5.0 },
                        noiseIntensity: { value: 0.1 + Math.random() * 0.1 },
                        opacity: { value: 0.4 + Math.random() * 0.3 }
                    },
                    vertexShader: `
                        varying vec2 vUv;
                        varying vec3 vPosition;
                        varying vec3 vNormal;

                        void main() {
                            vUv = uv;
                            vPosition = position;
                            vNormal = normal;
                            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform float time;
                        uniform vec3 baseColor;
                        uniform float noiseScale;
                        uniform float noiseIntensity;
                        uniform float opacity;
                        varying vec2 vUv;
                        varying vec3 vPosition;
                        varying vec3 vNormal;

                        // Simplex noise function
                        float noise(vec3 p) {
                            return fract(sin(dot(p, vec3(12.9898, 78.233, 45.164))) * 43758.5453);
                        }

                        void main() {
                            // Create swirling toxic effect
                            vec3 p = vPosition * noiseScale + time * 0.5;
                            float noiseVal = noise(p) * noiseIntensity;

                            // Add edge fading for cloud-like appearance
                            float edge = length(vUv - vec2(0.5));
                            float edgeFade = smoothstep(0.5, 0.0, edge + noiseVal);

                            // Create color variation
                            vec3 finalColor = baseColor * (0.8 + 0.4 * noise(p * 2.0));

                            // Add subtle color shifts over time
                            float hueShift = sin(time * 0.2) * 0.1;
                            finalColor.r += hueShift;
                            finalColor.b -= hueShift;

                            // Combine effects
                            float alpha = opacity * edgeFade;

                            gl_FragColor = vec4(finalColor, alpha);
                        }
                    `,
                    transparent: true,
                    depthWrite: false
                });

                const puff = new THREE.Mesh(puffGeometry, puffMaterial);

                // Position puffs around the center with more variation
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 0.2;
                puff.position.set(
                    Math.cos(angle) * radius,
                    Math.sin(angle) * radius,
                    (Math.random() - 0.5) * 0.2
                );

                // Slightly deform each puff
                puff.scale.x = 0.8 + Math.random() * 0.4;
                puff.scale.y = 0.8 + Math.random() * 0.4;
                puff.scale.z = 0.8 + Math.random() * 0.4;

                // Store properties for animation
                puff.userData.originalPos = puff.position.clone();
                puff.userData.pulseOffset = Math.random() * Math.PI * 2;
                puff.userData.rotationSpeed = new THREE.Vector3(
                    (Math.random() - 0.5) * 0.2,
                    (Math.random() - 0.5) * 0.2,
                    (Math.random() - 0.5) * 0.2
                );

                group.add(puff);
                puffs.push(puff);
            }

            // Add toxic dripping particles
            const particleCount = 15; // Increased from 8
            const particles = [];

            for (let i = 0; i < particleCount; i++) {
                // Create different particle shapes for more variety
                let particleGeometry;
                const particleType = Math.floor(Math.random() * 3);
                const size = 0.03 + Math.random() * 0.04;

                if (particleType === 0) {
                    // Sphere
                    particleGeometry = new THREE.SphereGeometry(size, 8, 8);
                } else if (particleType === 1) {
                    // Tetrahedron
                    particleGeometry = new THREE.TetrahedronGeometry(size * 1.2);
                } else {
                    // Cube
                    particleGeometry = new THREE.BoxGeometry(size, size, size);
                }

                // Create glowing toxic material
                const particleMaterial = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        color: { value: new THREE.Color(i % 3 === 0 ? 0xCCFF00 : (i % 3 === 1 ? 0x00FF00 : 0x009900)) },
                        glowStrength: { value: 0.6 + Math.random() * 0.4 }
                    },
                    vertexShader: `
                        varying vec2 vUv;
                        varying vec3 vPosition;

                        void main() {
                            vUv = uv;
                            vPosition = position;
                            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform float time;
                        uniform vec3 color;
                        uniform float glowStrength;
                        varying vec2 vUv;
                        varying vec3 vPosition;

                        void main() {
                            // Create pulsing glow effect
                            float pulse = 0.8 + 0.2 * sin(time * 5.0 + vPosition.x * 10.0);

                            // Add edge glow
                            float edge = length(vUv - vec2(0.5));
                            float glow = smoothstep(0.5, 0.0, edge) * glowStrength * pulse;

                            // Add color variation
                            vec3 finalColor = color * (0.9 + 0.1 * sin(time * 3.0 + vPosition.y * 5.0));

                            gl_FragColor = vec4(finalColor, glow);
                        }
                    `,
                    transparent: true,
                    depthWrite: false
                });

                const particle = new THREE.Mesh(particleGeometry, particleMaterial);

                // Position particles throughout the cloud
                const angle = Math.random() * Math.PI * 2;
                const radius = 0.1 + Math.random() * 0.3;
                particle.position.set(
                    Math.cos(angle) * radius,
                    Math.sin(angle) * radius,
                    (Math.random() - 0.5) * 0.2
                );

                // Store properties for animation
                particle.userData.originalPos = particle.position.clone();
                particle.userData.speed = 0.5 + Math.random() * 1.5;
                particle.userData.angle = Math.random() * Math.PI * 2;
                particle.userData.pulseOffset = Math.random() * Math.PI * 2;
                particle.userData.dripping = Math.random() < 0.3; // Some particles will drip
                particle.userData.dripSpeed = 0.05 + Math.random() * 0.1;
                particle.userData.dripDistance = 0;
                particle.userData.maxDripDistance = 0.2 + Math.random() * 0.3;

                group.add(particle);
                particles.push(particle);
            }

            // Add toxic bubble effect
            const bubbleCount = 5;
            const bubbles = [];

            for (let i = 0; i < bubbleCount; i++) {
                const bubbleSize = 0.02 + Math.random() * 0.03;
                const bubbleGeometry = new THREE.SphereGeometry(bubbleSize, 8, 8);
                const bubbleMaterial = new THREE.MeshBasicMaterial({
                    color: 0xAAFF00,
                    transparent: true,
                    opacity: 0.6
                });

                const bubble = new THREE.Mesh(bubbleGeometry, bubbleMaterial);

                // Position bubbles at the bottom of the cloud
                const angle = Math.random() * Math.PI * 2;
                const radius = 0.1 + Math.random() * 0.2;

                bubble.position.set(
                    Math.cos(angle) * radius,
                    Math.sin(angle) * radius,
                    -0.2 - Math.random() * 0.1
                );

                // Store properties for animation
                bubble.userData.angle = angle;
                bubble.userData.radius = radius;
                bubble.userData.riseSpeed = 0.05 + Math.random() * 0.1;
                bubble.userData.wobbleSpeed = 1 + Math.random() * 2;
                bubble.userData.wobbleAmount = 0.01 + Math.random() * 0.02;
                bubble.userData.lifetime = 0;
                bubble.userData.maxLifetime = 1 + Math.random() * 2;

                group.add(bubble);
                bubbles.push(bubble);
            }

            // Add subtle green glow with two lights for more depth
            const mainLight = new THREE.PointLight(0x00FF00, 0.8, 2);
            mainLight.position.set(0, 0, 0);
            group.add(mainLight);

            const secondaryLight = new THREE.PointLight(0xCCFF00, 0.4, 1);
            secondaryLight.position.set(0.2, 0.1, 0.1);
            group.add(secondaryLight);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniforms for core
                if (core.material.uniforms) {
                    core.material.uniforms.time.value = time;
                }

                // Animate cloud puffs
                puffs.forEach((puff, index) => {
                    // Update shader time uniform
                    if (puff.material.uniforms) {
                        puff.material.uniforms.time.value = time;
                    }

                    // Slowly rotate each puff
                    puff.rotation.x += delta * puff.userData.rotationSpeed.x;
                    puff.rotation.y += delta * puff.userData.rotationSpeed.y;
                    puff.rotation.z += delta * puff.userData.rotationSpeed.z;

                    // More complex movement pattern
                    const offset = index * 0.5;
                    const pulse = Math.sin(time * 2 + offset) * 0.1 + 1.0;
                    puff.scale.set(
                        puff.scale.x * pulse,
                        puff.scale.y * pulse,
                        puff.scale.z * pulse
                    );

                    // Fade in and out
                    if (puff.material.uniforms && puff.material.uniforms.opacity) {
                        puff.material.uniforms.opacity.value = 0.4 + Math.sin(time * 1.5 + offset) * 0.2;
                    }
                });

                // Animate toxic particles
                particles.forEach((particle, index) => {
                    // Update shader time uniform
                    if (particle.material.uniforms) {
                        particle.material.uniforms.time.value = time;
                    }

                    if (particle.userData.dripping) {
                        // Dripping animation
                        particle.userData.dripDistance += delta * particle.userData.dripSpeed;

                        // Move downward
                        particle.position.y -= delta * particle.userData.dripSpeed;
                        particle.position.z -= delta * particle.userData.dripSpeed * 0.5;

                        // Add wobble
                        particle.position.x += (Math.random() - 0.5) * 0.01;

                        // Reset if dripped too far
                        if (particle.userData.dripDistance > particle.userData.maxDripDistance) {
                            // Reset position
                            const angle = Math.random() * Math.PI * 2;
                            const radius = 0.1 + Math.random() * 0.3;

                            particle.position.set(
                                Math.cos(angle) * radius,
                                Math.sin(angle) * radius,
                                (Math.random() - 0.5) * 0.2
                            );

                            particle.userData.angle = angle;
                            particle.userData.radius = radius;
                            particle.userData.dripDistance = 0;
                            particle.userData.maxDripDistance = 0.2 + Math.random() * 0.3;

                            // Create drip splash effect
                            if (Math.random() < 0.3) {
                                const splash = createParticleBurst(
                                    new THREE.Vector3(particle.position.x, particle.position.y - 0.1, particle.position.z),
                                    new THREE.Color(0x00FF00),
                                    3 // Small number of particles
                                );
                                group.add(splash);
                            }
                        }
                    } else {
                        // Circular motion
                        particle.userData.angle += delta * particle.userData.speed;
                        const radius = 0.1 + Math.sin(time + index) * 0.05;
                        particle.position.x = particle.userData.originalPos.x + Math.cos(particle.userData.angle) * radius;
                        particle.position.y = particle.userData.originalPos.y + Math.sin(particle.userData.angle) * radius;

                        // Randomly start dripping
                        if (Math.random() < 0.005) {
                            particle.userData.dripping = true;
                        }
                    }

                    // Rotate particles
                    particle.rotation.x += delta * 2;
                    particle.rotation.y += delta * 1.5;
                });

                // Animate toxic bubbles
                bubbles.forEach((bubble, index) => {
                    bubble.userData.lifetime += delta;

                    // Rise upward with wobble
                    bubble.position.z += delta * bubble.userData.riseSpeed;
                    bubble.position.x += Math.sin(time * bubble.userData.wobbleSpeed) * bubble.userData.wobbleAmount * delta;
                    bubble.position.y += Math.cos(time * bubble.userData.wobbleSpeed) * bubble.userData.wobbleAmount * delta;

                    // Fade out as they rise
                    const lifetimeRatio = bubble.userData.lifetime / bubble.userData.maxLifetime;
                    bubble.material.opacity = 0.6 * (1 - lifetimeRatio);

                    // Grow slightly as they rise
                    const growFactor = 1 + lifetimeRatio * 0.5;
                    bubble.scale.set(growFactor, growFactor, growFactor);

                    // Reset if lifetime exceeded
                    if (bubble.userData.lifetime >= bubble.userData.maxLifetime) {
                        // Reset position
                        const angle = Math.random() * Math.PI * 2;
                        const radius = 0.1 + Math.random() * 0.2;

                        bubble.position.set(
                            Math.cos(angle) * radius,
                            Math.sin(angle) * radius,
                            -0.2 - Math.random() * 0.1
                        );

                        bubble.userData.angle = angle;
                        bubble.userData.radius = radius;
                        bubble.userData.lifetime = 0;
                        bubble.userData.maxLifetime = 1 + Math.random() * 2;
                        bubble.material.opacity = 0.6;
                        bubble.scale.set(1, 1, 1);
                    }
                });

                // Animate lights
                mainLight.intensity = 0.7 + 0.3 * Math.sin(time * 4);
                secondaryLight.intensity = 0.3 + 0.2 * Math.sin(time * 7 + 1);
                secondaryLight.position.set(
                    0.2 + Math.sin(time) * 0.1,
                    0.1 + Math.cos(time) * 0.1,
                    0.1 + Math.sin(time * 1.5) * 0.1
                );

                // Randomly emit toxic gas puffs
                if (Math.random() < 0.02) {
                    const puffPosition = new THREE.Vector3(
                        (Math.random() - 0.5) * 0.3,
                        (Math.random() - 0.5) * 0.3,
                        (Math.random() - 0.5) * 0.3
                    );

                    const gasPuff = createParticleBurst(
                        puffPosition,
                        new THREE.Color(0x00FF00),
                        8 // More particles for gas puff
                    );

                    // Customize the gas puff behavior
                    if (gasPuff.userData.animate) {
                        const originalAnimate = gasPuff.userData.animate;
                        gasPuff.userData.animate = (delta) => {
                            // Call original animation
                            const result = originalAnimate(delta);

                            // Add custom behavior - slower movement, more spread
                            if (gasPuff.children && gasPuff.children.length > 0) {
                                for (let i = 0; i < gasPuff.children.length; i++) {
                                    const particle = gasPuff.children[i];
                                    if (particle.userData && particle.userData.velocity) {
                                        // Slow down the particles
                                        particle.userData.velocity.multiplyScalar(0.95);
                                    }
                                }
                            }

                            return result;
                        };
                    }

                    group.add(gasPuff);
                }
            };

            return group;
        }
    },

    // Shadow bolt
    shadow_bolt: {
        name: 'Shadow Bolt',
        damage: 1, // Reduced from 7 to 1
        speed: 9.0,
        range: 50.0, // Increased from 16.0 for much longer travel distance
        size: 0.25,
        gravity: -2.5,
        color: 0x800080, // Purple
        trailEffect: true,
        trailColor: 0x4B0082,
        trailLength: 12,
        trailType: 'shadow',
        trailWidth: 2,
        trailFade: true,
        impactEffect: 'shadow_impact',
        createMesh: (position) => {
            // Use imported procedural effects

            // Create a more impressive shadow bolt with tendrils and dark energy
            const group = new THREE.Group();
            group.position.copy(position);

            // Create the main shadow core with animated shader
            const coreGeometry = new THREE.SphereGeometry(0.18, 16, 16);
            const coreMaterial = createAnimatedShaderMaterial({
                baseColor: new THREE.Color(0x300030), // Very dark purple
                pulseColor: new THREE.Color(0x800080), // Purple pulse
                pulseRate: 3.0, // Slower, more ominous pulsing
                noiseStrength: 0.15, // Subtle noise for shadow effect
                glowStrength: 0.6
            });

            const core = new THREE.Mesh(coreGeometry, coreMaterial);
            group.add(core);

            // Create volumetric shadow aura
            const shadowAura = createVolumetricEffect(
                new THREE.Color(0x400040), // Dark purple core
                new THREE.Color(0x800080), // Purple outer
                5, // Layers
                0.3 // Size
            );
            group.add(shadowAura);

            // Create void effect (black hole-like distortion)
            const voidGeometry = new THREE.SphereGeometry(0.25, 16, 16);
            const voidMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    intensity: { value: 0.8 }
                },
                vertexShader: `
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    void main() {
                        vUv = uv;
                        vPosition = position;
                        vNormal = normal;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform float intensity;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    // Simple noise function
                    float noise(vec3 p) {
                        return fract(sin(dot(p, vec3(12.9898, 78.233, 45.164))) * 43758.5453);
                    }

                    void main() {
                        // Calculate fresnel effect for edge glow
                        vec3 viewDirection = normalize(cameraPosition - vPosition);
                        float fresnel = 1.0 - max(0.0, dot(viewDirection, vNormal));
                        fresnel = pow(fresnel, 3.0) * intensity;

                        // Add time-based distortion
                        float noiseVal = noise(vPosition * 5.0 + time * 0.5) * 0.1;

                        // Create void effect with purple edge glow
                        vec3 voidColor = mix(
                            vec3(0.0, 0.0, 0.0), // Black center
                            vec3(0.5, 0.0, 0.5), // Purple edge
                            fresnel + noiseVal
                        );

                        // Add subtle pulsing
                        float pulse = 0.8 + 0.2 * sin(time * 2.0);

                        // Combine effects
                        float alpha = (fresnel * pulse + noiseVal) * 0.7;

                        gl_FragColor = vec4(voidColor, alpha);
                    }
                `,
                transparent: true,
                depthWrite: false,
                blending: THREE.AdditiveBlending,
                side: THREE.DoubleSide
            });

            const voidEffect = new THREE.Mesh(voidGeometry, voidMaterial);
            group.add(voidEffect);

            // Create dark energy tendrils
            const tendrilCount = 8; // Increased from 5
            const tendrils = [];

            for (let i = 0; i < tendrilCount; i++) {
                // Create more complex curved tendrils
                const points = [];
                const segments = 12; // Increased from 8
                const length = 0.3 + Math.random() * 0.3;
                const curve = Math.random() * 0.2 + 0.1;

                // Create a more interesting curve with multiple sine waves
                for (let j = 0; j <= segments; j++) {
                    const t = j / segments;
                    const x = Math.sin(t * Math.PI) * curve;
                    const y = Math.sin(t * Math.PI * 2) * curve * 0.5;
                    const z = -t * length;
                    points.push(new THREE.Vector3(x, y, z));
                }

                // Create a dynamic trail for each tendril
                const tendrilTrail = createDynamicTrail(
                    points,
                    new THREE.Color(i % 2 === 0 ? 0xAA00AA : 0x800080), // Alternate purple shades
                    0.03 + Math.random() * 0.02 // Varying width
                );

                // Rotate tendrils around the core
                tendrilTrail.rotation.z = (i / tendrilCount) * Math.PI * 2;
                tendrilTrail.rotation.y = Math.random() * Math.PI * 0.5;

                // Store original rotation for animation
                tendrilTrail.userData.originalRotation = {
                    y: tendrilTrail.rotation.y,
                    z: tendrilTrail.rotation.z
                };
                tendrilTrail.userData.rotationSpeed = {
                    y: (Math.random() - 0.5) * 0.5,
                    z: (Math.random() - 0.5) * 0.5
                };
                tendrilTrail.userData.pulseOffset = Math.random() * Math.PI * 2;

                group.add(tendrilTrail);
                tendrils.push(tendrilTrail);
            }

            // Add shadow particles with more variety
            const particleCount = 15; // Increased from 10
            const particles = [];

            for (let i = 0; i < particleCount; i++) {
                // Create different particle shapes
                let particleGeometry;
                const particleType = Math.floor(Math.random() * 3);
                const size = 0.04 + Math.random() * 0.06;

                if (particleType === 0) {
                    // Sphere
                    particleGeometry = new THREE.SphereGeometry(size, 8, 8);
                } else if (particleType === 1) {
                    // Tetrahedron
                    particleGeometry = new THREE.TetrahedronGeometry(size * 1.2);
                } else {
                    // Octahedron
                    particleGeometry = new THREE.OctahedronGeometry(size);
                }

                // Create shader material for particles
                const particleMaterial = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        color: { value: new THREE.Color(i % 2 === 0 ? 0x800080 : 0x4B0082) }, // Alternate purple shades
                        opacity: { value: 0.6 + Math.random() * 0.4 }
                    },
                    vertexShader: `
                        varying vec2 vUv;
                        varying vec3 vPosition;

                        void main() {
                            vUv = uv;
                            vPosition = position;
                            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform float time;
                        uniform vec3 color;
                        uniform float opacity;
                        varying vec2 vUv;
                        varying vec3 vPosition;

                        void main() {
                            // Add subtle color variation
                            vec3 finalColor = color * (0.8 + 0.2 * sin(time * 5.0 + vPosition.x * 10.0));

                            // Add edge glow
                            float edge = length(vUv - vec2(0.5));
                            float glow = smoothstep(0.5, 0.0, edge) * 0.5 + 0.5;

                            gl_FragColor = vec4(finalColor, opacity * glow);
                        }
                    `,
                    transparent: true,
                    depthWrite: false
                });

                const particle = new THREE.Mesh(particleGeometry, particleMaterial);

                // Position particles around the core
                const angle = Math.random() * Math.PI * 2;
                const radius = 0.15 + Math.random() * 0.15;
                const height = (Math.random() - 0.5) * 0.2;

                particle.position.set(
                    Math.cos(angle) * radius,
                    Math.sin(angle) * radius,
                    height
                );

                // Store properties for animation
                particle.userData.angle = angle;
                particle.userData.radius = radius;
                particle.userData.height = height;
                particle.userData.speed = 0.5 + Math.random() * 2;
                particle.userData.pulseOffset = Math.random() * Math.PI * 2;
                particle.userData.verticalSpeed = (Math.random() - 0.5) * 0.2;

                group.add(particle);
                particles.push(particle);
            }

            // Add dark energy glow with color variation
            const mainLight = new THREE.PointLight(0x800080, 0.8, 2.5);
            mainLight.position.set(0, 0, 0);
            group.add(mainLight);

            // Add secondary light for color variation
            const secondaryLight = new THREE.PointLight(0x4B0082, 0.4, 1.5);
            secondaryLight.position.set(0, 0, 0);
            group.add(secondaryLight);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniforms
                if (core.material.uniforms) {
                    core.material.uniforms.time.value = time;
                }

                if (voidEffect.material.uniforms) {
                    voidEffect.material.uniforms.time.value = time;
                }

                // Animate volumetric shadow aura
                if (shadowAura.userData.animate) {
                    shadowAura.userData.animate(delta);
                }

                // Animate tendrils
                tendrils.forEach((tendril, index) => {
                    // Animate the dynamic trail if it has an animation function
                    if (tendril.userData.animate) {
                        tendril.userData.animate(delta);
                    }

                    // Rotate tendrils with varying speeds
                    tendril.rotation.y = tendril.userData.originalRotation.y +
                                        Math.sin(time * 0.5 + index) * 0.2 +
                                        time * tendril.userData.rotationSpeed.y;

                    tendril.rotation.z = tendril.userData.originalRotation.z +
                                        Math.sin(time * 0.7 + index * 0.5) * 0.3 +
                                        time * tendril.userData.rotationSpeed.z;

                    // Scale tendrils with pulsing effect
                    const pulse = 0.8 + 0.2 * Math.sin(time * 2 + tendril.userData.pulseOffset);
                    tendril.scale.set(pulse, pulse, pulse);
                });

                // Animate particles
                particles.forEach((particle, index) => {
                    // Update shader time uniform
                    if (particle.material.uniforms) {
                        particle.material.uniforms.time.value = time;
                    }

                    // Update particle position
                    particle.userData.angle += delta * particle.userData.speed;

                    // Orbit around the core with vertical bobbing
                    particle.position.x = Math.cos(particle.userData.angle) * particle.userData.radius;
                    particle.position.y = Math.sin(particle.userData.angle) * particle.userData.radius;
                    particle.position.z = particle.userData.height +
                                         Math.sin(time * 2 + particle.userData.pulseOffset) * 0.05;

                    // Rotate particles
                    particle.rotation.x += delta * 2;
                    particle.rotation.y += delta * 1.5;
                    particle.rotation.z += delta * 1;

                    // Pulse size
                    const sizePulse = 0.9 + 0.2 * Math.sin(time * 3 + particle.userData.pulseOffset);
                    particle.scale.set(sizePulse, sizePulse, sizePulse);
                });

                // Animate lights
                mainLight.intensity = 0.7 + 0.3 * Math.sin(time * 3);
                mainLight.color.setHSL(0.75 + 0.05 * Math.sin(time), 1, 0.5); // Subtle color shift

                secondaryLight.intensity = 0.3 + 0.2 * Math.sin(time * 5 + 1);
                secondaryLight.position.set(
                    Math.sin(time) * 0.1,
                    Math.cos(time) * 0.1,
                    Math.sin(time * 1.5) * 0.1
                );

                // Slowly rotate the entire bolt with subtle wobble
                group.rotation.z += delta * 0.3;
                group.rotation.x = Math.sin(time * 0.5) * 0.1;
                group.rotation.y = Math.cos(time * 0.7) * 0.1;
            };

            return group;
        }
    },

    // Energy blast
    energy_blast: {
        name: 'Energy Blast',
        damage: 15, // Restored to original value
        speed: 10.0,
        range: 55.0, // Increased from 18.0 for much longer travel distance
        size: 0.35,
        gravity: -1.5,
        color: 0xFFFFFF, // White
        trailEffect: true,
        trailColor: 0xF0F8FF,
        trailLength: 15,
        trailType: 'energy',
        trailWidth: 2.5,
        trailFade: true,
        impactEffect: 'energy_impact',
        createMesh: (position) => {
            // Use imported procedural effects

            // Create a more impressive energy blast with multiple layers and effects
            const group = new THREE.Group();
            group.position.copy(position);

            // Create the core of the energy blast with animated shader
            const coreGeometry = new THREE.SphereGeometry(0.18, 16, 16);
            const coreMaterial = createAnimatedShaderMaterial({
                baseColor: new THREE.Color(0xFFFFFF), // Bright white core
                pulseColor: new THREE.Color(0xAAFFFF), // Light blue-white pulse
                pulseRate: 6.0, // Fast pulsing for energy effect
                noiseStrength: 0.1, // Subtle noise for energy fluctuation
                glowStrength: 0.9 // Strong glow
            });

            const core = new THREE.Mesh(coreGeometry, coreMaterial);
            group.add(core);

            // Add volumetric energy field with multiple layers
            const energyField = createVolumetricEffect(
                new THREE.Color(0xFFFFFF), // White core
                new THREE.Color(0xAADDFF), // Light blue outer
                7, // More layers for more detailed energy field
                0.35 // Size
            );
            group.add(energyField);

            // Create energy rings with custom shader
            const ringCount = 4; // Increased from 3
            const rings = [];

            for (let i = 0; i < ringCount; i++) {
                const ringRadius = 0.25 + i * 0.08;
                const ringThickness = 0.02 + (i * 0.005);
                const ringGeometry = new THREE.TorusGeometry(ringRadius, ringThickness, 12, 48);

                // Create custom shader for energy rings
                const ringMaterial = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        baseColor: { value: new THREE.Color(0xF0F8FF) },
                        pulseColor: { value: new THREE.Color(0xFFFFFF) },
                        pulseRate: { value: 3.0 + i * 0.5 },
                        ringRadius: { value: ringRadius },
                        opacity: { value: 0.6 - (i * 0.1) }
                    },
                    vertexShader: `
                        varying vec2 vUv;
                        varying vec3 vPosition;

                        void main() {
                            vUv = uv;
                            vPosition = position;
                            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform float time;
                        uniform vec3 baseColor;
                        uniform vec3 pulseColor;
                        uniform float pulseRate;
                        uniform float ringRadius;
                        uniform float opacity;

                        varying vec2 vUv;
                        varying vec3 vPosition;

                        // Simple noise function
                        float noise(vec3 p) {
                            return fract(sin(dot(p, vec3(12.9898, 78.233, 45.164))) * 43758.5453);
                        }

                        void main() {
                            // Calculate pulse effect
                            float pulse = 0.5 + 0.5 * sin(time * pulseRate);

                            // Add flowing energy effect along the ring
                            float flowSpeed = time * 2.0;
                            float flowPos = mod(vUv.x * 6.28318 + flowSpeed, 6.28318) / 6.28318;
                            float flow = smoothstep(0.0, 0.3, flowPos) * smoothstep(1.0, 0.7, flowPos);

                            // Add noise for energy fluctuation
                            vec3 noisePos = vPosition * 5.0 + time * 0.5;
                            float noiseVal = noise(noisePos) * 0.1;

                            // Mix colors based on pulse and flow
                            vec3 finalColor = mix(baseColor, pulseColor, pulse * flow + noiseVal);

                            // Add energy glow
                            float glow = flow * 0.4 + 0.6;
                            finalColor *= glow;

                            // Set final color with opacity
                            gl_FragColor = vec4(finalColor, opacity * (0.8 + flow * 0.2));
                        }
                    `,
                    transparent: true,
                    depthWrite: false,
                    side: THREE.DoubleSide,
                    blending: THREE.AdditiveBlending
                });

                const ring = new THREE.Mesh(ringGeometry, ringMaterial);

                // Set random rotation for each ring
                ring.rotation.x = Math.random() * Math.PI;
                ring.rotation.y = Math.random() * Math.PI;
                ring.rotation.z = Math.random() * Math.PI;

                // Store rotation speeds for animation
                ring.userData.rotationSpeed = {
                    x: (0.2 + Math.random() * 0.3) * (Math.random() > 0.5 ? 1 : -1),
                    y: (0.2 + Math.random() * 0.3) * (Math.random() > 0.5 ? 1 : -1),
                    z: (0.2 + Math.random() * 0.3) * (Math.random() > 0.5 ? 1 : -1)
                };

                group.add(ring);
                rings.push(ring);
            }

            // Add energy particles with more variety and effects
            const particleCount = 20; // Increased from 12
            const particles = [];

            for (let i = 0; i < particleCount; i++) {
                // Create different particle shapes for more variety
                let particleGeometry;
                const particleType = Math.floor(Math.random() * 3);
                const size = 0.03 + Math.random() * 0.02;

                if (particleType === 0) {
                    // Sphere
                    particleGeometry = new THREE.SphereGeometry(size, 8, 8);
                } else if (particleType === 1) {
                    // Tetrahedron
                    particleGeometry = new THREE.TetrahedronGeometry(size * 1.2);
                } else {
                    // Octahedron
                    particleGeometry = new THREE.OctahedronGeometry(size);
                }

                // Create glowing energy material with custom shader
                const particleMaterial = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        color: { value: new THREE.Color(i % 3 === 0 ? 0xFFFFFF : (i % 3 === 1 ? 0xAAFFFF : 0x88DDFF)) },
                        glowStrength: { value: 0.7 + Math.random() * 0.3 }
                    },
                    vertexShader: `
                        varying vec2 vUv;
                        varying vec3 vPosition;

                        void main() {
                            vUv = uv;
                            vPosition = position;
                            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform float time;
                        uniform vec3 color;
                        uniform float glowStrength;
                        varying vec2 vUv;
                        varying vec3 vPosition;

                        void main() {
                            // Create pulsing glow effect
                            float pulse = 0.8 + 0.2 * sin(time * 8.0 + vPosition.x * 10.0);

                            // Add edge glow
                            float edge = length(vUv - vec2(0.5));
                            float glow = smoothstep(0.5, 0.0, edge) * glowStrength * pulse;

                            // Add color variation
                            vec3 finalColor = color * (0.9 + 0.1 * sin(time * 5.0 + vPosition.y * 8.0));

                            gl_FragColor = vec4(finalColor, glow);
                        }
                    `,
                    transparent: true,
                    depthWrite: false,
                    blending: THREE.AdditiveBlending
                });

                const particle = new THREE.Mesh(particleGeometry, particleMaterial);

                // Position particles in a sphere around the core
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.acos(2 * Math.random() - 1);
                const radius = 0.3 + Math.random() * 0.15;

                particle.position.x = radius * Math.sin(phi) * Math.cos(theta);
                particle.position.y = radius * Math.sin(phi) * Math.sin(theta);
                particle.position.z = radius * Math.cos(phi);

                // Store properties for animation
                particle.userData.originalPos = particle.position.clone();
                particle.userData.orbitSpeed = 0.5 + Math.random() * 2.0;
                particle.userData.orbitRadius = 0.05 + Math.random() * 0.15;
                particle.userData.orbitOffset = Math.random() * Math.PI * 2;
                particle.userData.pulseOffset = Math.random() * Math.PI * 2;
                particle.userData.rotationSpeed = new THREE.Vector3(
                    (Math.random() - 0.5) * 5,
                    (Math.random() - 0.5) * 5,
                    (Math.random() - 0.5) * 5
                );

                group.add(particle);
                particles.push(particle);
            }

            // Add energy burst particles that occasionally emit from the core
            const burstEmitter = new THREE.Object3D();
            burstEmitter.userData.nextBurstTime = Math.random() * 0.5;
            burstEmitter.userData.burstInterval = 0.5 + Math.random() * 0.5;
            group.add(burstEmitter);

            // Add dynamic lighting with multiple lights for more depth
            const mainLight = new THREE.PointLight(0xFFFFFF, 1.0, 2.0);
            mainLight.position.set(0, 0, 0);
            group.add(mainLight);

            const secondaryLight = new THREE.PointLight(0xAADDFF, 0.6, 1.5);
            secondaryLight.position.set(0.1, 0.1, 0.1);
            group.add(secondaryLight);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniforms for core
                if (core.material.uniforms) {
                    core.material.uniforms.time.value = time;
                }

                // Animate energy field layers
                if (energyField.userData && energyField.userData.animate) {
                    energyField.userData.animate(delta);
                }

                // Animate energy rings
                rings.forEach((ring, index) => {
                    // Update shader time uniform
                    if (ring.material.uniforms) {
                        ring.material.uniforms.time.value = time;
                    }

                    // Rotate rings at different speeds
                    ring.rotation.x += delta * ring.userData.rotationSpeed.x;
                    ring.rotation.y += delta * ring.userData.rotationSpeed.y;
                    ring.rotation.z += delta * ring.userData.rotationSpeed.z;
                });

                // Animate energy particles
                particles.forEach((particle, index) => {
                    // Update shader time uniform
                    if (particle.material.uniforms) {
                        particle.material.uniforms.time.value = time;
                    }

                    // Complex orbital motion
                    const offset = particle.userData.orbitOffset;
                    const speed = particle.userData.orbitSpeed;
                    const radius = particle.userData.orbitRadius;
                    const originalPos = particle.userData.originalPos;

                    // Create more complex orbital paths using multiple sine/cosine waves
                    particle.position.x = originalPos.x +
                        Math.sin(time * speed + offset) * radius * 1.2 +
                        Math.cos(time * speed * 0.7 + offset * 2) * radius * 0.3;

                    particle.position.y = originalPos.y +
                        Math.cos(time * speed + offset) * radius * 1.2 +
                        Math.sin(time * speed * 0.9 + offset * 3) * radius * 0.3;

                    particle.position.z = originalPos.z +
                        Math.sin(time * speed * 0.5 + offset) * radius * 1.0 +
                        Math.cos(time * speed * 0.6 + offset * 1.5) * radius * 0.4;

                    // Rotate particles
                    particle.rotation.x += delta * particle.userData.rotationSpeed.x;
                    particle.rotation.y += delta * particle.userData.rotationSpeed.y;
                    particle.rotation.z += delta * particle.userData.rotationSpeed.z;
                });

                // Handle energy burst emissions
                burstEmitter.userData.nextBurstTime -= delta;
                if (burstEmitter.userData.nextBurstTime <= 0) {
                    // Create a new energy burst
                    const burstDirection = new THREE.Vector3(
                        (Math.random() - 0.5) * 2,
                        (Math.random() - 0.5) * 2,
                        (Math.random() - 0.5) * 2
                    ).normalize();

                    const burstPosition = burstDirection.clone().multiplyScalar(0.2);
                    const burstColor = Math.random() > 0.5 ? 0xFFFFFF : 0xAADDFF;

                    const burst = createParticleBurst(
                        burstPosition,
                        new THREE.Color(burstColor),
                        5 + Math.floor(Math.random() * 5) // Random number of particles
                    );

                    // Customize the burst behavior
                    if (burst.userData && burst.userData.animate) {
                        const originalAnimate = burst.userData.animate;
                        burst.userData.animate = (delta) => {
                            // Call original animation
                            const result = originalAnimate(delta);

                            // Add custom behavior - faster movement, more energy-like
                            if (burst.children && burst.children.length > 0) {
                                for (let i = 0; i < burst.children.length; i++) {
                                    const particle = burst.children[i];
                                    if (particle.userData && particle.userData.velocity) {
                                        // Add some randomness to the velocity
                                        particle.userData.velocity.x += (Math.random() - 0.5) * 0.05;
                                        particle.userData.velocity.y += (Math.random() - 0.5) * 0.05;
                                        particle.userData.velocity.z += (Math.random() - 0.5) * 0.05;
                                    }
                                }
                            }

                            return result;
                        };
                    }

                    group.add(burst);

                    // Reset timer for next burst
                    burstEmitter.userData.nextBurstTime = burstEmitter.userData.burstInterval;
                    burstEmitter.userData.burstInterval = 0.3 + Math.random() * 0.7; // Randomize interval
                }

                // Animate lights
                mainLight.intensity = 0.8 + 0.4 * Math.sin(time * 8);
                secondaryLight.intensity = 0.4 + 0.3 * Math.sin(time * 12 + 2);
                secondaryLight.position.set(
                    0.1 + Math.sin(time * 3) * 0.1,
                    0.1 + Math.cos(time * 4) * 0.1,
                    0.1 + Math.sin(time * 5) * 0.1
                );
            };

            return group;
        }
    },

    // --- NEW PROJECTILE TYPES ---

    // Ash Cinders – Small, glowing red coals that drift like falling ash
    ash_cinders: {
        name: 'Ash Cinders',
        damage: 1,
        speed: 4.0, // Slow speed for drifting effect
        range: 12.0,
        size: 0.15, // Small size
        gravity: -0.5, // Very light gravity for floating effect
        color: 0xFF3300, // Glowing red
        trailEffect: true,
        trailColor: 0xFF6600,
        trailLength: 5, // Short trail
        trailType: 'flame',
        trailWidth: 1.2,
        trailFade: true,
        impactEffect: 'fire_impact',
        behavior: 'drift', // Custom behavior
        createMesh: (position) => {
            // Create a more realistic ash cinders effect with glowing embers
            const group = new THREE.Group();
            group.position.copy(position);

            // Create multiple ember particles
            const emberCount = 8;
            const embers = [];

            for (let i = 0; i < emberCount; i++) {
                // Create ember with random size
                const size = 0.05 + Math.random() * 0.1;
                const emberGeometry = new THREE.SphereGeometry(size, 6, 6);

                // Use different shades of red/orange for variety
                const emberColor = i % 3 === 0 ? 0xFF3300 : (i % 3 === 1 ? 0xFF6600 : 0xFF9900);
                const emberMaterial = new THREE.MeshBasicMaterial({
                    color: emberColor,
                    transparent: true,
                    opacity: 0.7 + Math.random() * 0.3
                });

                const ember = new THREE.Mesh(emberGeometry, emberMaterial);

                // Position embers in a loose cluster
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 0.15;
                ember.position.set(
                    Math.cos(angle) * radius,
                    Math.sin(angle) * radius,
                    (Math.random() - 0.5) * 0.1
                );

                // Store original position for animation
                ember.userData.originalPos = ember.position.clone();
                ember.userData.speed = 0.3 + Math.random() * 1.0;
                ember.userData.angle = Math.random() * Math.PI * 2;
                ember.userData.verticalSpeed = (Math.random() - 0.5) * 0.2;

                group.add(ember);
                embers.push(ember);
            }

            // Add ash particles (smaller, darker)
            const ashCount = 12;
            const ashes = [];

            for (let i = 0; i < ashCount; i++) {
                // Create smaller ash particles
                const size = 0.02 + Math.random() * 0.04;
                const ashGeometry = new THREE.SphereGeometry(size, 4, 4);

                // Use darker colors for ash
                const ashColor = i % 2 === 0 ? 0x663300 : 0x331100;
                const ashMaterial = new THREE.MeshBasicMaterial({
                    color: ashColor,
                    transparent: true,
                    opacity: 0.4 + Math.random() * 0.3
                });

                const ash = new THREE.Mesh(ashGeometry, ashMaterial);

                // Position ash particles around the embers
                const angle = Math.random() * Math.PI * 2;
                const radius = 0.1 + Math.random() * 0.2;
                ash.position.set(
                    Math.cos(angle) * radius,
                    Math.sin(angle) * radius,
                    (Math.random() - 0.5) * 0.15
                );

                // Store original position for animation
                ash.userData.originalPos = ash.position.clone();
                ash.userData.speed = 0.2 + Math.random() * 0.5; // Slower than embers
                ash.userData.angle = Math.random() * Math.PI * 2;
                ash.userData.verticalSpeed = (Math.random() - 0.3) * 0.1; // Mostly falling

                group.add(ash);
                ashes.push(ash);
            }

            // Add glow effect in the center
            const glowGeometry = new THREE.SphereGeometry(0.1, 8, 8);
            const glowMaterial = new THREE.MeshBasicMaterial({
                color: 0xFF6600,
                transparent: true,
                opacity: 0.4
            });
            const glow = new THREE.Mesh(glowGeometry, glowMaterial);
            group.add(glow);

            // Add flickering point light
            const light = new THREE.PointLight(0xFF3300, 0.7, 1.5);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;

                // Animate embers - slow drifting and pulsing
                embers.forEach((ember, index) => {
                    // Drift movement
                    ember.userData.angle += delta * ember.userData.speed;

                    // Circular drift with slight vertical movement
                    ember.position.x = ember.userData.originalPos.x + Math.cos(ember.userData.angle) * 0.05;
                    ember.position.y = ember.userData.originalPos.y + Math.sin(ember.userData.angle) * 0.05;
                    ember.position.y += ember.userData.verticalSpeed * delta;

                    // Keep embers within bounds by resetting position if they drift too far
                    if (ember.position.distanceTo(new THREE.Vector3(0, 0, 0)) > 0.3) {
                        ember.position.copy(ember.userData.originalPos);
                    }

                    // Pulse size for glowing effect
                    const pulse = 0.8 + Math.sin(group.userData.time * 5 + index) * 0.2;
                    ember.scale.set(pulse, pulse, pulse);

                    // Pulse opacity
                    ember.material.opacity = 0.7 + Math.sin(group.userData.time * 3 + index * 0.5) * 0.3;
                });

                // Animate ash particles - slower, more random movement
                ashes.forEach((ash, index) => {
                    // Slower drift
                    ash.userData.angle += delta * ash.userData.speed;

                    // More random movement
                    ash.position.x = ash.userData.originalPos.x + Math.cos(ash.userData.angle) * 0.08;
                    ash.position.y = ash.userData.originalPos.y + Math.sin(ash.userData.angle) * 0.08;
                    ash.position.y += ash.userData.verticalSpeed * delta; // Mostly falling

                    // Reset position if ash drifts too far
                    if (ash.position.distanceTo(new THREE.Vector3(0, 0, 0)) > 0.4) {
                        ash.position.copy(ash.userData.originalPos);
                    }

                    // Subtle opacity changes
                    ash.material.opacity = 0.4 + Math.sin(group.userData.time * 2 + index * 0.7) * 0.2;
                });

                // Pulse the central glow
                const glowPulse = 0.9 + Math.sin(group.userData.time * 8) * 0.3;
                glow.scale.set(glowPulse, glowPulse, glowPulse);
                glowMaterial.opacity = 0.3 + Math.sin(group.userData.time * 6) * 0.2;

                // Flicker the light
                light.intensity = 0.5 + Math.random() * 0.4;
            };

            return group;
        }
    },

    // Eye Tears – Teardrop-shaped bullets that fall diagonally
    eye_tears: {
        name: 'Eye Tears',
        damage: 1,
        speed: 5.0,
        range: 40.0, // Increased from 10.0 for much longer travel distance
        size: 0.2,
        gravity: -3.0, // Stronger gravity for falling effect
        color: 0x88CCFF, // Light blue
        trailEffect: true,
        trailColor: 0xAADDFF,
        trailLength: 8,
        trailType: 'water',
        trailWidth: 1.5,
        trailFade: true,
        impactEffect: 'water_impact',
        behavior: 'weep', // Custom behavior for splitting
        createMesh: (position) => {
            // Create a more realistic teardrop shape
            const group = new THREE.Group();
            group.position.copy(position);

            // Create a custom teardrop shape using LatheGeometry
            const tearPoints = [];
            const tearSegments = 12;

            // Define the teardrop profile curve
            for (let i = 0; i <= tearSegments; i++) {
                const t = i / tearSegments;
                // Create a teardrop shape - wider at top, pointed at bottom
                const y = -t * 0.4; // Height
                let radius;
                if (t < 0.5) {
                    // Top half is more rounded
                    radius = 0.15 * Math.sqrt(1 - t);
                } else {
                    // Bottom half tapers to a point
                    radius = 0.15 * Math.pow(1 - t, 1.5);
                }
                tearPoints.push(new THREE.Vector2(radius, y));
            }

            // Create the teardrop geometry
            const tearGeometry = new THREE.LatheGeometry(tearPoints, 16);
            const tearMaterial = new THREE.MeshBasicMaterial({
                color: 0x88CCFF,
                transparent: true,
                opacity: 0.8
            });
            const tear = new THREE.Mesh(tearGeometry, tearMaterial);
            tear.rotation.x = Math.PI; // Orient with point downward
            group.add(tear);

            // Add smaller droplets that follow behind
            const dropletCount = 2 + Math.floor(Math.random() * 2);
            for (let i = 0; i < dropletCount; i++) {
                const size = 0.05 + Math.random() * 0.05;
                const dropletGeometry = new THREE.SphereGeometry(size, 6, 6);
                const dropletMaterial = new THREE.MeshBasicMaterial({
                    color: 0xAADDFF,
                    transparent: true,
                    opacity: 0.6 + Math.random() * 0.2
                });
                const droplet = new THREE.Mesh(dropletGeometry, dropletMaterial);

                // Position droplets behind the main tear
                droplet.position.z = 0.1 + Math.random() * 0.2;
                droplet.position.x = (Math.random() - 0.5) * 0.1;
                droplet.position.y = (Math.random() - 0.5) * 0.1;

                group.add(droplet);
            }

            // Add water ripple effect at the tip
            const rippleGeometry = new THREE.RingGeometry(0.02, 0.05, 16);
            const rippleMaterial = new THREE.MeshBasicMaterial({
                color: 0xCCEEFF,
                transparent: true,
                opacity: 0.5,
                side: THREE.DoubleSide
            });
            const ripple = new THREE.Mesh(rippleGeometry, rippleMaterial);
            ripple.rotation.x = Math.PI / 2; // Lay flat
            ripple.position.y = -0.2; // Position at the tip
            group.add(ripple);

            // Add subtle blue glow
            const light = new THREE.PointLight(0x88CCFF, 0.7, 2);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;

                // Make the tear wobble slightly as it falls
                const wobble = Math.sin(group.userData.time * 10) * 0.05;
                tear.rotation.z = wobble;

                // Pulse the transparency for a liquid effect
                tearMaterial.opacity = 0.7 + Math.sin(group.userData.time * 5) * 0.1;

                // Animate the ripple
                ripple.scale.set(
                    1 + Math.sin(group.userData.time * 8) * 0.2,
                    1 + Math.sin(group.userData.time * 8) * 0.2,
                    1
                );
                rippleMaterial.opacity = 0.5 + Math.sin(group.userData.time * 8) * 0.2;

                // Animate the light
                light.intensity = 0.6 + Math.sin(group.userData.time * 6) * 0.1;
            };

            return group;
        }
    },

    // Blood Threads – Fine red lines that slowly move
    blood_threads: {
        name: 'Blood Threads',
        damage: 1,
        speed: 3.0, // Very slow
        range: 50.0, // Increased from 15.0 for much longer travel distance
        size: 0.1, // Very thin
        gravity: 0, // No gravity
        color: 0x990000, // Dark red
        trailEffect: true,
        trailColor: 0xCC0000,
        trailLength: 20, // Long trail to create thread effect
        trailType: 'ribbon',
        trailWidth: 0.8,
        trailFade: true,
        impactEffect: 'blood_impact',
        behavior: 'web', // Custom behavior for forming webs
        createMesh: (position) => {
            // Create a more complex blood thread system with multiple strands and droplets
            const group = new THREE.Group();
            group.position.copy(position);

            // Create main blood core
            const coreGeometry = new THREE.SphereGeometry(0.08, 8, 8);
            const coreMaterial = createAnimatedShaderMaterial({
                baseColor: new THREE.Color(0x990000), // Dark red
                pulseColor: new THREE.Color(0xCC0000), // Brighter red pulse
                pulseRate: 2.0, // Slow pulsing for viscous effect
                noiseStrength: 0.15, // Subtle noise for organic look
                glowStrength: 0.7 // Moderate glow
            });

            const core = new THREE.Mesh(coreGeometry, coreMaterial);
            group.add(core);

            // Create multiple blood strands using curves
            const strandCount = 5;
            const strands = [];

            for (let i = 0; i < strandCount; i++) {
                // Create a curved path for each strand
                const curvePoints = [];
                const segments = 10 + Math.floor(Math.random() * 5);
                const length = 0.3 + Math.random() * 0.2;
                const curviness = 0.1 + Math.random() * 0.1;

                // Generate points for a curved strand
                for (let j = 0; j <= segments; j++) {
                    const t = j / segments;
                    const angle = Math.PI * 2 * (i / strandCount);

                    // Add some randomness to the curve
                    const xOffset = Math.sin(t * Math.PI * (1 + i)) * curviness;
                    const yOffset = Math.cos(t * Math.PI * (2 + i)) * curviness;

                    curvePoints.push(new THREE.Vector3(
                        Math.cos(angle) * 0.05 * t + xOffset,
                        Math.sin(angle) * 0.05 * t + yOffset,
                        -t * length
                    ));
                }

                // Create a smooth curve from the points
                const curve = new THREE.CatmullRomCurve3(curvePoints);

                // Create tube geometry along the curve
                const tubeGeometry = new THREE.TubeGeometry(
                    curve,
                    segments * 2, // tubular segments
                    0.02 - (i * 0.002), // radius - thinner for secondary strands
                    8, // radial segments
                    false // closed
                );

                // Create custom shader material for the blood strand
                const strandMaterial = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        baseColor: { value: new THREE.Color(i === 0 ? 0x990000 : 0x880000) },
                        flowSpeed: { value: 0.5 + Math.random() * 0.5 },
                        viscosity: { value: 0.7 + Math.random() * 0.3 },
                        opacity: { value: 0.9 - (i * 0.1) }
                    },
                    vertexShader: `
                        varying vec2 vUv;
                        varying vec3 vPosition;

                        void main() {
                            vUv = uv;
                            vPosition = position;
                            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform float time;
                        uniform vec3 baseColor;
                        uniform float flowSpeed;
                        uniform float viscosity;
                        uniform float opacity;

                        varying vec2 vUv;
                        varying vec3 vPosition;

                        // Simple noise function
                        float noise(vec2 p) {
                            return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
                        }

                        void main() {
                            // Create flowing blood effect along the strand
                            float flow = fract(vUv.x - time * flowSpeed);

                            // Add viscous, organic variations
                            float noiseVal = noise(vUv * 10.0 + time) * 0.1;

                            // Darker at edges for rounded look
                            float edge = smoothstep(0.0, 0.4, vUv.y) * smoothstep(1.0, 0.6, vUv.y);

                            // Create viscous flow patterns
                            float viscousPattern = smoothstep(viscosity, viscosity + 0.1, flow + noiseVal);

                            // Darken color based on viscous pattern
                            vec3 finalColor = baseColor * (0.8 + viscousPattern * 0.4);

                            // Add slight color variation
                            finalColor *= (0.9 + noiseVal);

                            // Set final color with opacity
                            gl_FragColor = vec4(finalColor, opacity * edge);
                        }
                    `,
                    transparent: true,
                    depthWrite: false,
                    side: THREE.DoubleSide
                });

                const strand = new THREE.Mesh(tubeGeometry, strandMaterial);

                // Add slight random rotation to each strand
                strand.rotation.x = (Math.random() - 0.5) * 0.2;
                strand.rotation.y = (Math.random() - 0.5) * 0.2;

                group.add(strand);
                strands.push(strand);

                // Add blood droplets along the strand
                if (Math.random() > 0.5) {
                    const dropletCount = 2 + Math.floor(Math.random() * 3);

                    for (let k = 0; k < dropletCount; k++) {
                        const t = 0.3 + (k / dropletCount) * 0.7; // Position along the strand
                        const point = curve.getPoint(t);

                        // Create droplet with teardrop shape
                        const dropletGeometry = new THREE.SphereGeometry(0.02 + Math.random() * 0.02, 8, 8);
                        const dropletMaterial = new THREE.MeshBasicMaterial({
                            color: 0xAA0000,
                            transparent: true,
                            opacity: 0.9
                        });

                        const droplet = new THREE.Mesh(dropletGeometry, dropletMaterial);
                        droplet.position.copy(point);
                        droplet.scale.y = 1.2 + Math.random() * 0.3; // Elongate slightly

                        // Store data for animation
                        droplet.userData.originalPos = point.clone();
                        droplet.userData.fallSpeed = 0.05 + Math.random() * 0.1;
                        droplet.userData.oscillateSpeed = 1.0 + Math.random() * 2.0;
                        droplet.userData.oscillateAmount = 0.005 + Math.random() * 0.01;
                        droplet.userData.falling = Math.random() > 0.5; // Some droplets fall, others stay attached

                        group.add(droplet);
                    }
                }
            }

            // Add subtle red glow
            const light = new THREE.PointLight(0xFF0000, 0.5, 1.0);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniforms for core
                if (core.material.uniforms) {
                    core.material.uniforms.time.value = time;
                }

                // Animate blood strands
                strands.forEach((strand) => {
                    if (strand.material.uniforms) {
                        strand.material.uniforms.time.value = time;
                    }
                });

                // Animate droplets
                group.children.forEach((child) => {
                    if (child.userData.originalPos && child.userData.fallSpeed) {
                        // Handle falling droplets
                        if (child.userData.falling) {
                            // Move downward
                            child.position.y -= child.userData.fallSpeed * delta;

                            // Add slight oscillation for realistic falling
                            const oscillation = Math.sin(time * child.userData.oscillateSpeed) * child.userData.oscillateAmount;
                            child.position.x = child.userData.originalPos.x + oscillation;
                            child.position.z = child.userData.originalPos.z + oscillation;

                            // Elongate as it falls
                            const stretch = 1.2 + Math.min(2.0, (child.userData.originalPos.y - child.position.y) * 5);
                            child.scale.y = stretch;

                            // Remove if fallen too far
                            if (child.position.y < child.userData.originalPos.y - 0.5) {
                                child.visible = false;
                            }
                        } else {
                            // For attached droplets, just add slight pulsing
                            const pulse = 0.9 + Math.sin(time * 2 + child.userData.oscillateSpeed) * 0.1;
                            child.scale.set(pulse, pulse * 1.2, pulse);
                        }
                    }
                });

                // Pulse the light
                light.intensity = 0.4 + Math.sin(time * 3) * 0.2;

                // Occasionally spawn new droplets
                if (Math.random() < 0.02) {
                    // Find a random strand
                    const strandIndex = Math.floor(Math.random() * strands.length);
                    const strand = strands[strandIndex];

                    if (strand.geometry.parameters && strand.geometry.parameters.path) {
                        const path = strand.geometry.parameters.path;
                        const t = Math.random(); // Random position along the strand
                        const point = path.getPoint(t);

                        // Create new droplet
                        const dropletGeometry = new THREE.SphereGeometry(0.02 + Math.random() * 0.02, 8, 8);
                        const dropletMaterial = new THREE.MeshBasicMaterial({
                            color: 0xAA0000,
                            transparent: true,
                            opacity: 0.9
                        });

                        const droplet = new THREE.Mesh(dropletGeometry, dropletMaterial);
                        droplet.position.copy(point);
                        droplet.scale.y = 1.2;

                        // Convert point to world space
                        const worldPoint = point.clone();
                        worldPoint.applyMatrix4(strand.matrixWorld);

                        // Store data for animation
                        droplet.userData.originalPos = worldPoint.clone();
                        droplet.userData.fallSpeed = 0.05 + Math.random() * 0.1;
                        droplet.userData.oscillateSpeed = 1.0 + Math.random() * 2.0;
                        droplet.userData.oscillateAmount = 0.005 + Math.random() * 0.01;
                        droplet.userData.falling = true; // New droplets always fall

                        group.add(droplet);
                    }
                }
            };

            return group;
        }
    },

    // Wax Drips – Thick droplets of glowing wax
    wax_drips: {
        name: 'Wax Drips',
        damage: 1,
        speed: 4.0,
        range: 35.0, // Increased from 8.0 for much longer travel distance
        size: 0.25,
        gravity: -5.0, // Heavy for dripping effect
        color: 0xFFEECC, // Warm wax color
        trailEffect: true,
        trailColor: 0xFFDDAA,
        trailLength: 6,
        impactEffect: 'wax_impact',
        behavior: 'stick', // Custom behavior for sticking to floor
        createMesh: (position) => {
            // Create a more realistic wax drip with multiple components
            const group = new THREE.Group();
            group.position.copy(position);

            // Create main wax body with custom teardrop shape
            const waxPoints = [];
            const segments = 12;

            // Define teardrop profile curve
            for (let i = 0; i <= segments; i++) {
                const t = i / segments;
                // Create teardrop shape - wider at top, pointed at bottom
                const y = -t * 0.5; // Height
                let radius;
                if (t < 0.3) {
                    // Top is more rounded
                    radius = 0.25 * (1 - t * 0.5);
                } else {
                    // Bottom tapers to a point
                    radius = 0.25 * Math.pow(1 - t, 1.2);
                }
                waxPoints.push(new THREE.Vector2(radius, y));
            }

            // Create the wax geometry
            const waxGeometry = new THREE.LatheGeometry(waxPoints, 16);

            // Create custom shader material for the wax
            const waxMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    baseColor: { value: new THREE.Color(0xFFEECC) },
                    glowColor: { value: new THREE.Color(0xFFDDAA) },
                    viscosity: { value: 0.8 },
                    flowSpeed: { value: 0.3 }
                },
                vertexShader: `
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    void main() {
                        vUv = uv;
                        vPosition = position;
                        vNormal = normalize(normalMatrix * normal);
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform vec3 baseColor;
                    uniform vec3 glowColor;
                    uniform float viscosity;
                    uniform float flowSpeed;

                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    // Simple noise function
                    float noise(vec2 p) {
                        return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
                    }

                    void main() {
                        // Create flowing wax effect
                        float flow = fract(vUv.y - time * flowSpeed);

                        // Add viscous, organic variations
                        float noiseVal = noise(vUv * 10.0 + time * 0.5) * 0.1;

                        // Create viscous flow patterns
                        float viscousPattern = smoothstep(viscosity, viscosity + 0.2, flow + noiseVal);

                        // Calculate rim lighting effect
                        float rimLight = 1.0 - max(0.0, dot(vNormal, vec3(0.0, 0.0, 1.0)));
                        rimLight = pow(rimLight, 3.0) * 0.5;

                        // Mix colors based on viscous pattern and rim lighting
                        vec3 finalColor = mix(baseColor, glowColor, viscousPattern * 0.3 + rimLight);

                        // Add slight color variation
                        finalColor *= (0.9 + noiseVal);

                        // Add subtle translucency
                        float alpha = 0.9 - rimLight * 0.2;

                        gl_FragColor = vec4(finalColor, alpha);
                    }
                `,
                transparent: true,
                side: THREE.DoubleSide
            });

            const waxBody = new THREE.Mesh(waxGeometry, waxMaterial);
            waxBody.rotation.x = Math.PI; // Orient with point downward
            group.add(waxBody);

            // Add smaller wax droplets
            const dropletCount = 3 + Math.floor(Math.random() * 3);
            const droplets = [];

            for (let i = 0; i < dropletCount; i++) {
                // Create different droplet shapes for variety
                let dropletGeometry;
                const dropletType = Math.floor(Math.random() * 3);
                const size = 0.08 + Math.random() * 0.08;

                if (dropletType === 0) {
                    // Teardrop shape using LatheGeometry
                    const tearPoints = [];
                    const tearSegments = 8;

                    for (let j = 0; j <= tearSegments; j++) {
                        const t = j / tearSegments;
                        const y = -t * 0.2;
                        const radius = 0.08 * Math.pow(1 - t, 1.3);
                        tearPoints.push(new THREE.Vector2(radius, y));
                    }

                    dropletGeometry = new THREE.LatheGeometry(tearPoints, 8);
                } else if (dropletType === 1) {
                    // Sphere
                    dropletGeometry = new THREE.SphereGeometry(size * 0.7, 8, 8);
                } else {
                    // Elongated sphere
                    dropletGeometry = new THREE.SphereGeometry(size * 0.6, 8, 8);
                }

                // Create material with slight color variation
                const hue = Math.random() * 0.05; // Small hue variation
                const dropletColor = new THREE.Color(0xFFEECC).offsetHSL(hue, 0, 0);

                const dropletMaterial = new THREE.MeshBasicMaterial({
                    color: dropletColor,
                    transparent: true,
                    opacity: 0.85 + Math.random() * 0.15
                });

                const droplet = new THREE.Mesh(dropletGeometry, dropletMaterial);

                // Position droplets around the main wax body
                const angle = Math.random() * Math.PI * 2;
                const radius = 0.1 + Math.random() * 0.15;
                const height = -0.1 - Math.random() * 0.3; // Below the main body

                droplet.position.set(
                    Math.cos(angle) * radius,
                    height,
                    Math.sin(angle) * radius
                );

                // Rotate teardrop shapes to point downward
                if (dropletType === 0) {
                    droplet.rotation.x = Math.PI;
                }

                // Elongate some droplets
                if (dropletType === 2) {
                    droplet.scale.y = 1.5 + Math.random() * 0.5;
                }

                // Store data for animation
                droplet.userData.originalPos = droplet.position.clone();
                droplet.userData.fallSpeed = 0.2 + Math.random() * 0.3;
                droplet.userData.fallDelay = Math.random() * 1.0; // Random delay before falling
                droplet.userData.fallTimer = 0;
                droplet.userData.isFalling = false;

                group.add(droplet);
                droplets.push(droplet);
            }

            // Add wax splatter at the bottom
            const splatterCount = 5 + Math.floor(Math.random() * 5);
            const splatters = [];

            for (let i = 0; i < splatterCount; i++) {
                // Create small splatter particles
                const size = 0.03 + Math.random() * 0.04;
                const splatterGeometry = new THREE.SphereGeometry(size, 6, 6);

                // Slightly darker color for splatters
                const splatterMaterial = new THREE.MeshBasicMaterial({
                    color: 0xFFDDAA,
                    transparent: true,
                    opacity: 0.7 + Math.random() * 0.3
                });

                const splatter = new THREE.Mesh(splatterGeometry, splatterMaterial);

                // Position splatters at the bottom of the wax drip
                const angle = Math.random() * Math.PI * 2;
                const radius = 0.05 + Math.random() * 0.25;
                const height = -0.4 - Math.random() * 0.1; // At the bottom

                splatter.position.set(
                    Math.cos(angle) * radius,
                    height,
                    Math.sin(angle) * radius
                );

                // Flatten the splatters
                splatter.scale.y = 0.3 + Math.random() * 0.2;

                // Store original position for animation
                splatter.userData.originalPos = splatter.position.clone();
                splatter.userData.pulseSpeed = 0.5 + Math.random() * 1.0;
                splatter.userData.pulseOffset = Math.random() * Math.PI * 2;

                group.add(splatter);
                splatters.push(splatter);
            }

            // Add warm glow with multiple lights for depth
            const mainLight = new THREE.PointLight(0xFFDDAA, 0.8, 2.0);
            mainLight.position.set(0, 0, 0);
            group.add(mainLight);

            const secondaryLight = new THREE.PointLight(0xFFCC88, 0.4, 1.0);
            secondaryLight.position.set(0, -0.3, 0);
            group.add(secondaryLight);

            // Add animation
            group.userData.time = 0;
            group.userData.nextDropTime = 0.5 + Math.random() * 0.5;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniform for wax body
                if (waxBody.material.uniforms) {
                    waxBody.material.uniforms.time.value = time;
                }

                // Animate wax body - subtle pulsing
                const bodyPulse = 1.0 + Math.sin(time * 1.5) * 0.05;
                waxBody.scale.x = bodyPulse;
                waxBody.scale.z = bodyPulse;

                // Animate droplets
                droplets.forEach((droplet) => {
                    // Update fall timer
                    if (!droplet.userData.isFalling) {
                        droplet.userData.fallTimer += delta;

                        // Start falling after delay
                        if (droplet.userData.fallTimer >= droplet.userData.fallDelay) {
                            droplet.userData.isFalling = true;
                        }
                    }

                    // Handle falling droplets
                    if (droplet.userData.isFalling) {
                        // Move downward
                        droplet.position.y -= droplet.userData.fallSpeed * delta;

                        // Add slight oscillation for realistic falling
                        const oscillation = Math.sin(time * 3) * 0.01;
                        droplet.position.x = droplet.userData.originalPos.x + oscillation;
                        droplet.position.z = droplet.userData.originalPos.z + oscillation;

                        // Elongate as it falls
                        const stretch = 1.0 + Math.min(1.5, (droplet.userData.originalPos.y - droplet.position.y) * 2);
                        droplet.scale.y = stretch;

                        // Remove if fallen too far
                        if (droplet.position.y < -0.8) {
                            droplet.visible = false;

                            // Create a new splatter at the bottom
                            if (Math.random() < 0.7) {
                                const size = 0.03 + Math.random() * 0.03;
                                const splatterGeometry = new THREE.SphereGeometry(size, 6, 6);
                                const splatterMaterial = new THREE.MeshBasicMaterial({
                                    color: 0xFFDDAA,
                                    transparent: true,
                                    opacity: 0.7
                                });

                                const newSplatter = new THREE.Mesh(splatterGeometry, splatterMaterial);

                                // Position at the bottom, using the x/z of the fallen droplet
                                newSplatter.position.set(
                                    droplet.position.x,
                                    -0.45 - Math.random() * 0.05,
                                    droplet.position.z
                                );

                                // Flatten the splatter
                                newSplatter.scale.y = 0.2 + Math.random() * 0.2;

                                group.add(newSplatter);
                                splatters.push(newSplatter);
                            }
                        }
                    } else {
                        // For attached droplets, add slight pulsing
                        const pulse = 0.95 + Math.sin(time * 2 + droplet.userData.fallDelay * 5) * 0.05;
                        droplet.scale.set(pulse, pulse, pulse);
                    }
                });

                // Animate splatters - subtle pulsing
                splatters.forEach((splatter) => {
                    const pulse = 0.95 + Math.sin(time * splatter.userData.pulseSpeed + splatter.userData.pulseOffset) * 0.05;
                    splatter.scale.x = pulse;
                    splatter.scale.z = pulse;
                });

                // Pulse the lights
                mainLight.intensity = 0.7 + Math.sin(time * 2) * 0.2;
                secondaryLight.intensity = 0.3 + Math.sin(time * 3 + 1) * 0.2;

                // Occasionally spawn new droplets
                group.userData.nextDropTime -= delta;
                if (group.userData.nextDropTime <= 0) {
                    // Find a hidden droplet to reuse, or create a new one if all are visible
                    let dropletToRespawn = droplets.find(d => !d.visible);

                    if (dropletToRespawn) {
                        // Reset the droplet
                        const angle = Math.random() * Math.PI * 2;
                        const radius = 0.1 + Math.random() * 0.15;
                        const height = -0.1 - Math.random() * 0.2;

                        dropletToRespawn.position.set(
                            Math.cos(angle) * radius,
                            height,
                            Math.sin(angle) * radius
                        );

                        dropletToRespawn.userData.originalPos = dropletToRespawn.position.clone();
                        dropletToRespawn.userData.fallSpeed = 0.2 + Math.random() * 0.3;
                        dropletToRespawn.userData.fallDelay = 0.1 + Math.random() * 0.3; // Shorter delay for respawned droplets
                        dropletToRespawn.userData.fallTimer = 0;
                        dropletToRespawn.userData.isFalling = false;

                        // Reset scale
                        dropletToRespawn.scale.set(1, 1, 1);
                        dropletToRespawn.visible = true;
                    }

                    // Reset timer for next drop
                    group.userData.nextDropTime = 0.3 + Math.random() * 0.5;
                }
            };

            return group;
        }
    },

    // Gold Coins – Cursed ancient coins that spin as they fly
    gold_coins: {
        name: 'Gold Coins',
        damage: 1,
        speed: 8.0,
        range: 45.0, // Increased from 12.0 for much longer travel distance
        size: 0.2,
        gravity: -3.0,
        color: 0xFFD700, // Gold
        trailEffect: true,
        trailColor: 0xFFF8A0,
        trailLength: 5,
        trailType: 'particles',
        trailWidth: 1.5,
        trailFade: true,
        impactEffect: 'coin_impact',
        behavior: 'spin', // Custom spinning behavior
        createMesh: (position) => {
            // Create a more detailed gold coin with embossed details
            const group = new THREE.Group();
            group.position.copy(position);

            // Create main coin body
            const coinGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.05, 32);
            const coinMaterial = new THREE.MeshBasicMaterial({
                color: 0xFFD700, // Gold
                transparent: false,
                opacity: 1.0
            });
            const coin = new THREE.Mesh(coinGeometry, coinMaterial);
            coin.rotation.x = Math.PI / 2; // Orient flat like a coin
            group.add(coin);

            // Add embossed rim
            const rimGeometry = new THREE.TorusGeometry(0.19, 0.02, 8, 32);
            const rimMaterial = new THREE.MeshBasicMaterial({
                color: 0xDAA520, // Darker gold for rim
                transparent: false,
                opacity: 1.0
            });
            const rim = new THREE.Mesh(rimGeometry, rimMaterial);
            rim.rotation.x = Math.PI / 2; // Orient with coin
            group.add(rim);

            // Add embossed details on front face (simplified skull symbol)
            const detailsGroup = new THREE.Group();

            // Create a simple embossed symbol (circle for skull)
            const symbolGeometry = new THREE.CircleGeometry(0.1, 16);
            const symbolMaterial = new THREE.MeshBasicMaterial({
                color: 0xDAA520, // Darker gold for details
                transparent: false,
                opacity: 1.0
            });
            const symbol = new THREE.Mesh(symbolGeometry, symbolMaterial);
            symbol.position.z = 0.026; // Slightly raised from coin surface
            detailsGroup.add(symbol);

            // Add eye sockets (two small circles)
            const eyeGeometry = new THREE.CircleGeometry(0.02, 8);
            const eyeMaterial = new THREE.MeshBasicMaterial({
                color: 0xB8860B, // Even darker gold for eyes
                transparent: false,
                opacity: 1.0
            });

            const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            leftEye.position.set(-0.04, 0.03, 0.027);
            detailsGroup.add(leftEye);

            const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            rightEye.position.set(0.04, 0.03, 0.027);
            detailsGroup.add(rightEye);

            // Add mouth (curved line)
            const mouthGeometry = new THREE.TorusGeometry(0.04, 0.01, 2, 8, Math.PI);
            const mouthMaterial = new THREE.MeshBasicMaterial({
                color: 0xB8860B, // Darker gold for mouth
                transparent: false,
                opacity: 1.0
            });
            const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
            mouth.position.set(0, -0.03, 0.027);
            mouth.rotation.x = Math.PI;
            detailsGroup.add(mouth);

            group.add(detailsGroup);

            // Add small sparkle particles
            const sparkleCount = 5;
            const sparkles = [];

            for (let i = 0; i < sparkleCount; i++) {
                const sparkleGeometry = new THREE.SphereGeometry(0.02, 4, 4);
                const sparkleMaterial = new THREE.MeshBasicMaterial({
                    color: 0xFFFFCC, // Bright yellow-white
                    transparent: true,
                    opacity: 0.8
                });

                const sparkle = new THREE.Mesh(sparkleGeometry, sparkleMaterial);

                // Position sparkles around the coin edge
                const angle = (i / sparkleCount) * Math.PI * 2;
                sparkle.position.set(
                    Math.cos(angle) * 0.2,
                    Math.sin(angle) * 0.2,
                    (Math.random() - 0.5) * 0.05
                );

                // Store original position for animation
                sparkle.userData.originalPos = sparkle.position.clone();
                sparkle.userData.pulseSpeed = 3 + Math.random() * 5;
                sparkle.userData.pulseOffset = Math.random() * Math.PI * 2;

                group.add(sparkle);
                sparkles.push(sparkle);
            }

            // Add golden glow
            const light = new THREE.PointLight(0xFFD700, 0.8, 2);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.spinSpeed = Math.random() * 5 + 5;
            group.userData.wobbleAmount = Math.random() * 0.2 + 0.1;
            group.userData.animate = (delta) => {
                group.userData.time += delta;

                // Spin the coin
                group.rotation.z += group.userData.spinSpeed * delta;

                // Add slight wobble for more realistic spinning
                const wobble = Math.sin(group.userData.time * 10) * group.userData.wobbleAmount;
                group.rotation.x = wobble;
                group.rotation.y = wobble * 0.5;

                // Animate sparkles
                sparkles.forEach((sparkle, index) => {
                    // Pulse opacity for twinkling effect
                    const pulse = Math.sin(group.userData.time * sparkle.userData.pulseSpeed + sparkle.userData.pulseOffset);
                    sparkle.material.opacity = Math.max(0, 0.5 + pulse * 0.5);

                    // Pulse size
                    const sizePulse = 0.8 + pulse * 0.4;
                    sparkle.scale.set(sizePulse, sizePulse, sizePulse);
                });

                // Pulse the light intensity
                light.intensity = 0.7 + Math.sin(group.userData.time * 8) * 0.3;
            };

            return group;
        }
    },

    // Thorn Seeds – Bullets that embed in the ground and sprout spikes after a delay
    thorn_seeds: {
        name: 'Thorn Seeds',
        damage: 1,
        speed: 6.0,
        range: 45.0, // Increased from 14.0 for much longer travel distance
        size: 0.15,
        gravity: -4.0,
        color: 0x336600, // Dark green
        trailEffect: true,
        trailColor: 0x448800,
        trailLength: 4,
        impactEffect: 'thorn_impact',
        behavior: 'sprout', // Custom behavior for sprouting
        createMesh: (position) => {
            // Create a more organic and detailed thorn seed
            const group = new THREE.Group();
            group.position.copy(position);

            // Create seed pod with custom shader
            const seedGeometry = new THREE.DodecahedronGeometry(0.12, 1);
            const seedMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    baseColor: { value: new THREE.Color(0x336600) },
                    accentColor: { value: new THREE.Color(0x448800) },
                    pulseRate: { value: 1.5 }
                },
                vertexShader: `
                    uniform float time;
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    void main() {
                        vUv = uv;
                        vPosition = position;
                        vNormal = normalize(normalMatrix * normal);

                        // Add subtle organic pulsing to vertices
                        float pulse = sin(time * 1.5) * 0.03;
                        vec3 pos = position;
                        pos += normal * pulse * (0.5 + 0.5 * sin(position.y * 10.0));

                        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform vec3 baseColor;
                    uniform vec3 accentColor;
                    uniform float pulseRate;

                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    // Simple noise function
                    float noise(vec3 p) {
                        return fract(sin(dot(p, vec3(12.9898, 78.233, 45.164))) * 43758.5453);
                    }

                    void main() {
                        // Create organic pattern
                        float noiseVal = noise(vPosition * 15.0 + time * 0.2) * 0.15;

                        // Add vein-like patterns
                        float veins = smoothstep(0.4, 0.6, sin(vPosition.x * 20.0) * sin(vPosition.y * 20.0) * sin(vPosition.z * 20.0));

                        // Add subtle pulsing
                        float pulse = 0.5 + 0.5 * sin(time * pulseRate);

                        // Mix colors based on pattern
                        vec3 finalColor = mix(baseColor, accentColor, veins * pulse + noiseVal);

                        // Add rim lighting effect
                        float rim = 1.0 - max(0.0, dot(vNormal, vec3(0.0, 0.0, 1.0)));
                        rim = pow(rim, 3.0) * 0.3;
                        finalColor = mix(finalColor, accentColor, rim);

                        gl_FragColor = vec4(finalColor, 1.0);
                    }
                `
            });

            const seedPod = new THREE.Mesh(seedGeometry, seedMaterial);
            // Rotate slightly for more interesting look
            seedPod.rotation.set(
                Math.random() * Math.PI * 0.2,
                Math.random() * Math.PI * 2,
                Math.random() * Math.PI * 0.2
            );
            group.add(seedPod);

            // Add thorns/spikes using custom geometry
            const thornCount = 6;
            const thorns = [];

            for (let i = 0; i < thornCount; i++) {
                // Create thorn geometry
                const height = 0.08 + Math.random() * 0.06;
                const radius = 0.01 + Math.random() * 0.01;

                // Create custom thorn shape
                const thornGeometry = new THREE.ConeGeometry(radius, height, 4, 1);

                // Create material with slight color variation
                const hue = Math.random() * 0.1; // Small hue variation
                const thornColor = new THREE.Color(0x448800).offsetHSL(hue, 0, 0);

                const thornMaterial = new THREE.MeshBasicMaterial({
                    color: thornColor,
                    transparent: false
                });

                const thorn = new THREE.Mesh(thornGeometry, thornMaterial);

                // Position thorns around the seed pod
                const angle = (i / thornCount) * Math.PI * 2;
                const distance = 0.1;

                // Position at edge of seed pod
                thorn.position.set(
                    Math.cos(angle) * distance,
                    Math.sin(angle) * distance * 0.2, // Flatten distribution vertically
                    Math.sin(angle) * distance
                );

                // Rotate thorns to point outward
                thorn.lookAt(thorn.position.clone().multiplyScalar(2));
                // Add slight random rotation
                thorn.rotateZ(Math.random() * 0.5 - 0.25);

                // Store original position and rotation for animation
                thorn.userData.originalPos = thorn.position.clone();
                thorn.userData.originalRot = thorn.rotation.clone();
                thorn.userData.pulseSpeed = 0.5 + Math.random() * 1.0;
                thorn.userData.pulseOffset = Math.random() * Math.PI * 2;

                group.add(thorn);
                thorns.push(thorn);
            }

            // Add small vines/tendrils using curves
            const vineCount = 4;
            const vines = [];

            for (let i = 0; i < vineCount; i++) {
                // Create a curved path for each vine
                const points = [];
                const segments = 8;
                const length = 0.15 + Math.random() * 0.1;
                const curviness = 0.05 + Math.random() * 0.05;

                // Generate points for a curved vine
                for (let j = 0; j <= segments; j++) {
                    const t = j / segments;
                    const angle = Math.PI * 2 * (i / vineCount);

                    // Add some randomness to the curve
                    const xOffset = Math.sin(t * Math.PI * (1 + i)) * curviness;
                    const yOffset = Math.cos(t * Math.PI * (2 + i)) * curviness;

                    points.push(new THREE.Vector3(
                        Math.cos(angle) * 0.05 * t + xOffset,
                        Math.sin(angle) * 0.05 * t + yOffset,
                        -t * length
                    ));
                }

                // Create a smooth curve from the points
                const curve = new THREE.CatmullRomCurve3(points);

                // Create tube geometry along the curve
                const tubeGeometry = new THREE.TubeGeometry(
                    curve,
                    segments * 2, // tubular segments
                    0.01, // radius
                    4, // radial segments
                    false // closed
                );

                // Create material with slight color variation
                const vineColor = new THREE.Color(0x448800).offsetHSL(Math.random() * 0.1, 0, 0);
                const vineMaterial = new THREE.MeshBasicMaterial({
                    color: vineColor,
                    transparent: false
                });

                const vine = new THREE.Mesh(tubeGeometry, vineMaterial);

                // Add slight random rotation
                vine.rotation.x = (Math.random() - 0.5) * 0.2;
                vine.rotation.y = (Math.random() - 0.5) * 0.2;

                group.add(vine);
                vines.push(vine);
            }

            // Add small leaves at the end of vines
            const leafCount = 3;
            const leaves = [];

            for (let i = 0; i < leafCount; i++) {
                // Create leaf geometry
                const leafGeometry = new THREE.PlaneGeometry(0.06, 0.1);

                // Create material with slight color variation
                const leafColor = new THREE.Color(0x55AA00).offsetHSL(Math.random() * 0.1, 0, 0);
                const leafMaterial = new THREE.MeshBasicMaterial({
                    color: leafColor,
                    transparent: true,
                    opacity: 0.9,
                    side: THREE.DoubleSide
                });

                const leaf = new THREE.Mesh(leafGeometry, leafMaterial);

                // Position leaves at random positions
                const angle = Math.random() * Math.PI * 2;
                const distance = 0.15 + Math.random() * 0.1;
                const height = -0.1 - Math.random() * 0.1;

                leaf.position.set(
                    Math.cos(angle) * distance,
                    height,
                    Math.sin(angle) * distance
                );

                // Rotate leaves to look more natural
                leaf.rotation.set(
                    Math.random() * Math.PI,
                    Math.random() * Math.PI,
                    Math.random() * Math.PI
                );

                // Store original position and rotation for animation
                leaf.userData.originalPos = leaf.position.clone();
                leaf.userData.originalRot = leaf.rotation.clone();
                leaf.userData.waveSpeed = 0.5 + Math.random() * 1.0;
                leaf.userData.waveOffset = Math.random() * Math.PI * 2;

                group.add(leaf);
                leaves.push(leaf);
            }

            // Add subtle green glow
            const light = new THREE.PointLight(0x55AA00, 0.4, 1.0);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniform for seed pod
                if (seedPod.material.uniforms) {
                    seedPod.material.uniforms.time.value = time;
                }

                // Rotate the seed pod slowly
                seedPod.rotation.y += delta * 0.5;

                // Animate thorns - subtle pulsing
                thorns.forEach((thorn) => {
                    // Pulse thorns in and out slightly
                    const pulse = 0.05 * Math.sin(time * thorn.userData.pulseSpeed + thorn.userData.pulseOffset);
                    thorn.position.copy(thorn.userData.originalPos);
                    thorn.position.addScaledVector(thorn.position.clone().normalize(), pulse);

                    // Slight rotation
                    thorn.rotation.z = thorn.userData.originalRot.z + Math.sin(time * 0.5 + thorn.userData.pulseOffset) * 0.1;
                });

                // Animate leaves - gentle swaying
                leaves.forEach((leaf) => {
                    // Wave leaves gently
                    const wave = Math.sin(time * leaf.userData.waveSpeed + leaf.userData.waveOffset) * 0.1;
                    leaf.rotation.x = leaf.userData.originalRot.x + wave;
                    leaf.rotation.z = leaf.userData.originalRot.z + wave * 0.5;
                });

                // Pulse the light
                light.intensity = 0.3 + Math.sin(time * 2) * 0.1;
            };

            return group;
        }
    },

    // Wind Spirits – Wispy, spiral shapes that twist and swirl
    wind_spirits: {
        name: 'Wind Spirits',
        damage: 1,
        speed: 7.0,
        range: 50.0, // Increased from 16.0 for much longer travel distance
        size: 0.3,
        gravity: -0.5, // Light gravity
        color: 0xCCFFFF, // Light cyan
        trailEffect: true,
        trailColor: 0xFFFFFF,
        trailLength: 15,
        trailType: 'ribbon',
        trailWidth: 2.0,
        trailFade: true,
        impactEffect: 'wind_impact',
        behavior: 'swirl', // Custom swirling behavior
        createMesh: (position) => {
            // Create an ethereal wind spirit using procedural effects
            const group = new THREE.Group();
            group.position.copy(position);

            // Create ethereal core with animated shader
            const coreGeometry = new THREE.SphereGeometry(0.15, 12, 12);
            const coreMaterial = createAnimatedShaderMaterial({
                baseColor: new THREE.Color(0xCCFFFF), // Light cyan
                pulseColor: new THREE.Color(0xFFFFFF), // White pulse
                pulseRate: 3.0, // Medium pulsing for wind effect
                noiseStrength: 0.3, // More noise for wind turbulence
                glowStrength: 0.8 // Strong glow
            });

            const core = new THREE.Mesh(coreGeometry, coreMaterial);
            group.add(core);

            // Create wind vortex using custom shader
            const vortexMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    color: { value: new THREE.Color(0xCCFFFF) },
                    opacity: { value: 0.7 }
                },
                vertexShader: `
                    uniform float time;
                    varying vec2 vUv;
                    varying vec3 vPosition;

                    void main() {
                        vUv = uv;
                        vPosition = position;

                        // Add wind-like movement to vertices
                        float windStrength = 0.1;
                        float windSpeed = 3.0;
                        vec3 pos = position;

                        // Apply sine wave deformation
                        pos.x += sin(pos.y * 10.0 + time * windSpeed) * windStrength;
                        pos.z += cos(pos.y * 8.0 + time * windSpeed * 0.8) * windStrength;

                        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform vec3 color;
                    uniform float opacity;
                    varying vec2 vUv;
                    varying vec3 vPosition;

                    void main() {
                        // Create flowing wind effect
                        float flow = fract(vUv.y - time * 0.5);

                        // Add turbulence
                        float turbulence = sin(vPosition.x * 10.0 + time * 5.0) *
                                          cos(vPosition.z * 8.0 + time * 4.0) * 0.1;

                        // Edge fading for wispy look
                        float edge = smoothstep(0.0, 0.4, vUv.y) * smoothstep(1.0, 0.6, vUv.y);

                        // Combine effects
                        float finalOpacity = opacity * edge * (0.7 + turbulence);

                        // Add subtle color variation
                        vec3 finalColor = color * (0.9 + turbulence);

                        gl_FragColor = vec4(finalColor, finalOpacity);
                    }
                `,
                transparent: true,
                side: THREE.DoubleSide,
                blending: THREE.AdditiveBlending
            });

            // Create spiral ribbons using the vortex material
            const spirals = [];
            const spiralCount = 4;

            for (let i = 0; i < spiralCount; i++) {
                const points = [];
                const turns = 2.5;
                const pointsPerTurn = 15;
                const totalPoints = Math.floor(turns * pointsPerTurn);

                for (let j = 0; j < totalPoints; j++) {
                    const t = j / totalPoints;
                    const angle = t * Math.PI * 2 * turns + (i * Math.PI / 2); // Offset each spiral
                    const radius = 0.05 + t * 0.25; // Increasing radius
                    const height = t * 0.4 - 0.2; // Vertical position

                    points.push(new THREE.Vector3(
                        Math.cos(angle) * radius,
                        height,
                        Math.sin(angle) * radius
                    ));
                }

                // Create tube geometry for more volume
                const curve = new THREE.CatmullRomCurve3(points);
                const tubeGeometry = new THREE.TubeGeometry(
                    curve,
                    totalPoints, // tubular segments
                    0.02 - (i * 0.003), // radius - thinner for secondary spirals
                    6, // radial segments
                    false // closed
                );

                // Clone the material to have independent time values
                const spiralMaterial = vortexMaterial.clone();
                spiralMaterial.uniforms.opacity.value = 0.7 - (i * 0.1);

                const spiral = new THREE.Mesh(tubeGeometry, spiralMaterial);
                spiral.userData.rotationSpeed = 0.5 + Math.random() * 0.5;
                spiral.userData.rotationDirection = i % 2 === 0 ? 1 : -1;

                group.add(spiral);
                spirals.push(spiral);
            }

            // Add wind particles using particle burst effect
            const particleBurst = createParticleBurst(
                new THREE.Vector3(0, 0, 0),
                new THREE.Color(0xFFFFFF),
                20 // More particles
            );

            // Customize particle behavior
            if (particleBurst.userData.animate) {
                const originalAnimate = particleBurst.userData.animate;
                particleBurst.userData.animate = (delta) => {
                    // Call original animation
                    originalAnimate(delta);

                    // Add custom behavior - slower movement, more swirling
                    if (particleBurst.children && particleBurst.children.length > 0) {
                        for (let i = 0; i < particleBurst.children.length; i++) {
                            const particle = particleBurst.children[i];
                            if (particle.userData && particle.userData.velocity) {
                                // Reduce speed for longer-lasting particles
                                particle.userData.velocity.multiplyScalar(0.98);

                                // Add swirling motion
                                const swirl = new THREE.Vector3(
                                    Math.sin(particle.position.y * 5 + group.userData.time * 3) * 0.01,
                                    0,
                                    Math.cos(particle.position.x * 5 + group.userData.time * 3) * 0.01
                                );
                                particle.userData.velocity.add(swirl);
                            }
                        }
                    }
                };
            }

            group.add(particleBurst);

            // Add volumetric effect for more depth
            const windField = createVolumetricEffect(
                new THREE.Color(0xCCFFFF), // Cyan core
                new THREE.Color(0xFFFFFF), // White outer
                5, // Fewer layers for better performance
                0.3 // Size
            );
            group.add(windField);

            // Add subtle cyan glow light
            const light = new THREE.PointLight(0xCCFFFF, 0.7, 2.5);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add occasional shockwave effect
            const shockwaveEmitter = new THREE.Object3D();
            shockwaveEmitter.userData.nextShockwaveTime = Math.random() * 0.5;
            shockwaveEmitter.userData.shockwaveInterval = 0.8 + Math.random() * 0.4;
            group.add(shockwaveEmitter);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniforms for core
                if (core.material.uniforms) {
                    core.material.uniforms.time.value = time;
                }

                // Animate spirals
                spirals.forEach((spiral) => {
                    if (spiral.material.uniforms) {
                        spiral.material.uniforms.time.value = time;
                    }

                    // Rotate spirals
                    spiral.rotation.y += delta * spiral.userData.rotationSpeed * spiral.userData.rotationDirection;
                });

                // Animate volumetric effect
                if (windField.userData && windField.userData.animate) {
                    windField.userData.animate(delta);
                }

                // Pulse the light
                light.intensity = 0.6 + Math.sin(time * 5) * 0.3;

                // Handle shockwave emissions
                shockwaveEmitter.userData.nextShockwaveTime -= delta;
                if (shockwaveEmitter.userData.nextShockwaveTime <= 0) {
                    // Create a new shockwave
                    const shockwave = createShockwaveEffect(
                        new THREE.Vector3(0, 0, 0),
                        new THREE.Color(0xCCFFFF)
                    );

                    // Customize shockwave properties
                    shockwave.userData.maxRadius = 0.4 + Math.random() * 0.2;
                    shockwave.userData.duration = 0.6 + Math.random() * 0.2;

                    group.add(shockwave);

                    // Reset timer for next shockwave
                    shockwaveEmitter.userData.nextShockwaveTime = shockwaveEmitter.userData.shockwaveInterval;
                }
            };

            return group;
        }
    },

    // Rust Flakes – Jagged orange flecks that fall like dust
    rust_flakes: {
        name: 'Rust Flakes',
        damage: 1,
        speed: 5.0,
        range: 40.0, // Increased from 10.0 for much longer travel distance
        size: 0.1, // Small size
        gravity: -2.0,
        color: 0xCC5500, // Rust orange
        trailEffect: true,
        trailColor: 0xAA4400,
        trailLength: 3, // Short trail
        impactEffect: 'rust_impact',
        behavior: 'corrode', // Custom corrosion behavior
        createMesh: (position) => {
            // Create a more detailed rust flake cluster with procedural effects
            const group = new THREE.Group();
            group.position.copy(position);

            // Create a central rust core
            const coreGeometry = new THREE.DodecahedronGeometry(0.08, 0);
            const coreMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    baseColor: { value: new THREE.Color(0xCC5500) },
                    darkColor: { value: new THREE.Color(0x883300) },
                    rustiness: { value: 0.8 }
                },
                vertexShader: `
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    void main() {
                        vUv = uv;
                        vPosition = position;
                        vNormal = normalize(normalMatrix * normal);
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform vec3 baseColor;
                    uniform vec3 darkColor;
                    uniform float rustiness;

                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    // Simplex noise function
                    vec3 mod289(vec3 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
                    vec4 mod289(vec4 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
                    vec4 permute(vec4 x) { return mod289(((x*34.0)+1.0)*x); }
                    vec4 taylorInvSqrt(vec4 r) { return 1.79284291400159 - 0.85373472095314 * r; }

                    float snoise(vec3 v) {
                        const vec2 C = vec2(1.0/6.0, 1.0/3.0);
                        const vec4 D = vec4(0.0, 0.5, 1.0, 2.0);

                        // First corner
                        vec3 i  = floor(v + dot(v, C.yyy));
                        vec3 x0 = v - i + dot(i, C.xxx);

                        // Other corners
                        vec3 g = step(x0.yzx, x0.xyz);
                        vec3 l = 1.0 - g;
                        vec3 i1 = min(g.xyz, l.zxy);
                        vec3 i2 = max(g.xyz, l.zxy);

                        vec3 x1 = x0 - i1 + C.xxx;
                        vec3 x2 = x0 - i2 + C.yyy;
                        vec3 x3 = x0 - D.yyy;

                        // Permutations
                        i = mod289(i);
                        vec4 p = permute(permute(permute(
                                 i.z + vec4(0.0, i1.z, i2.z, 1.0))
                               + i.y + vec4(0.0, i1.y, i2.y, 1.0))
                               + i.x + vec4(0.0, i1.x, i2.x, 1.0));

                        // Gradients: 7x7 points over a square, mapped onto an octahedron.
                        float n_ = 0.142857142857; // 1.0/7.0
                        vec3 ns = n_ * D.wyz - D.xzx;

                        vec4 j = p - 49.0 * floor(p * ns.z * ns.z);

                        vec4 x_ = floor(j * ns.z);
                        vec4 y_ = floor(j - 7.0 * x_);

                        vec4 x = x_ *ns.x + ns.yyyy;
                        vec4 y = y_ *ns.x + ns.yyyy;
                        vec4 h = 1.0 - abs(x) - abs(y);

                        vec4 b0 = vec4(x.xy, y.xy);
                        vec4 b1 = vec4(x.zw, y.zw);

                        vec4 s0 = floor(b0)*2.0 + 1.0;
                        vec4 s1 = floor(b1)*2.0 + 1.0;
                        vec4 sh = -step(h, vec4(0.0));

                        vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy;
                        vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww;

                        vec3 p0 = vec3(a0.xy, h.x);
                        vec3 p1 = vec3(a0.zw, h.y);
                        vec3 p2 = vec3(a1.xy, h.z);
                        vec3 p3 = vec3(a1.zw, h.w);

                        // Normalise gradients
                        vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
                        p0 *= norm.x;
                        p1 *= norm.y;
                        p2 *= norm.z;
                        p3 *= norm.w;

                        // Mix final noise value
                        vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
                        m = m * m;
                        return 42.0 * dot(m*m, vec4(dot(p0,x0), dot(p1,x1), dot(p2,x2), dot(p3,x3)));
                    }

                    void main() {
                        // Create rust texture with noise
                        float noise1 = snoise(vPosition * 20.0 + time * 0.1) * 0.5 + 0.5;
                        float noise2 = snoise(vPosition * 10.0 - time * 0.05) * 0.5 + 0.5;

                        // Create rust pattern
                        float rustPattern = smoothstep(0.4, 0.6, noise1 * noise2);

                        // Add edge darkening for more realistic look
                        float edge = 1.0 - max(0.0, dot(vNormal, vec3(0.0, 0.0, 1.0)));
                        edge = pow(edge, 2.0) * 0.5;

                        // Create flaking effect
                        float flaking = step(0.7, noise1 * noise2 + edge);

                        // Mix colors based on pattern
                        vec3 finalColor = mix(baseColor, darkColor, rustPattern * rustiness + edge);

                        // Darken edges where rust flakes off
                        finalColor = mix(finalColor, darkColor, flaking * 0.7);

                        gl_FragColor = vec4(finalColor, 1.0);
                    }
                `
            });

            const core = new THREE.Mesh(coreGeometry, coreMaterial);
            group.add(core);

            // Create detailed rust flakes with custom geometries
            const flakeCount = 12; // More flakes for better effect
            const flakes = [];

            for (let i = 0; i < flakeCount; i++) {
                // Create different flake shapes for variety
                let flakeGeometry;
                const flakeType = Math.floor(Math.random() * 4);
                const size = 0.03 + Math.random() * 0.04;

                if (flakeType === 0) {
                    // Tetrahedron
                    flakeGeometry = new THREE.TetrahedronGeometry(size, 0);
                } else if (flakeType === 1) {
                    // Octahedron
                    flakeGeometry = new THREE.OctahedronGeometry(size * 0.8, 0);
                } else if (flakeType === 2) {
                    // Flat shard
                    const shardShape = new THREE.Shape();
                    const shardSize = size * 15;

                    // Create jagged shape
                    shardShape.moveTo(0, 0);
                    shardShape.lineTo(shardSize, 0);
                    shardShape.lineTo(shardSize * 0.8, shardSize * 0.5);
                    shardShape.lineTo(shardSize, shardSize);
                    shardShape.lineTo(0, shardSize * 0.7);

                    flakeGeometry = new THREE.ShapeGeometry(shardShape);
                    flakeGeometry.scale(0.01, 0.01, 0.01); // Scale down to match other flakes
                } else {
                    // Custom jagged piece
                    flakeGeometry = new THREE.DodecahedronGeometry(size * 0.7, 0);
                }

                // Create material with color variation
                const hue = Math.random() * 0.1; // Small hue variation
                const lightness = Math.random() * 0.2; // Lightness variation
                const flakeColor = new THREE.Color(0xCC5500).offsetHSL(hue, 0, lightness);

                const flakeMaterial = new THREE.MeshBasicMaterial({
                    color: flakeColor,
                    transparent: true,
                    opacity: 0.9 - Math.random() * 0.3
                });

                const flake = new THREE.Mesh(flakeGeometry, flakeMaterial);

                // Position flakes in a cloud around the core
                const radius = 0.1 + Math.random() * 0.1;
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.acos(2 * Math.random() - 1);

                flake.position.set(
                    radius * Math.sin(phi) * Math.cos(theta),
                    radius * Math.sin(phi) * Math.sin(theta),
                    radius * Math.cos(phi)
                );

                // Random rotation
                flake.rotation.set(
                    Math.random() * Math.PI * 2,
                    Math.random() * Math.PI * 2,
                    Math.random() * Math.PI * 2
                );

                // Store original position and rotation for animation
                flake.userData.originalPos = flake.position.clone();
                flake.userData.originalRot = flake.rotation.clone();
                flake.userData.spinSpeed = {
                    x: (Math.random() * 2 - 1) * 2,
                    y: (Math.random() * 2 - 1) * 2,
                    z: (Math.random() * 2 - 1) * 2
                };
                flake.userData.oscillateSpeed = 0.5 + Math.random() * 2.0;
                flake.userData.oscillateAmount = 0.01 + Math.random() * 0.02;
                flake.userData.oscillateOffset = Math.random() * Math.PI * 2;

                group.add(flake);
                flakes.push(flake);
            }

            // Add rust dust particles
            const dustCount = 20;
            const dustParticles = [];

            for (let i = 0; i < dustCount; i++) {
                // Create tiny dust particles
                const size = 0.01 + Math.random() * 0.01;
                const dustGeometry = new THREE.SphereGeometry(size, 4, 4);

                // Create material with color variation
                const dustColor = new THREE.Color(0xAA4400).offsetHSL(Math.random() * 0.1, 0, Math.random() * 0.2);
                const dustMaterial = new THREE.MeshBasicMaterial({
                    color: dustColor,
                    transparent: true,
                    opacity: 0.4 + Math.random() * 0.3
                });

                const dust = new THREE.Mesh(dustGeometry, dustMaterial);

                // Position dust in a wider cloud
                const radius = 0.15 + Math.random() * 0.15;
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.acos(2 * Math.random() - 1);

                dust.position.set(
                    radius * Math.sin(phi) * Math.cos(theta),
                    radius * Math.sin(phi) * Math.sin(theta),
                    radius * Math.cos(phi)
                );

                // Store data for animation
                dust.userData.originalPos = dust.position.clone();
                dust.userData.speed = 0.2 + Math.random() * 0.3;
                dust.userData.direction = new THREE.Vector3(
                    Math.random() * 2 - 1,
                    Math.random() * 2 - 1,
                    Math.random() * 2 - 1
                ).normalize();
                dust.userData.lifetime = 0;
                dust.userData.maxLifetime = 1.0 + Math.random() * 1.0;

                group.add(dust);
                dustParticles.push(dust);
            }

            // Add subtle orange glow
            const light = new THREE.PointLight(0xFF6600, 0.3, 1.0);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.nextDustTime = 0.1 + Math.random() * 0.2;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniform for core
                if (core.material.uniforms) {
                    core.material.uniforms.time.value = time;
                }

                // Rotate the core slowly
                core.rotation.y += delta * 0.2;
                core.rotation.z += delta * 0.1;

                // Animate flakes - spinning and slight oscillation
                flakes.forEach((flake) => {
                    // Spin flakes
                    flake.rotation.x += flake.userData.spinSpeed.x * delta;
                    flake.rotation.y += flake.userData.spinSpeed.y * delta;
                    flake.rotation.z += flake.userData.spinSpeed.z * delta;

                    // Add oscillation for more natural movement
                    const oscillation = Math.sin(time * flake.userData.oscillateSpeed + flake.userData.oscillateOffset) * flake.userData.oscillateAmount;
                    flake.position.x = flake.userData.originalPos.x + oscillation;
                    flake.position.y = flake.userData.originalPos.y + oscillation * 0.5;
                    flake.position.z = flake.userData.originalPos.z + oscillation * 0.7;
                });

                // Animate dust particles
                dustParticles.forEach((dust, index) => {
                    // Update lifetime
                    dust.userData.lifetime += delta;

                    // Move dust in its direction
                    dust.position.addScaledVector(dust.userData.direction, dust.userData.speed * delta);

                    // Fade out as lifetime increases
                    const fadeRatio = dust.userData.lifetime / dust.userData.maxLifetime;
                    dust.material.opacity = Math.max(0, 0.7 * (1 - fadeRatio));

                    // Reset dust if lifetime exceeded
                    if (dust.userData.lifetime >= dust.userData.maxLifetime) {
                        // Reset position to near the core
                        const radius = 0.08 + Math.random() * 0.05;
                        const theta = Math.random() * Math.PI * 2;
                        const phi = Math.acos(2 * Math.random() - 1);

                        dust.position.set(
                            radius * Math.sin(phi) * Math.cos(theta),
                            radius * Math.sin(phi) * Math.sin(theta),
                            radius * Math.cos(phi)
                        );

                        // Reset properties
                        dust.userData.originalPos = dust.position.clone();
                        dust.userData.direction = new THREE.Vector3(
                            Math.random() * 2 - 1,
                            Math.random() * 2 - 1,
                            Math.random() * 2 - 1
                        ).normalize();
                        dust.userData.lifetime = 0;
                        dust.userData.maxLifetime = 1.0 + Math.random() * 1.0;
                        dust.material.opacity = 0.4 + Math.random() * 0.3;
                    }
                });

                // Pulse the light
                light.intensity = 0.2 + Math.sin(time * 3) * 0.1;
            };

            return group;
        }
    },

    // Teeth – Crooked, cracked teeth shot in arcs
    teeth: {
        name: 'Teeth',
        damage: 1,
        speed: 9.0, // Fast
        range: 45.0, // Increased from 12.0 for much longer travel distance
        size: 0.15,
        gravity: -6.0, // Heavy for fast falling
        color: 0xFFFFF0, // Off-white
        trailEffect: false,
        impactEffect: 'tooth_impact',
        behavior: 'chatter', // Custom chattering behavior
        createMesh: (position) => {
            // Create a more realistic tooth with procedural effects
            const group = new THREE.Group();
            group.position.copy(position);

            // Create anatomically-inspired tooth using custom geometry
            // We'll create a molar-like shape with roots and detailed crown

            // Create tooth crown using custom shape
            const crownShape = new THREE.Shape();

            // Define the crown profile (half of a molar)
            crownShape.moveTo(0, 0);
            crownShape.lineTo(0.12, 0);
            crownShape.bezierCurveTo(0.13, 0.02, 0.14, 0.04, 0.15, 0.06); // Cusp 1
            crownShape.bezierCurveTo(0.14, 0.08, 0.12, 0.09, 0.10, 0.10); // Valley
            crownShape.bezierCurveTo(0.11, 0.12, 0.12, 0.14, 0.13, 0.15); // Cusp 2
            crownShape.bezierCurveTo(0.10, 0.16, 0.05, 0.16, 0, 0.15);    // Back to center

            // Create the 3D crown by extruding the shape
            const crownGeometry = new THREE.ExtrudeGeometry(crownShape, {
                steps: 1,
                depth: 0.2,
                bevelEnabled: true,
                bevelThickness: 0.02,
                bevelSize: 0.02,
                bevelSegments: 3
            });

            // Create custom shader material for the tooth
            const toothMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    baseColor: { value: new THREE.Color(0xFFFFF0) },  // Off-white
                    crackColor: { value: new THREE.Color(0xCCCCB0) },  // Yellowish
                    bloodColor: { value: new THREE.Color(0xAA0000) },  // Dark red
                    decayLevel: { value: 0.7 }                          // Level of decay/damage
                },
                vertexShader: `
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    void main() {
                        vUv = uv;
                        vPosition = position;
                        vNormal = normalize(normalMatrix * normal);
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform vec3 baseColor;
                    uniform vec3 crackColor;
                    uniform vec3 bloodColor;
                    uniform float decayLevel;

                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    // Simplex noise function
                    vec3 mod289(vec3 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
                    vec4 mod289(vec4 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
                    vec4 permute(vec4 x) { return mod289(((x*34.0)+1.0)*x); }
                    vec4 taylorInvSqrt(vec4 r) { return 1.79284291400159 - 0.85373472095314 * r; }

                    float snoise(vec3 v) {
                        const vec2 C = vec2(1.0/6.0, 1.0/3.0);
                        const vec4 D = vec4(0.0, 0.5, 1.0, 2.0);

                        // First corner
                        vec3 i  = floor(v + dot(v, C.yyy));
                        vec3 x0 = v - i + dot(i, C.xxx);

                        // Other corners
                        vec3 g = step(x0.yzx, x0.xyz);
                        vec3 l = 1.0 - g;
                        vec3 i1 = min(g.xyz, l.zxy);
                        vec3 i2 = max(g.xyz, l.zxy);

                        vec3 x1 = x0 - i1 + C.xxx;
                        vec3 x2 = x0 - i2 + C.yyy;
                        vec3 x3 = x0 - D.yyy;

                        // Permutations
                        i = mod289(i);
                        vec4 p = permute(permute(permute(
                                 i.z + vec4(0.0, i1.z, i2.z, 1.0))
                               + i.y + vec4(0.0, i1.y, i2.y, 1.0))
                               + i.x + vec4(0.0, i1.x, i2.x, 1.0));

                        // Gradients
                        float n_ = 0.142857142857; // 1.0/7.0
                        vec3 ns = n_ * D.wyz - D.xzx;

                        vec4 j = p - 49.0 * floor(p * ns.z * ns.z);

                        vec4 x_ = floor(j * ns.z);
                        vec4 y_ = floor(j - 7.0 * x_);

                        vec4 x = x_ *ns.x + ns.yyyy;
                        vec4 y = y_ *ns.x + ns.yyyy;
                        vec4 h = 1.0 - abs(x) - abs(y);

                        vec4 b0 = vec4(x.xy, y.xy);
                        vec4 b1 = vec4(x.zw, y.zw);

                        vec4 s0 = floor(b0)*2.0 + 1.0;
                        vec4 s1 = floor(b1)*2.0 + 1.0;
                        vec4 sh = -step(h, vec4(0.0));

                        vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy;
                        vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww;

                        vec3 p0 = vec3(a0.xy, h.x);
                        vec3 p1 = vec3(a0.zw, h.y);
                        vec3 p2 = vec3(a1.xy, h.z);
                        vec3 p3 = vec3(a1.zw, h.w);

                        // Normalise gradients
                        vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
                        p0 *= norm.x;
                        p1 *= norm.y;
                        p2 *= norm.z;
                        p3 *= norm.w;

                        // Mix final noise value
                        vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
                        m = m * m;
                        return 42.0 * dot(m*m, vec4(dot(p0,x0), dot(p1,x1), dot(p2,x2), dot(p3,x3)));
                    }

                    void main() {
                        // Create enamel texture with noise
                        float noise1 = snoise(vPosition * 30.0 + time * 0.1) * 0.5 + 0.5;
                        float noise2 = snoise(vPosition * 15.0 - time * 0.05) * 0.5 + 0.5;

                        // Create crack pattern
                        float crackPattern = smoothstep(0.4, 0.6, noise1 * noise2);

                        // Create blood stain pattern near the root
                        float bloodPattern = smoothstep(0.3, 0.7, vPosition.y + 0.1) *
                                           smoothstep(0.7, 0.3, vPosition.y + 0.05) *
                                           noise2;

                        // Add edge yellowing for more realistic look
                        float edge = 1.0 - max(0.0, dot(vNormal, vec3(0.0, 0.0, 1.0)));
                        edge = pow(edge, 2.0) * 0.5;

                        // Create decay spots
                        float decay = step(0.7, noise1 * noise2 + edge) * decayLevel;

                        // Mix colors based on patterns
                        vec3 finalColor = mix(baseColor, crackColor, crackPattern * 0.5 + edge);

                        // Add decay spots
                        finalColor = mix(finalColor, crackColor, decay * 0.7);

                        // Add blood stains
                        finalColor = mix(finalColor, bloodColor, bloodPattern * 0.3);

                        gl_FragColor = vec4(finalColor, 1.0);
                    }
                `
            });

            // Create the crown mesh and position it
            const crown = new THREE.Mesh(crownGeometry, toothMaterial);
            crown.rotation.x = Math.PI; // Point downward
            crown.position.y = 0.1; // Adjust position
            group.add(crown);

            // Create tooth roots using custom geometry
            const rootCount = 2; // Molar typically has 2-3 roots
            const roots = [];

            for (let i = 0; i < rootCount; i++) {
                // Create a curved root shape
                const points = [];
                const rootLength = 0.2 + Math.random() * 0.05;
                const rootCurve = 0.05 + Math.random() * 0.05;
                const segments = 8;

                // Generate points for a curved root
                for (let j = 0; j <= segments; j++) {
                    const t = j / segments;
                    const angle = Math.PI * (i / rootCount);

                    // Add some curvature to the root
                    const xOffset = Math.sin(t * Math.PI) * rootCurve;

                    points.push(new THREE.Vector3(
                        (i === 0 ? -0.05 : 0.05) + xOffset, // Position roots on either side
                        -t * rootLength,
                        0
                    ));
                }

                // Create a smooth curve from the points
                const curve = new THREE.CatmullRomCurve3(points);

                // Create tube geometry along the curve
                const rootGeometry = new THREE.TubeGeometry(
                    curve,
                    segments, // tubular segments
                    0.02, // radius
                    6, // radial segments
                    false // closed
                );

                // Clone the material for the root
                const rootMaterial = toothMaterial.clone();
                if (rootMaterial.uniforms) {
                    rootMaterial.uniforms.decayLevel.value = 0.3 + Math.random() * 0.3; // Less decay on roots
                }

                const root = new THREE.Mesh(rootGeometry, rootMaterial);

                group.add(root);
                roots.push(root);
            }

            // Add detailed cracks using custom geometry
            const crackCount = 3;
            const cracks = [];

            for (let i = 0; i < crackCount; i++) {
                // Create a jagged crack line
                const points = [];
                const crackLength = 0.1 + Math.random() * 0.1;
                const crackWidth = 0.01 + Math.random() * 0.01;
                const segments = 6;

                // Generate points for a jagged crack
                for (let j = 0; j <= segments; j++) {
                    const t = j / segments;

                    // Add jaggedness to the crack
                    const xOffset = (Math.random() - 0.5) * 0.02;
                    const yOffset = (Math.random() - 0.5) * 0.02;

                    points.push(new THREE.Vector3(
                        (i === 0 ? -0.05 : i === 1 ? 0 : 0.05) + xOffset, // Distribute cracks
                        0.05 + t * crackLength + yOffset,
                        0.05 // Slightly on the surface
                    ));
                }

                // Create a jagged line from the points
                const crackGeometry = new THREE.BufferGeometry().setFromPoints(points);

                // Create material for the crack
                const crackMaterial = new THREE.LineBasicMaterial({
                    color: 0xAA9977, // Darker yellowish
                    linewidth: 2
                });

                const crack = new THREE.Line(crackGeometry, crackMaterial);

                group.add(crack);
                cracks.push(crack);
            }

            // Add blood stains at the root using particle system
            const bloodCount = 10;
            const bloodParticles = [];

            for (let i = 0; i < bloodCount; i++) {
                // Create tiny blood particles
                const size = 0.01 + Math.random() * 0.01;
                const bloodGeometry = new THREE.SphereGeometry(size, 4, 4);

                // Create material with color variation
                const bloodColor = new THREE.Color(0xAA0000).offsetHSL(Math.random() * 0.05, 0, Math.random() * 0.1);
                const bloodMaterial = new THREE.MeshBasicMaterial({
                    color: bloodColor,
                    transparent: true,
                    opacity: 0.7 + Math.random() * 0.3
                });

                const blood = new THREE.Mesh(bloodGeometry, bloodMaterial);

                // Position blood at the roots
                const angle = Math.random() * Math.PI * 2;
                const radius = 0.05 + Math.random() * 0.05;

                blood.position.set(
                    Math.cos(angle) * radius,
                    -0.15 - Math.random() * 0.1, // At the roots
                    Math.sin(angle) * radius
                );

                // Store data for animation
                blood.userData.pulseSpeed = 0.5 + Math.random() * 2.0;
                blood.userData.pulseOffset = Math.random() * Math.PI * 2;

                group.add(blood);
                bloodParticles.push(blood);
            }

            // Add subtle red glow at the roots
            const light = new THREE.PointLight(0xAA0000, 0.3, 0.5);
            light.position.set(0, -0.2, 0); // At the roots
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.chatterTime = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniforms
                if (crown.material.uniforms) {
                    crown.material.uniforms.time.value = time;
                }

                roots.forEach((root) => {
                    if (root.material.uniforms) {
                        root.material.uniforms.time.value = time;
                    }
                });

                // Chattering animation
                group.userData.chatterTime += delta;
                const chatterFreq = 15 + Math.sin(time) * 5; // Varying chatter frequency
                const chatter = Math.sin(group.userData.chatterTime * chatterFreq) * 0.1;
                crown.rotation.z = chatter;

                // Animate roots - subtle pulsing
                roots.forEach((root, index) => {
                    const pulse = 0.05 * Math.sin(time * 2 + index);
                    root.scale.x = 1.0 + pulse;
                    root.scale.z = 1.0 + pulse;
                });

                // Animate blood particles - pulsing
                bloodParticles.forEach((blood) => {
                    const pulse = 0.8 + Math.sin(time * blood.userData.pulseSpeed + blood.userData.pulseOffset) * 0.2;
                    blood.scale.set(pulse, pulse, pulse);
                });

                // Pulse the light
                light.intensity = 0.2 + Math.sin(time * 3) * 0.1;
            };

            return group;
        }
    },

    // Halo Fragments – Projectiles that orbit heavenly objects
    halo_fragments: {
        name: 'Halo Fragments',
        damage: 1,
        speed: 6.0,
        range: 45.0, // Increased from 14.0 for much longer travel distance
        size: 0.2,
        gravity: -1.0, // Light gravity
        color: 0xFFFF80, // Bright yellow
        trailEffect: true,
        trailColor: 0xFFFFCC,
        trailLength: 10,
        impactEffect: 'holy_impact',
        behavior: 'orbit', // Custom orbiting behavior
        createMesh: (position) => {
            // Create a halo fragment
            const group = new THREE.Group();
            group.position.copy(position);

            // Create curved segment
            const curve = new THREE.EllipseCurve(
                0, 0,                // Center x, y
                0.2, 0.2,            // x radius, y radius
                0, Math.PI / 2,      // Start angle, end angle
                false,               // Clockwise
                0                    // Rotation
            );

            const points = curve.getPoints(10);
            const curveGeometry = new THREE.BufferGeometry().setFromPoints(
                points.map(p => new THREE.Vector3(p.x, 0, p.y))
            );

            const curveMaterial = new THREE.LineBasicMaterial({
                color: 0xFFFF80,
                transparent: true,
                opacity: 0.9,
                linewidth: 3
            });

            const curveObject = new THREE.Line(curveGeometry, curveMaterial);
            group.add(curveObject);

            // Add orbiting animation
            group.userData.orbitSpeed = 2;
            group.userData.animate = (delta) => {
                group.rotation.y += group.userData.orbitSpeed * delta;
            };

            // Add glow
            const light = new THREE.PointLight(0xFFFF80, 0.8, 2);
            light.position.set(0, 0, 0);
            group.add(light);

            return group;
        }
    },

    // Screamer Skulls – Screaming skulls that launch in short bursts
    screamer_skulls: {
        name: 'Screamer Skulls',
        damage: 1,
        speed: 8.0,
        range: 40.0, // Increased from 10.0 for much longer travel distance
        size: 0.25,
        gravity: -2.0,
        color: 0xCCCCCC, // Bone white
        trailEffect: true,
        trailColor: 0x888888,
        trailLength: 8,
        impactEffect: 'scream_impact',
        behavior: 'scream', // Custom screaming behavior
        createMesh: (position) => {
            // Create a skull
            const group = new THREE.Group();
            group.position.copy(position);

            // Create skull body (simplified)
            const skullGeometry = new THREE.SphereGeometry(0.25, 8, 8);
            const skullMaterial = new THREE.MeshBasicMaterial({
                color: 0xCCCCCC,
                transparent: false,
                opacity: 1.0
            });
            const skull = new THREE.Mesh(skullGeometry, skullMaterial);
            group.add(skull);

            // Create eye sockets
            const eyeGeometry = new THREE.SphereGeometry(0.07, 6, 6);
            const eyeMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });

            const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            leftEye.position.set(-0.08, 0.05, 0.15);
            skull.add(leftEye);

            const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            rightEye.position.set(0.08, 0.05, 0.15);
            skull.add(rightEye);

            // Create mouth
            const mouthGeometry = new THREE.SphereGeometry(0.1, 6, 6);
            const mouthMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
            const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
            mouth.position.set(0, -0.1, 0.15);
            mouth.scale.y = 0.5;
            skull.add(mouth);

            // Add screaming animation
            group.userData.screamTime = 0;
            group.userData.animate = (delta) => {
                group.userData.screamTime += delta;
                const screamFactor = Math.sin(group.userData.screamTime * 10) * 0.5 + 1.5;
                mouth.scale.y = screamFactor * 0.5;
            };

            return group;
        }
    },

    // Iron Chains – Chain-shaped projectiles that drag across the screen
    iron_chains: {
        name: 'Iron Chains',
        damage: 1,
        speed: 5.0,
        range: 50.0, // Increased from 15.0 for much longer travel distance
        size: 0.2,
        gravity: -1.0, // Light gravity
        color: 0x777777, // Iron gray
        trailEffect: false,
        impactEffect: 'metal_impact',
        behavior: 'drag', // Custom dragging behavior
        createMesh: (position) => {
            // Create a chain of links
            const chain = new THREE.Group();
            chain.position.copy(position);

            // Create several chain links
            const linkCount = 5;
            const linkSpacing = 0.15;

            for (let i = 0; i < linkCount; i++) {
                // Create a torus (ring) for each link
                const linkGeometry = new THREE.TorusGeometry(0.1, 0.03, 8, 12);
                const linkMaterial = new THREE.MeshBasicMaterial({ color: 0x777777 });
                const link = new THREE.Mesh(linkGeometry, linkMaterial);

                // Position links in a chain
                link.position.z = -i * linkSpacing;

                // Alternate link orientation
                if (i % 2 === 0) {
                    link.rotation.y = Math.PI / 2;
                }

                chain.add(link);
            }

            // Add swinging animation
            chain.userData.swingTime = 0;
            chain.userData.animate = (delta) => {
                chain.userData.swingTime += delta;
                const swing = Math.sin(chain.userData.swingTime * 3) * 0.2;
                chain.rotation.x = swing;
            };

            return chain;
        }
    },

    // Lava Droplets – Fall from the ceiling, land, then explode upward
    lava_droplets: {
        name: 'Lava Droplets',
        damage: 1,
        speed: 6.0,
        range: 40.0, // Increased from 10.0 for much longer travel distance
        size: 0.2,
        gravity: -7.0, // Heavy for fast falling
        color: 0xFF3300, // Deeper red-orange
        trailEffect: true,
        trailColor: 0xFF2200, // More red trail
        trailLength: 6,
        impactEffect: 'lava_impact',
        behavior: 'explode', // Custom explosion behavior
        createMesh: (position) => {
            // Create a more realistic lava droplet with procedural effects
            const group = new THREE.Group();
            group.position.copy(position);

            // Create a teardrop shape for the main droplet
            const dropletPoints = [];
            const segments = 12;

            // Create teardrop profile
            for (let i = 0; i <= segments; i++) {
                const t = i / segments;
                // Create teardrop shape - wider at bottom, pointed at top
                const y = t * 0.4; // Height
                let radius;
                if (t < 0.6) {
                    // Bottom is wider
                    radius = 0.2 * (1 - Math.pow(t - 0.6, 2));
                } else {
                    // Top tapers to a point
                    radius = 0.2 * Math.pow(1 - t, 1.2);
                }
                dropletPoints.push(new THREE.Vector2(radius, y));
            }

            // Create the droplet geometry
            const dropletGeometry = new THREE.LatheGeometry(dropletPoints, 16);

            // Create material for the lava
            const lavaMaterial = new THREE.MeshBasicMaterial({
                color: 0xFF3300, // Deep red-orange
                transparent: true,
                opacity: 0.9
            });

            // Create the main droplet
            const droplet = new THREE.Mesh(dropletGeometry, lavaMaterial);
            droplet.rotation.x = Math.PI; // Point downward
            group.add(droplet);

            // Add smaller trailing droplets
            const trailCount = 3;
            const trailDroplets = [];

            for (let i = 0; i < trailCount; i++) {
                // Create smaller droplet
                const size = 0.1 - (i * 0.02);
                const trailGeometry = new THREE.SphereGeometry(size, 8, 8);

                // Create material with slight color variation
                const trailColor = new THREE.Color(0xFF4400).offsetHSL(Math.random() * 0.05, 0, Math.random() * 0.1);
                const trailMaterial = new THREE.MeshBasicMaterial({
                    color: trailColor,
                    transparent: true,
                    opacity: 0.8 - (i * 0.1)
                });

                const trailDroplet = new THREE.Mesh(trailGeometry, trailMaterial);

                // Position trailing behind the main droplet
                trailDroplet.position.y = 0.15 + (i * 0.1);

                // Add slight random offset
                trailDroplet.position.x = (Math.random() - 0.5) * 0.05;
                trailDroplet.position.z = (Math.random() - 0.5) * 0.05;

                // Store data for animation
                trailDroplet.userData.offset = i;
                trailDroplet.userData.oscillateSpeed = 3.0 + Math.random() * 2.0;

                group.add(trailDroplet);
                trailDroplets.push(trailDroplet);
            }

            // Add lava particles
            const particleCount = 12;
            const particles = [];

            for (let i = 0; i < particleCount; i++) {
                // Create tiny lava particles
                const size = 0.02 + Math.random() * 0.02;
                const particleGeometry = new THREE.SphereGeometry(size, 4, 4);

                // Create material with color variation
                const hue = Math.random() * 0.05; // Small hue variation
                const particleColor = new THREE.Color(0xFF5500).offsetHSL(hue, 0, Math.random() * 0.2);

                const particleMaterial = new THREE.MeshBasicMaterial({
                    color: particleColor,
                    transparent: true,
                    opacity: 0.8 + Math.random() * 0.2
                });

                const particle = new THREE.Mesh(particleGeometry, particleMaterial);

                // Position particles around the droplet
                const angle = Math.random() * Math.PI * 2;
                const radius = 0.05 + Math.random() * 0.1;
                const height = -0.1 + Math.random() * 0.2;

                particle.position.set(
                    Math.cos(angle) * radius,
                    height,
                    Math.sin(angle) * radius
                );

                // Store data for animation
                particle.userData.originalPos = particle.position.clone();
                particle.userData.speed = 0.5 + Math.random() * 1.0;
                particle.userData.angle = angle;
                particle.userData.radius = radius;
                particle.userData.rising = Math.random() > 0.7; // Some particles rise
                particle.userData.riseSpeed = 0.1 + Math.random() * 0.2;

                group.add(particle);
                particles.push(particle);
            }

            // Add heat distortion effect
            const distortionGeometry = new THREE.SphereGeometry(0.3, 12, 12);
            const distortionMaterial = new THREE.MeshBasicMaterial({
                color: 0x000000,
                transparent: true,
                opacity: 0,
                depthWrite: false
            });

            const distortion = new THREE.Mesh(distortionGeometry, distortionMaterial);
            group.add(distortion);

            // Add point light - more red
            const light = new THREE.PointLight(0xFF2200, 1.2, 2.0);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Animate trailing droplets
                trailDroplets.forEach((trailDroplet) => {
                    // Add oscillation for more natural movement
                    const oscillation = Math.sin(time * trailDroplet.userData.oscillateSpeed) * 0.02;
                    trailDroplet.position.x = (Math.random() - 0.5) * 0.05 + oscillation;
                    trailDroplet.position.z = (Math.random() - 0.5) * 0.05 + oscillation * 0.7;
                });

                // Animate particles
                particles.forEach((particle) => {
                    // Update particle angle based on speed
                    particle.userData.angle += delta * particle.userData.speed;

                    // Move in spiral pattern
                    particle.position.x = Math.cos(particle.userData.angle) * particle.userData.radius;
                    particle.position.z = Math.sin(particle.userData.angle) * particle.userData.radius;

                    // Some particles rise
                    if (particle.userData.rising) {
                        particle.position.y += particle.userData.riseSpeed * delta;

                        // If risen too high, start falling
                        if (particle.position.y > particle.userData.originalPos.y + 0.2) {
                            particle.userData.rising = false;
                        }
                    } else {
                        // Fall back to original position
                        if (particle.position.y > particle.userData.originalPos.y) {
                            particle.position.y -= (particle.userData.riseSpeed * 0.5) * delta;
                        }
                    }

                    // Fade out as they rise
                    const heightDiff = Math.abs(particle.position.y - particle.userData.originalPos.y);
                    particle.material.opacity = Math.max(0, 1.0 - heightDiff * 2.5);
                });

                // Pulse the light
                light.intensity = 1.0 + Math.sin(time * 8) * 0.3;

                // Pulse the main droplet opacity slightly
                droplet.material.opacity = 0.85 + Math.sin(time * 5) * 0.1;
            };

            return group;
        }
    },

    // Shatter Spikes – Icicle-shaped bullets that break into frozen shards
    shatter_spikes: {
        name: 'Shatter Spikes',
        damage: 1,
        speed: 7.0,
        range: 45.0, // Increased from 12.0 for much longer travel distance
        size: 0.25,
        gravity: -2.0,
        color: 0xAADDFF, // Ice blue
        trailEffect: true,
        trailColor: 0xCCEEFF,
        trailLength: 7,
        impactEffect: 'ice_impact',
        behavior: 'shatter', // Custom shattering behavior
        createMesh: (position) => {
            // Create a more realistic crystalline ice spike
            const group = new THREE.Group();
            group.position.copy(position);

            // Create the main crystalline spike with facets
            const mainSpikeGroup = new THREE.Group();

            // Create a more complex ice crystal using multiple geometries
            // Main central spike
            const centralSpikeGeometry = new THREE.ConeGeometry(0.08, 0.5, 6);
            const iceMaterial = new THREE.MeshBasicMaterial({
                color: 0xAADDFF,
                transparent: true,
                opacity: 0.7
            });

            const centralSpike = new THREE.Mesh(centralSpikeGeometry, iceMaterial);
            centralSpike.rotation.x = Math.PI; // Point downward
            mainSpikeGroup.add(centralSpike);

            // Add facets to the main spike
            const facetCount = 5;
            for (let i = 0; i < facetCount; i++) {
                // Create a facet (thin, flat crystal face)
                const facetGeometry = new THREE.BoxGeometry(0.15, 0.3, 0.02);
                const facetMaterial = new THREE.MeshBasicMaterial({
                    color: 0xCCEEFF,
                    transparent: true,
                    opacity: 0.5
                });

                const facet = new THREE.Mesh(facetGeometry, facetMaterial);

                // Position around the central spike
                const angle = (i / facetCount) * Math.PI * 2;
                facet.position.set(
                    Math.cos(angle) * 0.05,
                    -0.1,
                    Math.sin(angle) * 0.05
                );

                // Rotate to create a star-like pattern
                facet.rotation.x = Math.PI / 2; // Align vertically
                facet.rotation.y = angle; // Rotate around the center

                mainSpikeGroup.add(facet);
            }

            group.add(mainSpikeGroup);

            // Add secondary crystal formations
            const secondaryCrystalCount = 6;
            const secondaryCrystals = [];

            for (let i = 0; i < secondaryCrystalCount; i++) {
                // Create a crystal cluster
                const crystalGroup = new THREE.Group();

                // Determine position around the main spike
                const angle = (i / secondaryCrystalCount) * Math.PI * 2;
                const radius = 0.12;
                const height = -0.15 + (Math.random() * 0.1);

                crystalGroup.position.set(
                    Math.cos(angle) * radius,
                    height,
                    Math.sin(angle) * radius
                );

                // Create 2-4 crystals per cluster
                const clusterSize = 2 + Math.floor(Math.random() * 3);

                for (let j = 0; j < clusterSize; j++) {
                    // Vary crystal shapes
                    let crystalGeometry;
                    const crystalType = Math.floor(Math.random() * 3);
                    const size = 0.04 + Math.random() * 0.04;

                    if (crystalType === 0) {
                        // Pointed crystal
                        crystalGeometry = new THREE.ConeGeometry(size * 0.5, size * 2, 4);
                    } else if (crystalType === 1) {
                        // Hexagonal crystal
                        crystalGeometry = new THREE.CylinderGeometry(size * 0.5, size * 0.5, size * 1.5, 6);
                    } else {
                        // Shard-like crystal
                        crystalGeometry = new THREE.TetrahedronGeometry(size, 0);
                    }

                    // Create material with slight color variation
                    const brightness = 0.8 + Math.random() * 0.2; // Slight brightness variation
                    const crystalColor = new THREE.Color(0xAADDFF);
                    crystalColor.r *= brightness;
                    crystalColor.g *= brightness;
                    crystalColor.b *= brightness;

                    const crystalMaterial = new THREE.MeshBasicMaterial({
                        color: crystalColor,
                        transparent: true,
                        opacity: 0.6 + Math.random() * 0.3
                    });

                    const crystal = new THREE.Mesh(crystalGeometry, crystalMaterial);

                    // Position within the cluster with slight variation
                    const clusterAngle = (j / clusterSize) * Math.PI * 2;
                    const clusterRadius = 0.02 + Math.random() * 0.01;

                    crystal.position.set(
                        Math.cos(clusterAngle) * clusterRadius,
                        Math.random() * 0.03,
                        Math.sin(clusterAngle) * clusterRadius
                    );

                    // Random rotation for natural look
                    crystal.rotation.set(
                        Math.random() * Math.PI,
                        Math.random() * Math.PI,
                        Math.random() * Math.PI
                    );

                    // Store data for animation
                    crystal.userData.pulseSpeed = 0.5 + Math.random() * 2.0;
                    crystal.userData.pulseOffset = Math.random() * Math.PI * 2;
                    crystal.userData.originalScale = 0.8 + Math.random() * 0.4;
                    crystal.scale.set(
                        crystal.userData.originalScale,
                        crystal.userData.originalScale,
                        crystal.userData.originalScale
                    );

                    crystalGroup.add(crystal);
                    secondaryCrystals.push(crystal);
                }

                group.add(crystalGroup);
            }

            // Add frost particles
            const particleCount = 15;
            const particles = [];

            for (let i = 0; i < particleCount; i++) {
                // Create tiny ice particles
                const size = 0.01 + Math.random() * 0.02;
                const particleGeometry = new THREE.SphereGeometry(size, 4, 4);

                // Create material with slight color variation
                const particleColor = new THREE.Color(0xFFFFFF).lerp(new THREE.Color(0xAADDFF), Math.random() * 0.5);

                const particleMaterial = new THREE.MeshBasicMaterial({
                    color: particleColor,
                    transparent: true,
                    opacity: 0.4 + Math.random() * 0.4
                });

                const particle = new THREE.Mesh(particleGeometry, particleMaterial);

                // Position particles around the spike
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.acos(2 * Math.random() - 1);
                const radius = 0.1 + Math.random() * 0.15;

                particle.position.set(
                    radius * Math.sin(phi) * Math.cos(theta),
                    radius * Math.cos(phi) - 0.1, // Mostly below the spike
                    radius * Math.sin(phi) * Math.sin(theta)
                );

                // Store data for animation
                particle.userData.originalPos = particle.position.clone();
                particle.userData.speed = 0.2 + Math.random() * 0.8;
                particle.userData.angle = Math.random() * Math.PI * 2;
                particle.userData.radius = radius * 0.3;
                particle.userData.verticalSpeed = (Math.random() - 0.5) * 0.1;

                group.add(particle);
                particles.push(particle);
            }

            // Add ice refraction effect
            const refractionGeometry = new THREE.SphereGeometry(0.25, 12, 12);
            const refractionMaterial = new THREE.MeshBasicMaterial({
                color: 0xFFFFFF,
                transparent: true,
                opacity: 0.1,
                blending: THREE.AdditiveBlending
            });

            const refraction = new THREE.Mesh(refractionGeometry, refractionMaterial);
            refraction.position.y = -0.1;
            group.add(refraction);

            // Add cold glow
            const light = new THREE.PointLight(0xAADDFF, 0.8, 2.0);
            light.position.set(0, -0.1, 0);
            group.add(light);

            // Add subtle blue fog effect
            const fogGeometry = new THREE.SphereGeometry(0.3, 8, 8);
            const fogMaterial = new THREE.MeshBasicMaterial({
                color: 0xCCEEFF,
                transparent: true,
                opacity: 0.15,
                side: THREE.DoubleSide
            });

            const fog = new THREE.Mesh(fogGeometry, fogMaterial);
            fog.position.y = -0.1;
            group.add(fog);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Rotate the main spike slightly for a floating effect
                mainSpikeGroup.rotation.y += delta * 0.2;

                // Subtle floating motion
                mainSpikeGroup.position.y = Math.sin(time * 1.5) * 0.01;

                // Animate secondary crystals
                secondaryCrystals.forEach((crystal) => {
                    // Pulse scale for a "growing" effect
                    const pulseFactor = crystal.userData.originalScale * (0.9 + Math.sin(time * crystal.userData.pulseSpeed + crystal.userData.pulseOffset) * 0.1);
                    crystal.scale.set(pulseFactor, pulseFactor, pulseFactor);

                    // Pulse opacity
                    crystal.material.opacity = 0.6 + Math.sin(time * crystal.userData.pulseSpeed * 0.5 + crystal.userData.pulseOffset) * 0.2;
                });

                // Animate frost particles
                particles.forEach((particle) => {
                    // Orbit around the spike
                    particle.userData.angle += delta * particle.userData.speed;

                    particle.position.x = particle.userData.originalPos.x + Math.cos(particle.userData.angle) * particle.userData.radius;
                    particle.position.z = particle.userData.originalPos.z + Math.sin(particle.userData.angle) * particle.userData.radius;

                    // Slight vertical drift
                    particle.position.y += particle.userData.verticalSpeed * delta;

                    // If drifted too far, reset
                    if (Math.abs(particle.position.y - particle.userData.originalPos.y) > 0.1) {
                        particle.userData.verticalSpeed *= -1;
                    }

                    // Fade based on distance from original position
                    const distFromOriginal = particle.position.distanceTo(particle.userData.originalPos);
                    particle.material.opacity = Math.max(0.1, 0.8 - distFromOriginal * 2);
                });

                // Pulse the fog
                const fogPulse = 0.9 + Math.sin(time * 0.8) * 0.1;
                fog.scale.set(fogPulse, fogPulse, fogPulse);
                fogMaterial.opacity = 0.15 + Math.sin(time * 0.5) * 0.05;

                // Pulse the light
                light.intensity = 0.7 + Math.sin(time * 2) * 0.3;

                // Pulse the refraction effect
                const refractionPulse = 0.95 + Math.sin(time * 1.2) * 0.15;
                refraction.scale.set(refractionPulse, refractionPulse, refractionPulse);
                refractionMaterial.opacity = 0.1 + Math.sin(time * 1.5) * 0.05;
            };

            return group;
        }
    },

    // Memory Echoes – Bullet ghosts of previous attacks
    memory_echoes: {
        name: 'Memory Echoes',
        damage: 1,
        speed: 5.0,
        range: 45.0, // Increased from 14.0 for much longer travel distance
        size: 0.25,
        gravity: -1.0, // Light gravity
        color: 0xCCCCFF, // Pale blue
        trailEffect: true,
        trailColor: 0xDDDDFF,
        trailLength: 12,
        impactEffect: 'echo_impact',
        behavior: 'echo', // Custom echo behavior
        createMesh: (position) => {
            // Create a more complex ghostly echo with layered effects
            const group = new THREE.Group();
            group.position.copy(position);

            // Create the main ethereal core
            const coreGeometry = new THREE.SphereGeometry(0.2, 12, 12);
            const coreMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    baseColor: { value: new THREE.Color(0xCCCCFF) },
                    pulseColor: { value: new THREE.Color(0xFFFFFF) },
                    noiseScale: { value: 15.0 },
                    noiseIntensity: { value: 0.15 },
                    opacity: { value: 0.6 }
                },
                vertexShader: `
                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    void main() {
                        vUv = uv;
                        vPosition = position;
                        vNormal = normalize(normalMatrix * normal);
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform vec3 baseColor;
                    uniform vec3 pulseColor;
                    uniform float noiseScale;
                    uniform float noiseIntensity;
                    uniform float opacity;

                    varying vec2 vUv;
                    varying vec3 vPosition;
                    varying vec3 vNormal;

                    // Simple noise function
                    float noise(vec3 p) {
                        return fract(sin(dot(p, vec3(12.9898, 78.233, 45.164))) * 43758.5453);
                    }

                    void main() {
                        // Create ethereal noise pattern
                        float noise1 = noise(vPosition * noiseScale + time * 0.5) * 0.5 + 0.5;
                        float noise2 = noise(vPosition * noiseScale * 0.5 - time * 0.3) * 0.5 + 0.5;

                        // Create pulsing effect
                        float pulse = 0.5 + 0.5 * sin(time * 2.0);

                        // Create edge glow
                        float edge = 1.0 - max(0.0, dot(vNormal, vec3(0.0, 0.0, 1.0)));
                        edge = pow(edge, 2.0) * 0.5;

                        // Mix colors based on noise and pulse
                        vec3 finalColor = mix(baseColor, pulseColor, noise1 * noise2 * pulse + edge);

                        // Vary opacity based on noise and pulse
                        float finalOpacity = opacity * (0.7 + 0.3 * pulse) * (0.8 + 0.2 * noise1);

                        gl_FragColor = vec4(finalColor, finalOpacity);
                    }
                `,
                transparent: true,
                depthWrite: false,
                blending: THREE.AdditiveBlending
            });

            const core = new THREE.Mesh(coreGeometry, coreMaterial);
            group.add(core);

            // Add outer ethereal shell
            const shellGeometry = new THREE.SphereGeometry(0.25, 16, 16);
            const shellMaterial = new THREE.MeshBasicMaterial({
                color: 0xDDDDFF,
                transparent: true,
                opacity: 0.3,
                side: THREE.DoubleSide,
                blending: THREE.AdditiveBlending
            });

            const shell = new THREE.Mesh(shellGeometry, shellMaterial);
            group.add(shell);

            // Add ghostly wisps/tendrils
            const wispCount = 5;
            const wisps = [];

            for (let i = 0; i < wispCount; i++) {
                // Create curved paths for each wisp
                const points = [];
                const segments = 8;
                const length = 0.3 + Math.random() * 0.2;
                const curviness = 0.1 + Math.random() * 0.1;

                // Generate points for a curved wisp
                for (let j = 0; j <= segments; j++) {
                    const t = j / segments;
                    const angle = Math.PI * 2 * (i / wispCount);

                    // Add some waviness to the wisp
                    const xOffset = Math.sin(t * Math.PI * 3) * curviness;
                    const yOffset = Math.cos(t * Math.PI * 2) * curviness;

                    points.push(new THREE.Vector3(
                        Math.cos(angle) * 0.1 * t + xOffset,
                        Math.sin(angle) * 0.1 * t + yOffset,
                        -t * length
                    ));
                }

                // Create a smooth curve from the points
                const curve = new THREE.CatmullRomCurve3(points);

                // Create tube geometry along the curve
                const wispGeometry = new THREE.TubeGeometry(
                    curve,
                    segments * 2, // tubular segments
                    0.02, // radius
                    6, // radial segments
                    false // closed
                );

                // Create material with slight color variation
                const wispMaterial = new THREE.MeshBasicMaterial({
                    color: 0xDDDDFF,
                    transparent: true,
                    opacity: 0.4,
                    blending: THREE.AdditiveBlending
                });

                const wisp = new THREE.Mesh(wispGeometry, wispMaterial);

                // Store data for animation
                wisp.userData.originalRot = new THREE.Euler().copy(wisp.rotation);
                wisp.userData.rotSpeed = {
                    x: (Math.random() - 0.5) * 0.5,
                    y: (Math.random() - 0.5) * 0.5,
                    z: (Math.random() - 0.5) * 0.5
                };
                wisp.userData.pulseSpeed = 0.5 + Math.random() * 1.0;
                wisp.userData.pulseOffset = Math.random() * Math.PI * 2;

                group.add(wisp);
                wisps.push(wisp);
            }

            // Add memory fragments (small geometric shapes)
            const fragmentCount = 8;
            const fragments = [];

            for (let i = 0; i < fragmentCount; i++) {
                // Create different fragment shapes
                let fragmentGeometry;
                const fragmentType = Math.floor(Math.random() * 3);
                const size = 0.03 + Math.random() * 0.02;

                if (fragmentType === 0) {
                    // Tetrahedron
                    fragmentGeometry = new THREE.TetrahedronGeometry(size, 0);
                } else if (fragmentType === 1) {
                    // Cube
                    fragmentGeometry = new THREE.BoxGeometry(size, size, size);
                } else {
                    // Octahedron
                    fragmentGeometry = new THREE.OctahedronGeometry(size, 0);
                }

                // Create material with slight color variation
                const fragmentMaterial = new THREE.MeshBasicMaterial({
                    color: 0xFFFFFF,
                    transparent: true,
                    opacity: 0.7,
                    blending: THREE.AdditiveBlending
                });

                const fragment = new THREE.Mesh(fragmentGeometry, fragmentMaterial);

                // Position fragments in a cloud around the core
                const radius = 0.2 + Math.random() * 0.2;
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.acos(2 * Math.random() - 1);

                fragment.position.set(
                    radius * Math.sin(phi) * Math.cos(theta),
                    radius * Math.sin(phi) * Math.sin(theta),
                    radius * Math.cos(phi)
                );

                // Random rotation
                fragment.rotation.set(
                    Math.random() * Math.PI * 2,
                    Math.random() * Math.PI * 2,
                    Math.random() * Math.PI * 2
                );

                // Store data for animation
                fragment.userData.originalPos = fragment.position.clone();
                fragment.userData.orbitSpeed = 0.3 + Math.random() * 0.5;
                fragment.userData.orbitRadius = radius;
                fragment.userData.orbitOffset = Math.random() * Math.PI * 2;
                fragment.userData.rotSpeed = {
                    x: (Math.random() - 0.5) * 2,
                    y: (Math.random() - 0.5) * 2,
                    z: (Math.random() - 0.5) * 2
                };

                group.add(fragment);
                fragments.push(fragment);
            }

            // Add ghostly glow
            const light = new THREE.PointLight(0xCCCCFF, 0.6, 3);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.animate = (delta) => {
                group.userData.time += delta;
                const time = group.userData.time;

                // Update shader time uniform for core
                if (core.material.uniforms) {
                    core.material.uniforms.time.value = time;
                }

                // Pulse the outer shell
                const shellPulse = 0.9 + Math.sin(time * 2) * 0.2;
                shell.scale.set(shellPulse, shellPulse, shellPulse);
                shellMaterial.opacity = 0.3 + Math.sin(time * 1.5) * 0.15;

                // Animate wisps
                wisps.forEach((wisp) => {
                    // Rotate wisps
                    wisp.rotation.x = wisp.userData.originalRot.x + Math.sin(time * wisp.userData.rotSpeed.x) * 0.3;
                    wisp.rotation.y = wisp.userData.originalRot.y + Math.sin(time * wisp.userData.rotSpeed.y) * 0.3;
                    wisp.rotation.z = wisp.userData.originalRot.z + Math.sin(time * wisp.userData.rotSpeed.z) * 0.3;

                    // Pulse opacity
                    wisp.material.opacity = 0.3 + Math.sin(time * wisp.userData.pulseSpeed + wisp.userData.pulseOffset) * 0.2;
                });

                // Animate memory fragments
                fragments.forEach((fragment) => {
                    // Orbit around the core
                    const orbitAngle = time * fragment.userData.orbitSpeed + fragment.userData.orbitOffset;
                    const radius = fragment.userData.orbitRadius;

                    fragment.position.x = Math.sin(orbitAngle) * radius;
                    fragment.position.y = Math.cos(orbitAngle) * radius * 0.8; // Slightly elliptical
                    fragment.position.z = Math.sin(orbitAngle * 0.5) * radius * 0.3; // Add some z-axis movement

                    // Rotate fragments
                    fragment.rotation.x += fragment.userData.rotSpeed.x * delta;
                    fragment.rotation.y += fragment.userData.rotSpeed.y * delta;
                    fragment.rotation.z += fragment.userData.rotSpeed.z * delta;

                    // Pulse opacity
                    fragment.material.opacity = 0.5 + Math.sin(time * 3 + fragment.userData.orbitOffset) * 0.3;
                });

                // Pulse the light
                light.intensity = 0.5 + Math.sin(time * 2.5) * 0.3;

                // Slowly rotate the entire group for more ethereal effect
                group.rotation.y += delta * 0.1;
            };

            return group;
        }
    },

    // Hex Strings – Thin, magical strings that curve through the air
    hex_strings: {
        name: 'Hex Strings',
        damage: 1,
        speed: 8.0,
        range: 50.0, // Increased from 16.0 for much longer travel distance
        size: 0.1, // Very thin
        gravity: -0.5, // Very light gravity
        color: 0xFF00FF, // Magenta
        trailEffect: true,
        trailColor: 0xFF80FF,
        trailLength: 15,
        impactEffect: 'magic_impact',
        behavior: 'whip', // Custom whipping behavior
        createMesh: (position) => {
            // Create a curved string
            const points = [];
            const segments = 10;

            // Create a wavy curve
            for (let i = 0; i <= segments; i++) {
                const t = i / segments;
                const x = Math.sin(t * Math.PI * 2) * 0.1;
                const z = -t * 0.5; // Extend backward
                points.push(new THREE.Vector3(x, 0, z));
            }

            const geometry = new THREE.BufferGeometry().setFromPoints(points);
            const material = new THREE.LineBasicMaterial({
                color: 0xFF00FF,
                linewidth: 3,
                transparent: true,
                opacity: 0.8
            });

            const curve = new THREE.Line(geometry, material);
            curve.position.copy(position);

            // Add glowing effect
            const light = new THREE.PointLight(0xFF00FF, 0.6, 1.5);
            light.position.set(0, 0, 0);
            curve.add(light);

            // Add whipping animation
            curve.userData.whipTime = 0;
            curve.userData.animate = (delta) => {
                curve.userData.whipTime += delta;
                const whip = Math.sin(curve.userData.whipTime * 5) * 0.3;
                curve.rotation.z = whip;
            };

            return curve;
        }
    },

    // Eclipsed Suns – Black orb bullets surrounded by solar flares
    eclipsed_suns: {
        name: 'Eclipsed Suns',
        damage: 1,
        speed: 4.0, // Slow
        range: 45.0, // Increased from 12.0 for much longer travel distance
        size: 0.3,
        gravity: -1.0, // Light gravity
        color: 0x000000, // Black
        trailEffect: true,
        trailColor: 0xFF8800,
        trailLength: 8,
        impactEffect: 'eclipse_impact',
        behavior: 'attract', // Custom attraction behavior
        createMesh: (position) => {
            // Create an eclipse
            const group = new THREE.Group();
            group.position.copy(position);

            // Create black core
            const coreGeometry = new THREE.SphereGeometry(0.2, 12, 12);
            const coreMaterial = new THREE.MeshBasicMaterial({
                color: 0x000000,
                transparent: false,
                opacity: 1.0
            });
            const core = new THREE.Mesh(coreGeometry, coreMaterial);
            group.add(core);

            // Create solar flares
            const flareCount = 8;
            for (let i = 0; i < flareCount; i++) {
                const angle = (i / flareCount) * Math.PI * 2;

                // Create a flare (cone)
                const flareGeometry = new THREE.ConeGeometry(0.05, 0.2, 4);
                const flareMaterial = new THREE.MeshBasicMaterial({
                    color: 0xFF8800,
                    transparent: true,
                    opacity: 0.8
                });
                const flare = new THREE.Mesh(flareGeometry, flareMaterial);

                // Position around the core
                flare.position.set(
                    Math.cos(angle) * 0.25,
                    Math.sin(angle) * 0.25,
                    0
                );

                // Rotate to point outward
                flare.rotation.z = angle + Math.PI / 2;

                group.add(flare);
            }

            // Add corona glow
            const light = new THREE.PointLight(0xFF8800, 1.0, 3);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add rotation animation
            group.userData.rotationSpeed = 1.5;
            group.userData.animate = (delta) => {
                group.rotation.z += group.userData.rotationSpeed * delta;

                // Pulse the light
                light.intensity = 0.8 + Math.sin(Date.now() / 200) * 0.2;
            };

            return group;
        }
    },

    // Alchemical Symbols – Bullets shaped like alchemical glyphs
    alchemical_symbols: {
        name: 'Alchemical Symbols',
        damage: 1,
        speed: 5.0, // Slower speed for better visibility
        range: 50.0,
        size: 0.4, // Larger size
        gravity: -0.5, // Very light gravity
        color: 0xFFD700, // Gold base color
        trailEffect: true,
        trailColor: 0xFFD700,
        trailLength: 8,
        trailType: 'particles',
        trailWidth: 1.5,
        trailFade: true,
        impactEffect: 'magic_impact',
        orientWithVelocity: false, // Don't rotate with movement
        createMesh: (position) => {
            // Create a group for the alchemical symbol
            const group = new THREE.Group();
            group.position.copy(position);

            // Choose one of four alchemical elements (fire, water, earth, air)
            const elementType = Math.floor(Math.random() * 4);
            let symbolGeometry;
            let symbolColor;
            let particleColor;

            switch(elementType) {
                case 0: // Fire - Triangle pointing up
                    symbolGeometry = new THREE.CylinderGeometry(0, 0.25, 0.4, 3);
                    symbolColor = 0xFF4500; // Orange-red
                    particleColor = 0xFF7700; // Lighter orange
                    break;
                case 1: // Water - Triangle pointing down
                    symbolGeometry = new THREE.CylinderGeometry(0.25, 0, 0.4, 3);
                    symbolColor = 0x1E90FF; // Dodger blue
                    particleColor = 0x00BFFF; // Deep sky blue
                    break;
                case 2: // Earth - Square
                    symbolGeometry = new THREE.BoxGeometry(0.35, 0.35, 0.1);
                    symbolColor = 0x8B4513; // Saddle brown
                    particleColor = 0xA0522D; // Sienna
                    break;
                case 3: // Air - Circle
                    symbolGeometry = new THREE.TorusGeometry(0.2, 0.05, 8, 24);
                    symbolColor = 0xE6E6FA; // Lavender
                    particleColor = 0xFFFFFF; // White
                    break;
            }

            // Create the symbol mesh
            const symbolMaterial = new THREE.MeshBasicMaterial({
                color: symbolColor,
                transparent: true,
                opacity: 0.9
            });
            const symbol = new THREE.Mesh(symbolGeometry, symbolMaterial);

            // Rotate the symbol to face forward
            if (elementType < 2) { // Fire and Water triangles
                symbol.rotation.x = Math.PI / 2;
            }

            group.add(symbol);

            // Add a glowing outline
            const outlineGeometry = symbolGeometry.clone();
            const outlineMaterial = new THREE.MeshBasicMaterial({
                color: 0xFFD700, // Gold outline
                transparent: true,
                opacity: 0.6,
                wireframe: true
            });
            const outline = new THREE.Mesh(outlineGeometry, outlineMaterial);

            // Make outline slightly larger
            outline.scale.set(1.2, 1.2, 1.2);

            // Match rotation of main symbol
            if (elementType < 2) {
                outline.rotation.x = Math.PI / 2;
            }

            group.add(outline);

            // Add particles around the symbol
            const particleCount = 8;
            const particles = [];

            for (let i = 0; i < particleCount; i++) {
                const particleGeometry = new THREE.SphereGeometry(0.03 + Math.random() * 0.02, 4, 4);
                const particleMaterial = new THREE.MeshBasicMaterial({
                    color: particleColor,
                    transparent: true,
                    opacity: 0.7
                });

                const particle = new THREE.Mesh(particleGeometry, particleMaterial);

                // Position particles in a circle around the symbol
                const angle = (i / particleCount) * Math.PI * 2;
                const radius = 0.3 + Math.random() * 0.1;
                particle.position.set(
                    Math.cos(angle) * radius,
                    Math.sin(angle) * radius,
                    0
                );

                // Store original position and movement data
                particle.userData.originalPos = particle.position.clone();
                particle.userData.speed = 0.5 + Math.random() * 2;
                particle.userData.angle = angle;

                group.add(particle);
                particles.push(particle);
            }

            // Add point light matching the element color
            const light = new THREE.PointLight(symbolColor, 0.7, 2);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.rotationSpeed = 1 + Math.random() * 2;
            group.userData.animate = (delta) => {
                group.userData.time += delta;

                // Rotate the symbol
                symbol.rotation.z += delta * group.userData.rotationSpeed;
                outline.rotation.z += delta * group.userData.rotationSpeed * 0.8; // Slightly slower

                // Pulse the outline
                const pulseFactor = 1.2 + Math.sin(group.userData.time * 5) * 0.1;
                outline.scale.set(pulseFactor, pulseFactor, pulseFactor);
                outlineMaterial.opacity = 0.6 + Math.sin(group.userData.time * 3) * 0.2;

                // Animate particles
                particles.forEach((particle, index) => {
                    // Orbit around the symbol
                    particle.userData.angle += delta * particle.userData.speed;

                    const radius = 0.3 + Math.sin(group.userData.time * 2 + index) * 0.05;
                    particle.position.x = Math.cos(particle.userData.angle) * radius;
                    particle.position.y = Math.sin(particle.userData.angle) * radius;

                    // Pulse opacity
                    particle.material.opacity = 0.7 + Math.sin(group.userData.time * 4 + index * 0.5) * 0.3;
                });

                // Pulse the light
                light.intensity = 0.7 + Math.sin(group.userData.time * 6) * 0.3;
            };

            return group;
        }
    },

    // Rotating Sigils – Glowing magical circles inscribed with runes
    rotating_sigils: {
        name: 'Rotating Sigils',
        damage: 1,
        speed: 4.0, // Slower speed for better visibility
        range: 50.0,
        size: 0.5, // Larger size
        gravity: -0.2, // Almost no gravity
        color: 0x4B0082, // Indigo
        trailEffect: true,
        trailColor: 0x9370DB, // Medium purple
        trailLength: 10,
        trailType: 'ribbon',
        trailWidth: 2.0,
        trailFade: true,
        impactEffect: 'magic_impact',
        orientWithVelocity: false, // Don't rotate with movement
        createMesh: (position) => {
            // Create a group for the sigil
            const group = new THREE.Group();
            group.position.copy(position);

            // Create the main sigil ring
            const ringGeometry = new THREE.TorusGeometry(0.3, 0.03, 8, 32);
            const ringMaterial = new THREE.MeshBasicMaterial({
                color: 0x9370DB, // Medium purple
                transparent: true,
                opacity: 0.9
            });
            const ring = new THREE.Mesh(ringGeometry, ringMaterial);
            group.add(ring);

            // Create inner circle
            const innerCircleGeometry = new THREE.CircleGeometry(0.25, 32);
            const innerCircleMaterial = new THREE.MeshBasicMaterial({
                color: 0x4B0082, // Indigo
                transparent: true,
                opacity: 0.5
            });
            const innerCircle = new THREE.Mesh(innerCircleGeometry, innerCircleMaterial);
            innerCircle.rotation.x = -Math.PI / 2; // Rotate to face forward
            group.add(innerCircle);

            // Add runes around the circle
            const runeCount = 6;
            const runes = [];

            for (let i = 0; i < runeCount; i++) {
                // Create a random rune shape
                const runeType = Math.floor(Math.random() * 4);
                let runeGeometry;

                switch(runeType) {
                    case 0: // Line rune
                        runeGeometry = new THREE.BoxGeometry(0.1, 0.02, 0.02);
                        break;
                    case 1: // V-shaped rune
                        const vPoints = [
                            new THREE.Vector3(-0.05, 0, 0),
                            new THREE.Vector3(0, 0.05, 0),
                            new THREE.Vector3(0.05, 0, 0)
                        ];
                        runeGeometry = new THREE.BufferGeometry().setFromPoints(vPoints);
                        break;
                    case 2: // Triangle rune
                        runeGeometry = new THREE.CylinderGeometry(0, 0.05, 0.08, 3);
                        break;
                    case 3: // Square rune
                        runeGeometry = new THREE.BoxGeometry(0.05, 0.05, 0.01);
                        break;
                }

                const runeMaterial = new THREE.MeshBasicMaterial({
                    color: 0xFFFFFF, // White
                    transparent: true,
                    opacity: 0.9
                });

                const rune = runeType === 1
                    ? new THREE.Line(runeGeometry, runeMaterial)
                    : new THREE.Mesh(runeGeometry, runeMaterial);

                // Position runes around the circle
                const angle = (i / runeCount) * Math.PI * 2;
                const radius = 0.3;
                rune.position.set(
                    Math.cos(angle) * radius,
                    Math.sin(angle) * radius,
                    0
                );

                // Rotate runes to face outward
                rune.rotation.z = angle + Math.PI / 2;

                // Store original position and rotation data
                rune.userData.originalPos = rune.position.clone();
                rune.userData.originalRot = rune.rotation.clone();
                rune.userData.angle = angle;

                group.add(rune);
                runes.push(rune);
            }

            // Add outer glow ring
            const glowRingGeometry = new THREE.RingGeometry(0.32, 0.4, 32);
            const glowRingMaterial = new THREE.MeshBasicMaterial({
                color: 0x9370DB, // Medium purple
                transparent: true,
                opacity: 0.3,
                side: THREE.DoubleSide
            });
            const glowRing = new THREE.Mesh(glowRingGeometry, glowRingMaterial);
            glowRing.rotation.x = -Math.PI / 2; // Rotate to face forward
            group.add(glowRing);

            // Add energy particles in the center
            const particleCount = 10;
            const particles = [];

            for (let i = 0; i < particleCount; i++) {
                const particleGeometry = new THREE.SphereGeometry(0.02 + Math.random() * 0.02, 4, 4);
                const particleMaterial = new THREE.MeshBasicMaterial({
                    color: 0xFFFFFF, // White
                    transparent: true,
                    opacity: 0.7
                });

                const particle = new THREE.Mesh(particleGeometry, particleMaterial);

                // Position particles within the inner circle
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 0.2;
                particle.position.set(
                    Math.cos(angle) * radius,
                    Math.sin(angle) * radius,
                    (Math.random() - 0.5) * 0.1
                );

                // Store original position and movement data
                particle.userData.originalPos = particle.position.clone();
                particle.userData.speed = 0.5 + Math.random() * 2;
                particle.userData.angle = angle;

                group.add(particle);
                particles.push(particle);
            }

            // Add point light
            const light = new THREE.PointLight(0x9370DB, 0.8, 3);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.rotationSpeed = 0.8 + Math.random() * 1.2;
            group.userData.animate = (delta) => {
                group.userData.time += delta;

                // Rotate the main ring
                ring.rotation.z += delta * group.userData.rotationSpeed;

                // Counter-rotate the inner circle
                innerCircle.rotation.z -= delta * group.userData.rotationSpeed * 0.5;

                // Pulse the inner circle
                const innerPulse = 0.9 + Math.sin(group.userData.time * 4) * 0.1;
                innerCircle.scale.set(innerPulse, innerPulse, 1);
                innerCircleMaterial.opacity = 0.5 + Math.sin(group.userData.time * 2) * 0.2;

                // Animate runes
                runes.forEach((rune, index) => {
                    // Rotate runes
                    rune.rotation.z = rune.userData.originalRot.z + group.userData.time * (index % 2 === 0 ? 1 : -1);

                    // Pulse rune opacity
                    rune.material.opacity = 0.7 + Math.sin(group.userData.time * 5 + index) * 0.3;
                });

                // Pulse the glow ring
                const glowPulse = 1.0 + Math.sin(group.userData.time * 3) * 0.2;
                glowRing.scale.set(glowPulse, glowPulse, 1);
                glowRingMaterial.opacity = 0.3 + Math.sin(group.userData.time * 2) * 0.15;

                // Animate particles
                particles.forEach((particle, index) => {
                    // Swirl around the center
                    particle.userData.angle += delta * particle.userData.speed;

                    const radius = 0.1 + Math.sin(group.userData.time * 3 + index) * 0.05;
                    particle.position.x = Math.cos(particle.userData.angle) * radius;
                    particle.position.y = Math.sin(particle.userData.angle) * radius;

                    // Pulse opacity
                    particle.material.opacity = 0.5 + Math.sin(group.userData.time * 6 + index * 0.5) * 0.5;
                });

                // Pulse the light
                light.intensity = 0.8 + Math.sin(group.userData.time * 8) * 0.4;
            };

            return group;
        }
    },

    // Sacred Geometry – Metallic geometric shapes
    sacred_geometry: {
        name: 'Sacred Geometry',
        damage: 1,
        speed: 3.5, // Slower speed for better visibility
        range: 50.0,
        size: 0.5, // Larger size
        gravity: -0.1, // Almost no gravity
        color: 0xC0C0C0, // Silver
        trailEffect: true,
        trailColor: 0xFFFFFF, // White
        trailLength: 12,
        trailType: 'energy',
        trailWidth: 2.0,
        trailFade: true,
        impactEffect: 'magic_impact',
        orientWithVelocity: false, // Don't rotate with movement
        createMesh: (position) => {
            // Create a group for the sacred geometry
            const group = new THREE.Group();
            group.position.copy(position);

            // Choose one of three sacred geometry shapes
            const shapeType = Math.floor(Math.random() * 3);
            let mainGeometry;

            switch(shapeType) {
                case 0: // Dodecahedron (12 faces)
                    mainGeometry = new THREE.DodecahedronGeometry(0.25, 0);
                    break;
                case 1: // Icosahedron (20 faces)
                    mainGeometry = new THREE.IcosahedronGeometry(0.25, 0);
                    break;
                case 2: // Octahedron (8 faces)
                    mainGeometry = new THREE.OctahedronGeometry(0.25, 0);
                    break;
            }

            // Create metallic material
            const mainMaterial = new THREE.MeshBasicMaterial({
                color: 0xC0C0C0, // Silver
                transparent: false,
                opacity: 1.0,
                wireframe: false
            });

            const mainShape = new THREE.Mesh(mainGeometry, mainMaterial);
            group.add(mainShape);

            // Add wireframe overlay
            const wireframeMaterial = new THREE.MeshBasicMaterial({
                color: 0xFFFFFF, // White
                transparent: true,
                opacity: 0.5,
                wireframe: true
            });

            const wireframe = new THREE.Mesh(mainGeometry, wireframeMaterial);
            wireframe.scale.set(1.05, 1.05, 1.05); // Slightly larger
            group.add(wireframe);

            // Add orbiting geometric shapes
            const orbitCount = 3;
            const orbiters = [];

            for (let i = 0; i < orbitCount; i++) {
                // Create a smaller geometric shape
                const orbiterType = (shapeType + i + 1) % 3; // Different from main shape
                let orbiterGeometry;

                switch(orbiterType) {
                    case 0: // Tetrahedron (4 faces)
                        orbiterGeometry = new THREE.TetrahedronGeometry(0.08, 0);
                        break;
                    case 1: // Cube (6 faces)
                        orbiterGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
                        break;
                    case 2: // Octahedron (8 faces)
                        orbiterGeometry = new THREE.OctahedronGeometry(0.08, 0);
                        break;
                }

                const orbiterMaterial = new THREE.MeshBasicMaterial({
                    color: 0xFFD700, // Gold
                    transparent: true,
                    opacity: 0.9
                });

                const orbiter = new THREE.Mesh(orbiterGeometry, orbiterMaterial);

                // Create orbit path
                const orbitGroup = new THREE.Group();
                group.add(orbitGroup);

                // Position orbiter on its path
                orbiter.position.set(0.4, 0, 0); // Start at x-axis
                orbitGroup.add(orbiter);

                // Set random orbit orientation
                orbitGroup.rotation.x = Math.random() * Math.PI;
                orbitGroup.rotation.y = Math.random() * Math.PI;

                // Store rotation data
                orbitGroup.userData.rotationSpeed = 0.5 + Math.random() * 1.5;
                orbitGroup.userData.rotationAxis = new THREE.Vector3(
                    Math.random() - 0.5,
                    Math.random() - 0.5,
                    Math.random() - 0.5
                ).normalize();

                orbiters.push({ shape: orbiter, orbit: orbitGroup });
            }

            // Add energy lines connecting the shapes
            const lineCount = 6;
            const lines = [];

            for (let i = 0; i < lineCount; i++) {
                // Create line geometry (will be updated in animation)
                const lineGeometry = new THREE.BufferGeometry();
                const lineMaterial = new THREE.LineBasicMaterial({
                    color: 0xFFFFFF, // White
                    transparent: true,
                    opacity: 0.5
                });

                // Create line with placeholder points
                const line = new THREE.Line(lineGeometry, lineMaterial);
                group.add(line);
                lines.push(line);
            }

            // Add central glow
            const glowGeometry = new THREE.SphereGeometry(0.15, 16, 16);
            const glowMaterial = new THREE.MeshBasicMaterial({
                color: 0xFFFFFF, // White
                transparent: true,
                opacity: 0.3
            });
            const glow = new THREE.Mesh(glowGeometry, glowMaterial);
            group.add(glow);

            // Add point light
            const light = new THREE.PointLight(0xFFFFFF, 0.8, 3);
            light.position.set(0, 0, 0);
            group.add(light);

            // Add animation
            group.userData.time = 0;
            group.userData.rotationSpeed = 0.5 + Math.random() * 0.5;
            group.userData.animate = (delta) => {
                group.userData.time += delta;

                // Rotate the main shape
                mainShape.rotation.x += delta * group.userData.rotationSpeed;
                mainShape.rotation.y += delta * group.userData.rotationSpeed * 0.7;
                mainShape.rotation.z += delta * group.userData.rotationSpeed * 0.5;

                // Match wireframe rotation
                wireframe.rotation.copy(mainShape.rotation);

                // Animate orbiters
                orbiters.forEach((orbiter, index) => {
                    // Rotate orbit
                    const axis = orbiter.orbit.userData.rotationAxis;
                    const speed = orbiter.orbit.userData.rotationSpeed;

                    orbiter.orbit.rotateOnAxis(axis, delta * speed);

                    // Rotate the shape itself
                    orbiter.shape.rotation.x += delta * speed * 2;
                    orbiter.shape.rotation.y += delta * speed * 1.5;

                    // Pulse orbiter opacity
                    orbiter.shape.material.opacity = 0.7 + Math.sin(group.userData.time * 4 + index) * 0.3;
                });

                // Update energy lines
                lines.forEach((line, index) => {
                    // Connect main shape to orbiters and between orbiters
                    const startPoint = index % 2 === 0 ?
                        new THREE.Vector3(0, 0, 0) : // From center
                        orbiters[index % orbiters.length].shape.getWorldPosition(new THREE.Vector3());

                    const endPoint = orbiters[(index + 1) % orbiters.length].shape.getWorldPosition(new THREE.Vector3());

                    // Convert to local space
                    startPoint.sub(group.position);
                    endPoint.sub(group.position);

                    // Update line geometry
                    const points = [startPoint, endPoint];
                    line.geometry.dispose();
                    line.geometry = new THREE.BufferGeometry().setFromPoints(points);

                    // Pulse line opacity
                    line.material.opacity = 0.3 + Math.sin(group.userData.time * 5 + index) * 0.2;
                });

                // Pulse the central glow
                const glowPulse = 0.9 + Math.sin(group.userData.time * 3) * 0.2;
                glow.scale.set(glowPulse, glowPulse, glowPulse);
                glowMaterial.opacity = 0.3 + Math.sin(group.userData.time * 2) * 0.15;

                // Pulse the light
                light.intensity = 0.8 + Math.sin(group.userData.time * 6) * 0.3;
            };

            return group;
        }
    }
};

/**
 * Get projectile type data
 * @param {String} type - Projectile type name
 * @returns {Object} - Projectile type data
 */
export function getProjectileType(type) {
    // Default to soul orb if type not found
    return ProjectileTypes[type] || ProjectileTypes.default_soul_orb;
}

/**
 * Create a projectile mesh
 * @param {String} type - Projectile type name
 * @param {THREE.Vector3} position - Initial position
 * @param {Object} customProps - Custom properties for the projectile
 * @returns {THREE.Object3D} - Projectile mesh
 */
export function createProjectileMesh(type, position, customProps = {}) {
    const projectileType = getProjectileType(type);
    let mesh;

    // Handle special projectile types
    if (customProps.isBigFireball && type === 'fireball') {
        console.log("[ProjectileTypes] Creating big fireball with scale:", customProps.scale);
        // Create a larger, flame-shaped fireball
        const scale = customProps.scale || 2.0;
        const group = new THREE.Group();
        group.position.copy(position);

        // Create a flame-shaped core using custom geometry
        const flameShape = new THREE.Group();

        // Create the main flame body using teardrop shape
        const flamePoints = [];
        const segments = 16;

        // Define teardrop profile curve for the flame
        for (let i = 0; i <= segments; i++) {
            const t = i / segments;
            // Create teardrop shape - wider at bottom, pointed at top
            const y = t * 0.6 * scale; // Height
            let radius;
            if (t < 0.6) {
                // Bottom is wider
                radius = 0.25 * scale * (1 - Math.pow(t - 0.6, 2));
            } else {
                // Top tapers to a point
                radius = 0.25 * scale * Math.pow(1 - t, 1.2);
            }
            flamePoints.push(new THREE.Vector2(radius, y));
        }

        // Create the flame geometry
        const flameGeometry = new THREE.LatheGeometry(flamePoints, 16);

        // Create animated shader material for the flame core
        const coreMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                baseColor: { value: new THREE.Color(0xFF3300) }, // Deep red-orange core
                flameColor: { value: new THREE.Color(0xFF0000) }, // Bright red flame
                flickerSpeed: { value: 8.0 }
            },
            vertexShader: `
                uniform float time;
                varying vec2 vUv;
                varying vec3 vPosition;

                void main() {
                    vUv = uv;
                    vPosition = position;

                    // Add flame-like vertex displacement
                    float flicker = sin(time * 8.0 + position.y * 10.0) * 0.05;
                    vec3 pos = position;
                    pos.x += flicker * (0.5 + 0.5 * sin(position.y * 20.0 + time * 5.0));
                    pos.z += flicker * (0.5 + 0.5 * cos(position.y * 20.0 + time * 5.0));

                    gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform vec3 baseColor;
                uniform vec3 flameColor;
                uniform float flickerSpeed;

                varying vec2 vUv;
                varying vec3 vPosition;

                // Simple noise function
                float noise(vec3 p) {
                    return fract(sin(dot(p, vec3(12.9898, 78.233, 45.164))) * 43758.5453);
                }

                void main() {
                    // Create flame texture with noise
                    float noise1 = noise(vPosition * 5.0 + time * 2.0) * 0.5 + 0.5;
                    float noise2 = noise(vPosition * 10.0 - time * 3.0) * 0.5 + 0.5;

                    // Create flickering effect
                    float flicker = 0.8 + 0.2 * sin(time * flickerSpeed + vPosition.y * 20.0);

                    // Create vertical gradient (brighter at bottom)
                    float gradient = 1.0 - vPosition.y * 0.5;

                    // Mix colors based on noise and gradient
                    vec3 finalColor = mix(baseColor, flameColor, noise1 * noise2 * gradient * flicker);

                    // Add glow at the edges
                    float edge = smoothstep(0.4, 0.5, noise1 * noise2);
                    finalColor = mix(finalColor, flameColor, edge * 0.5);

                    // Add brightness variation
                    finalColor *= (0.8 + 0.4 * noise1 * flicker);

                    gl_FragColor = vec4(finalColor, 1.0);
                }
            `
        });

        const flameCore = new THREE.Mesh(flameGeometry, coreMaterial);
        // Rotate to point upward
        flameCore.rotation.x = Math.PI;
        flameShape.add(flameCore);

        // Add smaller flame tendrils around the main flame - reduced count for better performance
        const tendrilCount = 3; // Reduced from 5 to improve performance
        const tendrils = [];

        for (let i = 0; i < tendrilCount; i++) {
            // Create a curved path for each tendril
            const points = [];
            const tendrilSegments = 10;
            const tendrilHeight = (0.5 + Math.random() * 0.3) * scale;
            const tendrilWidth = (0.05 + Math.random() * 0.05) * scale;

            // Generate points for a curved tendril
            for (let j = 0; j <= tendrilSegments; j++) {
                const t = j / tendrilSegments;
                const angle = Math.PI * 2 * (i / tendrilCount);

                // Add some waviness to the tendril
                const xOffset = Math.sin(t * Math.PI * 3) * tendrilWidth;
                const zOffset = Math.cos(t * Math.PI * 2) * tendrilWidth;

                points.push(new THREE.Vector3(
                    Math.cos(angle) * tendrilWidth * 2 + xOffset,
                    t * tendrilHeight,
                    Math.sin(angle) * tendrilWidth * 2 + zOffset
                ));
            }

            // Create a smooth curve from the points
            const curve = new THREE.CatmullRomCurve3(points);

            // Create tube geometry along the curve - reduced complexity for better performance
            const tendrilGeometry = new THREE.TubeGeometry(
                curve,
                tendrilSegments, // tubular segments - reduced from tendrilSegments * 2
                tendrilWidth * 0.5, // radius
                4, // radial segments - reduced from 6
                false // closed
            );

            // Create material with slight color variation
            const tendrilMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color(0xFF2200).offsetHSL(Math.random() * 0.1, 0, Math.random() * 0.2),
                transparent: true,
                opacity: 0.7
            });

            const tendril = new THREE.Mesh(tendrilGeometry, tendrilMaterial);

            // Store original position for animation
            tendril.userData.originalPos = tendril.position.clone();
            tendril.userData.waveSpeed = 2.0 + Math.random() * 2.0;
            tendril.userData.waveOffset = Math.random() * Math.PI * 2;

            flameShape.add(tendril);
            tendrils.push(tendril);
        }

        // Add the flame shape to the group
        group.add(flameShape);

        // Add ember particles - reduced count for better performance
        const emberCount = 10; // Reduced from 20 to improve performance
        const embers = [];

        for (let i = 0; i < emberCount; i++) {
            // Create ember particle
            const emberSize = (0.03 + Math.random() * 0.03) * scale;
            const emberGeometry = new THREE.SphereGeometry(emberSize, 4, 4);
            const emberMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color().lerpColors(
                    new THREE.Color(0xFF3300), // Red-orange
                    new THREE.Color(0xFFFF00), // Yellow
                    Math.random()
                ),
                transparent: true,
                opacity: 0.7 + Math.random() * 0.3
            });

            const ember = new THREE.Mesh(emberGeometry, emberMaterial);

            // Position embers around the flame
            const angle = Math.random() * Math.PI * 2;
            const radius = (0.1 + Math.random() * 0.2) * scale;
            const height = Math.random() * 0.6 * scale;

            ember.position.set(
                Math.cos(angle) * radius,
                height,
                Math.sin(angle) * radius
            );

            // Store data for animation
            ember.userData.originalPos = ember.position.clone();
            ember.userData.speed = 0.5 + Math.random() * 1.0;
            ember.userData.angle = angle;
            ember.userData.radius = radius;
            ember.userData.rising = true;
            ember.userData.riseSpeed = 0.2 + Math.random() * 0.3;

            group.add(ember);
            embers.push(ember);
        }

        // Add point light - more red
        const light = new THREE.PointLight(0xFF2200, 1.8, 3.0 * scale);
        light.position.set(0, 0.2 * scale, 0);
        group.add(light);

        // Add animation
        group.userData.time = 0;
        group.userData.animate = (delta) => {
            group.userData.time += delta;
            const time = group.userData.time;

            // Update shader time uniform for flame core
            if (flameCore.material.uniforms) {
                flameCore.material.uniforms.time.value = time;
            }

            // Update heat distortion effect
            if (distortion.material.uniforms) {
                distortion.material.uniforms.time.value = time;
            }

            // Animate flame tendrils
            tendrils.forEach((tendril) => {
                // Add waviness for more natural flame movement
                const wave = Math.sin(time * tendril.userData.waveSpeed + tendril.userData.waveOffset) * 0.05 * scale;
                tendril.position.x = tendril.userData.originalPos.x + wave;
                tendril.position.z = tendril.userData.originalPos.z + wave * 0.7;
            });

            // Animate embers - rising and circling
            embers.forEach((ember) => {
                // Update ember angle based on speed
                ember.userData.angle += delta * ember.userData.speed;

                // Move in spiral pattern
                ember.position.x = Math.cos(ember.userData.angle) * ember.userData.radius;
                ember.position.z = Math.sin(ember.userData.angle) * ember.userData.radius;

                // Rise upward
                if (ember.userData.rising) {
                    ember.position.y += ember.userData.riseSpeed * delta;

                    // If reached top, start falling
                    if (ember.position.y > 0.7 * scale) {
                        ember.userData.rising = false;
                    }
                } else {
                    // Fall slower than rise
                    ember.position.y -= (ember.userData.riseSpeed * 0.5) * delta;

                    // If reached bottom, start rising again
                    if (ember.position.y < 0.1 * scale) {
                        ember.userData.rising = true;
                    }
                }

                // Fade out as they rise
                const heightRatio = ember.position.y / (0.7 * scale);
                ember.material.opacity = Math.max(0, 1.0 - heightRatio * 0.7);
            });

            // Pulse the light
            light.intensity = 1.5 + Math.sin(time * 8) * 0.5;

            // Rotate the flame slightly for more dynamic appearance
            flameShape.rotation.y += delta * 0.2;
        };

        // Rotate the entire flame to face upward
        flameShape.rotation.x = Math.PI;

        // Add heat distortion effect
        const distortionGeometry = new THREE.SphereGeometry(0.5 * scale, 16, 16);
        const distortionMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                intensity: { value: 0.03 }
            },
            vertexShader: `
                uniform float time;
                uniform float intensity;
                varying vec2 vUv;

                void main() {
                    vUv = uv;
                    vec3 pos = position;

                    // Add heat distortion effect
                    float distortion = sin(pos.y * 10.0 + time * 5.0) * intensity;
                    pos.x += distortion;
                    pos.z += distortion;

                    gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                varying vec2 vUv;

                void main() {
                    // Completely transparent material, just for distortion effect
                    gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);
                }
            `,
            transparent: true,
            opacity: 0
        });

        const distortion = new THREE.Mesh(distortionGeometry, distortionMaterial);
        group.add(distortion);

        return group;
    } else if (customProps.isSkeleton && type === 'screamer_skulls') {
        console.log("[ProjectileTypes] Creating skeleton minion with health:", customProps.health);
        // Create a skeleton minion
        const group = new THREE.Group();
        group.position.copy(position);

        // Create skull
        const skullGeometry = new THREE.SphereGeometry(0.25, 8, 8);
        const skullMaterial = new THREE.MeshBasicMaterial({
            color: 0xCCCCCC, // Bone white
            transparent: false,
            opacity: 1.0
        });
        const skull = new THREE.Mesh(skullGeometry, skullMaterial);
        skull.position.y = 0.5; // Place skull on top
        group.add(skull);

        // Create body (simplified skeleton)
        const bodyGeometry = new THREE.BoxGeometry(0.3, 0.6, 0.2);
        const bodyMaterial = new THREE.MeshBasicMaterial({
            color: 0xCCCCCC, // Bone white
            transparent: true,
            opacity: 0.8
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        group.add(body);

        // Add limbs
        const limbGeometry = new THREE.BoxGeometry(0.1, 0.4, 0.1);
        const limbMaterial = new THREE.MeshBasicMaterial({
            color: 0xCCCCCC, // Bone white
            transparent: true,
            opacity: 0.8
        });

        // Arms
        const leftArm = new THREE.Mesh(limbGeometry, limbMaterial);
        leftArm.position.set(0.25, 0.1, 0);
        leftArm.rotation.z = -Math.PI / 4;
        group.add(leftArm);

        const rightArm = new THREE.Mesh(limbGeometry, limbMaterial);
        rightArm.position.set(-0.25, 0.1, 0);
        rightArm.rotation.z = Math.PI / 4;
        group.add(rightArm);

        // Legs
        const leftLeg = new THREE.Mesh(limbGeometry, limbMaterial);
        leftLeg.position.set(0.15, -0.5, 0);
        group.add(leftLeg);

        const rightLeg = new THREE.Mesh(limbGeometry, limbMaterial);
        rightLeg.position.set(-0.15, -0.5, 0);
        group.add(rightLeg);

        // Add glowing eyes
        const eyeGeometry = new THREE.SphereGeometry(0.05, 8, 8);
        const eyeMaterial = new THREE.MeshBasicMaterial({
            color: 0xFF0000, // Red eyes
            transparent: false,
            opacity: 1.0
        });

        const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        leftEye.position.set(0.08, 0.55, 0.15);
        group.add(leftEye);

        const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        rightEye.position.set(-0.08, 0.55, 0.15);
        group.add(rightEye);

        return group;
    } else if (customProps.isFireCircle && type === 'fireball') {
        console.log("[ProjectileTypes] Creating fire circle projectile");
        // Create a fire circle projectile
        const group = new THREE.Group();
        group.position.copy(position);

        // Create fire particle
        const particleGeometry = new THREE.SphereGeometry(0.2, 8, 8);
        const particleMaterial = new THREE.MeshBasicMaterial({
            color: 0xFF4500, // Orange-red
            transparent: true,
            opacity: 0.8
        });
        const particle = new THREE.Mesh(particleGeometry, particleMaterial);
        group.add(particle);

        // Add point light
        const light = new THREE.PointLight(0xFF6600, 1.0, 2.0);
        light.position.set(0, 0, 0);
        group.add(light);

        return group;
    }

    // Standard projectile creation
    if (projectileType.createMesh) {
        mesh = projectileType.createMesh(position);
    } else {
        // Fallback to simple sphere if no createMesh function
        const geometry = new THREE.SphereGeometry(projectileType.size || 0.2, 8, 8);
        const material = new THREE.MeshBasicMaterial({
            color: projectileType.color || 0xFFFFFF
        });
        mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(position);
    }

    return mesh;
}
