// Wall prefabs
import { createStonebrickWallSegment } from '../generators/prefabs/stonebrickWall.js';
// ... other wall imports

// Floor prefabs
import { createCaveFloor } from '../generators/prefabs/caveFloor.js';
// ... other floor imports

// Door prefabs
import { createStoneArchwayDoor } from '../generators/prefabs/stoneArchwayDoor.js';
// ... other door imports

// Interior Object prefabs
import { createVineObject } from '../generators/prefabs/vineObject.js';
import { createTorchObject } from '../generators/prefabs/torchObject.js';
import { createStoneVaseObject } from '../generators/prefabs/stoneVaseObject.js';
import { createStonePillarObject } from '../generators/prefabs/stonePillarObject.js'; // <-- Import Pillar
import { createStoneRubbleObject } from '../generators/prefabs/stoneRubbleObject.js'; // <-- Import Rubble
import { createRitualCircleObject } from '../generators/prefabs/ritualCircleObject.js'; // <-- Import Ritual Circle
import { createAetherTorchObject } from '../generators/prefabs/aetherTorchObject.js'; // <-- Import Aether Torch
import { createSoulOrbObject } from '../generators/prefabs/soulOrbObject.js'; // <-- Import Soul Orb
// ... other interior object imports

const prefabMap = {
    wall: {
        'stonebrick': createStonebrickWallSegment,
        // ... other walls
    },
    floor: {
        'cave_floor': createCaveFloor,
        // ... other floors
    },
    door: {
        'stone_archway': createStoneArchwayDoor,
        // ... other doors
    },
    interior: {
        'vine': createVineObject,
        'torch': createTorchObject,
        'stone_vase': createStoneVaseObject,
        'stone_pillar': createStonePillarObject, // <-- Register Pillar
        'stone_rubble': createStoneRubbleObject, // <-- Register Rubble
        'ritual_circle': createRitualCircleObject, // <-- Register Ritual Circle
        'aether_torch': createAetherTorchObject, // <-- Register Aether Torch
        'soul_orb': createSoulOrbObject, // <-- Register Soul Orb
        // ... other interior objects
    },
    // Potentially other categories like 'enemy', 'item'
};

// Debug: Log all available prefabs
console.log('Available prefabs:', {
    wall: Object.keys(prefabMap.wall),
    floor: Object.keys(prefabMap.floor),
    door: Object.keys(prefabMap.door),
    interior: Object.keys(prefabMap.interior)
});

/**
 * Gets the prefab generator function for a given type and category.
 * @param {string} type - The specific type name (e.g., 'stonebrick', 'torch').
 * @param {string} category - The category ('wall', 'floor', 'door', 'interior').
 * @returns {function|null} - The generator function or null if not found.
 */
export function getPrefabFunction(type, category) {
    if (prefabMap[category] && prefabMap[category][type]) {
        const func = prefabMap[category][type];
        console.log(`[getPrefabFunction] Found prefab function for ${category}/${type}`);
        return func;
    }

    // Log the available prefabs for debugging
    console.warn(`[getPrefabFunction] Prefab not found for category '${category}', type '${type}'`);
    console.log(`Available prefabs for category '${category}':`, prefabMap[category] ? Object.keys(prefabMap[category]) : 'Category not found');

    return null;
}