import * as THREE from 'three';
// import { FontLoader } from 'three/addons/loaders/FontLoader.js';
// import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';
import { STATE } from '../constants.js';
// import { createTextMesh } from '../utils/textUtils.js'; // Assuming text helpers are moved

class HeroPageHandler {
    constructor(sceneManager) {
        this.sceneManager = sceneManager;
        this.scene = null; // Will be set in init
        // this.titleMesh = null; // Removed
        this.logoMesh = null;
        this.logoTexture = null;
        this.interactableObjects = [];
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.blinkTime = 0;
        this.rotateMessageElement = null; // Add reference for rotate message
        this.isStarting = false; // <<< ADD FLAG
        this.boundHandleOrientationChange = this._handleOrientationChange.bind(this); // Bind listener
        this.boundOnKeyDown = this.onKeyDown.bind(this); // <<< STORE BOUND FUNCTION
        this.boundOnClick = this.onClick.bind(this); // <<< STORE BOUND FUNCTION
        this.boundOnTouchEnd = this.onTouchEnd.bind(this); // <<< STORE BOUND FUNCTION
        this.boundOnMouseMove = this.onMouseMove.bind(this); // <<< STORE BOUND FUNCTION

        // DOM elements for video overlay - REMOVED FROM HERE
        // this.videoOverlay = document.getElementById('video-overlay');
        // this.introVideo = document.getElementById('intro-video');

        this.zoomAnimation = {
            active: false,
            startTime: 0,
            duration: 1200, // ms
            startPosition: new THREE.Vector3(),
            endPosition: new THREE.Vector3(0, -0.5, 1.5) // Zoom target near button
        };
    }

    async loadAssets() {
        const textureLoader = new THREE.TextureLoader();
        // const fontLoader = new FontLoader(); // Removed

        const promises = [];

        // Remove font loading
        /*
        if (!this.sceneManager.font) {
            console.log("HeroPageHandler: Loading font...");
            promises.push(
                fontLoader.loadAsync('assets/fonts/helvetiker_bold.typeface.json')
                    .then(font => {
                        console.log("HeroPageHandler: Font loaded.");
                        this.sceneManager.font = font;
                    })
                    .catch(err => {
                        console.error('ERROR loading font in HeroPageHandler:', err);
                        throw err; 
                    })
            );
        }
        */

        // Load Logo Texture
        console.log("HeroPageHandler: Loading logo texture...");
        promises.push(
            textureLoader.loadAsync('assets/textures/logo.png') // <<< CORRECTED PATH
                .then(texture => {
                    console.log("HeroPageHandler: Logo texture loaded.");
                    this.logoTexture = texture;
                    // Set filtering if needed (e.g., for pixel art)
                    // this.logoTexture.minFilter = THREE.NearestFilter;
                    // this.logoTexture.magFilter = THREE.NearestFilter;
                })
                .catch(err => {
                    console.error('ERROR loading logo texture in HeroPageHandler:', err);
                    // Handle texture loading error (e.g., maybe proceed without logo?)
                    throw err; // Re-throw for now
                })
        );

        await Promise.all(promises);
        console.log("HeroPageHandler: All assets loaded.");
    }

    async init(scene) {
        console.log("HeroPageHandler: Initializing...");
        this.scene = scene;
        this.scene.background = new THREE.Color(0x000000);

        // Reset blink timer
        this.blinkTime = 0;

        await this.loadAssets(); // Ensure assets are loaded

        // Remove font check
        /*
        if (!this.sceneManager.font) {
             console.error("HeroPageHandler: Font not available for title, cannot proceed.");
             return;
        }
        */
        if (!this.logoTexture) {
            console.error("HeroPageHandler: Logo texture not available, cannot create start button.");
            // Maybe show an error state?
            return;
        }

        // --- Remove Title Text ---
        /*
        const titleOptions = {
            fontStyle: 'bold 80px sans-serif',
            fontColor: '#00FFFF',
            emissiveColor: 0x00ffff,
            emissiveIntensity: 0.4
        };
        this.titleMesh = createTextMesh("Get Isekai'd", this.sceneManager.font, titleOptions, 0.015);
        this.titleMesh.position.y = 1.5; 
        this.scene.add(this.titleMesh);
        */

        // --- Start Button Logo ---
        const logoMaterial = new THREE.MeshBasicMaterial({
            map: this.logoTexture,
            transparent: true, // Needed for opacity animation and PNG transparency
            alphaTest: 0.1 // Adjust if needed to prevent transparent edges from blocking raycast
            // side: THREE.DoubleSide // If needed
        });

        // Determine aspect ratio to scale plane correctly
        const aspect = this.logoTexture.image.naturalWidth / this.logoTexture.image.naturalHeight;
        const logoHeight = 3.5; // <<< Slightly reduced size
        const logoWidth = logoHeight * aspect;

        const logoGeometry = new THREE.PlaneGeometry(logoWidth, logoHeight);
        this.logoMesh = new THREE.Mesh(logoGeometry, logoMaterial);

        // Set position (adjust z if needed so it's in front of background)
        this.logoMesh.position.set(0, 0, 0.1); // <<< Centered vertically

        // Store original scale for hover effect
        this.logoMesh.userData = {
            isButton: true,
            action: 'start_character_creation',
            originalScale: this.logoMesh.scale.clone() // Store initial scale
        };

        this.scene.add(this.logoMesh);

        // Make the logo interactable
        this.interactableObjects = [this.logoMesh];

        console.log("HeroPageHandler: Initialized with logo button.");

        // Add event listeners specific to this scene
        window.addEventListener('mousemove', this.boundOnMouseMove, false);
        window.addEventListener('click', this.boundOnClick, false);
        window.addEventListener('keydown', this.boundOnKeyDown, false);
        window.addEventListener('orientationchange', this.boundHandleOrientationChange); // Listen for orientation changes
        // Add touch listener specifically for mobile taps
        window.addEventListener('touchend', this.boundOnTouchEnd, false);

        // Get rotate message element
        this.rotateMessageElement = document.getElementById('rotate-message');
        if (!this.rotateMessageElement) {
            console.warn("HeroPageHandler: Rotate message element not found.");
        }

        // --- Initial Orientation Check --- 
        this._handleOrientationChange(); // Check orientation on init
        // --------------------------------
    }

    cleanup() {
        console.log("HeroPageHandler: Cleaning up...");
        // Remove title mesh cleanup
        /*
        if (this.titleMesh) {
            this.scene.remove(this.titleMesh);
        }
        */
        if (this.logoMesh) {
            this.scene.remove(this.logoMesh);
            // Dispose geometry and material to free GPU memory
            this.logoMesh.geometry.dispose();
            this.logoMesh.material.dispose();
        }
         // Dispose texture if it's not used elsewhere (might be cached by Loader)
         if (this.logoTexture) {
             // this.logoTexture.dispose(); // Be cautious if texture is shared/cached
         }

        // Clean up video references - REMOVED FROM HERE
        // this.videoOverlay = null;
        // this.introVideo = null;

        // this.titleMesh = null; // Removed
        this.logoMesh = null;
        this.logoTexture = null;
        this.interactableObjects = [];

        // Remove event listeners
        window.removeEventListener('mousemove', this.boundOnMouseMove, false);
        window.removeEventListener('click', this.boundOnClick, false);
        window.removeEventListener('keydown', this.boundOnKeyDown, false);
        window.removeEventListener('orientationchange', this.boundHandleOrientationChange); // Remove listener
        window.removeEventListener('touchend', this.boundOnTouchEnd, false); // Remove touch listener

        // Hide rotate message if shown
        if (this.rotateMessageElement) {
            this.rotateMessageElement.classList.remove('active');
        }

        // Reset cursor
        document.body.style.cursor = 'default';

        console.log("HeroPageHandler: Cleaned up.");

        this._startGame();
    }

    update(deltaTime, scene, camera) {
        this.blinkTime += deltaTime;

        // Fading Blink Animation for Logo
        if (this.logoMesh && this.logoMesh.material) {
            // Simple sine wave blink: oscillates between 0.5 and 1.0 opacity over ~2 seconds
            const blinkSpeed = Math.PI; // Adjust speed (higher value = faster blink)
            const minOpacity = 0.5;
            const maxOpacity = 1.0;
            const range = maxOpacity - minOpacity;
            this.logoMesh.material.opacity = minOpacity + (Math.sin(this.blinkTime * blinkSpeed) + 1) / 2 * range;
        }

        // Update zoom animation if active
        if (this.zoomAnimation.active) {
            const elapsed = this.sceneManager.clock.getElapsedTime() - this.zoomAnimation.startTime;
            let progress = Math.min(elapsed / (this.zoomAnimation.duration / 1000), 1);
            progress = progress * progress * (3 - 2 * progress); // Smoothstep

            camera.position.lerpVectors(this.zoomAnimation.startPosition, this.zoomAnimation.endPosition, progress);

            if (progress >= 1) {
                console.log("HeroPage zoom finished.");
                this.zoomAnimation.active = false;
                // Transition is handled by the fade overlay logic in SceneManager now
                // The SceneManager will call changeState based on fade completion
            }
        }
        // Other hero page specific animations (like title pulse maybe)
    }

    // --- NEW: Public method to trigger start from SceneManager ---
    triggerStartGame() {
        console.log("HeroPageHandler: triggerStartGame called.");
        this._startGame();
    }
    // --- END NEW METHOD ---

    onMouseMove(event) {
        if (this.zoomAnimation.active) return; // Ignore hover during zoom

        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = - (event.clientY / window.innerHeight) * 2 + 1;
        this.raycaster.setFromCamera(this.mouse, this.sceneManager.camera);

        const intersects = this.raycaster.intersectObjects(this.interactableObjects);

        // Reset scale/cursor before checking intersects
        let intersectedButton = null;
        this.interactableObjects.forEach(obj => {
            if (obj && obj.userData.isButton && obj.userData.originalScale) {
                 // Check if scale exists before trying to copy (it should after init)
                 if (obj.scale) {
                    obj.scale.copy(obj.userData.originalScale);
                 }
            }
        });
        document.body.style.cursor = 'default';

        if (intersects.length > 0) {
            // Check the first *visible* intersected object
             for (let i = 0; i < intersects.length; i++) {
                const intersectedObject = intersects[i].object;
                 // Check if the object is visible and is a button before reacting
                 if (intersectedObject.visible && intersectedObject.userData.isButton) {
                    intersectedButton = intersectedObject;
                    break; // Found the topmost interactable button
                 }
            }
        }
        
        // Apply hover effect only to the intersected button
         if (intersectedButton && intersectedButton.userData.originalScale) {
             intersectedButton.scale.copy(intersectedButton.userData.originalScale).multiplyScalar(1.15); // Apply hover scale
             document.body.style.cursor = 'pointer';
         }
    }

    onClick(event) {
        // This handles actual mouse clicks
        this._handleTapOrClick(event);
    }

    onTouchEnd(event) {
        // This handles the end of a touch interaction (potential tap)
        // Prevent default if needed, e.g., to stop emulated clicks
        // event.preventDefault(); // <<< REMOVED to fix desktop clicks
        this._handleTapOrClick(event);
    }

    _handleTapOrClick(event) {
        console.log(`_handleTapOrClick triggered by event type: ${event.type}`);

        // --- REMOVED TEMPORARY VISUAL FEEDBACK --- 
        /*
        if (this.logoMesh && this.logoMesh.material) {
            const originalColor = this.logoMesh.material.color.clone();
            this.logoMesh.material.color.setHex(0xff0000); // Flash Red
            // Ensure opacity is high enough to see the flash
            const originalOpacity = this.logoMesh.material.opacity;
            this.logoMesh.material.opacity = 1.0;

            setTimeout(() => {
                 if (this.logoMesh && this.logoMesh.material) { // Check if still exists
                    this.logoMesh.material.color.copy(originalColor);
                    this.logoMesh.material.opacity = originalOpacity; // Restore opacity
                 }
            }, 150); // Duration of the flash
        }
        */
        // --- END FEEDBACK REMOVAL ---

        if (this.zoomAnimation.active) {
            console.log("Ignoring tap/click: Zoom animation active.");
            return; // Ignore click/tap during zoom
        }

        let clientX, clientY;

        // Determine coordinates based on event type
        if (event.type === 'click' || event.type === 'mousedown' || event.type === 'mouseup' || event.type === 'mousemove') {
            clientX = event.clientX;
            clientY = event.clientY;
        } else if (event.type === 'touchend' || event.type === 'touchstart' || event.type === 'touchmove') {
            // Use changedTouches for touchend to get the last touch point
            const touch = event.changedTouches && event.changedTouches[0];
            if (!touch) {
                console.log("Ignoring tap: No touch data found in touchend event.");
                return; // No touch data
            }
            clientX = touch.clientX;
            clientY = touch.clientY;
        } else {
            console.log(`Ignoring event: Unhandled type ${event.type}`);
            return; // Unhandled event type
        }

        // Use the determined coordinates for raycasting
        this.mouse.x = (clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = - (clientY / window.innerHeight) * 2 + 1;
        this.raycaster.setFromCamera(this.mouse, this.sceneManager.camera);
        const intersects = this.raycaster.intersectObjects(this.interactableObjects);
        console.log(`Raycaster intersects: ${intersects.length}`);

        let logoClicked = false;
        if (intersects.length > 0) {
            for (let i = 0; i < intersects.length; i++) {
                const intersectedObject = intersects[i].object;
                console.log(`Intersected object ${i}:`, intersectedObject.name || intersectedObject.uuid);
                 if (intersectedObject.visible && intersectedObject.userData.isButton && intersectedObject === this.logoMesh) {
                    logoClicked = true;
                    console.log("Intersection with logo confirmed.");
                    break;
                 }
            }
        }

        if (logoClicked) {
            console.log("Start Logo interaction detected.");

            // --- Mobile Portrait Check --- 
            const likelyMobile = ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);
            const isPortrait = window.matchMedia("(orientation: portrait)").matches;
            console.log(`Device Check: likelyMobile=${likelyMobile}, isPortrait=${isPortrait}`);

            if (likelyMobile && isPortrait) {
                console.log("Mobile portrait detected. Showing rotate message.");
                if (this.rotateMessageElement) {
                    // Message is likely already shown by _handleOrientationChange, but ensure it is
                    this.rotateMessageElement.classList.add('active');
                } else {
                    console.warn("Rotate message element not found!");
                }
                // <<< REMOVED: Don't start game here on portrait tap >>>
            } else {
                console.log("Attempting to start game (Desktop or Mobile Landscape).");
                 // Hide rotate message if it was somehow visible
                 if (this.rotateMessageElement) {
                    this.rotateMessageElement.classList.remove('active');
                 }
                // Ensure we don't double-start if called by both touch and click
                if (!this.zoomAnimation.active) {
                     this._startGame(); // Proceed to start game
                }
            }
            // --- End Mobile Check ---

        } else {
             console.log("Interaction was not with the logo or no intersection.");
             // Hide rotate message if user clicks/taps elsewhere
             if (this.rotateMessageElement) {
                this.rotateMessageElement.classList.remove('active');
             }
        }
    }

    onKeyDown(event) {
        if (event.key === 'Enter') {
            console.log("Enter key pressed on Hero Page.");
             // Hide rotate message if it was shown
             if (this.rotateMessageElement) {
                this.rotateMessageElement.classList.remove('active');
             }
            this._startGame();
        }
    }

    _startGame() {
        // <<< ADD CHECK FOR FLAG >>>
        if (this.isStarting) {
            console.log("_startGame called again, but already starting. Ignoring.");
            return;
        }
        this.isStarting = true; // <<< SET FLAG
        console.log("HeroPageHandler: _startGame running...");

        // Trigger zoom animation
        this.zoomAnimation.active = true;
        this.zoomAnimation.startTime = this.sceneManager.clock.getElapsedTime();
        this.zoomAnimation.startPosition.copy(this.sceneManager.camera.position);

        // Play sound (Assuming SceneManager has an audio manager)
        // Ensure audioManager exists before trying to play
        if (this.sceneManager.audioManager) {
             this.sceneManager.audioManager.playSound('start_button'); 
        } else {
            console.warn("AudioManager not found on SceneManager in HeroPageHandler");
        }

        // Trigger fade in SceneManager, and change state directly in callback
        this.sceneManager.startFade(() => {
            // This callback executes *after* the screen is black
            this.sceneManager.changeState(STATE.CHARACTER_CREATION);
        });
    }

    // --- Add Orientation Change Handler ---
    _handleOrientationChange() {
        // If the message is currently shown and the device rotates to landscape, hide it.
        // const isLandscape = window.matchMedia("(orientation: landscape)").matches;
        // if (this.rotateMessageElement?.classList.contains('active') && isLandscape) {
        //    this.rotateMessageElement.classList.remove('active');
        // }
        // --- New Logic: Show/Hide based purely on orientation --- 
        const likelyMobile = ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);
        if (likelyMobile && this.rotateMessageElement) {
            const isPortrait = window.matchMedia("(orientation: portrait)").matches;
            if (isPortrait) {
                this.rotateMessageElement.classList.add('active');
                console.log("Orientation: Portrait (Mobile) - Showing rotate message.");
            } else {
                this.rotateMessageElement.classList.remove('active');
                console.log("Orientation: Landscape (Mobile) - Hiding rotate message.");
            }
        } else if (this.rotateMessageElement) {
            // Ensure message is hidden on desktop
            this.rotateMessageElement.classList.remove('active');
        }
        // --- End New Logic ---
    }
    // --- End Orientation Change Handler ---
}

export default HeroPageHandler; 