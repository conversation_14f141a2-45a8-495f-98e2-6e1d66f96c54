import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js'; // Use namespace import
import { STATE } from '../constants.js';
import PlayerController from '../core/PlayerController.js';
// --- Import constants from shared.js ---
import { VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE } from '../generators/prefabs/shared.js';
import {
    getOrCreateGeometry, // Added getOrCreateGeometry
    _getMaterialByHex_Cached // Keep internal access if needed, or remove if unused
} from '../generators/prefabs/shared.js';
// --- Import prefab functions from index.js ---
import { getEnemyData, AI_BRAIN_TYPES } from '../entities/EnemyTypes.js'; // Import enemy data getter and AI types
import { getAreaData } from '../gameData/areas.js';
// --- Import AI system ---
import { createAIBrain, updateAIBrain, applyKnockback } from '../ai/AIFactory.js';
// Removed import for createDamageNumber
import { groupCoordinator } from '../ai/GroupCoordinator.js';
import { AIStates } from '../ai/AIStates.js';
import { BatAnimationHandler } from '../ai/animations/BatAnimationHandler.js';
import { SimpleZombieAnimationHandler } from '../ai/animations/SimpleZombieAnimationHandler.js';
import { SimpleMagmaGolemAnimationHandler } from '../ai/animations/SimpleMagmaGolemAnimationHandler.js';
import { fixMagmaGolemAnimationHandler } from '../ai/animations/MagmaGolemAnimationHandlerFix.js';
import { MagmaGolemAnimationHandler } from '../ai/animations/MagmaGolemAnimationHandler.js';
import { CatacombOverlordAnimationHandler } from '../ai/animations/CatacombOverlordAnimationHandler.js';
import { fixZombieAnimationHandler } from '../ai/animations/ZombieAnimationHandlerFix.js';
// --- Import projectile system ---
import { getProjectileType, createProjectileMesh } from '../projectiles/ProjectileTypes.js';
import { Projectile } from '../projectiles/Projectile.js';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
// --- Import the new room generator ---
import { generateRoomVisuals } from '../scenes/roomGenerator.js';
// --- Import necessary constants from roomGenerator ---
import { ROOM_WORLD_SIZE, WALL_HEIGHT, DOOR_WIDTH } from '../scenes/roomGenerator.js';
// --- Import prefab functions from index.js ---
import { createArrowProjectileModel } from '../generators/prefabs/index.js'; // Import arrow model
// --- Import item and enemy spawn systems ---
import itemDropManager from '../systems/ItemDropManager.js';
import simpleSoulDropManager from '../systems/SimpleSoulDropManager.js'; // Import the simplified soul drop manager
import enemySpawnManager from '../systems/EnemySpawnManager.js';
import Item from '../entities/Item.js';
// --- Import environment systems ---
import environmentObjectManager from '../systems/EnvironmentObjectManager.js';
import environmentTypeManager from '../systems/EnvironmentTypeManager.js';

// --- Helper Function: Integer to Roman Numerals ---
function intToRoman(num) {
    if (num <= 0) return ''; // Handle non-positive numbers
    const lookup = {
        M: 1000, CM: 900, D: 500, CD: 400, C: 100, XC: 90, L: 50, XL: 40, X: 10, IX: 9, V: 5, IV: 4, I: 1
    };
    let roman = '';
    for (let i in lookup) {
        while (num >= lookup[i]) {
            roman += i;
            num -= lookup[i];
        }
    }
    return roman;
}
// --------------------------------------------------

// --- Constants ---
// Constants related to room dimensions (ROOM_WORLD_SIZE, WALL_HEIGHT, WALL_DEPTH, DOOR_HEIGHT, ARCH_VOXEL_SIZE) moved to roomGenerator.js

const DOOR_TRIGGER_SIZE = 1.5; // Size of the trigger box for doors (Increased from 1.0)
const PLAYER_SPAWN_OFFSET = 1.5; // How far from the entry wall the player spawns (Reduced back from 3.0)
// REMOVED: const DOOR_WIDTH = 2.0;
// REMOVED: const WALL_HEIGHT = 3;

// --- Door Placeholder --- (Keep for debug/potential use)
const DOOR_COLOR = 0xffff00; // Yellow
const doorPlaceholderMaterial = new THREE.MeshStandardMaterial({
    color: DOOR_COLOR,
    emissive: DOOR_COLOR,
    emissiveIntensity: 0.3,
    roughness: 0.8,
    metalness: 0.2,
    transparent: true,
    opacity: 0.65
});
// --- End Door Placeholder ---

// --- Dynamic Lighting Constants --- (Keep, used by _updateDynamicLighting)
const BASE_HEALTH = 5;
const MAX_BRIGHTNESS_HEALTH = 20;
const BASE_AMBIENT_INTENSITY = 0.6; // Increased from 0.2 for brighter base lighting
const MIN_AMBIENT_INTENSITY = 0.2; // Increased from 0.02 for brighter minimum
const MAX_AMBIENT_INTENSITY = 2.0; // Increased from 1.2 for brighter maximum
const INTENSITY_LERP_FACTOR = 3.0;
const BASE_TORCH_INTENSITY = 1.5; // Increased from 0.5 for brighter torches
const MAX_TORCH_INTENSITY = 5.0; // Increased from 3.5 for brighter maximum
const BASE_TORCH_RANGE = 8.0; // Increased from 5.0 for wider light range
const MAX_TORCH_RANGE = 15.0; // Increased from 12.0 for wider maximum range
// ----------------------------------

// --- Projectile/Arrow/Dodge/Axes Constants --- (Keep, used by various methods)
const PROJECTILE_SIZE = 0.15;
const PROJECTILE_SPEED = 10.0;
const PROJECTILE_COLOR = 0xa0ffff;
const PROJECTILE_EMISSIVE_INTENSITY = 1.5;
const PROJECTILE_GRAVITY = -4.9;

const ARROW_COLOR = 0xaaaaaa;
const ARROW_GRAVITY = -7.0;

const DODGE_ACTIVATION_DISTANCE = 2.0;
const DODGE_SPEED = 5.0;
const DODGE_DURATION = 0.3;
const DODGE_COOLDOWN = 1.5;

const AXES_HELPER_SIZE = 1.5;
// --------------------------

// --- Enemy Constants --- (Mostly superseded by EnemyTypes.js)
// ----------------------

// Add these constants near the other lighting constants
const FLICKER_MIN_INTENSITY = 0.3; // Increased from 0.001 for much brighter minimum
const FLICKER_MAX_INTENSITY = 1.2;  // Increased from 0.4 for much brighter maximum
const FLICKER_SPEED = 0.8;          // Keep the same slower flicker speed
const FLICKER_NOISE_SCALE = 0.15;    // Keep the same noise scale

class DungeonHandler {
    constructor(sceneManager, params) {
        this.sceneManager = sceneManager;
        this.audioManager = sceneManager.audioManager;
        this.initParams = params;
        this.scene = null;
        this.camera = null;
        this.userAnswers = params.userAnswers || [];

        // PERFORMANCE: Global performance mode setting
        // This will be used throughout the game to adjust visual quality
        this.performanceMode = 'high'; // 'low', 'medium', 'high'

        // Auto-detect performance mode based on device capabilities
        this._detectPerformanceMode();

        this.player = null;
        this.playerController = null;
        this.auraParticles = null;

        // Track active bosses for easy access
        this.activeBosses = [];
        this.debugGlobalLight = null;
        this.isDebugLightOn = false;
        this.ambientLight = null;
        this.floatingTexts = []; // Array to store floating text objects

        // Camera state
        this.isTopDownView = false;
        this.originalCameraOffset = new THREE.Vector3(0, 15, 20); // Increased height and distance for better angle
        this.topDownCameraOffset = new THREE.Vector3(0, 40, 0);

        // Zoom state
        this.defaultZoom = 0.85; // Slightly increased default zoom
        this.currentZoom = this.defaultZoom;
        this.minZoom = 0.5;
        this.maxZoom = 2.5;
        this.zoomSpeed = 1.0;

        this.floorLayout = null;
        this.currentRoomId = null;
        this.currentRoomObjects = [];
        this.doorTriggers = [];
        this.transitionCooldown = 0;
        this.transitionDebounce = 0.2;

        this.collisionObjects = [];
        this.activeProjectiles = [];
        this.activeEnemies = [];
        this.activeItems = []; // Array to store active items in the world

        // Initialize floor bounds
        this.floorBounds = new THREE.Box3(new THREE.Vector3(-50, -10, -50), new THREE.Vector3(50, 10, 50));

        // --- Object Pools ---
        this.playerProjectilePool = [];
        this.enemyProjectilePool = [];
        // --------------------

        // Minimap DOM reference
        this.minimapGridElement = null;
        this.areaNameElement = null;
        this.totalRoomCountElement = null;
        this.roomShapeElement = null; // NEW: Element to display room shape

        this.currentArea = null; // NEW - Stores the data object for the current room's area

        this.isEspEnabled = false;

        this.enemies = []; // ??? Is this used? activeEnemies seems to be the one. Keep for now.
        this.teleportTriggerMap = new Map();
        this.enemyStateTimers = new Map();
        // REMOVED: this.currentPrefabData = null;

        // --- Rotation Helpers State (Enemies) ---
        this.isEspRotationViewActive = false;
        this.enemyRotationHelpersGroup = new THREE.Group();
        // ----------------------------------------

        // --- PREDICTIVE PRE-LOADING SYSTEM ---
        this.preLoadedRooms = new Map(); // Map<RoomID, {roomGroup, collisionMeshes, lights, boundingBox, floorData}>
        this.roomStates = new Map(); // Map<RoomID, {enemies, items, events, environmental}>
        this.dungeonMetadata = null; // Cached dungeon information
        this.preLoadQueue = new Set(); // Rooms queued for pre-loading
        this.preLoadInProgress = new Set(); // Rooms currently being pre-loaded
        this.isPreLoading = false; // Flag to prevent floor validation during pre-loading
        // ----------------------------------------

        // FIXED: Add event listener for destructible object destruction
        this._boundObjectDestroyedHandler = this._handleObjectDestroyedEvent.bind(this);
        window.addEventListener('objectDestroyed', this._boundObjectDestroyedHandler);
        console.log(`[DungeonHandler] Added objectDestroyed event listener`);

        // --- FLOOR DETECTION SYSTEM ---
        this.globalFloorData = new Map(); // Map<RoomID, {floorMeshes, validSpawnAreas, floorBounds}>
        this.currentRoomFloorData = null; // Current room's floor data for quick access
        // ------------------------------

        // Boss battle state
        this.activeBosses = [];
        this.isBossBattle = false;
        this.bossMusicPlaying = false;

        // --- Boss Timeline ---
        this.bossTimeline = [];
        this.bossFightStartTime = 0;
        this.bossFightElapsedTime = 0;
    }

    async init(scene) {
        console.log("DungeonHandler: Initializing (Graph Mode)...");
        this.scene = scene;
        this.scene.background = new THREE.Color(0x050510);
        this.scene.fog = new THREE.FogExp2(0x050510, 0.03);

        // Force top-down view flag to false to ensure it gets initialized
        this._hasInitializedTopDownView = false;

        // --- Get DOM Element References directly ---
        console.log("Attempting DOM lookup directly in init...");
        this.minimapGridElement = document.getElementById('minimap-grid');
        this.areaNameElement = document.getElementById('area-name-display');
        this.totalRoomCountElement = document.getElementById('total-room-count-display');
        this.roomShapeElement = document.getElementById('room-shape-display');

        if (!this.areaNameElement) {
            console.warn("Area name display element (#area-name-display) not found in init!");
        } else {
            console.log("Area name display element FOUND in init.");
            this.areaNameElement.style.opacity = '0';
            this.areaNameElement.style.visibility = 'hidden';
        }
        if (!this.minimapGridElement) {
            console.warn("Minimap grid element (#minimap-grid) not found in init!");
        }
        if (!this.totalRoomCountElement) {
            console.warn("Total room count element (#total-room-count-display) not found in init!");
        } else {
            console.log("Total room count element FOUND in init.");
            this._updateTotalRoomCountDisplay(false);
        }
        if (!this.roomShapeElement) {
            console.warn("Room shape display element (#room-shape-display) not found in init!");
            // Create the element if it doesn't exist
            this.roomShapeElement = document.createElement('div');
            this.roomShapeElement.id = 'room-shape-display';
            this.roomShapeElement.style.position = 'absolute';
            this.roomShapeElement.style.top = '60px';
            this.roomShapeElement.style.left = '50%';
            this.roomShapeElement.style.transform = 'translateX(-50%)';
            this.roomShapeElement.style.color = '#ffffff';
            this.roomShapeElement.style.fontFamily = '"Press Start 2P", monospace';
            this.roomShapeElement.style.fontSize = '12px';
            this.roomShapeElement.style.textAlign = 'center';
            this.roomShapeElement.style.opacity = '0';
            this.roomShapeElement.style.visibility = 'hidden';
            this.roomShapeElement.style.transition = 'opacity 0.5s ease-in-out';
            this.roomShapeElement.style.zIndex = '1000';
            this.roomShapeElement.style.textShadow = '2px 2px 4px rgba(0, 0, 0, 0.8)';
            document.body.appendChild(this.roomShapeElement);
            console.log("Created room shape display element.");
        } else {
            console.log("Room shape display element FOUND in init.");
            this.roomShapeElement.style.opacity = '0';
            this.roomShapeElement.style.visibility = 'hidden';
        }
        // ---------------------------------------------

        this._setupCamera();
        this._setupLighting();

        // --- Use Pre-generated Data ---
        this.floorLayout = this.initParams.floorLayout;
        this.player = this.initParams.playerMesh;

        // ... (fallback generation logic) ...
        if (!this.floorLayout || this.floorLayout.size === 0) {
            console.error("DungeonHandler: Received invalid floor layout data!");
            console.warn("Attempting fallback dungeon generation...");
            // NOTE: Needs DungeonGenerator class if fallback is desired
            // const fallbackGenerator = new DungeonGenerator();
            // this.floorLayout = fallbackGenerator.generateLayout();
            // if (!this.floorLayout || this.floorLayout.size === 0) {
                console.error("CRITICAL: Fallback dungeon generation also failed! (DungeonGenerator missing?)");
                this.sceneManager.changeState(STATE.HERO_PAGE);
                return;
            // }
        }
        if (!this.player) {
            console.error("DungeonHandler: Received invalid player mesh data!");
            // Handle error - create fallback player
            console.warn("Creating fallback player model...");
            this.player = createElementalPlayerModel();
        }
        // --------------------------------

        // Add pre-created Player to scene
        this.player.name = "player";
        const playerScale = 0.7;
        this.player.scale.set(playerScale, playerScale, playerScale);
        this.scene.add(this.player);

        // Set up player userData for damage handling
        this.player.userData = this.player.userData || {};

        // Create Player Controller (using the pre-created player mesh)
        this.playerController = new PlayerController(
            this.player,
            this.camera,
            this.scene,
            this.sceneManager.clock,
            this, // Pass DungeonHandler instance
            [], // Initial collision objects (empty)
            { minX: -Infinity, maxX: Infinity, minZ: -Infinity, maxZ: Infinity } // No bounds initially
        );

        // Load the starting room (ID 0) immediately
        this.currentRoomId = 0;
        this.loadRoom(this.currentRoomId);

        // Enable player controls
        this.playerController.enable();

        // Set up player userData with takeDamage method
        this.player.userData.takeDamage = (amount, sourcePosition) => {
            console.log(`Player userData takeDamage called with amount: ${amount}`);
            if (this.playerController && this.playerController.takeDamage) {
                return this.playerController.takeDamage(amount, sourcePosition);
            } else {
                console.error('Cannot apply damage: playerController or takeDamage method is missing');
                if (!this.playerController) {
                    console.error('playerController is null or undefined');
                } else if (!this.playerController.takeDamage) {
                    console.error('playerController.takeDamage is null or undefined');
                }
            }
            return false;
        };

        // Create Aura and other elements
        this._createAura();
        this.debugGlobalLight = new THREE.HemisphereLight(0xffffff, 0x444444, 1.5);
        this.debugGlobalLight.position.set(0, 50, 0);
        this.debugGlobalLight.visible = false;
        this.debugGlobalLight.name = "debugGlobalLight";
        this.scene.add(this.debugGlobalLight);

        // Initial minimap update (may show only start room)
        this._updateMinimap();

        // --- PREDICTIVE PRE-LOADING SYSTEM ---
        console.log("Initializing predictive pre-loading system...");
        // Generate dungeon metadata and start predictive pre-loading
        setTimeout(() => {
            this._generateDungeonMetadata();
            this._startPredictivePreLoading();
        }, 100);
        // ------------------------------------------

        console.log("DungeonHandler: Initialized successfully.");

        this.ambientLight = new THREE.AmbientLight(0xffffff, BASE_AMBIENT_INTENSITY);
        this.scene.add(this.ambientLight);

        this.enemyRotationHelpersGroup.visible = true;
        this.scene.add(this.enemyRotationHelpersGroup);

        // --- Initialize Music System ---
        if (this.audioManager) {
            console.log("Initializing music system...");
            this.audioManager.initMusicSystem().then(success => {
                if (success) {
                    console.log("Music system initialized successfully");
                    // Start the music for the current area (catacombs)
                    this.audioManager.startAreaMusic('catacombs');
                } else {
                    console.warn("Music system initialization failed");
                }
            }).catch(error => {
                console.error("Error initializing music system:", error);
            });
        }

        // Store reference to dungeonHandler in scene userData for access by other systems
        this.scene.userData.dungeonHandler = this;
        if (this.audioManager) {
            this.scene.userData.audioManager = this.audioManager;
        }

        // CRITICAL FIX: Process any pending floor detection system initialization
        this._processPendingFloorDetection();
    }

    _setupCamera() {
        console.log("Setting up camera with isTopDownView =", this.isTopDownView);
        const aspect = window.innerWidth / window.innerHeight;
        const frustumSize = 16; // Increased frustum size for wider view in top-down mode
        this.camera = new THREE.OrthographicCamera(
            frustumSize * aspect / -2,
            frustumSize * aspect / 2,
            frustumSize / 2,
            frustumSize / -2,
            1,
            1000
        );

        // Set initial position based on view mode
        if (this.isTopDownView) {
            // Top-down view setup - using direct matrix manipulation
            this.camera.position.set(0, this.topDownCameraOffset.y, 0); // Position camera above the scene
            this.camera.up.set(0, 1, 0); // Standard up vector

            // Apply the top-down view matrix directly
            this.camera.matrixAutoUpdate = false;
            this.camera.matrix.identity(); // Reset matrix
            this.camera.matrix.multiply(new THREE.Matrix4().makeTranslation(0, this.topDownCameraOffset.y, 0)); // Position
            this.camera.matrix.multiply(new THREE.Matrix4().makeRotationX(-Math.PI/2)); // Rotation
            this.camera.matrixWorldNeedsUpdate = true;
            this.camera.updateMatrixWorld();

            // Extract position and rotation from matrix
            this.camera.matrix.decompose(this.camera.position, this.camera.quaternion, this.camera.scale);
        } else {
            // Normal view setup
            this.camera.position.copy(this.originalCameraOffset);
            this.camera.up.set(0, 1, 0); // Standard up vector
            this.camera.lookAt(0, 2, 0); // Look at a point slightly above ground for better perspective
        }

        // Set initial zoom
        this.currentZoom = this.isTopDownView ? 0.9 : this.defaultZoom;
        this.camera.zoom = this.currentZoom;
        this.camera.updateProjectionMatrix(); // IMPORTANT: Update projection after setting zoom

        // Store the original camera reference from SceneManager
        this.originalSceneManagerCamera = this.sceneManager.camera;

        // Assign our camera to SceneManager
        this.sceneManager.camera = this.camera;

        // Force update CRT effect with our camera
        try {
            if (this.sceneManager.crtEffect && this.sceneManager.crtEffect.manager) {
                this.sceneManager.crtEffect.manager.camera = this.camera;
                if (this.sceneManager.crtEffect.manager.crtPass &&
                    this.sceneManager.crtEffect.manager.crtPass.uniforms &&
                    this.sceneManager.crtEffect.manager.crtPass.uniforms.cameraZoom) {
                    this.sceneManager.crtEffect.manager.crtPass.uniforms.cameraZoom.value = this.currentZoom;
                }
            }
        } catch (error) {
            console.warn('Error updating CRT effect camera:', error);
        }

        // Force update HDR effect with our camera
        try {
            if (this.sceneManager.hdrFramerateEffect && this.sceneManager.hdrFramerateEffect.manager) {
                this.sceneManager.hdrFramerateEffect.manager.camera = this.camera;
            }
        } catch (error) {
            console.warn('Error updating HDR effect camera:', error);
        }

        console.log(`DungeonHandler: Orthographic camera set up in ${this.isTopDownView ? 'top-down' : 'normal'} view.`);
    }

    _setupLighting() {
        const globalDirLight = this.scene.getObjectByName("globalDirectionalLight");
        if (globalDirLight) this.scene.remove(globalDirLight);
        const globalAmbLight = this.scene.getObjectByName("globalAmbientLight");
        if (globalAmbLight) this.scene.remove(globalAmbLight);
        const oldAmbLight = this.scene.getObjectByName("dungeonAmbientLight");
        if (oldAmbLight) this.scene.remove(oldAmbLight);
        const oldKeyLight = this.scene.getObjectByName("dungeonKeyLight");
        if (oldKeyLight) this.scene.remove(oldKeyLight);
        const oldDirLightDungeon = this.scene.getObjectByName("dungeonDirectionalLight");
        if (oldDirLightDungeon) this.scene.remove(oldDirLightDungeon);

        // Store the ambient light reference - using brighter blue-tinted light
        this.ambientLight = new THREE.AmbientLight(0x6060a0, BASE_AMBIENT_INTENSITY);
        this.ambientLight.name = "dungeonAmbientLight";
        this.scene.add(this.ambientLight);

        // Add a directional light for better illumination
        const dirLight = new THREE.DirectionalLight(0xffffff, 0.8);
        dirLight.position.set(0, 10, 0);
        dirLight.name = "dungeonKeyLight";
        this.scene.add(dirLight);

        // Add a hemisphere light for more natural lighting
        const hemiLight = new THREE.HemisphereLight(0xffffff, 0x404080, 0.6);
        hemiLight.name = "dungeonHemiLight";
        this.scene.add(hemiLight);

        console.log(`Dungeon lighting set up with enhanced brightness`);
    }

    async loadRoom(roomId, entryDirection = null) {
        console.log(`DungeonHandler: Loading room ID: ${roomId} (Entering from: ${entryDirection || 'Start'})`);
        this.cleanupRoom();

        // Flag to track if we're entering a new room (for music system)
        this.isEnteringNewRoom = true;

        const roomData = this.floorLayout.get(roomId);
        if (!roomData) {
            console.error(`DungeonHandler: Room data not found for ID ${roomId}`);
            return;
        }

        this.currentRoomId = roomId;
        roomData.visited = true;

        // Initialize room state if it doesn't exist
        if (!this.roomStates.has(roomId)) {
            this._initializeRoomState(roomId, roomData);
        }

        // --- Get Area Data --- // UPDATED BLOCK
        const areaId = roomData.state.area || 'catacombs'; // Get areaId, fallback
        this.currentArea = getAreaData(areaId);
        if (!this.currentArea) {
            console.warn(`Area data for "${areaId}" not found for room ${roomId}. Using fallback data.`);
            // Fallback data (using dummy prefabs and basic structure)
            const dummyPrefab = () => new THREE.Group();
            this.currentArea = {
                name: areaId.replace(/_/g, ' ').toUpperCase(), // Basic name from ID
                walls: [dummyPrefab],
                floors: [dummyPrefab],
            };
        } else {
            console.log(`   - Applying Area: ${this.currentArea.name} (ID: ${areaId})`);
        }

        // --- Update Music for Area --- // NEW BLOCK
        if (this.audioManager && this.previousAreaId !== areaId) {
            if (this.previousAreaId) {
                // Transition to new area music
                console.log(`   - Transitioning music from ${this.previousAreaId} to ${areaId}`);
                this.audioManager.transitionAreaMusic(areaId);
            } else {
                // First area, start music
                console.log(`   - Starting music for ${areaId}`);
                this.audioManager.startAreaMusic(areaId);
            }
            this.previousAreaId = areaId;
        }

        // Ensure ambient light exists and is set up
        if (!this.ambientLight) {
            this.ambientLight = new THREE.AmbientLight(0x404080, BASE_AMBIENT_INTENSITY);
            this.ambientLight.name = "dungeonAmbientLight";
            this.scene.add(this.ambientLight);
            this.currentRoomObjects.push(this.ambientLight);
            console.log(`Created new ambient light with base intensity: ${BASE_AMBIENT_INTENSITY}`);
        }

        // --- Display Area Name --- // UPDATED BLOCK
        if (this.currentArea && this.currentArea.name) { // Use name from area data
            let displayString = this.currentArea.name;
            if (roomId > 0) {
                const romanNumeral = intToRoman(roomId);
                if (romanNumeral) displayString += ` ${romanNumeral}`;
            }
            const displayDelay = 100;
            setTimeout(() => this._displayAreaName(displayString), displayDelay);
        }
        // --- End Display Area Name ---

        // --- Use pre-generated room visuals or generate on demand ---
        let roomGroup, collisionMeshes, lights, boundingBox, doorCenterPoints;

        if (this.preLoadedRooms.has(roomId)) {
            console.log(`   - Using pre-loaded visuals for room ${roomId}`);
            const preLoadData = this.preLoadedRooms.get(roomId);
            roomGroup = preLoadData.roomGroup;
            collisionMeshes = preLoadData.collisionMeshes;
            lights = preLoadData.lights;
            boundingBox = preLoadData.boundingBox;
            doorCenterPoints = preLoadData.doorCenterPoints;
        } else {
            // Show loading indicator if generating on-demand
            const messageElement = document.getElementById('loading-message');
            if (messageElement) {
                messageElement.textContent = 'Generating room...';
                messageElement.style.display = 'block';
            }

            console.log(`   - Pre-generated visuals NOT FOUND for room ${roomId}. Generating on demand...`);

            // CRITICAL FIX: Set temporary bounds for this room during generation
            this._setTemporaryRoomBounds(roomData);

            const result = generateRoomVisuals(roomData, this.currentArea); // NEW - Pass currentArea
            roomGroup = result.roomGroup;
            collisionMeshes = result.collisionMeshes;
            lights = result.lights;
            boundingBox = result.boundingBox;
            doorCenterPoints = result.doorCenterPoints; // <<< Get points from generation

            // CRITICAL FIX: Clear temporary bounds, wall segments, and floor meshes after room generation
            this._clearTemporaryRoomBounds();
            this._clearCurrentRoomWallSegments();
            this._clearCurrentRoomFloorMeshes();

            // Hide loading indicator
            if (messageElement) {
                messageElement.style.display = 'none';
            }

            // Cache the generated room for future use, including door points and floor data
            if (roomGroup) {
                // CRITICAL FIX: Generate floor data for on-demand rooms too with error handling
                let floorData;
                try {
                    floorData = this._generateRoomFloorData(roomId, collisionMeshes, boundingBox);
                } catch (floorError) {
                    console.error(`Error generating floor data for on-demand room ${roomId}:`, floorError);
                    // Create fallback floor data
                    floorData = {
                        floorMeshes: [],
                        validSpawnAreas: [],
                        floorBounds: new THREE.Box3(
                            new THREE.Vector3(-25, -10, -25),
                            new THREE.Vector3(25, 10, 25)
                        )
                    };
                }

                this.preLoadedRooms.set(roomId, {
                    roomGroup, // Direct reference is likely okay for the group itself
                    collisionMeshes, // Direct reference okay
                    lights, // Direct reference okay
                    boundingBox, // Direct reference okay
                    doorCenterPoints: { ...doorCenterPoints }, // <<< Store a shallow clone of the map
                    floorData // Store floor data with room
                });
                console.log(`Generated room ${roomId} (${roomData.type}) on-demand with ${floorData.validSpawnAreas.length} spawn points`);
            }
        }

        if (!roomGroup) {
            console.error("   - Failed to get or generate room visuals!");
            return; // Cannot proceed without visuals
        }

        // Add the room group to the scene
        this.scene.add(roomGroup);
        this.currentRoomObjects.push(roomGroup); // Add for cleanup

        // Assign collision meshes
        this.collisionObjects = collisionMeshes || [];

        // Add lights to the scene
        lights.forEach(light => {
            if (light instanceof THREE.PointLight) {
                this.scene.add(light);
                this.currentRoomObjects.push(light); // Add for cleanup
            } else {
                console.warn("   - Invalid light object in lights array:", light);
            }
        });
        // --- End Visual Setup ---

        // --- Create Door Triggers ---
        this.doorTriggers = []; // Clear existing triggers
        const connections = roomData.connections;

        // Use precise door positions if available, otherwise fallback (though fallback is less likely now)
        const usePreciseDoorPositions = doorCenterPoints && Object.values(doorCenterPoints).some(p => p !== null);

        for (const dirKey in connections) {
            const dirLower = dirKey.toLowerCase();
            if (!['n', 's', 'e', 'w'].includes(dirLower)) continue;

            const neighborId = connections[dirKey];
            if (neighborId !== null && neighborId !== undefined) {
                let connectionPos = null;

                if (usePreciseDoorPositions && doorCenterPoints[dirLower]) {
                    connectionPos = doorCenterPoints[dirLower].clone();
                    // Keep Y position at 0 for ground-based player collision
                    connectionPos.y = 0;
                } else {
                     // Fallback using bounding box (less accurate)
                     const doorTriggerY = (WALL_HEIGHT || 3) / 2;
                     const fallbackPoints = {
                         n: new THREE.Vector3(0, doorTriggerY, boundingBox.minZ),
                         s: new THREE.Vector3(0, doorTriggerY, boundingBox.maxZ),
                         w: new THREE.Vector3(boundingBox.minX, doorTriggerY, 0),
                         e: new THREE.Vector3(boundingBox.maxX, doorTriggerY, 0)
                     };
                     connectionPos = fallbackPoints[dirLower];
                     console.warn(`[loadRoom] Using fallback bounding box position for door trigger ${dirLower.toUpperCase()}`);
                }

                if (!connectionPos) {
                     console.error(`[loadRoom] CRITICAL: Could not determine connection point for dir: ${dirLower.toUpperCase()}`);
                     continue;
                }
                console.log(`[loadRoom] Creating trigger for dir: ${dirLower.toUpperCase()} targeting ${neighborId} at`, connectionPos);

                // Create Invisible Trigger Mesh
                let triggerGeo;
                const triggerHeight = (WALL_HEIGHT || 3) * 1.0; // Standard height
                const triggerThickness = DOOR_TRIGGER_SIZE * 1.0; // Standard thickness
                const triggerWidth = (DOOR_WIDTH || 2.0) * 1.0; // Standard width

                if (dirLower === 'n' || dirLower === 's') {
                    triggerGeo = new THREE.BoxGeometry(triggerWidth, triggerHeight, triggerThickness);
                } else { // dir === 'e' || dir === 'w'
                    triggerGeo = new THREE.BoxGeometry(triggerThickness, triggerHeight, triggerWidth);
                }

                const triggerMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00, wireframe: true, visible: this.isEspEnabled });
                const doorTrigger = new THREE.Mesh(triggerGeo, triggerMaterial);

                // Position trigger AT the precise door center
                doorTrigger.position.copy(connectionPos);
                doorTrigger.position.y = triggerHeight / 2; // Center the trigger vertically at standard height

                // Apply a smaller offset INTO the room from the precise door center
                const offsetFactor = triggerThickness * 0.2;
                const offsetDir = new THREE.Vector3();
                if (dirLower === 'n') offsetDir.set(0, 0, 1);  // Move trigger South (into room)
                else if (dirLower === 's') offsetDir.set(0, 0, -1); // Move trigger North (into room)
                else if (dirLower === 'w') offsetDir.set(1, 0, 0);  // Move trigger East (into room)
                else if (dirLower === 'e') offsetDir.set(-1, 0, 0); // Move trigger West (into room)
                doorTrigger.position.addScaledVector(offsetDir, offsetFactor);

                doorTrigger.userData = { isDoorTrigger: true, direction: dirLower, targetRoomId: neighborId };
                doorTrigger.name = `doorTrigger_${dirLower.toUpperCase()}`;
                this.scene.add(doorTrigger);
                this.currentRoomObjects.push(doorTrigger);
                this.doorTriggers.push(doorTrigger);
            }
        }
        // --- End Door Trigger Creation ---

        // 2. Populate Room Content (Enemies, Items)
        this._populateRoom(roomData);

        // Mark all enemies in this room as active for updates
        this.activeEnemies.forEach(enemy => {
            if (enemy.userData) {
                enemy.userData.inCurrentRoom = true;
                enemy.visible = true;

                // Show any helpers if ESP is enabled
                if (this.isEspEnabled) {
                    if (enemy.userData.boundingBoxHelper) {
                        enemy.userData.boundingBoxHelper.visible = true;
                    }
                    if (enemy.userData.skeletonLines) {
                        enemy.userData.skeletonLines.visible = true;
                    }
                }
            }
        });

        // 3. Set Player Position using the boundingBox from generateRoomVisuals
        // <<< PASS doorCenterPoints >>>
        this._positionPlayerForEntry(entryDirection, boundingBox, doorCenterPoints);

        // 4. Update Player Controller collisions and bounds
        if (this.playerController) {
            this.playerController.updateCollisionObjects(this.collisionObjects);

            // --- USE boundingBox FROM generateRoomVisuals ---
            if (boundingBox && boundingBox.minX !== undefined) { // Check if valid boundingBox
                const playerSize = this.playerController.playerSize;
                const playerHalfWidth = playerSize.x / 2;
                const playerHalfDepth = playerSize.z / 2;

                this.playerController.floorBounds = {
                    minX: boundingBox.minX + playerHalfWidth,
                    maxX: boundingBox.maxX - playerHalfWidth,
                    minZ: boundingBox.minZ + playerHalfDepth,
                    maxZ: boundingBox.maxZ - playerHalfDepth
                };

                // Update the DungeonHandler's floorBounds to match the player controller's bounds
                this.floorBounds = new THREE.Box3(
                    new THREE.Vector3(this.playerController.floorBounds.minX, -10, this.playerController.floorBounds.minZ),
                    new THREE.Vector3(this.playerController.floorBounds.maxX, 10, this.playerController.floorBounds.maxZ)
                );

                console.log("   - Updated player controller bounds:", this.playerController.floorBounds);
                console.log("   - Updated dungeon handler bounds:", this.floorBounds);
            } else {
                 console.warn("   - Bounding box not available from generateRoomVisuals. Using default large bounds.");
                 this.playerController.floorBounds = { minX: -50, maxX: 50, minZ: -50, maxZ: 50 };

                 // Update the DungeonHandler's floorBounds to match the player controller's default bounds
                 this.floorBounds = new THREE.Box3(
                     new THREE.Vector3(-50, -10, -50),
                     new THREE.Vector3(50, 10, 50)
                 );
                 console.log("   - Updated dungeon handler bounds with defaults:", this.floorBounds);
            }
            // --- END BOUNDING BOX USAGE ---

            // CRITICAL FIX: Load pre-generated floor data for this room
            this._loadRoomFloorData(this.currentRoomId);

            // Make DungeonHandler available globally for floor validation
            window.dungeonHandler = this;

            this.playerController._updatePlayerBounds(); // Recalculate internal derived bounds
            this.playerController._resolveGroundCollision(); // Ensure player is on the ground
        }

        // 5. Update ESP helpers visibility
        this.playerController?._updateEspHelpersVisibility();
        this._updateEnemyEspHelpersVisibility();

        // 6. Update Minimap
        this._updateMinimap();

        // 7. Update music system for room entry
        if (this.audioManager && this.audioManager.musicConductor && this.isEnteringNewRoom) {
            // Count active enemies in the new room
            const activeEnemyCount = this.activeEnemies.filter(enemy => enemy.userData.inCurrentRoom).length;

            // Get player health
            const currentHealth = this.playerController ? this.playerController.currentHealth : 1;
            const maxHealth = this.playerController ? this.playerController.maxHealth : 1;

            // Update music system with room entry flag
            this.audioManager.updateCombatState(
                activeEnemyCount,
                currentHealth,
                maxHealth,
                false,  // enemyKilled
                true    // enteredRoom
            );

            // Reset the flag
            this.isEnteringNewRoom = false;
        }

        // Restore debris and soul orbs for this room
        this._restoreRoomDebris(roomId);
        this._restoreRoomSoulOrbs(roomId);

        // Trigger predictive pre-loading for adjacent rooms (if not already triggered by transition)
        if (!this.sceneManager.isFading) {
            this._queueAdjacentRooms(roomId);
            this._processPreLoadQueue();
        }

        console.log(`DungeonHandler: Room ${roomId} (${roomData.type}) loaded successfully.`);
    }

    cleanupRoom() {
        console.log(`DungeonHandler: Cleaning up previous room (ID: ${this.currentRoomId})...`);

        // Save debris and soul orb state before cleanup
        this._saveCurrentRoomDebrisState();
        this._saveCurrentRoomSoulOrbState();

        // ENHANCED: More aggressive projectile cleanup
        // Remove ALL projectiles from the scene completely
        console.log(`DungeonHandler: Removing ${this.activeProjectiles.length} active projectiles`);

        for (let i = this.activeProjectiles.length - 1; i >= 0; i--) {
            const projectileMesh = this.activeProjectiles[i];
            const projectile = projectileMesh.userData;

            // Call the projectile's destroy method to clean up trails
            try {
                if (projectile && typeof projectile.destroy === 'function') {
                    projectile.destroy();
                }
            } catch (error) {
                console.error('[DungeonHandler] Error destroying projectile:', error);
                // Continue with cleanup despite the error
            }

            // Remove from scene
            this.scene.remove(projectileMesh);

            // Dispose of geometries and materials
            if (projectileMesh.geometry) projectileMesh.geometry.dispose();
            if (projectileMesh.material) {
                if (Array.isArray(projectileMesh.material)) {
                    projectileMesh.material.forEach(m => m.dispose());
                } else {
                    projectileMesh.material.dispose();
                }
            }

            // Check for trail mesh and dispose it thoroughly
            if (projectile.userData && projectile.userData.trailMesh) {
                // Remove from scene
                this.scene.remove(projectile.userData.trailMesh);

                // Dispose of geometry
                if (projectile.userData.trailMesh.geometry) {
                    projectile.userData.trailMesh.geometry.dispose();
                }

                // Dispose of materials (handle both single and array materials)
                if (projectile.userData.trailMesh.material) {
                    if (Array.isArray(projectile.trailMesh.material)) {
                        projectile.trailMesh.material.forEach(material => {
                            if (material.map) material.map.dispose();
                            material.dispose();
                        });
                    } else {
                        if (projectile.userData.trailMesh.material.map) {
                            projectile.trailMesh.material.map.dispose();
                        }
                        projectile.userData.trailMesh.material.dispose();
                    }
                }

                // Clean up any child objects (like particles in complex trails)
                if (projectile.userData.trailMesh.children && projectile.userData.trailMesh.children.length > 0) {
                    for (let i = projectile.userData.trailMesh.children.length - 1; i >= 0; i--) {
                        const child = projectile.userData.trailMesh.children[i];

                        // Dispose of child geometries and materials
                        if (child.geometry) child.geometry.dispose();
                        if (child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach(material => {
                                    if (material.map) material.map.dispose();
                                    material.dispose();
                                });
                            } else {
                                if (child.material.map) child.material.map.dispose();
                                child.material.dispose();
                            }
                        }

                        // Remove child from parent
                        projectile.userData.trailMesh.remove(child);
                    }
                }

                // Clear reference
                projectile.userData.trailMesh = null;
            }
        }

        // Clear the active projectiles array
        this.activeProjectiles = []; // Clear active list

        // --- Remove enemies from the scene when changing rooms ---
        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {
            const enemy = this.activeEnemies[i];

            // Remove enemy from the scene
            this.scene.remove(enemy);

            // Remove any helpers
            if (enemy.userData && enemy.userData.boundingBoxHelper) {
                this.scene.remove(enemy.userData.boundingBoxHelper);
            }
            if (enemy.userData && enemy.userData.skeletonLines) {
                this.scene.remove(enemy.userData.skeletonLines);
            }

            // Remove from active enemies array
            this.activeEnemies.splice(i, 1);
        }

        // Clear enemy state timers
        this.enemyStateTimers.clear();

        // --- Handle Room Objects (Don't Dispose Pre-Generated Visuals) ---
        this.currentRoomObjects.forEach(obj => {
            // Skip the ambient light to preserve health-based lighting
            if (obj === this.ambientLight) return;

            // Only remove objects from the scene, don't dispose of them if they're
            // part of pre-generated room visuals
            if (obj.userData && obj.userData.isDoorTrigger) {
                // Fully dispose of door triggers as they're recreated each time
                obj.traverse((child) => {
                    if (child.isMesh) {
                        if (child.geometry) child.geometry.dispose();
                        if (child.material) {
                            if (Array.isArray(child.material)) child.material.forEach(m => m.dispose());
                            else if (child.material.dispose) child.material.dispose();
                        }
                    }
                });
            }

            // Simply remove all objects from the scene
            this.scene.remove(obj);
        });

        // Filter out the ambient light from currentRoomObjects instead of clearing
        this.currentRoomObjects = this.currentRoomObjects.filter(obj => obj === this.ambientLight);
        this.collisionObjects = []; // Clear collisions
        this.doorTriggers = []; // Clear door triggers

        // Hide items when changing rooms (don't destroy them)
        if (this.activeItems && this.activeItems.length > 0) {
            this.activeItems.forEach(item => {
                if (item.mesh) item.mesh.visible = false;
                if (item.light) item.light.visible = false;
            });
        }

        // Clear soul orbs when changing rooms
        simpleSoulDropManager.clearOrbs(this.scene);

        console.log("DungeonHandler: Previous room cleaned.");

        // --- Dispose Object Pools ---
        const disposePool = (pool) => {
             pool.forEach(obj => {
                 if (obj.geometry) obj.geometry.dispose();
                 if (obj.material) {
                     if (Array.isArray(obj.material)) obj.material.forEach(m => m.dispose());
                     else if (obj.material.dispose) obj.material.dispose();
                 }
            });
            pool.length = 0;
        };

        // Empty enemy projectile pool only
        disposePool(this.enemyProjectilePool);
        // Keep playerProjectilePool to avoid re-creating player projectiles
    }

    /**
     * Cleanup method to remove event listeners and dispose resources
     */
    dispose() {
        console.log("DungeonHandler: Disposing...");

        // Remove event listeners
        if (this._boundObjectDestroyedHandler) {
            window.removeEventListener('objectDestroyed', this._boundObjectDestroyedHandler);
            this._boundObjectDestroyedHandler = null;
        }

        // Clean up room
        this.cleanupRoom();

        console.log("DungeonHandler: Disposed successfully.");
    }

    _populateRoom(roomData) {
        console.log(` -> Populating room ${roomData.id} (${roomData.type})`);

        // --- Enemy Spawning ---
        // Check state: If enemies are NOT cleared
        if (!roomData.state.enemiesCleared) {
            // Get room type and biome for enemy spawning context
            const roomType = roomData.type.toLowerCase() || 'normal';
            const biome = roomData.areaId || 'catacombs';

            // Set the current floor level in the enemy spawn manager
            const floorLevel = roomData.floorLevel || 1;
            enemySpawnManager.setFloorLevel(floorLevel);

            // Generate a seed based on room ID for deterministic spawning
            const spawnSeed = roomData.id * 1000 + 53;

            // If there are predefined enemies, use those
            if (roomData.state.initialEnemies && roomData.state.initialEnemies.length > 0) {
                console.log(`   - Spawning ${roomData.state.initialEnemies.length} predefined enemies:`, roomData.state.initialEnemies);
                // Iterate through the defined list
                for (const enemyType of roomData.state.initialEnemies) {
                    // Specify enemy type to spawn
                    this._spawnEnemy(enemyType);
                }
            } else {
                // Otherwise, use the enemy spawn system to generate a balanced group
                console.log(`   - Generating enemy group for ${biome} (${roomType}, floor ${floorLevel})`);

                // Choose a balanced enemy group for this room
                const enemyGroup = enemySpawnManager.chooseEnemyGroup({
                    biome,
                    roomType,
                    floorLevel,
                    seed: spawnSeed
                });

                if (enemyGroup && enemyGroup.length > 0) {
                    console.log(`   - Spawning ${enemyGroup.length} enemies from generated group:`, enemyGroup);

                    // Spawn each enemy in the group
                    for (const enemyType of enemyGroup) {
                        this._spawnEnemy(enemyType);
                    }

                    // Store the generated enemies in the room state for persistence
                    roomData.state.initialEnemies = [...enemyGroup];
                } else {
                    console.log("   - No enemies generated for this room.");
                }
            }
        } else {
            console.log("   - Enemies already cleared.");
        }
        // --- End Enemy Spawning ---

        // --- Item Spawning ---
        // Check if this is a special item room
        if (roomData.type === 'Item') {
            // Handle special item room logic
            if (roomData.state.itemTaken) {
                console.log("   - Item already taken.");
            } else {
                // Spawn a guaranteed item in the center of the room
                const roomCenter = new THREE.Vector3(0, 0.5, 0);

                // Get room type and biome for item context
                const roomType = 'item';
                const biome = roomData.areaId || 'catacombs';

                // Generate a seed based on room ID for deterministic item selection
                const itemSeed = roomData.id * 1000 + 17;

                // Get a random item appropriate for an item room
                const itemType = itemDropManager.getRandomDrop('normal', roomType, biome, itemSeed);

                if (itemType) {
                    console.log(`   - Spawning guaranteed item: ${itemType}`);
                    const item = itemDropManager.spawnVoxelItem(this.scene, itemType, roomCenter);

                    // Add the item to the active items list
                    if (item) {
                        if (!this.activeItems) this.activeItems = [];
                        this.activeItems.push(item);
                    }
                }
            }
        }
        // --- End Item Spawning ---
    }

    // --- REWRITTEN Player Positioning Logic ---
    _positionPlayerForEntry(entryDirection, roomBounds, doorCenterPoints) {
        if (!this.player) return;

        const playerY = Math.max(0.5, this.player.position.y); // Maintain current Y or minimum
        let spawnPos = new THREE.Vector3(0, playerY, 0); // Default spawn point (center)
        let lookAtPos = new THREE.Vector3(0, playerY, 0); // Look towards center by default

        const roomData = this.floorLayout?.get(this.currentRoomId);
        const shapeKey = roomData?.shapeKey || 'SQUARE_1X1'; // Get shape for logging/potential fallback

        // --- Determine Room Center (Used for LookAt and Fallback) ---
        const minX = roomBounds?.minX ?? -ROOM_WORLD_SIZE / 2;
        const maxX = roomBounds?.maxX ?? ROOM_WORLD_SIZE / 2;
        const minZ = roomBounds?.minZ ?? -ROOM_WORLD_SIZE / 2;
        const maxZ = roomBounds?.maxZ ?? ROOM_WORLD_SIZE / 2;
        const roomCenterX = (minX + maxX) / 2;
        const roomCenterZ = (minZ + maxZ) / 2;
        lookAtPos.set(roomCenterX, playerY, roomCenterZ); // Default lookAt is room center

        // --- Use Precise Door Position if available ---
        const entryDirLower = entryDirection?.toLowerCase();
        const doorPos = doorCenterPoints && entryDirLower ? doorCenterPoints[entryDirLower] : null;

        if (doorPos) {
            console.log(`[PositionPlayer] Using precise door position for entry '${entryDirLower}' at`, doorPos);
            spawnPos.copy(doorPos); // Start at the door's center point
            spawnPos.y = playerY; // Ensure correct Y level

            // Offset INTO the room based on entry direction
            const offsetVector = new THREE.Vector3();
            switch (entryDirLower) {
                case 'n': offsetVector.set(0, 0, 1); break;  // Came from North, move South
                case 's': offsetVector.set(0, 0, -1); break; // Came from South, move North
                case 'w': offsetVector.set(1, 0, 0); break;  // Came from West, move East
                case 'e': offsetVector.set(-1, 0, 0); break; // Came from East, move West
                default: console.warn(`[PositionPlayer] Unknown entry direction: ${entryDirLower}`); break; // Should not happen
            }
            spawnPos.addScaledVector(offsetVector, PLAYER_SPAWN_OFFSET);

            // Look towards the room center from the spawn position
            lookAtPos.set(roomCenterX, playerY, roomCenterZ);

        } else if (entryDirLower) {
             // --- Fallback to Bounding Box Logic (Original Method, slightly adapted) ---
            console.warn(`[PositionPlayer] Precise door position for '${entryDirLower}' not found. Using bounding box fallback for shape ${shapeKey}.`);
            switch (entryDirLower) {
                case 'n': // Came from North, spawn near North wall (min Z)
                    spawnPos.z = minZ + PLAYER_SPAWN_OFFSET;
                    spawnPos.x = roomCenterX;
                    lookAtPos.z = maxZ; // Look South
                    lookAtPos.x = roomCenterX;
                    break;
                case 's': // Came from South, spawn near South wall (max Z)
                    spawnPos.z = maxZ - PLAYER_SPAWN_OFFSET;
                    spawnPos.x = roomCenterX;
                    lookAtPos.z = minZ; // Look North
                    lookAtPos.x = roomCenterX;
                    break;
                case 'w': // Came from West, spawn near West wall (min X)
                    spawnPos.x = minX + PLAYER_SPAWN_OFFSET;
                    spawnPos.z = roomCenterZ;
                    lookAtPos.x = maxX; // Look East
                    lookAtPos.z = roomCenterZ;
                    break;
                case 'e': // Came from East, spawn near East wall (max X)
                    spawnPos.x = maxX - PLAYER_SPAWN_OFFSET;
                    spawnPos.z = roomCenterZ;
                    lookAtPos.x = minX; // Look West
                    lookAtPos.z = roomCenterZ;
                    break;
                default: // Start of dungeon or error
                    console.log("[PositionPlayer] Positioning player at center (Start or unknown entry/fallback)");
                    spawnPos.x = roomCenterX;
                    spawnPos.z = roomCenterZ;
                    lookAtPos.z = minZ; // Look North by default
                    lookAtPos.x = roomCenterX;
                    break;
            }
            spawnPos.y = playerY; // Ensure correct Y level in fallback too

        } else {
            // --- Initial Spawn (No entry direction) ---
            console.log("[PositionPlayer] Positioning player at center (Initial dungeon entry)");
            spawnPos.set(roomCenterX, playerY, roomCenterZ);
            lookAtPos.set(roomCenterX, playerY, minZ); // Look North initially
        }

        // --- Collision Safety Check (Keep for now, but should be less needed) ---
        const playerSize = this.playerController?.playerSize || { x: 0.8, y: 1.6, z: 0.8 };
        const tempPlayerBox = new THREE.Box3(
            new THREE.Vector3(spawnPos.x - playerSize.x/2, 0, spawnPos.z - playerSize.z/2),
            new THREE.Vector3(spawnPos.x + playerSize.x/2, playerSize.y, spawnPos.z + playerSize.z/2)
        );

        let hasCollision = false;
        for (const collObj of this.collisionObjects) {
             // Check if collObj and its geometry/children exist before creating Box3
            if (collObj && (collObj.geometry || (collObj.children && collObj.children.length > 0))) {
                try {
                    const collBox = new THREE.Box3().setFromObject(collObj);
                    if (tempPlayerBox.intersectsBox(collBox)) {
                        hasCollision = true;
                        console.warn("Player spawn position intersects collision object. Applying safe offset.");
                        // Move slightly towards the room center
                        const safeOffset = new THREE.Vector3(roomCenterX - spawnPos.x, 0, roomCenterZ - spawnPos.z).normalize().multiplyScalar(0.5);
                        spawnPos.add(safeOffset);
                        lookAtPos.set(roomCenterX, playerY, roomCenterZ); // Look at center after adjustment
                        break; // Only apply one offset
                    }
                } catch (error) {
                     console.error(`[PositionPlayer] Error creating Box3 for collision object: ${collObj.name || collObj.uuid}`, error);
                     // Continue checking other objects
                }
            }
        }
        // --- End Collision Safety Check ---

        // --- Final Application ---
        this.player.position.copy(spawnPos);
        this.player.lookAt(lookAtPos);

        if(this.playerController) this.playerController._updatePlayerBounds(); // Update bounds based on new position
        console.log(`[PositionPlayer] Final Player Position: ${spawnPos.x.toFixed(2)}, ${spawnPos.y.toFixed(2)}, ${spawnPos.z.toFixed(2)}`);
    }
    // --- END REWRITTEN FUNCTION ---





    /**
     * Check if a position has valid floor beneath it
     * @param {THREE.Vector3} position - Position to check
     * @param {number} tolerance - Distance tolerance for floor detection
     * @param {string} objectType - Type of object being placed (for wall validation)
     * @returns {boolean} True if valid floor exists
     */
    _hasValidFloor(position, tolerance = 1.0, objectType = 'default') {
        // CRITICAL FIX: Use basic bounds checking during pre-loading to prevent objects spawning outside walls
        if (this.isPreLoading) {
            return this._isPositionWithinRoomBounds(position, objectType);
        }

        // CRITICAL FIX: Use current room's floor data
        if (!this.scene || !this.currentRoomFloorData || !this.currentRoomFloorData.floorMeshes || this.currentRoomFloorData.floorMeshes.length === 0) {
            console.warn("[FloorDetection] Scene or current room floor data not available for validation");
            return false;
        }

        const raycaster = new THREE.Raycaster();
        raycaster.set(
            new THREE.Vector3(position.x, position.y + 5, position.z),
            new THREE.Vector3(0, -1, 0)
        );

        const intersects = raycaster.intersectObjects(this.currentRoomFloorData.floorMeshes, false);

        if (intersects.length > 0) {
            const floorY = intersects[0].point.y;
            return Math.abs(position.y - floorY) <= tolerance;
        }

        return false;
    }

    /**
     * Get a random valid spawn position from the floor detection system
     * @param {number} minDistanceFromWalls - Minimum distance from walls/edges
     * @returns {THREE.Vector3|null} Valid spawn position or null if none found
     */
    _getRandomValidSpawnPosition(minDistanceFromWalls = 1.0) {
        // ENHANCED DEBUG: Log detailed floor detection state
        console.log("[FloorDetection] Attempting to get random valid spawn position...");
        console.log("[FloorDetection] Debug state:", {
            hasScene: !!this.scene,
            hasCurrentRoomFloorData: !!this.currentRoomFloorData,
            hasValidSpawnAreas: !!(this.currentRoomFloorData?.validSpawnAreas),
            validSpawnAreasCount: this.currentRoomFloorData?.validSpawnAreas?.length || 0,
            currentRoomId: this.currentRoomId
        });

        // CRITICAL FIX: Use current room's floor data
        if (!this.scene || !this.currentRoomFloorData || !this.currentRoomFloorData.validSpawnAreas || this.currentRoomFloorData.validSpawnAreas.length === 0) {
            console.warn("[FloorDetection] Scene or current room floor data not available");
            return null;
        }

        const validSpawnAreas = this.currentRoomFloorData.validSpawnAreas;
        const floorBounds = this.currentRoomFloorData.floorBounds;

        // Filter spawn areas that are far enough from edges
        const safeSpawnAreas = validSpawnAreas.filter(area => {
            return area.x >= floorBounds.min.x + minDistanceFromWalls &&
                   area.x <= floorBounds.max.x - minDistanceFromWalls &&
                   area.z >= floorBounds.min.z + minDistanceFromWalls &&
                   area.z <= floorBounds.max.z - minDistanceFromWalls;
        });

        if (safeSpawnAreas.length === 0) {
            console.warn("[FloorDetection] No safe spawn areas available with required distance from walls");
            // Fallback to any valid area
            const randomArea = validSpawnAreas[Math.floor(Math.random() * validSpawnAreas.length)];
            return new THREE.Vector3(randomArea.x, randomArea.y, randomArea.z);
        }

        // Return random safe spawn area
        const randomArea = safeSpawnAreas[Math.floor(Math.random() * safeSpawnAreas.length)];
        console.log(`[FloorDetection] Successfully selected spawn position: (${randomArea.x.toFixed(2)}, ${randomArea.y.toFixed(2)}, ${randomArea.z.toFixed(2)}) from ${safeSpawnAreas.length} safe areas`);
        return new THREE.Vector3(randomArea.x, randomArea.y, randomArea.z);
    }

    /**
     * Find the nearest valid floor position to a given position
     * @param {THREE.Vector3} position - Position to find nearest valid floor for
     * @param {number} maxSearchDistance - Maximum distance to search
     * @returns {THREE.Vector3|null} Nearest valid position or null if none found
     */
    _findNearestValidFloorPosition(position, maxSearchDistance = 5.0) {
        // CRITICAL FIX: Use basic bounds-based position correction during pre-loading
        if (this.isPreLoading) {
            return this._findNearestBoundsPosition(position, maxSearchDistance);
        }

        // CRITICAL FIX: Use current room's floor data
        if (!this.scene || !this.currentRoomFloorData || !this.currentRoomFloorData.validSpawnAreas || this.currentRoomFloorData.validSpawnAreas.length === 0) {
            console.warn("[FloorDetection] Scene or current room floor data not available for nearest search");
            return null;
        }

        let nearestArea = null;
        let nearestDistance = Infinity;

        // Find the closest valid spawn area
        for (const area of this.currentRoomFloorData.validSpawnAreas) {
            const distance = Math.sqrt(
                Math.pow(position.x - area.x, 2) +
                Math.pow(position.z - area.z, 2)
            );

            if (distance < nearestDistance && distance <= maxSearchDistance) {
                nearestDistance = distance;
                nearestArea = area;
            }
        }

        if (nearestArea) {
            return new THREE.Vector3(nearestArea.x, nearestArea.y, nearestArea.z);
        }

        return null;
    }

    /**
     * Check if a position is within basic room bounds (used during pre-generation)
     * @param {THREE.Vector3} position - Position to check
     * @param {string} objectType - Type of object being placed (e.g., 'torch', 'vine')
     * @returns {boolean} True if position is within room bounds
     * @private
     */
    _isPositionWithinRoomBounds(position, objectType = 'default') {
        // For wall objects like torches, check if they're on actual stone brick walls
        if (objectType === 'torch' || objectType === 'vine') {
            return this._isPositionOnStoneBrickWall(position);
        }

        // For floor objects, check if they're on actual cave floor tiles
        if (objectType !== 'default') {
            return this._isPositionOnCaveFloor(position);
        }

        // Fallback to basic bounds checking for unknown object types
        const bounds = this.floorBounds || {
            min: { x: -25, z: -25 },
            max: { x: 25, z: 25 }
        };

        // Add buffer to stay away from walls
        const buffer = 2.0;

        return position.x >= bounds.min.x + buffer &&
               position.x <= bounds.max.x - buffer &&
               position.z >= bounds.min.z + buffer &&
               position.z <= bounds.max.z - buffer;
    }

    /**
     * Find nearest position within room bounds (used during pre-generation)
     * @param {THREE.Vector3} position - Original position
     * @param {number} maxSearchDistance - Maximum distance to search
     * @returns {THREE.Vector3|null} Corrected position or null if none found
     * @private
     */
    _findNearestBoundsPosition(position, maxSearchDistance = 5.0) {
        // Use floorBounds if available, otherwise use default room size
        const bounds = this.floorBounds || {
            min: { x: -25, z: -25 },
            max: { x: 25, z: 25 }
        };

        const buffer = 2.0;
        const minX = bounds.min.x + buffer;
        const maxX = bounds.max.x - buffer;
        const minZ = bounds.min.z + buffer;
        const maxZ = bounds.max.z - buffer;

        // If position is already within bounds, return it
        if (this._isPositionWithinRoomBounds(position)) {
            return position.clone();
        }

        // Clamp position to bounds
        const correctedPosition = position.clone();
        correctedPosition.x = Math.max(minX, Math.min(maxX, position.x));
        correctedPosition.z = Math.max(minZ, Math.min(maxZ, position.z));

        // Check if the correction is within the search distance
        const distance = position.distanceTo(correctedPosition);
        if (distance <= maxSearchDistance) {
            return correctedPosition;
        }

        return null; // Too far to correct
    }

    /**
     * Check if a position is on a stone brick wall (used for torch/vine placement)
     * @param {THREE.Vector3} position - Position to check
     * @returns {boolean} True if position is on a visible stone brick wall
     * @private
     */
    _isPositionOnStoneBrickWall(position) {
        // During pre-generation, we need to check against the current room's wall data
        if (!this._currentRoomWallSegments) {
            return false; // No wall data available
        }

        const tolerance = 1.0; // Distance tolerance for wall detection

        // Check each wall segment to see if the position is near a visible stone brick wall
        for (const wallSegment of this._currentRoomWallSegments) {
            // Skip invisible wall segments (like south walls that are hidden)
            if (!wallSegment.isVisible) {
                continue;
            }

            // Calculate distance from position to wall segment
            const wallPos = wallSegment.position;
            const distance = position.distanceTo(wallPos);

            if (distance <= tolerance) {
                // Check if this wall segment has stone brick material
                // Wall segments with stone brick walls should be on outer boundaries
                if (wallSegment.isOuterBoundary) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if a position is on actual cave floor tiles (used for floor object placement)
     * @param {THREE.Vector3} position - Position to check
     * @returns {boolean} True if position is on a visible cave floor tile
     * @private
     */
    _isPositionOnCaveFloor(position) {
        // During pre-generation, we need to check against the current room's floor meshes
        if (!this._currentRoomFloorMeshes || this._currentRoomFloorMeshes.length === 0) {
            // Fallback to bounds checking if no floor mesh data available
            console.warn("[FloorDetection] No floor mesh data available, using bounds checking");
            return this._isPositionWithinBasicBounds(position);
        }

        const tolerance = 1.0; // Distance tolerance for floor detection
        const raycastHeight = 5.0; // Height to cast ray from

        // Create a simple raycaster to check for floor beneath the position
        const raycaster = new THREE.Raycaster();
        raycaster.set(
            new THREE.Vector3(position.x, raycastHeight, position.z),
            new THREE.Vector3(0, -1, 0)
        );

        // Check intersection with floor meshes
        const intersects = raycaster.intersectObjects(this._currentRoomFloorMeshes, false);

        if (intersects.length > 0) {
            // Found floor beneath this position
            const floorY = intersects[0].point.y;

            // Check if the floor is at a reasonable height (not too far below)
            if (Math.abs(position.y - floorY) <= tolerance) {
                return true;
            }
        }

        return false;
    }

    /**
     * Basic bounds checking without floor validation
     * @param {THREE.Vector3} position - Position to check
     * @returns {boolean} True if position is within basic room bounds
     * @private
     */
    _isPositionWithinBasicBounds(position) {
        const bounds = this.floorBounds || {
            min: { x: -25, z: -25 },
            max: { x: 25, z: 25 }
        };

        const buffer = 2.0;

        return position.x >= bounds.min.x + buffer &&
               position.x <= bounds.max.x - buffer &&
               position.z >= bounds.min.z + buffer &&
               position.z <= bounds.max.z - buffer;
    }

    /**
     * Set temporary room bounds for bounds-based validation during pre-generation
     * @param {Object} roomData - Room data containing position and size
     * @private
     */
    _setTemporaryRoomBounds(roomData) {
        // Calculate room bounds based on room position and size
        const roomSize = ROOM_WORLD_SIZE || 50; // Default room size
        const halfSize = roomSize / 2;

        // Get room position (default to 0,0 if not available)
        const roomX = (roomData.x || 0) * roomSize;
        const roomZ = (roomData.y || 0) * roomSize; // Note: roomData.y is the Z coordinate in world space

        // Set temporary bounds for this room
        this.floorBounds = {
            min: {
                x: roomX - halfSize,
                z: roomZ - halfSize
            },
            max: {
                x: roomX + halfSize,
                z: roomZ + halfSize
            }
        };

        console.log(`[FloorDetection] Set temporary bounds for room at (${roomData.x}, ${roomData.y}):`, this.floorBounds);
    }

    /**
     * Clear temporary room bounds after pre-generation
     * @private
     */
    _clearTemporaryRoomBounds() {
        this.floorBounds = null;
    }

    /**
     * Set current room wall segments for torch placement validation
     * @param {Array} wallSegments - Array of wall segment data
     * @private
     */
    _setCurrentRoomWallSegments(wallSegments) {
        this._currentRoomWallSegments = wallSegments;
        console.log(`[FloorDetection] Stored ${wallSegments.length} wall segments for torch validation`);
    }

    /**
     * Clear current room wall segments
     * @private
     */
    _clearCurrentRoomWallSegments() {
        this._currentRoomWallSegments = null;
    }

    /**
     * Set current room floor meshes for floor object placement validation
     * @param {Array} floorMeshes - Array of floor mesh objects
     * @private
     */
    _setCurrentRoomFloorMeshes(floorMeshes) {
        this._currentRoomFloorMeshes = floorMeshes;
        console.log(`[FloorDetection] Stored ${floorMeshes.length} floor meshes for floor object validation`);
    }

    /**
     * Clear current room floor meshes
     * @private
     */
    _clearCurrentRoomFloorMeshes() {
        this._currentRoomFloorMeshes = null;
    }

    /**
     * Load pre-generated floor data for a specific room
     * @param {number} roomId - Room ID to load floor data for
     * @private
     */
    _loadRoomFloorData(roomId) {
        if (this.globalFloorData.has(roomId)) {
            this.currentRoomFloorData = this.globalFloorData.get(roomId);
            console.log(`[FloorDetection] Loaded pre-generated floor data for room ${roomId}: ${this.currentRoomFloorData.validSpawnAreas.length} spawn points`);
        } else {
            console.warn(`[FloorDetection] No pre-generated floor data found for room ${roomId}`);
            this.currentRoomFloorData = null;
        }
    }

    /**
     * Generate floor data for a room during pre-generation
     * @param {number} roomId - Room ID
     * @param {Array} collisionMeshes - Collision meshes for the room
     * @param {Object|THREE.Box3} boundingBox - Bounding box for the room
     * @private
     */
    _generateRoomFloorData(roomId, collisionMeshes, boundingBox) {
        console.log(`[FloorDetection] Generating floor data for room ${roomId}...`);

        const floorMeshes = [];
        const caveFloorColors = ['5d4a3a', '6a5546', '4a3a2a'];

        // Find all floor meshes
        for (const mesh of collisionMeshes) {
            if (mesh.userData && mesh.userData.isFloor) {
                floorMeshes.push(mesh);
            }
        }

        console.log(`[FloorDetection] Found ${floorMeshes.length} floor meshes for room ${roomId}`);

        // CRITICAL FIX: Convert boundingBox to proper THREE.Box3 format
        let floorBounds;
        if (boundingBox && typeof boundingBox === 'object') {
            if (boundingBox.min && boundingBox.max) {
                // Already a THREE.Box3
                floorBounds = boundingBox;
            } else if (boundingBox.minX !== undefined && boundingBox.maxX !== undefined) {
                // Plain object format from room generator
                floorBounds = new THREE.Box3(
                    new THREE.Vector3(boundingBox.minX, boundingBox.minY || -10, boundingBox.minZ),
                    new THREE.Vector3(boundingBox.maxX, boundingBox.maxY || 10, boundingBox.maxZ)
                );
            } else {
                console.warn(`[FloorDetection] Invalid bounding box format for room ${roomId}:`, boundingBox);
                floorBounds = new THREE.Box3(
                    new THREE.Vector3(-25, -10, -25),
                    new THREE.Vector3(25, 10, 25)
                );
            }
        } else {
            console.warn(`[FloorDetection] No bounding box provided for room ${roomId}, using default bounds`);
            floorBounds = new THREE.Box3(
                new THREE.Vector3(-25, -10, -25),
                new THREE.Vector3(25, 10, 25)
            );
        }

        // Generate valid spawn areas using optimized grid with room shape validation
        const roomShape = this._extractRoomShapeFromId(roomId);
        const validSpawnAreas = this._generateOptimizedSpawnGrid(floorMeshes, floorBounds, roomShape);

        // Store the floor data
        const floorData = {
            floorMeshes,
            validSpawnAreas,
            floorBounds: floorBounds.clone()
        };

        this.globalFloorData.set(roomId, floorData);
        console.log(`[FloorDetection] Generated floor data for room ${roomId}: ${validSpawnAreas.length} spawn points`);

        return floorData;
    }

    /**
     * Generate optimized spawn grid for a room with room shape validation
     * @param {Array} floorMeshes - Floor meshes to check against
     * @param {THREE.Box3} floorBounds - Bounds to generate grid within
     * @param {string} roomShape - Room shape key for validation (optional)
     * @returns {Array} Array of valid spawn positions
     * @private
     */
    _generateOptimizedSpawnGrid(floorMeshes, floorBounds, roomShape = null) {
        // CRITICAL FIX: Add comprehensive error checking
        if (!floorBounds) {
            console.warn("[FloorDetection] No floor bounds provided for grid generation");
            return [];
        }

        if (!floorBounds.min || !floorBounds.max) {
            console.warn("[FloorDetection] Invalid floor bounds structure:", floorBounds);
            return [];
        }

        if (!floorMeshes || floorMeshes.length === 0) {
            console.warn("[FloorDetection] No floor meshes provided for grid generation");
            return [];
        }

        const gridSpacing = 2.0; // Optimized spacing for performance
        const raycastHeight = 10;
        const raycaster = new THREE.Raycaster();
        const validSpawnAreas = [];

        try {
            // SIMPLIFIED: Use floor meshes as the authoritative source for valid spawn areas
            // Generate grid points within floor bounds
            for (let x = floorBounds.min.x; x <= floorBounds.max.x; x += gridSpacing) {
                for (let z = floorBounds.min.z; z <= floorBounds.max.z; z += gridSpacing) {
                    raycaster.set(
                        new THREE.Vector3(x, raycastHeight, z),
                        new THREE.Vector3(0, -1, 0)
                    );

                    const intersects = raycaster.intersectObjects(floorMeshes, false);

                    // SIMPLE RULE: If there's a floor mesh here, it's a valid spawn area
                    if (intersects.length > 0) {
                        const floorY = intersects[0].point.y;

                        // Add small buffer from edges to prevent spawning too close to walls
                        const edgeBuffer = 1.5;
                        const isNearEdge = (
                            x <= floorBounds.min.x + edgeBuffer ||
                            x >= floorBounds.max.x - edgeBuffer ||
                            z <= floorBounds.min.z + edgeBuffer ||
                            z >= floorBounds.max.z - edgeBuffer
                        );

                        // Only add if not too close to edges
                        if (!isNearEdge) {
                            validSpawnAreas.push({
                                x: x,
                                y: floorY,
                                z: z
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.error("[FloorDetection] Error during grid generation:", error);
            return [];
        }

        console.log(`[FloorDetection] Generated ${validSpawnAreas.length} valid spawn areas for room shape: ${roomShape || 'unknown'}`);
        return validSpawnAreas;
    }

    /**
     * Process any pending floor detection system initialization
     * Called after scene is fully initialized
     * @private
     */
    _processPendingFloorDetection() {
        // This method is now deprecated in favor of pre-generation
        // Floor data is generated during room pre-generation instead
        console.log("[FloorDetection] Using pre-generated floor data system");
    }

    /**
     * Extract room shape from room ID by looking up room data
     * @param {number} roomId - Room ID to extract shape for
     * @returns {string|null} Room shape key or null if not found
     * @private
     */
    _extractRoomShapeFromId(roomId) {
        try {
            // Try to get room data from floor layout
            if (this.floorLayout && this.floorLayout.rooms) {
                const roomData = this.floorLayout.rooms.find(room => room.id === roomId);
                if (roomData && roomData.shapeKey) {
                    return roomData.shapeKey;
                }
            }

            // Try to get from pre-generated rooms
            if (this.preGeneratedRooms && this.preGeneratedRooms.has(roomId)) {
                const preGenData = this.preGeneratedRooms.get(roomId);
                if (preGenData.roomData && preGenData.roomData.shapeKey) {
                    return preGenData.roomData.shapeKey;
                }
            }

            console.warn(`[FloorDetection] Could not extract room shape for room ${roomId}`);
            return null;
        } catch (error) {
            console.error(`[FloorDetection] Error extracting room shape for room ${roomId}:`, error);
            return null;
        }
    }

    /**
     * Check if a position is within the valid area of a room shape
     * @param {number} x - X coordinate
     * @param {number} z - Z coordinate
     * @param {string} roomShape - Room shape key
     * @returns {boolean} True if position is valid for the room shape
     * @private
     */
    _isPositionInRoomShape(x, z, roomShape) {
        if (!roomShape) {
            return true; // If no shape specified, allow all positions
        }

        // Room constants (matching roomGenerator.js)
        const R = 14; // ROOM_WORLD_SIZE
        const H = 7;  // Half of ROOM_WORLD_SIZE

        switch (roomShape) {
            case 'SQUARE_1X1':
            case 'RECTANGULAR':
            case 'RECT_2X1':
            case 'RECT_1X2':
            case 'SQUARE_2X2':
            case 'RECT_3X1':
            case 'RECT_1X3':
            case 'RECT_3X2':
                // Simple rectangular shapes - use bounding box validation
                return true; // These are already handled by bounding box

            case 'L_SHAPE':
                // L-shape: bottom-left and top-right squares, missing top-left
                // Valid areas: bottom-left (-R to 0, -R to R) and top-right (0 to R, -R to 0)
                const inBottomLeft = (x >= -R && x <= 0 && z >= -R && z <= R);
                const inTopRight = (x >= 0 && x <= R && z >= -R && z <= 0);
                return inBottomLeft || inTopRight;

            case 'T_SHAPE':
                // T-shape: horizontal bar at top (3x1) and vertical stem at bottom (1x1)
                // Top bar: (-R-H to R+H, -R to 0)
                // Bottom stem: (-H to H, 0 to R)
                const inTopBar = (x >= -(R + H) && x <= (R + H) && z >= -R && z <= 0);
                const inBottomStem = (x >= -H && x <= H && z >= 0 && z <= R);
                return inTopBar || inBottomStem;

            case 'U_SHAPE_DOWN':
                // U-shape with opening at bottom: top bar (3x1) and two side legs (1x1 each)
                // Top bar: (-R-H to R+H, -R to 0)
                // Left leg: (-R-H to -H, 0 to R)
                // Right leg: (H to R+H, 0 to R)
                const inUTopBar = (x >= -(R + H) && x <= (R + H) && z >= -R && z <= 0);
                const inULeftLeg = (x >= -(R + H) && x <= -H && z >= 0 && z <= R);
                const inURightLeg = (x >= H && x <= (R + H) && z >= 0 && z <= R);
                return inUTopBar || inULeftLeg || inURightLeg;

            case 'CROSS_SHAPE':
                // Cross shape: horizontal bar (3x1) and vertical bar (1x3) intersecting at center
                // Horizontal: (-R-H to R+H, -H to H)
                // Vertical: (-H to H, -R-H to R+H)
                const inHorizontalBar = (x >= -(R + H) && x <= (R + H) && z >= -H && z <= H);
                const inVerticalBar = (x >= -H && x <= H && z >= -(R + H) && z <= (R + H));
                return inHorizontalBar || inVerticalBar;

            case 'BOSS_ARENA':
                // Boss arena is typically much larger - use bounding box validation
                return true;

            default:
                console.warn(`[FloorDetection] Unknown room shape: ${roomShape}, allowing position`);
                return true;
        }
    }

    /**
     * Find a spawn position on cave floor using direct raycast
     * @param {number} minDistanceFromCenter - Minimum distance from room center
     * @returns {THREE.Vector3|null} Valid spawn position or null if none found
     * @private
     */
    _findCaveFloorSpawnPosition(minDistanceFromCenter = 2.0) {
        // Get all floor meshes from current room
        const floorMeshes = [];

        if (this.scene) {
            this.scene.traverse(child => {
                if (child.isMesh && child.userData && child.userData.isFloor) {
                    floorMeshes.push(child);
                }
            });
        }

        if (floorMeshes.length === 0) {
            console.warn("[CaveFloorSpawn] No floor meshes found in scene");
            return null;
        }

        console.log(`[CaveFloorSpawn] Found ${floorMeshes.length} floor meshes`);

        const raycaster = new THREE.Raycaster();
        const maxAttempts = 50;
        const searchRadius = 15; // Search within 15 units of center

        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            // Generate random position within search radius
            const angle = Math.random() * Math.PI * 2;
            const radius = minDistanceFromCenter + Math.random() * (searchRadius - minDistanceFromCenter);
            const x = Math.cos(angle) * radius;
            const z = Math.sin(angle) * radius;

            // Cast ray downward from above
            raycaster.set(
                new THREE.Vector3(x, 10, z),
                new THREE.Vector3(0, -1, 0)
            );

            const intersects = raycaster.intersectObjects(floorMeshes, false);

            if (intersects.length > 0) {
                const floorY = intersects[0].point.y;
                console.log(`[CaveFloorSpawn] Found floor at (${x.toFixed(2)}, ${floorY.toFixed(2)}, ${z.toFixed(2)}) on attempt ${attempt + 1}`);
                return new THREE.Vector3(x, floorY, z);
            }
        }

        console.warn(`[CaveFloorSpawn] Could not find floor after ${maxAttempts} attempts`);
        return null;
    }

    _createAura() {
        console.log("[_createAura] Creating aura particles...");
        const particleCount = 100;
        const positions = new Float32Array(particleCount * 3);
        const opacities = new Float32Array(particleCount);
        const auraColor = new THREE.Color(0x66DDFF);

        const radius = 0.8;

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            const r = radius * Math.pow(Math.random(), 1.5);
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.acos(2 * Math.random() - 1);

            positions[i3] = r * Math.sin(phi) * Math.cos(theta);
            positions[i3 + 1] = r * Math.cos(phi);
            positions[i3 + 2] = r * Math.sin(phi) * Math.sin(theta);

            opacities[i] = 1.0 - (r / radius);
        }

        const geometry = new THREE.BufferGeometry();
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('opacityAttr', new THREE.BufferAttribute(opacities, 1));

        const material = new THREE.PointsMaterial({
            color: auraColor,
            size: 0.15,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending,
            depthWrite: false,
            sizeAttenuation: true
        });

        this.auraParticles = new THREE.Points(geometry, material);
        this.auraParticles.name = "playerAura";
        this.scene.add(this.auraParticles);
        console.log("[_createAura] Aura particles created and added to SCENE.", this.auraParticles);
    }

    cleanup() {
        console.log("DungeonHandler: Cleaning up...");
        this.cleanupRoom();

        // --- Dispose Pre-Generated Rooms ---
        console.log(`Disposing of ${this.preGeneratedRooms.size} pre-generated rooms...`);
        this.preGeneratedRooms.forEach((preGenData, roomId) => {
            const { roomGroup, collisionMeshes, lights } = preGenData;

            // Dispose room group
            if (roomGroup) {
                roomGroup.traverse(child => {
                    if (child.isMesh) {
                        if (child.geometry) child.geometry.dispose();
                        if (child.material) {
                            if (Array.isArray(child.material)) child.material.forEach(m => m.dispose());
                            else if (child.material.dispose) child.material.dispose();
                        }
                    }
                });
            }

            // Dispose lights
            if (lights) {
                lights.forEach(light => {
                    if (light.dispose) light.dispose();
                });
            }
        });
        this.preGeneratedRooms.clear();
        // --- End Pre-Generated Rooms Cleanup ---

        // Clean up active items
        if (this.activeItems && this.activeItems.length > 0) {
            console.log(`Cleaning up ${this.activeItems.length} active items...`);
            for (const item of this.activeItems) {
                item.remove(); // This will remove the item from the scene and dispose resources
            }
            this.activeItems = [];
        }

        // Clean up soul orbs
        console.log('Cleaning up soul orbs...');
        simpleSoulDropManager.clearOrbs(this.scene);

        // Dispose player model
        if (this.player) {
            // Dispose aura first if it's attached or related
            if (this.auraParticles) {
                 if(this.auraParticles.geometry) this.auraParticles.geometry.dispose();
                 if(this.auraParticles.material) this.auraParticles.material.dispose();
                 this.scene.remove(this.auraParticles);
                 this.auraParticles = null;
            }
            // Traverse and dispose player geometry/materials
            this.player.traverse((child) => {
                 if (child.isMesh) {
                     if (child.geometry) child.geometry.dispose();
                     if (child.material) {
                          if (Array.isArray(child.material)) child.material.forEach(m => m.dispose());
                          else if (child.material.dispose) child.material.dispose();
                     }
                 }
                 // Remove lights attached to player?
                 if (child.isPointLight) child.removeFromParent();
             });
            this.scene.remove(this.player);
            this.player = null;
        }
        this.playerController = null; // Nullify controller reference

        // Remove lights
         const lightNames = ["dungeonAmbientLight", "debugGlobalLight"];
         lightNames.forEach(name => {
             const light = this.scene.getObjectByName(name);
             if (light) this.scene.remove(light);
         });
        this.ambientLight = null;
        this.debugGlobalLight = null;

        // Clear layout data
        this.floorLayout = null;
        // this.dungeonGenerator = null; // Allow garbage collection

        // Reset camera view initialization flag
        this._hasInitializedTopDownView = false;

        // Restore original camera if we stored it
        try {
            if (this.originalSceneManagerCamera && this.sceneManager) {
                console.log("Restoring original SceneManager camera");
                this.sceneManager.camera = this.originalSceneManagerCamera;

                // Restore camera in effects
                try {
                    if (this.sceneManager.crtEffect && this.sceneManager.crtEffect.manager) {
                        this.sceneManager.crtEffect.manager.camera = this.originalSceneManagerCamera;
                    }
                } catch (error) {
                    console.warn('Error restoring CRT effect camera:', error);
                }

                try {
                    if (this.sceneManager.hdrFramerateEffect && this.sceneManager.hdrFramerateEffect.manager) {
                        this.sceneManager.hdrFramerateEffect.manager.camera = this.originalSceneManagerCamera;
                    }
                } catch (error) {
                    console.warn('Error restoring HDR effect camera:', error);
                }
            }
        } catch (error) {
            console.warn('Error restoring original camera:', error);
        }

        console.log("DungeonHandler: Cleanup complete.");
    }

    /**
     * Detect the appropriate performance mode based on device capabilities
     * @private
     */
    _detectPerformanceMode() {
        // Start with high performance mode
        let detectedMode = 'high';

        // Check FPS if available
        if (window.lastFps) {
            if (window.lastFps < 20) {
                detectedMode = 'low';
            } else if (window.lastFps < 40) {
                detectedMode = 'medium';
            }
        }

        // Check for mobile devices (typically lower performance)
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        if (isMobile) {
            // Mobile devices get at most medium performance mode
            detectedMode = detectedMode === 'high' ? 'medium' : detectedMode;
        }

        // Check for low memory conditions
        if (navigator.deviceMemory && navigator.deviceMemory < 4) {
            // Devices with less than 4GB RAM get at most medium performance mode
            detectedMode = detectedMode === 'high' ? 'medium' : detectedMode;
        }

        // Set the performance mode
        this.performanceMode = detectedMode;
        console.log(`[DungeonHandler] Performance mode set to: ${this.performanceMode}`);

        // Set global window property for other systems to access
        window.gamePerformanceMode = this.performanceMode;
    }

    /**
     * Clean up any orphaned trail objects in the scene
     * This helps prevent white trails from staying on screen permanently
     * @private
     */
    _cleanupOrphanedTrails() {
        // CRITICAL FIX: Check if scene exists before attempting cleanup
        if (!this.scene) {
            console.warn("[_cleanupOrphanedTrails] Scene not available, skipping cleanup");
            return;
        }

        // SAFETY: Additional checks for scene integrity
        if (!this.scene.children || !Array.isArray(this.scene.children)) {
            console.warn("[_cleanupOrphanedTrails] Scene children array is corrupted, skipping cleanup");
            return;
        }

        // PERFORMANCE: More aggressive cleanup of orphaned trails
        // Track how many objects were cleaned up
        let cleanupCount = 0;

        // SAFETY: Use try-catch around traverse to prevent crashes
        try {
            // Find all objects in the scene that might be orphaned trails
            this.scene.traverse(object => {
                // SAFETY: Check if object is valid
                if (!object) {
                    return;
                }
            // CRITICAL FIX: Skip debris pieces and bone pieces - they should persist until room change
            if (object.userData && (object.userData.isBonePiece || object.userData.isDebrisPiece)) {
                return; // Skip debris/bone pieces completely
            }

            // Check if this is likely a trail object
            const isLikelyTrail = object.type === 'Line' ||
                                  object.type === 'Mesh' ||
                                  object.type === 'Points';

            // Check if this object has minimal userData (typical for trails)
            const hasMinimalUserData = !object.userData ||
                                      Object.keys(object.userData).length === 0 ||
                                      (object.userData && !object.userData.isImportant);

            // PERFORMANCE: Also check for objects that have been in the scene too long
            const hasBeenAroundTooLong = object.userData &&
                                        object.userData.createdAt &&
                                        (Date.now() - object.userData.createdAt > 1000); // 1 second (changed from 3 seconds)

            // Check if this object is not part of any active projectile
            let isOrphaned = true;
            if ((isLikelyTrail && hasMinimalUserData) || hasBeenAroundTooLong) {
                // Check if this object is referenced by any active projectile
                for (const projectile of this.activeProjectiles) {
                    if (projectile.userData &&
                        projectile.userData.trailMesh === object) {
                        isOrphaned = false;
                        break;
                    }
                }

                // If it's an orphaned trail, remove and dispose it
                if (isOrphaned) {
                    cleanupCount++;

                    // Remove from scene
                    this.scene.remove(object);

                    // Dispose of geometry
                    if (object.geometry) {
                        object.geometry.dispose();
                    }

                    // Dispose of materials
                    if (object.material) {
                        if (Array.isArray(object.material)) {
                            object.material.forEach(material => {
                                if (material.map) material.map.dispose();
                                material.dispose();
                            });
                        } else {
                            if (object.material.map) object.material.map.dispose();
                            object.material.dispose();
                        }
                    }

                    // PERFORMANCE: Clean up any child objects
                    if (object.children && object.children.length > 0) {
                        for (let i = object.children.length - 1; i >= 0; i--) {
                            const child = object.children[i];

                            // Dispose of child geometries and materials
                            if (child.geometry) child.geometry.dispose();
                            if (child.material) {
                                if (Array.isArray(child.material)) {
                                    child.material.forEach(material => {
                                        if (material.map) material.map.dispose();
                                        material.dispose();
                                    });
                                } else {
                                    if (child.material.map) child.material.map.dispose();
                                    child.material.dispose();
                                }
                            }

                            // Remove child from parent
                            object.remove(child);
                        }
                    }
                }
            }
            });

        } catch (error) {
            console.error("[_cleanupOrphanedTrails] Error during scene traversal:", error);
            console.warn("[_cleanupOrphanedTrails] Skipping cleanup due to scene corruption");
            return;
        }

        // Log cleanup results
        if (cleanupCount > 0) {

            // PERFORMANCE: Force garbage collection hint
            if (window.gc) {
                try {
                    window.gc();
                } catch (e) {
                    // Ignore if gc is not available
                }
            }
        }
    }

    update(deltaTime, scene, camera) {
        // SAFETY: Check if scene is initialized before proceeding
        if (!this.scene) {
            console.warn('[DungeonHandler] Update called before scene initialization - skipping update');
            return;
        }

        // Update cooldown timer
        if (this.transitionCooldown > 0) {
            this.transitionCooldown -= deltaTime;
        }

        // Periodically clean up orphaned trails (every 1 second)
        // SAFETY: Add option to disable cleanup if it causes issues
        const enableTrailCleanup = false; // Temporarily disabled due to scene corruption issues

        if (enableTrailCleanup) {
            if (!this._lastTrailCleanupTime) {
                this._lastTrailCleanupTime = Date.now();
            } else {
                const now = Date.now();
                if (now - this._lastTrailCleanupTime > 1000) { // 1 second (changed from 3 seconds)
                    this._cleanupOrphanedTrails();
                    this._lastTrailCleanupTime = now;
                }
            }
        }

        // Check for boss battle state
        if (this.isBossBattle && !this.bossMusicPlaying && this.activeBosses.length > 0) {
            // Start boss music if we have active bosses
            this.bossMusicPlaying = true;
            console.log("Boss battle detected - boss music should start via BossController");
            // Note: The actual music transition is handled by the BossController
        }

        // --- Update Player Controller FIRST ---
        this.playerController?.update(deltaTime);

        // --- Process Camera Zoom Input ---
        let zoomChanged = false;
        if (this.playerController && this.camera) {
            const zoomDirection = (this.playerController.keys.zoomIn ? 1 : 0) - (this.playerController.keys.zoomOut ? 1 : 0);
            if (zoomDirection !== 0) {
                const newZoom = this.currentZoom + zoomDirection * this.zoomSpeed * deltaTime;
                const clampedZoom = THREE.MathUtils.clamp(newZoom, this.minZoom, this.maxZoom);
                if (clampedZoom !== this.currentZoom) {
                    this.currentZoom = clampedZoom;
                    this.camera.zoom = this.currentZoom;
                    this.camera.updateProjectionMatrix(); // Ensure projection matrix is updated
                    zoomChanged = true;

                    // Update CRT effect if active
                    if (this.sceneManager?.crtEffect) {
                        this.sceneManager.crtEffect.update();
                    }
                }
            }
        }

        // --- Update Aura ---
        const time = this.sceneManager.clock.getElapsedTime(); // Ensure time is available
        if (this.auraParticles && this.player) {
            this.auraParticles.position.copy(this.player.position);
            // Adjust Y offset to center the aura slightly above the player model's base
            const playerHeight = this.playerController?.playerSize?.y || 1.0; // Get player height if available
            this.auraParticles.position.y += playerHeight * 0.5; // Center around player's vertical middle

            // Restore animation
            const sizeSpeed = 2.5;
            const minSize = 0.1;
            const maxSize = 0.2;
            const calculatedSize = minSize + (maxSize - minSize) * (Math.sin(time * sizeSpeed) + 1) / 2;
            this.auraParticles.material.size = calculatedSize;
            this.auraParticles.material.opacity = 0.8;
            this.auraParticles.rotation.y += deltaTime * 0.2;
        }
        // -------------------

        // Update dynamic lighting
        // const time = this.sceneManager.clock.getElapsedTime(); // Time is already declared above
        this._updateDynamicLighting(deltaTime, time);

        // --- Update and Check for Item Collection ---
        if (this.activeItems && this.activeItems.length > 0 && this.player) {
            const playerPosition = this.player.position.clone();

            // Update each item and check for collection
            for (let i = this.activeItems.length - 1; i >= 0; i--) {
                const item = this.activeItems[i];

                // Update item animation
                item.update(deltaTime);

                // Check if player can collect this item
                if (item.canCollect(playerPosition)) {
                    console.log(`Player collecting item: ${item.itemType}`);

                    // Collect the item and apply its effect
                    const collectedItem = itemDropManager.collectItem(item, this.playerController);

                    if (collectedItem) {
                        console.log(`Item collected: ${collectedItem.name}`);
                        // TODO: Show UI notification or play sound
                    }

                    // Remove from active items list
                    this.activeItems.splice(i, 1);
                }
            }
        }

        // --- Update and Check for Soul Orb Collection ---
        if (this.player) {
            const playerPosition = this.player.position.clone();

            // Update soul orbs and check for collection
            simpleSoulDropManager.update(
                deltaTime,
                playerPosition,
                (orb) => {
                    // Handle soul orb collection
                    console.log(`Player collected soul orb with value: ${orb.value}`);

                    // Add to player's soul count
                    if (this.playerController.addSouls) {
                        this.playerController.addSouls(orb.value);
                    } else {
                        console.log(`Player gained ${orb.value} souls`);
                    }

                    // Heal player by exactly 1 health when collecting a soul orb
                    if (this.playerController.heal) {
                        this.playerController.heal(1, true); // Added true to force exactly 1 health
                    }

                    // Play collection sound
                    // TODO: Add sound effect
                }
            );
        }

        // --- Check for Door Trigger Collisions ---
        if (this.playerController && this.doorTriggers.length > 0 && !this.sceneManager.isFading && this.transitionCooldown <= 0) {
            for (const trigger of this.doorTriggers) {
                const triggerBox = new THREE.Box3().setFromObject(trigger);
                if (this.player && triggerBox.containsPoint(this.player.position)) {
                    console.log(`Player entered door trigger: ${trigger.name}`);
                    // Get target room ID and entry direction for the *next* room
                    const targetRoomId = trigger.userData.targetRoomId;
                    // Normalize direction to lowercase for getOppositeDirection
                    const direction = trigger.userData.direction.toLowerCase();
                    const entryDirForNextRoom = getOppositeDirection(direction);

                    // Initiate transition (now asynchronous)
                    this._transitionToRoom(targetRoomId, entryDirForNextRoom);
                    break; // Exit loop after first trigger hit
                }
            }
        }
        // --- End Door Trigger Check ---

        // --- Update Enemies ---
        // Initialize or reset AI update stats for this frame
        if (!this.aiUpdateStats) {
            this.aiUpdateStats = {
                totalEnemies: 0,
                updatedEnemies: 0,
                skippedEnemies: 0,
                lastLogTime: 0
            };
        }
        this.aiUpdateStats.totalEnemies = this.activeEnemies.length;
        this.aiUpdateStats.updatedEnemies = 0;
        this.aiUpdateStats.skippedEnemies = 0;

        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {
            const enemy = this.activeEnemies[i];
            const enemyData = enemy.userData;

            // Skip updates for enemies not in the current room
            if (!enemyData.inCurrentRoom) {
                continue;
            }

            // --- Frustum Culling for Enemies ---
            // Always update zombies and magma golems regardless of frustum culling or distance
            const alwaysUpdate = enemyData.type === 'zombie' || enemyData.type === 'magma_golem';
            const isVisible = alwaysUpdate || !this.sceneManager.enableFrustumCulling ||
                this.sceneManager.frustumCulling.isVisible(enemy, 5.0); // 5.0 unit margin

            // Set visibility flag for later use
            enemyData.isVisible = isVisible;

            // Skip AI updates if not visible and not close to player
            if (!isVisible && !alwaysUpdate) {
                // Still update if close to player (regardless of visibility)
                const distanceToPlayerSq = this.player ?
                    enemy.position.distanceToSquared(this.player.position) : Infinity;

                // Skip update if not visible AND far from player (20 units)
                if (distanceToPlayerSq > 400) {
                    continue;
                }
            }

            // --- AI Update Rate Throttling ---
            // Initialize update frequency properties if they don't exist
            if (enemyData.lastUpdateTime === undefined) {
                enemyData.lastUpdateTime = 0;
                enemyData.updateFrequency = 1; // Default: update every frame
            }

            // CRITICAL FIX: For ranged enemies (skeletons), always update every frame
            // This ensures they can shoot frequently
            if (enemyData.aiType === AI_BRAIN_TYPES.RANGED) {
                enemyData.updateFrequency = 1; // Force update every frame for ranged enemies
                enemyData.priorityUpdate = true; // Always treat ranged enemies as priority
            }

            // Calculate distance to player for update frequency
            const distanceToPlayerSq = this.player ?
                enemy.position.distanceToSquared(this.player.position) : Infinity;

            // Check if enemy has priority update flag (recently hit or important state change)
            const hasPriorityUpdate = enemyData.priorityUpdate || false;

            // Determine update frequency based on distance or priority
            // Priority: every frame (1)
            // Close: every frame (1)
            // Medium: every frame (1) - Changed from 3 to make animations consistent
            // Far: every frame (1) - Changed from 2 to ensure consistent shooting for ranged enemies
            const closeThresholdSq = 100; // 10 units squared
            const mediumThresholdSq = 400; // 20 units squared
            const rangedDetectionThresholdSq = 900; // 30 units squared (matches skeleton max range)

            // Update the frequency based on distance or priority
            let targetFrequency;
            if (hasPriorityUpdate) {
                targetFrequency = 1; // Always update every frame when priority is set
            } else if (enemyData.aiType === AI_BRAIN_TYPES.RANGED && distanceToPlayerSq <= rangedDetectionThresholdSq) {
                // CRITICAL FIX: Always update ranged enemies every frame when within detection range
                targetFrequency = 1;
            } else if (distanceToPlayerSq <= closeThresholdSq) {
                targetFrequency = 1; // Update every frame when close
            } else if (distanceToPlayerSq <= mediumThresholdSq) {
                targetFrequency = 1; // Update every frame at medium distance
            } else {
                targetFrequency = 1; // Update every frame even when far (was 2)
            }

            // Smooth transition between frequencies to avoid sudden behavior changes
            if (targetFrequency !== enemyData.updateFrequency) {
                enemyData.updateFrequency = targetFrequency;
            }

            // Calculate time since last update
            const currentTime = this.sceneManager.clock.getElapsedTime();
            const timeSinceLastUpdate = currentTime - enemyData.lastUpdateTime;

            // Determine if we should update this frame
            // Convert frequency to time (assuming 60fps as baseline)
            const updateInterval = enemyData.updateFrequency / 60;
            const shouldUpdate = timeSinceLastUpdate >= updateInterval;

            // Skip update if not enough time has passed
            if (!shouldUpdate) {
                // Track skipped updates for stats
                if (this.aiUpdateStats) {
                    this.aiUpdateStats.skippedEnemies++;
                }
                continue;
            }

            // Update the last update time
            enemyData.lastUpdateTime = currentTime;

            // Handle priority update duration
            if (hasPriorityUpdate) {
                // Check if we need to initialize the priority timer
                if (enemyData.priorityUpdateStartTime === undefined) {
                    enemyData.priorityUpdateStartTime = currentTime;
                }

                // Check if priority update duration has expired (0.5 seconds)
                const priorityDuration = 0.5; // Half a second of priority updates
                if (currentTime - enemyData.priorityUpdateStartTime > priorityDuration) {
                    // Reset priority update flag and timer
                    enemyData.priorityUpdate = false;
                    enemyData.priorityUpdateStartTime = undefined;
                }
            }



            // Track AI update stats for performance monitoring
            if (!this.aiUpdateStats) {
                this.aiUpdateStats = {
                    totalEnemies: 0,
                    updatedEnemies: 0,
                    skippedEnemies: 0,
                    lastLogTime: 0
                };
            }

            this.aiUpdateStats.updatedEnemies++;

            // --- AI Logic ---
            if (!this.player) continue; // Skip if player doesn't exist

            // Check if enemy has AI brain
            if (enemyData.aiBrain) {
                // Update AI brain
                const stateData = updateAIBrain(
                    enemyData.aiBrain,
                    deltaTime, // Use unscaled deltaTime for more consistent behavior
                    this.collisionObjects,
                    this.floorBounds
                );

                // Store state data
                if (stateData) {
                    this.enemyStateTimers.set(enemyData.id, stateData);
                }

                // Get state data for animation
                const enemyStateData = this.enemyStateTimers.get(enemyData.id) || { state: AIStates.IDLE, timer: 0 };

                // Health Check
                if (enemyData.health <= 0) {
                    console.log(`Enemy ${enemyData.id} defeated!`);

                    // Call proper death handler to spawn soul orbs and debris
                    this._handleEnemyDeath(enemy, i);
                    continue;
                }

                // Update animations based on AI state
                this._updateEnemyAnimations(enemy, enemyStateData);

                // Movement, rotation, and other actions are now handled by the AI brain
                // -----------------------------------------------------

                // Update the enemy's bounding box after movement and animation
                // This ensures collision detection works properly
                enemy.updateMatrixWorld(true); // Ensure world matrix is updated

                // Update bounding box helper if it exists
                if (enemy.userData.boundingBoxHelper) {
                    enemy.userData.boundingBoxHelper.update();
                }

                // Movement, animations, and AI behavior are now handled by the AI brain system
                // --- End Animation ---
            } else {
                // Handle legacy AI types or enemies without AI brain
                console.warn(`Enemy ${enemyData.id} has no AI brain. Using legacy AI or no AI.`);
            }
            // --- End AI Logic ---
        } // --- End Enemy Update ---

        // --- Update Projectiles ---
        // PERFORMANCE: Enforce a strict 3-second lifetime for all projectiles
        const now = Date.now();
        const maxProjectileAge = 3000; // 3 second maximum lifetime for any projectile (unchanged)

        for (let i = this.activeProjectiles.length - 1; i >= 0; i--) {
            const projectileMesh = this.activeProjectiles[i];
            const projectile = projectileMesh.userData;

            // Force cleanup of old projectiles regardless of other conditions
            if (!projectileMesh.userData.spawnTime) {
                projectileMesh.userData.spawnTime = now;
            }

            const projectileAge = now - projectileMesh.userData.spawnTime;
            // PERFORMANCE: Use the 3-second lifetime defined above
            const forceCleanup = projectileAge > maxProjectileAge;

            if (forceCleanup) {
                console.log(`[DungeonHandler] Force cleaning up projectile that's been alive for ${projectileAge}ms`);

                // Call the projectile's destroy method to clean up trails
                if (projectile && typeof projectile.destroy === 'function') {
                    projectile.destroy();
                } else {
                    // Fallback cleanup if destroy method is not available
                    // Check for trail mesh and dispose it thoroughly
                    if (projectile && projectile.trailMesh) {
                        // Remove from scene
                        this.scene.remove(projectile.trailMesh);

                        // Dispose of geometry
                        if (projectile.trailMesh.geometry) {
                            projectile.trailMesh.geometry.dispose();
                        }

                        // Dispose of materials
                        if (projectile.trailMesh.material) {
                            if (Array.isArray(projectile.trailMesh.material)) {
                                projectile.trailMesh.material.forEach(material => {
                                    if (material.map) material.map.dispose();
                                    material.dispose();
                                });
                            } else {
                                if (projectile.trailMesh.material.map) {
                                    projectile.trailMesh.material.map.dispose();
                                }
                                projectile.trailMesh.material.dispose();
                            }
                        }

                        // Clear reference
                        projectile.trailMesh = null;
                    }
                }

                // Remove from scene
                this.scene.remove(projectileMesh);

                // Remove from active list
                this.activeProjectiles.splice(i, 1);
                continue;
            }

            // --- Frustum Culling for Projectiles ---
            // Skip updates for projectiles outside the frustum (with a margin)
            const isVisible = !this.sceneManager.enableFrustumCulling ||
                this.sceneManager.frustumCulling.isVisible(projectileMesh, 2.0); // 2.0 unit margin

            // Always update projectiles that are close to player or enemies
            let forceUpdate = false;

            if (!isVisible && this.player) {
                // Check distance to player
                const distToPlayerSq = projectileMesh.position.distanceToSquared(this.player.position);
                if (distToPlayerSq < 100) { // 10 units
                    forceUpdate = true;
                }
            }

            // Skip update if not visible and not forced
            if (!isVisible && !forceUpdate) {
                continue;
            }

            // Update projectile
            const shouldRemove = projectile.update(
                deltaTime,
                this.collisionObjects,
                this.player,
                this.activeEnemies
            );

            // Remove projectile if needed
            if (shouldRemove) {
                // Note: The projectile.destroy() method has already been called in the update method
                // when it returns true, so we don't need to call it again here

                // Remove from scene
                this.scene.remove(projectileMesh);

                // Remove from active list
                this.activeProjectiles.splice(i, 1);
            }
        }
        // --- End Projectile Update ---

        // --- Update Active Bone Pieces (Iterate room objects) ---
        // <<< DEFINE PHYSICS CONSTANTS HERE >>>
        const gravity = -9.8;
        const groundLevel = 0.0;
        const tempQuaternion = new THREE.Quaternion();
        const tempAxis = new THREE.Vector3();
        const tempBox = new THREE.Box3();
        const groundReactionFactor = 15.0;
        // <<< FINAL TUNING FOR SPREAD >>>
        const groundDragFactor = 0.995;     // Almost no sliding drag (was 0.98)
        const groundAngularDragFactor = 0.99; // Almost no angular drag (was 0.98)
        const groundVerticalDamping = 0.95;   // Minimal vertical damping (Max bounce) (was 0.9)
        // <<< END TUNING >>>
        const maxReactionVelocity = 1.0;
        // Resting thresholds (Increased AGAIN)
        const restLinearThresholdSq = 0.5 * 0.5; // Drastically increased (was 0.1*0.1)

        // --- Frustum Culling for Physics Objects ---
        // Get visibility flags for all active bone pieces
        let visibleBonePieces = [];
        if (this.sceneManager.enableFrustumCulling) {
            // Only process bone pieces that are visible or recently created
            this.currentRoomObjects.forEach((piece, index) => {
                // Skip if not a bone piece or debris piece
                if (!piece.userData?.isBonePiece && !piece.userData?.isDebrisPiece) {
                    return;
                }
                // Always process recently created pieces (for 1 second)
                let isRecent = false;
                if (piece.userData.creationTime !== undefined) {
                    const timeSinceCreation = this.sceneManager.clock.getElapsedTime() - piece.userData.creationTime;
                    isRecent = timeSinceCreation < 1.0;
                }

                // Check if visible in frustum
                const isVisible = isRecent || this.sceneManager.frustumCulling.isVisible(piece, 1.0);

                if (isVisible) {
                    visibleBonePieces.push(piece);
                }
            });
        } else {
            // Process all bone pieces if culling is disabled
            this.currentRoomObjects.forEach(piece => {
                // Only add bone pieces and debris pieces
                if (piece.userData?.isBonePiece || piece.userData?.isDebrisPiece) {
                    visibleBonePieces.push(piece);
                }
            });
        }
        const restAngularThresholdSq = 1.0 * 1.0; // Drastically increased (was 0.5*0.5)
        // <<< END INCREASE >>>
        const groundThreshold = VOXEL_SIZE * 0.5;
        const wallBounceDamping = 0.5;
        // <<< NEW: Temporary objects for collision checks (Re-adding) >>>
        const tempDebrisBox = new THREE.Box3();
        const tempColliderBox = new THREE.Box3();
        const tempDebrisCenter = new THREE.Vector3();
        const tempColliderCenter = new THREE.Vector3();
        const tempCollisionNormal = new THREE.Vector3();
        const tempMTV = new THREE.Vector3(); // Minimum Translation Vector
        // <<< ----------------------------- >>>

        // <<< START Refactored Loop >>>
        // Use visibleBonePieces array instead of all currentRoomObjects
        for (let i = visibleBonePieces.length - 1; i >= 0; i--) {
            const pieceMesh = visibleBonePieces[i];
                const pieceData = pieceMesh.userData;

            // Skip if not a bone piece or debris piece (should never happen with visibleBonePieces)
            if (!pieceData?.isBonePiece && !pieceData?.isDebrisPiece) {
                continue;
            }



                if (pieceData.isFalling) {

                    // <<< START INSERTED PHYSICS LOGIC >>>
                    // Apply gravity
                    pieceData.velocity.y += gravity * deltaTime;

                    // --- NEW: Inter-Debris Collision ---
                    const currentPiece = pieceMesh;
                    const currentData = pieceData;
                    const currentPos = currentPiece.position;
                    // Use bounding sphere radius if available, otherwise approximate based on VOXEL_SIZE
                    let currentRadius = VOXEL_SIZE * 0.4; // Default approx half-voxel
                    if (currentPiece.geometry?.boundingSphere) {
                        currentRadius = currentPiece.geometry.boundingSphere.radius * Math.max(currentPiece.scale.x, currentPiece.scale.y, currentPiece.scale.z);
                    }
                    // <<< REVERT SEPARATION FORCE >>>
                    const separationForce = 1.0; // Reverted (was 5.0)

                    for (let j = i - 1; j >= 0; j--) { // Compare with pieces already processed
                    const otherPiece = visibleBonePieces[j];
                        if ((otherPiece.userData?.isBonePiece || otherPiece.userData?.isDebrisPiece) && otherPiece.userData?.isFalling) {
                            const otherData = otherPiece.userData;
                            const otherPos = otherPiece.position;
                            let otherRadius = VOXEL_SIZE * 0.4;
                            if (otherPiece.geometry?.boundingSphere) {
                                otherRadius = otherPiece.geometry.boundingSphere.radius * Math.max(otherPiece.scale.x, otherPiece.scale.y, otherPiece.scale.z);
                            }

                            const vecBetween = otherPos.clone().sub(currentPos);
                            const distSq = vecBetween.lengthSq();
                            const combinedRadii = currentRadius + otherRadius;
                            const combinedRadiiSq = combinedRadii * combinedRadii;

                            if (distSq < combinedRadiiSq && distSq > 0.0001) { // They overlap
                                const dist = Math.sqrt(distSq);
                                const overlap = combinedRadii - dist;
                                const pushVector = vecBetween.normalize().multiplyScalar(overlap * separationForce * deltaTime); // Scale force by deltaTime

                                // Apply separation impulse (split between the two)
                                currentData.velocity.sub(pushVector);
                                otherData.velocity.add(pushVector);

                            }
                        }
                    }
                    // --- End Inter-Debris Collision ---

                    // Calculate Potential Position & Check Ground/Wall Collision
                    // <<< DEFINE currentPosition HERE >>>
                    const currentPosition = pieceMesh.position;
                    const verticalMove = pieceData.velocity.y * deltaTime;
                    // REMOVED: const tempBox = new THREE.Box3(); // Use tempDebrisBox now
                    // REMOVED: Incorrect declaration inside loop
                    // const tempDebrisBox = new THREE.Box3();
                    tempDebrisBox.setFromObject(pieceMesh); // Use the box declared outside loop
                    const currentLowestPoint = tempDebrisBox.min.y;
                    const potentialLowestY = currentLowestPoint + verticalMove;
                    const potentialPenetration = groundLevel - potentialLowestY;
                    let finalPosition = currentPosition.clone().addScaledVector(pieceData.velocity, deltaTime);
                    let isOnGroundNow = false;
                    let hitWall = false; // <<< Will be set by new collision logic

                    // FIXED: Comprehensive Wall Collision Check (before ground collision)
                    // Only check if debris is moving and hasn't settled yet
                    if (pieceData.isFalling && pieceData.velocity.lengthSq() > 0.1) {
                        // Check if the final position would intersect with walls
                        const originalPosition = pieceMesh.position.clone();
                        pieceMesh.position.copy(finalPosition);
                        pieceMesh.updateMatrixWorld(true);
                        tempDebrisBox.setFromObject(pieceMesh);
                        pieceMesh.position.copy(originalPosition);
                        pieceMesh.updateMatrixWorld(true);

                        // FIXED: Check ALL collision objects, not just wall segments
                        for (const cObject of this.collisionObjects) {
                            if (!cObject || !cObject.visible) continue;

                            // Skip destructible objects (they can be broken)
                            if (cObject.userData?.isDestructible) continue;

                            // Skip floor objects using enhanced detection
                            const isFloor = cObject.name && (
                                cObject.name.toLowerCase().includes('floor') ||
                                cObject.name.toLowerCase().includes('ground') ||
                                cObject.name.toLowerCase().includes('terrain')
                            );

                            if (!isFloor) {
                                // Additional floor detection based on object properties
                                try {
                                    const objBox = new THREE.Box3().setFromObject(cObject);
                                    const objHeight = objBox.max.y - objBox.min.y;
                                    const debrisY = finalPosition.y;
                                    const isAtFeetLevel = objBox.max.y <= (debrisY + 0.2);
                                    const isHorizontalPlane = objHeight < 0.8;
                                    const isProbablyFloor = isHorizontalPlane && isAtFeetLevel;

                                    if (isProbablyFloor) continue; // Skip floor objects
                                } catch (error) {
                                    // Continue if floor detection fails
                                }
                            } else {
                                continue; // Skip named floor objects
                            }

                            try {
                                tempColliderBox.setFromObject(cObject);
                                if (tempDebrisBox.intersectsBox(tempColliderBox)) {
                                    // ENHANCED: Realistic bounce physics with collision normal
                                    const collisionNormal = this._calculateDebrisCollisionNormal(finalPosition, cObject);

                                    // Calculate reflection vector
                                    const velocityDotNormal = pieceData.velocity.dot(collisionNormal);
                                    if (velocityDotNormal < 0) { // Moving towards the surface
                                        // Reflect velocity based on collision normal
                                        const reflection = collisionNormal.clone().multiplyScalar(2 * velocityDotNormal);
                                        pieceData.velocity.sub(reflection);

                                        // Apply realistic damping
                                        pieceData.velocity.multiplyScalar(0.4); // Energy loss on bounce

                                        // Add slight randomness for natural scatter
                                        pieceData.velocity.add(new THREE.Vector3(
                                            (Math.random() - 0.5) * 0.2,
                                            Math.random() * 0.1,
                                            (Math.random() - 0.5) * 0.2
                                        ));
                                    }

                                    // Move debris away from wall to prevent penetration
                                    const penetrationDepth = this._calculateDebrisPenetrationDepth(tempDebrisBox, tempColliderBox);
                                    const separationVector = collisionNormal.clone().multiplyScalar(penetrationDepth + 0.05);
                                    finalPosition.add(separationVector);

                                    hitWall = true;
                                    break; // Only handle one collision per frame
                                }
                            } catch (error) {
                                // Continue if collision check fails
                                console.warn('[Debris] Collision check error:', error);
                            }
                        }
                    }

                    // Ground Collision Response
                    if (potentialPenetration > -groundThreshold) {
                        isOnGroundNow = true;
                        // <<< INSTANT FREEZE LOGIC >>>
                        pieceData.isFalling = false;
                        pieceData.velocity.set(0, 0, 0);
                        pieceData.angularVelocity.set(0, 0, 0);
                        // Adjust final Y position to sit exactly on ground
                        finalPosition.y = currentPosition.y + (groundLevel - currentLowestPoint);
                        // <<< END INSTANT FREEZE >>>
                    } else {
                        // Still airborne, apply gravity effect (already done before)
                    }

                    // Apply final position (calculated with potential ground adjustment)
                    pieceMesh.position.copy(finalPosition);

                    // Update rotation (only if still falling/tumbling)
                    if (pieceData.isFalling) { // <<< Check isFalling flag BEFORE rotation >>>
                        const angle = pieceData.angularVelocity.length() * deltaTime;
                        if (angle > 0.001) {
                            tempAxis.copy(pieceData.angularVelocity).normalize();
                            tempQuaternion.setFromAxisAngle(tempAxis, angle);
                            pieceMesh.quaternion.premultiply(tempQuaternion);
                        }
                    }
                    // --- REMOVE OLD RESTING CHECK ---
                    /*
                    // Resting Check (REMOVED - Replaced by Instant Freeze)
                    if (isOnGroundNow &&
                        pieceData.velocity.lengthSq() < restLinearThresholdSq &&
                        pieceData.angularVelocity.lengthSq() < restAngularThresholdSq)
                    {
                        // ... old resting logic ...
                    }
                    */
                    // --- END REMOVAL ---

                } // End if (pieceData.isFalling) initially check
        } // End for loop over visibleBonePieces
        // <<< END Refactored Loop >>>
        // --- End Bone Piece Update ---

        // --- Camera Update ---
        if (this.player && this.camera) {
            // No need to force camera setup on every frame - let the toggle handle it
            const roomCenter = new THREE.Vector3(
                (this.playerController.floorBounds.minX + this.playerController.floorBounds.maxX) / 2,
                0,
                (this.playerController.floorBounds.minZ + this.playerController.floorBounds.maxZ) / 2
            );

            // Calculate room dimensions
            const roomWidth = this.playerController.floorBounds.maxX - this.playerController.floorBounds.minX;
            const roomDepth = this.playerController.floorBounds.maxZ - this.playerController.floorBounds.minZ;

            // Calculate the edge threshold (distance from edge where camera starts moving)
            const edgeThreshold = Math.min(roomWidth, roomDepth) * 0.4; // 40% of room size

            // Calculate how close the player is to room edges
            const distanceToRightEdge = this.playerController.floorBounds.maxX - this.player.position.x;
            const distanceToLeftEdge = this.player.position.x - this.playerController.floorBounds.minX;
            const distanceToTopEdge = this.playerController.floorBounds.maxZ - this.player.position.z;
            const distanceToBottomEdge = this.player.position.z - this.playerController.floorBounds.minZ;

            // Calculate camera target position
            const targetCameraPosition = new THREE.Vector3();

            // Check if player is outside the room bounds
            const isOutsideX = distanceToRightEdge < 0 || distanceToLeftEdge < 0;
            const isOutsideZ = distanceToTopEdge < 0 || distanceToBottomEdge < 0;

            if (isOutsideX || isOutsideZ) {
                // Player is outside room bounds - camera should follow player
                if (this.isTopDownView) {
                    targetCameraPosition.set(
                        this.player.position.x,
                        this.topDownCameraOffset.y,
                        this.player.position.z
                    );
                } else {
                    targetCameraPosition.set(
                        this.player.position.x,
                        this.originalCameraOffset.y,
                        this.player.position.z + this.originalCameraOffset.z
                    );
                }
            } else {
                // Player is inside room bounds - check edge thresholds
                let offsetX = 0;
                let offsetZ = 0;

                if (distanceToRightEdge < edgeThreshold) {
                    offsetX = (edgeThreshold - distanceToRightEdge);
                } else if (distanceToLeftEdge < edgeThreshold) {
                    offsetX = -(edgeThreshold - distanceToLeftEdge);
                }

                if (distanceToTopEdge < edgeThreshold) {
                    offsetZ = (edgeThreshold - distanceToTopEdge);
                } else if (distanceToBottomEdge < edgeThreshold) {
                    offsetZ = -(edgeThreshold - distanceToBottomEdge);
                }

                if (this.isTopDownView) {
                    targetCameraPosition.set(
                        roomCenter.x + offsetX,
                        this.topDownCameraOffset.y,
                        roomCenter.z + offsetZ
                    );
                } else {
                    targetCameraPosition.set(
                        roomCenter.x + offsetX,
                        this.originalCameraOffset.y,
                        roomCenter.z + this.originalCameraOffset.z + offsetZ
                    );
                }
            }

            // Smoothly move camera
            const lerpFactor = 0.05;

            // Only update the camera position directly if we're not currently shaking
            // This prevents conflicts between camera movement and shake effect
            if (!this._shakeInterval) {
                this.camera.position.lerp(targetCameraPosition, lerpFactor);

                // Update the original camera position for shake reference
                if (this._originalCameraPosition) {
                    this._originalCameraPosition.copy(this.camera.position);
                }
            } else {
                // If we're shaking, update the reference position but not the actual camera
                if (this._originalCameraPosition) {
                    this._originalCameraPosition.lerp(targetCameraPosition, lerpFactor);
                }
            }

            // Handle camera orientation based on mode
            if (this.isTopDownView) {
                // In top-down view, simply set rotation and up vector
                this.camera.rotation.x = -Math.PI/2; // Look straight down
                this.camera.rotation.y = 0;
                this.camera.rotation.z = 0;
                this.camera.up.set(0, 0, -1); // Set up vector for proper orientation
            } else {
                // In normal view, look at the point ahead of the camera
            const lookAtPoint = new THREE.Vector3(
                this.camera.position.x,
                0,
                this.camera.position.z - this.originalCameraOffset.z
            );
            this.camera.lookAt(lookAtPoint);

                // Set standard up vector for normal view
                this.camera.up.set(0, 1, 0);
            }
        }
        // ... rest of update method ...

        // --- Update Projection Matrix if Zoom Changed (or if view toggled) ---
        // The toggleCameraView function already calls updateProjectionMatrix,
        // so we only need to call it here if zoom changed via keys.
        if (zoomChanged) {
            this.camera.updateProjectionMatrix();
        }
        // -----------------------------------------------------------------

        // --- Update Enemy Rotation Helpers ---
        // Only update if ESP is active and frustum culling is disabled or we have visible enemies
        if (this.isEspRotationViewActive &&
            (!this.sceneManager.enableFrustumCulling || this.activeEnemies.some(enemy => enemy.userData.isVisible))) {
        this._updateEnemyRotationHelpers();
        }
        // -----------------------------------

        // --- Log Frustum Culling Stats (if enabled) ---
        if (this.sceneManager.enableFrustumCulling && this.activeEnemies.length > 0) {
            const stats = this.sceneManager.frustumCulling.getStats();
        }

        // --- Log AI Update Rate Throttling Stats ---
        if (this.aiUpdateStats && this.aiUpdateStats.totalEnemies > 0) {
            // Only log every second to avoid console spam
            const currentTime = this.sceneManager.clock.getElapsedTime();
            if (currentTime - this.aiUpdateStats.lastLogTime > 1.0) {
                this.aiUpdateStats.lastLogTime = currentTime;
            }
        }

        try {
            if (this.isDebugLightOn) {
                // Optionally update debug light position if needed
            }

            // --- Update Music System Combat State --- // NEW BLOCK
            if (this.audioManager && this.audioManager.musicConductor) {
                // Count active enemies in the current room
                const activeEnemyCount = this.activeEnemies.filter(enemy => enemy.userData.inCurrentRoom).length;

                // Get player health
                const currentHealth = this.playerController ? this.playerController.currentHealth : 1;
                const maxHealth = this.playerController ? this.playerController.maxHealth : 1;

                // Regular update - no enemy killed, no room entry
                // We'll handle those events separately in their respective methods
                this.audioManager.updateCombatState(
                    activeEnemyCount,
                    currentHealth,
                    maxHealth,
                    false, // enemyKilled
                    false  // enteredRoom
                );
            }
        } catch (error) {
            console.error("Error in DungeonHandler update:", error);
        }
    }

    _transitionToRoom(targetRoomId, entryDirection) {
        // Prevent double transitions if already fading
        if (this.sceneManager.isFading) return;

        // Prevent triggering immediately if already cooling down (safety)
        if (this.transitionCooldown > 0) return;

        console.log(`Transitioning to Room ID: ${targetRoomId}, entering from ${entryDirection}`);

        // Disable player input during transition
        this.playerController?.disable();

        // Start fade out, load room in callback
        this.sceneManager.startFade(() => {
            // This code runs when screen is black
            this.loadRoom(targetRoomId, entryDirection);
            // SceneManager.endFade() is automatically called after this callback completes

            // Trigger predictive pre-loading for adjacent rooms
            this._queueAdjacentRooms(targetRoomId);
            this._processPreLoadQueue();

            // Clean up distant rooms to manage memory
            this._cleanupDistantRooms(targetRoomId);

            // Start the cooldown AFTER the new room starts loading
            this.transitionCooldown = 0.5; // Set cooldown duration (e.g., 0.5 seconds)

            // Re-enable player input AFTER new room is loaded and fade starts/ends
            // Need to ensure fade completes before enabling
            // For now, enable slightly delayed after load starts
            // A better way would be a callback on sceneManager.endFade completion
            setTimeout(() => {
                this.playerController?.enable();
            }, 50); // Small delay, adjust if needed
        });
    }

    // --- _updateDynamicLighting (Extracted from previous update) ---
    _updateDynamicLighting(deltaTime, time) {
        // Update Ambient Light
        if (this.ambientLight && this.playerController && typeof this.playerController.currentHealth === 'number') {
            const currentHealth = this.playerController.currentHealth;
            const maxHealth = this.playerController.maxHealth;
            let targetIntensity = BASE_AMBIENT_INTENSITY;

            // Special case for room 0 flicker effect
            if (this.currentRoomId === 0) {
                // Create a complex flicker pattern using multiple sine waves at different slow frequencies
                const baseFlicker = Math.sin(time * FLICKER_SPEED);
                const noiseFlicker = Math.sin(time * FLICKER_SPEED * 1.3) * FLICKER_NOISE_SCALE; // Slower secondary wave
                const fastFlicker = Math.sin(time * FLICKER_SPEED * 1.7) * FLICKER_NOISE_SCALE * 0.3; // Slower tertiary wave

                // Add occasional deeper darkness
                const deepDarkness = Math.sin(time * 0.2) * 0.4; // Very slow wave for occasional deeper darkness

                // Combine the flickers and normalize to 0-1 range with deep darkness influence
                const flickerValue = ((baseFlicker + noiseFlicker + fastFlicker + deepDarkness + 1) / 2) * 0.8; // Reduce overall brightness by 20%

                // Interpolate between min and max intensity
                targetIntensity = FLICKER_MIN_INTENSITY + (FLICKER_MAX_INTENSITY - FLICKER_MIN_INTENSITY) * flickerValue;
            } else if (currentHealth <= 0) {
                targetIntensity = MIN_AMBIENT_INTENSITY;
            } else if (currentHealth <= BASE_HEALTH) {
                const healthRatio = currentHealth / BASE_HEALTH;
                const easedRatio = Math.pow(healthRatio, 2.5);
                targetIntensity = MIN_AMBIENT_INTENSITY + (BASE_AMBIENT_INTENSITY - MIN_AMBIENT_INTENSITY) * easedRatio;
            } else {
                const clampedHealth = Math.min(currentHealth, MAX_BRIGHTNESS_HEALTH);
                targetIntensity = THREE.MathUtils.mapLinear(
                    clampedHealth,
                    BASE_HEALTH, MAX_BRIGHTNESS_HEALTH,
                    BASE_AMBIENT_INTENSITY, MAX_AMBIENT_INTENSITY
                );
            }
            this.ambientLight.intensity = THREE.MathUtils.lerp(
                this.ambientLight.intensity,
                targetIntensity,
                INTENSITY_LERP_FACTOR * deltaTime * 0.5 // Slower lerp for smoother transitions
            );
        }

        // Update Torch Lights
        this.currentRoomObjects.forEach((object) => {
            if (object.isPointLight && object.name === 'torchLight') {
                if (this.playerController && typeof this.playerController.currentHealth === 'number') {
                    const currentHealth = this.playerController.currentHealth;

                    // Special case for room 0 flicker effect
                    if (this.currentRoomId === 0) {
                        // Create a unique flicker pattern for each torch based on its position
                        const uniqueOffset = object.position.x + object.position.z;
                        const baseFlicker = Math.sin(time * FLICKER_SPEED + uniqueOffset);
                        const noiseFlicker = Math.sin(time * FLICKER_SPEED * 1.3 + uniqueOffset) * FLICKER_NOISE_SCALE;
                        const fastFlicker = Math.sin(time * FLICKER_SPEED * 1.7 + uniqueOffset) * FLICKER_NOISE_SCALE * 0.3;
                        const deepDarkness = Math.sin(time * 0.2 + uniqueOffset * 0.1) * 0.4; // Add deep darkness to torches too

                        // Combine the flickers and normalize with deep darkness influence
                        const flickerValue = ((baseFlicker + noiseFlicker + fastFlicker + deepDarkness + 1) / 2) * 0.6; // Reduce torch brightness by 40%

                        // Apply the flicker effect to torch intensity and range
                        object.intensity = BASE_TORCH_INTENSITY * flickerValue * 0.4; // Reduce torch intensity
                        object.distance = BASE_TORCH_RANGE * flickerValue * 0.7; // Reduce torch range
                    } else if (currentHealth < 3) {
                        object.intensity = 0;
                        object.distance = 0;
                    } else if (currentHealth <= BASE_HEALTH) {
                        object.intensity = BASE_TORCH_INTENSITY * (0.8 + Math.sin(time * 6 + object.position.x) * 0.2 + Math.random() * 0.15);
                        object.distance = BASE_TORCH_RANGE;
                    } else {
                        const highHealthRatio = Math.min(1, (currentHealth - BASE_HEALTH) / (MAX_BRIGHTNESS_HEALTH - BASE_HEALTH));
                        const targetIntensity = BASE_TORCH_INTENSITY + (MAX_TORCH_INTENSITY - BASE_TORCH_INTENSITY) * highHealthRatio;
                        const targetRange = BASE_TORCH_RANGE + (MAX_TORCH_RANGE - BASE_TORCH_RANGE) * highHealthRatio;
                        object.intensity = targetIntensity * (0.8 + Math.sin(time * 6 + object.position.x) * 0.2 + Math.random() * 0.15);
                        object.distance = targetRange;
                    }
                } else {
                    object.intensity = BASE_TORCH_INTENSITY * (0.8 + Math.sin(time * 6 + object.position.x) * 0.2 + Math.random() * 0.15);
                    object.distance = BASE_TORCH_RANGE;
                }
            }
        });
    }

    onResize() {
         if (this.camera instanceof THREE.OrthographicCamera) {
            const aspect = window.innerWidth / window.innerHeight;
            // Use larger frustum size for top-down view
            const frustumSize = this.isTopDownView ? 16 : 12;
            this.camera.left = frustumSize * aspect / -2;
            this.camera.right = frustumSize * aspect / 2;
            this.camera.top = frustumSize / 2;
            this.camera.bottom = frustumSize / -2;
            this.camera.updateProjectionMatrix();
         } else if (this.camera instanceof THREE.PerspectiveCamera) {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
         }
     }

    // --- Minimap Update Logic ---
    _updateMinimap() {
        if (!this.minimapGridElement || !this.floorLayout) {
            console.warn("Minimap element or floor layout not found, cannot update minimap.");
            return;
        }

        // Clear previous minimap
        this.minimapGridElement.innerHTML = '';

        // Find bounds of visited rooms
        let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
        let visitedRoomsExist = false;
        this.floorLayout.forEach(room => {
            if (room.visited) {
                visitedRoomsExist = true;
                minX = Math.min(minX, room.coords.x);
                maxX = Math.max(maxX, room.coords.x);
                minY = Math.min(minY, room.coords.y);
                maxY = Math.max(maxY, room.coords.y);
            }
        });

        if (!visitedRoomsExist) return; // Don't draw anything if no rooms visited

        const mapWidth = maxX - minX + 1;
        const mapHeight = maxY - minY + 1;

        // Set both grid columns and rows with fixed sizes
        this.minimapGridElement.style.gridTemplateColumns = `repeat(${mapWidth}, 10px)`;
        this.minimapGridElement.style.gridTemplateRows = `repeat(${mapHeight}, 10px)`;

        // Create a 2D array to store room positions
        const gridCells = Array(mapHeight).fill().map(() => Array(mapWidth).fill(null));

        // First pass: Map rooms to grid positions
        this.floorLayout.forEach(room => {
            if (room.visited) {
                const gridX = room.coords.x - minX;
                const gridY = room.coords.y - minY;
                if (gridX >= 0 && gridX < mapWidth && gridY >= 0 && gridY < mapHeight) {
                    gridCells[gridY][gridX] = room;
                }
            }
        });

        // Second pass: Create and position room divs
        for (let y = 0; y < mapHeight; y++) {
            for (let x = 0; x < mapWidth; x++) {
                const roomDiv = document.createElement('div');
                roomDiv.classList.add('minimap-room');

                const room = gridCells[y][x];
                if (room) {
                    roomDiv.classList.add('visited');
                    if (room.id === this.currentRoomId) {
                        roomDiv.classList.add('current');
                    }
                    if (room.type === 'Boss') {
                        roomDiv.classList.add('boss');
                    }
                } else {
                    roomDiv.style.backgroundColor = 'transparent';
                    roomDiv.style.borderColor = 'transparent';
                }

                // Set explicit grid position
                roomDiv.style.gridColumn = `${x + 1}`;
                roomDiv.style.gridRow = `${y + 1}`;

                this.minimapGridElement.appendChild(roomDiv);
            }
        }
    }

    /**
     * Spawn a projectile
     * @param {THREE.Vector3} startPos - Start position
     * @param {THREE.Vector3} direction - Direction
     * @param {Object} ownerData - Owner data
     */
    spawnProjectile(startPos, direction, ownerData) {
        // CRITICAL FIX: Validate inputs with detailed logging
        if (!startPos || !direction || !ownerData) {
            console.error("[DungeonHandler] CRITICAL ERROR: Invalid parameters for spawnProjectile:", {
                startPos: startPos ? `valid (${startPos.x.toFixed(2)},${startPos.y.toFixed(2)},${startPos.z.toFixed(2)})` : "invalid",
                direction: direction ? `valid (${direction.x.toFixed(2)},${direction.y.toFixed(2)},${direction.z.toFixed(2)})` : "invalid",
                ownerData: ownerData ? "valid" : "invalid"
            });
            return; // Exit early if any parameter is invalid
        }

        // Validate direction vector
        if (!isFinite(direction.x) || !isFinite(direction.y) || !isFinite(direction.z)) {
            console.error("[DungeonHandler] CRITICAL ERROR: Invalid direction vector in spawnProjectile");
            direction = new THREE.Vector3(0, 0, -1); // Use fallback direction
        }

        // CRITICAL FIX: Ensure direction is normalized
        if (direction.length() !== 1) {
            console.log(`[DungeonHandler] Normalizing direction vector with length ${direction.length().toFixed(4)}`);
            direction.normalize();
        }

        // Determine if player or enemy projectile
        const isPlayerProjectile = !ownerData?.aiType;

        // Get projectile type
        let projectileType = isPlayerProjectile ? 'default_soul_orb' : (ownerData.projectileType || 'arrow');

        // Get projectile data
        const projectileData = getProjectileType(projectileType);

        // Extract any custom properties from ownerData
        const customProps = {};
        if (ownerData) {
            // Copy custom properties that start with 'is' or other special properties
            Object.keys(ownerData).forEach(key => {
                if (key.startsWith('is') || ['scale', 'health', 'lifetime'].includes(key)) {
                    customProps[key] = ownerData[key];
                }
            });

            // Check if this is a boss projectile
            const isBossProjectile = ownerData.isBoss ||
                                    (ownerData.aiType &&
                                     (ownerData.aiType.includes('boss') ||
                                      ownerData.aiType.includes('overlord') ||
                                      ownerData.aiType === 'catacombs_overlord'));

            if (isBossProjectile) {
                // Disable trails for boss projectiles
                customProps.disableTrails = true;
                console.log("[DungeonHandler] Disabled trails for boss projectile");
            }
        }

        // Create projectile mesh with custom properties
        const projectileMesh = createProjectileMesh(projectileType, startPos, customProps);
        if (!projectileMesh) {
            console.error("[DungeonHandler] CRITICAL ERROR: Failed to create projectile mesh");
            return; // Exit if mesh creation failed
        }

        // Log custom properties for debugging
        if (Object.keys(customProps).length > 0) {
            console.log(`[DungeonHandler] Created projectile with custom properties:`, customProps);
        }

        // Calculate velocity
        const speed = isPlayerProjectile ? projectileData.speed : (ownerData.projectileSpeed || projectileData.speed);

        // CRITICAL FIX: Ensure we have a valid speed
        const finalSpeed = isFinite(speed) && speed > 0 ? speed : 15.0;
        if (finalSpeed !== speed) {
            console.log(`[DungeonHandler] WARNING: Invalid speed ${speed}, using default ${finalSpeed}`);
        }

        // CRITICAL FIX: Create velocity with additional validation
        let velocity;
        try {
            velocity = direction.clone().multiplyScalar(finalSpeed);

            // Validate the velocity vector
            if (!isFinite(velocity.x) || !isFinite(velocity.y) || !isFinite(velocity.z)) {
                console.error('[DungeonHandler] Invalid velocity after calculation, using default');
                velocity = new THREE.Vector3(0, 0, -finalSpeed); // Default direction with proper speed
            }
        } catch (error) {
            console.error('[DungeonHandler] Error creating velocity:', error);
            velocity = new THREE.Vector3(0, 0, -finalSpeed); // Default direction with proper speed
        }

        // Create projectile instance
        const damage = isPlayerProjectile ? projectileData.damage : (ownerData.projectileDamage || projectileData.damage);
        const range = isPlayerProjectile ? projectileData.range : (ownerData.projectileRange || projectileData.range);
        const size = isPlayerProjectile ? projectileData.size : (ownerData.projectileSize || projectileData.size);

        // Custom properties already extracted above

        // Create projectile with custom properties
        const projectile = new Projectile(
            this.scene,
            startPos,
            velocity,
            damage,
            range,
            size,
            !isPlayerProjectile,
            projectileType,
            customProps
        );

        // Store projectile in mesh userData
        projectileMesh.userData = projectile;

        // Set mesh reference in projectile
        projectile.mesh = projectileMesh;

        // Add impact handler
        projectileMesh.userData.onImpact = (position, effectType, targetType, velocity) => {
            this._handleProjectileImpact(position, effectType, targetType, velocity);
        };

        // CRITICAL FIX: Ensure projectile is oriented correctly
        projectileMesh.lookAt(startPos.clone().add(direction));

        // PERFORMANCE: Add timestamps for lifetime tracking
        const now = Date.now();
        projectileMesh.userData.spawnTime = now;

        // Also add timestamp to the mesh itself for orphaned trail detection
        projectileMesh.userData.createdAt = now;

        // If there's a trail mesh, add timestamp to it too
        if (projectile.trailMesh) {
            projectile.trailMesh.userData = projectile.trailMesh.userData || {};
            projectile.trailMesh.userData.createdAt = now;
        }

        // Add to scene and active projectiles
        this.scene.add(projectileMesh);
        this.activeProjectiles.push(projectileMesh);
    }

    /**
     * Handle projectile impact
     * @param {THREE.Vector3} position - Impact position
     * @param {String} effectType - Effect type
     * @param {String} targetType - Target type
     * @private
     */
    _handleProjectileImpact(position, effectType, targetType, velocity) {
        // Log impact
        console.log(`Projectile impact: ${effectType} on ${targetType} at ${position.x}, ${position.y}, ${position.z}`);

        // Check if we hit a destructible object
        if (targetType === 'environment') {
            // Find the closest object to the impact position
            let closestObject = null;
            let closestDistance = Infinity;

            for (const obj of this.collisionObjects) {
                // Skip non-destructible objects
                if (!obj.userData || !obj.userData.isDestructible) continue;

                // Calculate distance to impact position
                const distance = obj.position.distanceTo(position);

                // If this object is closer than the current closest, update
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestObject = obj;
                }
            }

            // If we found a destructible object within range, destroy it
            if (closestObject && closestDistance < 2.0) { // 2.0 is the max distance to consider a hit
                console.log(`Destroying object: ${closestObject.name}`);
                this._handleObjectDestruction(closestObject, position, velocity);
            }
        }

        // TODO: Add impact effects (particles, sound, etc.)
    }
    // Renamed function for clarity
    spawnEnemyProjectile(startPos, direction, enemyDefinition) {
        this.spawnProjectile(startPos, direction, enemyDefinition);
    }

    /**
     * Handle enemy melee attack
     * @param {Object} enemy - The enemy object
     * @param {Object} attackHitbox - Attack hitbox data
     * @private
     */
    _handleEnemyMeleeAttack(enemy, attackHitbox) {
        if (!enemy || !this.player) {
            console.log(`Enemy melee attack failed: ${!enemy ? 'enemy is null' : 'player is null'}`);
            return;
        }

        // Get attack data
        const hitRadius = attackHitbox?.radius || 1.0;
        const damage = 1; // Always use 1 damage regardless of enemy type
        const knockback = attackHitbox?.knockback || 5.0;

        console.log(`Enemy ${enemy.name} attempting melee attack with radius: ${hitRadius}, damage: ${damage}`);

        // Check if player is within attack range
        const distanceToPlayer = enemy.position.distanceTo(this.player.position);
        console.log(`Distance to player: ${distanceToPlayer.toFixed(2)}, hit radius: ${hitRadius}`);

        if (distanceToPlayer <= hitRadius) {
            console.log(`Enemy ${enemy.name} hit player with melee attack! Damage: ${damage}`);

            // Apply damage to player
            if (this.playerController && this.playerController.takeDamage) {
                console.log(`Applying ${damage} damage to player from enemy ${enemy.name}`);
                try {
                    const isDead = this.playerController.takeDamage(damage, enemy.position);
                    console.log(`Player health after damage: ${this.playerController.currentHealth}/${this.playerController.maxHealth}`);
                    if (isDead) {
                        console.log(`Player died from enemy attack!`);
                    }
                } catch (error) {
                    console.error(`Error applying damage to player:`, error);
                }
            } else {
                console.error('Cannot apply damage: playerController or takeDamage method is missing');
                if (!this.playerController) {
                    console.error('playerController is null or undefined');
                } else if (!this.playerController.takeDamage) {
                    console.error('playerController.takeDamage is null or undefined');
                }
            }

            // Apply knockback to player
            if (this.playerController && this.playerController.applyKnockback) {
                const knockbackDirection = this.player.position.clone().sub(enemy.position).normalize();
                this.playerController.applyKnockback(knockbackDirection, knockback);
            }
        } else {
            console.log(`Enemy ${enemy.name} melee attack missed - player out of range`);
        }
    }

    /**
     * Handle enemy shooting
     * @param {Object} enemy - The enemy object
     * @param {THREE.Vector3} direction - Direction to shoot
     * @param {Number} projectileCount - Number of projectiles to shoot
     * @private
     */
    _handleEnemyShoot(enemy, direction, projectileCount = 1) {
        // CRITICAL FIX: Validate direction
        if (!direction || !isFinite(direction.x) || !isFinite(direction.y) || !isFinite(direction.z)) {
            console.log("[DungeonHandler] CRITICAL ERROR: Invalid direction in _handleEnemyShoot, using fallback");
            // Use a default direction if the provided one is invalid
            direction = new THREE.Vector3(0, 0, -1);
        }

        const enemyData = enemy.userData;

        // Calculate start position
        let startPos = new THREE.Vector3();
        enemy.updateMatrixWorld(true);

        // Try to find bow or weapon
        const weapon = enemy.getObjectByName('SkeletonBow') || enemy.getObjectByName('Weapon');
        if (weapon) {
            // Get position from weapon
            startPos.setFromMatrixPosition(weapon.matrixWorld);
        } else {
            // Fallback to enemy position
            startPos.copy(enemy.position);
            startPos.y += (enemyData.size || 0.6) * 0.7;
        }

        // CRITICAL FIX: Ensure we have valid projectile data
        if (!enemyData.projectileType) {
            console.log(`[DungeonHandler] WARNING: No projectileType defined for ${enemy.name}, using default 'arrow'`);
            enemyData.projectileType = 'arrow';
        }

        if (!enemyData.projectileSpeed) {
            console.log(`[DungeonHandler] WARNING: No projectileSpeed defined for ${enemy.name}, using default 15.0`);
            enemyData.projectileSpeed = 15.0;
        }

        // Spawn projectiles
        for (let i = 0; i < projectileCount; i++) {
            // Add slight variation for multiple projectiles
            let shootDir = direction.clone();
            if (projectileCount > 1) {
                const spreadAngle = (Math.random() - 0.5) * Math.PI / 8; // +/- 22.5 degrees
                const spreadMatrix = new THREE.Matrix4().makeRotationY(spreadAngle);
                shootDir.applyMatrix4(spreadMatrix);
            }


            // Spawn projectile
            this.spawnProjectile(startPos, shootDir, enemyData);
        }
    }

    // Old _handleEnemyMeleeAttack method removed - using the attackHitbox version instead

    /**
     * Handle enemy phase change (for bosses)
     * @param {Object} enemy - The enemy object
     * @param {Number} phase - New phase
     * @private
     */
    _handleEnemyPhaseChange(enemy, phase) {
        // Handle phase change effects
        console.log(`Enemy ${enemy.userData.id} entering phase ${phase}`);

        // TODO: Add visual effects, sound, etc.
    }

    /**
     * Handle enemy taking damage
     * @param {Object} enemy - The enemy object
     * @param {Number} damage - Damage amount
     * @private
     */
    _handleEnemyTakeDamage(enemy, damage) {
        const enemyData = enemy.userData;

        // Apply damage
        enemyData.health -= damage;

        // Set priority update flag to ensure immediate reaction to being hit
        enemyData.priorityUpdate = true;
        enemyData.priorityUpdateStartTime = this.sceneManager.clock.getElapsedTime();

        console.log(`Enemy ${enemy.name} took ${damage} damage. Health: ${enemyData.health}`);

        // Check if enemy is dead
        if (enemyData.health <= 0) {
            console.log(`Enemy ${enemy.name} died!`);

            // Find the enemy's index in the activeEnemies array
            const enemyIndex = this.activeEnemies.findIndex(e => e === enemy);
            if (enemyIndex !== -1) {
                // Handle enemy death
                this._handleEnemyDeath(enemy, enemyIndex);
                return; // Exit early since enemy is now dead and removed
            } else {
                console.warn(`Could not find enemy ${enemy.name} in activeEnemies array`);
            }
        } else {
            // Apply knockback from player
            if (this.player) {
                const knockbackDirection = enemy.position.clone().sub(this.player.position).normalize();
                const knockbackStrength = 20.0; // Increased for better visual impact and tactical spacing (3-4 units)

                if (enemyData.aiBrain) {
                    applyKnockback(enemyData.aiBrain, knockbackDirection, knockbackStrength);
                }
            }

            // Update health bar if it exists
            if (enemyData.updateHealthBar) {
                enemyData.updateHealthBar();
            }
        }
    }

    /**
     * Update enemy animations based on state
     * @param {Object} enemy - The enemy object
     * @param {Object} stateData - State data from AI brain
     * @private
     */
    _updateEnemyAnimations(enemy, stateData) {
        if (!stateData) return;

        const state = stateData.state;
        const enemyData = enemy.userData;
        const time = this.sceneManager.clock.getElapsedTime();
        const deltaTime = this.sceneManager.clock.getDelta();

        // More detailed animation debug logging
        const debugInfo = {
            name: enemy.name,
            type: enemyData.type,
            hasAnimHandler: !!enemyData.animationHandler,
            state: state,
            handlerType: enemyData.animationHandler ? enemyData.animationHandler.constructor.name : 'none'
        };



        // Fix for zombie enemies without animation handler
        if (!enemyData.animationHandler &&
            (enemyData.type === 'zombie' || enemy.name.includes('zombie') || enemy.name.includes('Zombie'))) {
            console.log(`[ANIM] Detected zombie without animation handler: ${enemy.name}. Fixing...`);
            // Try to fix the zombie animation handler
            const fixed = fixZombieAnimationHandler(enemy);
            if (fixed) {
                console.log(`[ANIM] Successfully fixed animation handler for ${enemy.name}`);
            } else {
                console.warn(`[ANIM] Failed to fix animation handler for ${enemy.name}`);
            }
        }

        // Fix for magma golem enemies without animation handler
        if (!enemyData.animationHandler &&
            (enemyData.type === 'magma_golem' || enemy.name.includes('magma_golem') || enemy.name.includes('MagmaGolem'))) {
            console.log(`[ANIM] Detected magma golem without animation handler: ${enemy.name}. Fixing...`);
            // Try to fix the magma golem animation handler
            const fixed = fixMagmaGolemAnimationHandler(enemy);
            if (fixed) {
                console.log(`[ANIM] Successfully fixed animation handler for ${enemy.name}`);
            } else {
                console.warn(`[ANIM] Failed to fix animation handler for ${enemy.name}`);
            }
        }

        // Check if enemy has a custom animation handler
        if (enemyData.animationHandler) {
            // Use custom animation handler
            enemyData.animationHandler.update(state, deltaTime, time);

            // Store current state for next frame
            enemyData.previousAnimState = state;
            return; // Skip standard animation handling
        }

        // Special case for bat enemies without animation handler
        if ((enemyData.type === 'bat' || enemy.name.includes('bat') || enemy.name.includes('Bat')) && !enemyData.animationHandler) {
            console.log('Detected bat without animation handler');

            // For bats without animation handler, use the standard animation
            // but make sure to apply wing flapping
            const leftWing = enemy.getObjectByName('leftWing');
            const rightWing = enemy.getObjectByName('rightWing');

            if (leftWing && rightWing) {
                console.log('Applying manual wing flapping animation');
                // Apply wing flapping animation manually
                const wingFlapSpeed = 6.0;
                const wingFlapAmplitude = Math.PI / 3;

                // Sinusoidal wing flapping
                const wingAngle = Math.sin(time * wingFlapSpeed) * wingFlapAmplitude;
                leftWing.rotation.z = Math.PI / 8 + wingAngle;
                rightWing.rotation.z = -Math.PI / 8 - wingAngle;

                // Add slight body bobbing
                const bodyBobSpeed = 2.5;
                const bodyBobAmplitude = 0.15;
                const bodyOffset = Math.sin(time * bodyBobSpeed) * bodyBobAmplitude;

                // Store original Y position if not already set
                if (enemyData.basePositionY === undefined) {
                    enemyData.basePositionY = enemy.position.y;
                }

                // Apply bobbing - BUT NOT during knockback
                if (!enemy.userData.aiBrain || !enemy.userData.aiBrain.isKnockedBack) {
                    enemy.position.y = enemyData.basePositionY + bodyOffset;
                }
            }
        }

        // Track previous state for transition handling
        if (!enemyData.previousAnimState) {
            enemyData.previousAnimState = state;
        }
        const previousState = enemyData.previousAnimState;

        // Check if enemy is hit reacting
        const isHitReacting = stateData.isHitReacting;

        // Get limbs
        let leftLeg, rightLeg, leftArm, rightArm;

        // Get skeleton limbs
        leftLeg = enemy.getObjectByName('leftLeg');
        rightLeg = enemy.getObjectByName('rightLeg');
        leftArm = enemy.getObjectByName('leftArm');
        rightArm = enemy.getObjectByName('rightArm');

        // Reset limbs when transitioning from certain states that can cause issues
        const problematicStates = [AIStates.DODGING, AIStates.AIMING];
        if (previousState !== state && problematicStates.includes(previousState)) {
            this._resetLimbs(leftLeg, rightLeg, leftArm, rightArm);
        }

        // If hit reacting, apply hit reaction animation
        if (isHitReacting) {
            this._applyHitReactionAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
            return; // Skip other animations
        }

        // Apply animations based on state
        switch (state) {
            case AIStates.IDLE:
                // Idle animation
                this._applyIdleAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
                break;

            case AIStates.MOVING:
            case AIStates.FLEEING:
            case AIStates.STRAFING:
                // Walking animation
                this._applyWalkAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
                break;

            case AIStates.AIMING:
                // Aiming animation
                this._applyAimAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
                break;

            case AIStates.ATTACKING:
                // Attack animation
                this._applyAttackAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
                break;

            case AIStates.SHOOTING:
                // Shooting animation
                this._applyShootAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
                break;

            case AIStates.DODGING:
                // Dodge animation
                this._applyDodgeAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
                break;

            case AIStates.HOVERING:
            case AIStates.SWOOPING:
            case AIStates.ASCENDING:
                // Flying animation
                this._applyFlyAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
                break;

            default:
                // Reset pose for other states
                if (leftLeg) leftLeg.rotation.set(0, 0, 0);
                if (rightLeg) rightLeg.rotation.set(0, 0, 0);
                if (leftArm) leftArm.rotation.set(0, 0, 0);
                if (rightArm) rightArm.rotation.set(0, 0, 0);
                break;
        }

        // Store current state for next frame
        enemyData.previousAnimState = state;
    }

    /**
     * Reset limbs to neutral position
     * @param {Object} leftLeg - Left leg object
     * @param {Object} rightLeg - Right leg object
     * @param {Object} leftArm - Left arm object
     * @param {Object} rightArm - Right arm object
     * @private
     */
    _resetLimbs(leftLeg, rightLeg, leftArm, rightArm) {
        // Reset all limbs to neutral position
        if (leftLeg) leftLeg.rotation.set(0, 0, 0);
        if (rightLeg) rightLeg.rotation.set(0, 0, 0);
        if (leftArm) leftArm.rotation.set(0, 0, 0);
        if (rightArm) rightArm.rotation.set(0, 0, 0);
    }

    /**
     * Apply idle animation
     * @param {Object} enemy - The enemy object
     * @param {Object} leftLeg - Left leg object
     * @param {Object} rightLeg - Right leg object
     * @param {Object} leftArm - Left arm object
     * @param {Object} rightArm - Right arm object
     * @private
     */
    _applyIdleAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm) {
        if (!leftLeg || !rightLeg) return;

        const time = this.sceneManager.clock.getElapsedTime();
        const idleSpeed = 1.5;
        const idleBobAmplitude = 0.05;
        const idleSwayAmplitude = Math.PI / 32;

        // Subtle idle movement
        const bobOffset = Math.sin(time * idleSpeed) * idleBobAmplitude;
        const swayOffset = Math.sin(time * idleSpeed * 0.7) * idleSwayAmplitude;

        // Apply to limbs
        if (leftLeg) leftLeg.rotation.x = bobOffset * 0.5;
        if (rightLeg) rightLeg.rotation.x = bobOffset * 0.5;
        if (leftLeg) leftLeg.rotation.z = swayOffset;
        if (rightLeg) rightLeg.rotation.z = -swayOffset;

        // Apply only sway to arms during idle
        if (leftArm) {
            leftArm.rotation.x = 0;
            leftArm.rotation.y = 0;
            leftArm.rotation.z = swayOffset * 0.8;
        }
        if (rightArm) {
            rightArm.rotation.x = 0;
            rightArm.rotation.y = 0;
            rightArm.rotation.z = -swayOffset * 0.8;
        }

        // Add slight body movement - use absolute positioning
        // Store base Y position if not already set
        if (enemy.userData.basePositionY === undefined) {
            enemy.userData.basePositionY = enemy.position.y;
        }

        // Set absolute position instead of incremental - BUT NOT during knockback
        if (!enemy.userData.aiBrain || !enemy.userData.aiBrain.isKnockedBack) {
            enemy.position.y = enemy.userData.basePositionY + bobOffset * 0.1;
        }
    }

    /**
     * Apply walk animation
     * @param {Object} enemy - The enemy object
     * @param {Object} leftLeg - Left leg object
     * @param {Object} rightLeg - Right leg object
     * @param {Object} leftArm - Left arm object
     * @param {Object} rightArm - Right arm object
     * @private
     */
    _applyWalkAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm) {
        if (!leftLeg || !rightLeg) return;

        const enemyData = enemy.userData;
        const walkSpeed = enemyData.speed * 3.0; // Increased from 2.0 for faster animation
        const walkAmplitude = Math.PI / 8; // Increased from PI/10 for more pronounced steps
        const time = this.sceneManager.clock.getElapsedTime();

        // Walk cycle
        if (leftLeg) leftLeg.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude;
        if (rightLeg) rightLeg.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude;

        // Arm swing
        if (leftArm) leftArm.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude * 0.5;
        if (rightArm) rightArm.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude * 0.5;

        // Add slight random offset to avoid robotic movement - use absolute positioning
        const randomOffset = (Math.random() - 0.5) * 0.01;

        // Store base Y position if not already set
        if (enemy.userData.basePositionY === undefined) {
            enemy.userData.basePositionY = enemy.position.y;
        }

        // Set absolute position with slight bobbing based on walk cycle - BUT NOT during knockback
        if (!enemy.userData.aiBrain || !enemy.userData.aiBrain.isKnockedBack) {
            const walkBob = Math.abs(Math.sin(time * walkSpeed)) * 0.05;
            enemy.position.y = enemy.userData.basePositionY + walkBob + randomOffset;
        }
    }

    /**
     * Apply aim animation
     * @param {Object} enemy - The enemy object
     * @param {Object} leftLeg - Left leg object
     * @param {Object} rightLeg - Right leg object
     * @param {Object} leftArm - Left arm object
     * @param {Object} rightArm - Right arm object
     * @param {Number} timer - Aim timer
     * @private
     */
    _applyAimAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm, timer) {
        const time = this.sceneManager.clock.getElapsedTime();
        const idleSwayAmplitude = Math.PI / 64; // Reduced sway
        const swayOffset = Math.sin(time * 1.0) * idleSwayAmplitude; // Slower sway

        // Add slight breathing animation to prevent looking frozen
        // Use absolute position setting instead of incremental to match analyzer
        const breathingOffset = Math.sin(time * 1.5) * 0.005;

        // Store base Y position if not already set
        if (enemy.userData.basePositionY === undefined) {
            enemy.userData.basePositionY = enemy.position.y;
        }

        // Set absolute position instead of incremental - BUT NOT during knockback
        if (enemy && (!enemy.userData.aiBrain || !enemy.userData.aiBrain.isKnockedBack)) {
            enemy.position.y = enemy.userData.basePositionY + breathingOffset;
        }

        // Create a continuous aiming animation that doesn't depend on timer
        // This ensures the animation continues even if the AI state is stuck

        // Use time-based animation cycle that repeats
        const aimCycle = (time % 3.0) / 3.0; // 0.0 to 1.0 every 3 seconds
        const aimPhase = Math.sin(aimCycle * Math.PI * 2) * 0.5 + 0.5; // 0.0 to 1.0 sinusoidal

        if (leftArm) {
            // Left arm supports the bow with continuous subtle movement
            const armSwayX = Math.sin(time * 0.8) * (Math.PI / 48); // Increased amplitude
            const armSwayZ = Math.sin(time * 0.7) * (Math.PI / 64);

            // Add breathing motion to arm
            const breathingAdjust = Math.sin(time * 1.2) * 0.02;

            leftArm.rotation.set(
                -Math.PI / 16 + armSwayX + breathingAdjust, // Slight raise with movement
                Math.sin(time * 0.5) * 0.01, // Tiny Y rotation
                swayOffset + armSwayZ // Z rotation with sway
            );
        }

        if (rightArm) {
            // Right arm pulls back the bow string with continuous movement
            // Use both the timer (if available) and time-based animation

            // Calculate pullback using a combination of timer and time
            const aimDuration = 0.5; // Default aim duration

            // If timer is valid and not stuck, use it; otherwise use time-based animation
            let pullbackProgress;
            if (timer !== undefined && timer < aimDuration * 1.5) {
                // Use timer-based progress when timer is working normally
                pullbackProgress = Math.min(1.0, 1.0 - (timer / aimDuration));
            } else {
                // Use time-based animation when timer is stuck or invalid
                pullbackProgress = 0.3 + Math.sin(time * 0.8) * 0.2; // Oscillate between 0.1 and 0.5
            }

            // Apply pullback angle with additional movement
            const pullbackAngleY = pullbackProgress * -Math.PI / 32;
            const armSwayY = Math.sin(time * 0.9) * (Math.PI / 64); // Increased amplitude
            const armSwayZ = Math.cos(time * 0.7) * (Math.PI / 96);

            rightArm.rotation.set(
                Math.sin(time * 0.6) * 0.02, // Slight X rotation
                pullbackAngleY + armSwayY, // Y rotation with pullback
                -swayOffset + armSwayZ // Z rotation with sway
            );
        }

        // Always animate legs during aiming, regardless of movement
        const enemyData = enemy.userData;

        // Get animation speed based on enemy type
        const animSpeed = (enemyData.speed || 1.5) * 1.0;

        // Check if enemy is actually moving
        let isActuallyMoving = false;
        if (enemyData.aiBrain && enemyData.aiBrain.velocity) {
            isActuallyMoving = enemyData.aiBrain.velocity.lengthSq() > 0.01;
        }

        // Use different animation styles based on whether the enemy is actually moving
        if (isActuallyMoving) {
            // Use a walking animation for legs while aiming and moving
            const walkAmplitude = Math.PI / 16; // Reduced amplitude for aiming

            if (leftLeg) leftLeg.rotation.x = Math.sin(time * animSpeed * 2.0) * walkAmplitude;
            if (rightLeg) rightLeg.rotation.x = Math.sin(time * animSpeed * 2.0 + Math.PI) * walkAmplitude;
        } else {
            // Use a subtle shifting weight animation when aiming but not moving
            // This ensures animation continues even when stationary
            const shiftSpeed = 0.8; // Slower than walking
            const shiftAmplitude = Math.PI / 24; // Subtle but visible

            // Shift weight from one leg to another
            if (leftLeg) leftLeg.rotation.x = Math.sin(time * shiftSpeed) * shiftAmplitude;
            if (rightLeg) rightLeg.rotation.x = Math.sin(time * shiftSpeed + Math.PI) * shiftAmplitude;

            // Add slight rotation to show tension while aiming
            if (leftLeg) leftLeg.rotation.z = Math.sin(time * 0.5) * 0.02;
            if (rightLeg) rightLeg.rotation.z = -Math.sin(time * 0.5) * 0.02;
        }
    }

    /**
     * Apply attack animation
     * @param {Object} enemy - The enemy object
     * @param {Object} leftLeg - Left leg object
     * @param {Object} rightLeg - Right leg object
     * @param {Object} leftArm - Left arm object
     * @param {Object} rightArm - Right arm object
     * @private
     */
    _applyAttackAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm) {
        const time = this.sceneManager.clock.getElapsedTime();

        // Add slight breathing animation to prevent looking frozen
        const breathingOffset = Math.sin(time * 1.5) * 0.005;

        // Store base Y position if not already set
        if (enemy.userData.basePositionY === undefined) {
            enemy.userData.basePositionY = enemy.position.y;
        }

        // Set absolute position instead of incremental - BUT NOT during knockback
        if (enemy && (!enemy.userData.aiBrain || !enemy.userData.aiBrain.isKnockedBack)) {
            enemy.position.y = enemy.userData.basePositionY + breathingOffset;
        }

        // Quick forward lunge for melee attack with slight movement
        const legSway = Math.sin(time * 3.0) * 0.03;
        if (leftLeg) leftLeg.rotation.x = Math.PI / 8 + legSway; // Forward step with movement
        if (rightLeg) rightLeg.rotation.x = -Math.PI / 16 - legSway; // Back leg braced with movement

        // Arms with slight movement
        const armSway = Math.sin(time * 2.5) * 0.04;
        if (leftArm) leftArm.rotation.set(-Math.PI / 4 + armSway, 0, 0); // Raised to balance with movement
        if (rightArm) rightArm.rotation.set(Math.PI / 2 - armSway, 0, 0); // Striking position with movement
    }

    /**
     * Apply shoot animation
     * @param {Object} enemy - The enemy object
     * @param {Object} leftLeg - Left leg object
     * @param {Object} rightLeg - Right leg object
     * @param {Object} leftArm - Left arm object
     * @param {Object} rightArm - Right arm object
     * @private
     */
    _applyShootAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm) {
        const time = this.sceneManager.clock.getElapsedTime();

        // Create a more dynamic shooting animation
        // Use a quick, sharp movement to indicate the release of an arrow

        // Add a more pronounced recoil effect
        const recoilOffset = Math.sin(time * 12.0) * 0.015; // Increased from 8.0 to 12.0 for faster recoil

        // Store base Y position if not already set
        if (enemy.userData.basePositionY === undefined) {
            enemy.userData.basePositionY = enemy.position.y;
        }

        // Apply a slight backward movement for recoil - BUT NOT during knockback
        if (enemy && (!enemy.userData.aiBrain || !enemy.userData.aiBrain.isKnockedBack)) {
            enemy.position.y = enemy.userData.basePositionY + recoilOffset;

            // Force movement to ensure animation is visible
            if (enemy.userData.aiBrain && enemy.userData.aiBrain.velocity) {
                // Create a small backward movement
                const direction = enemy.userData.aiBrain.player.position.clone()
                    .sub(enemy.position).normalize().multiplyScalar(-0.05);

                // Apply to velocity to ensure movement animation triggers
                enemy.userData.aiBrain.velocity.copy(direction);
            }
        }

        // Dynamic release animation for shooting
        if (leftArm) {
            // Left arm holds the bow steady but with a faster recoil effect
            const armSwayX = Math.sin(time * 6.0) * (Math.PI / 28); // Increased speed and amplitude
            const armSwayZ = Math.cos(time * 5.0) * (Math.PI / 40); // Increased speed and amplitude

            leftArm.rotation.set(
                -Math.PI / 16 + armSwayX, // Slight raise with recoil
                Math.sin(time * 2.0) * 0.02, // Small Y rotation
                armSwayZ // Z rotation with recoil
            );
        }

        if (rightArm) {
            // Right arm has a more pronounced and faster follow-through motion
            const releaseAngle = Math.PI / 7; // Increased from PI/8 for more dramatic release
            const followThrough = Math.sin(time * 8.0) * (Math.PI / 20); // Increased speed and amplitude

            rightArm.rotation.set(
                Math.sin(time * 5.0) * 0.08, // Increased speed and amplitude for X rotation
                releaseAngle + followThrough, // Y rotation with follow through
                Math.cos(time * 4.0) * 0.05 // Increased speed and amplitude for Z rotation
            );
        }

        // Add a more dynamic stance shift for shooting
        const stanceShift = Math.sin(time * 5.0) * 0.05; // Increased speed and amplitude

        if (leftLeg) leftLeg.rotation.set(stanceShift, 0, Math.sin(time * 4.0) * 0.03); // Faster leg movement
        if (rightLeg) rightLeg.rotation.set(-stanceShift, 0, -Math.sin(time * 4.0) * 0.03); // Faster leg movement

        // Force a state transition after a short time
        // This ensures we don't get stuck in the shooting state
        const enemyData = enemy.userData;
        if (enemyData.aiBrain && enemyData.aiBrain.currentState === 'SHOOTING') {
            // If we've been in shooting state for more than 0.2 seconds, force transition (reduced from 0.3)
            if (enemyData.aiBrain.stateTimer > 0.2) {
                console.log("DEBUG: Forcing transition out of SHOOTING state from animation");
                enemyData.aiBrain.setState('IDLE');
            }
        }
    }

    /**
     * Apply dodge animation
     * @param {Object} enemy - The enemy object
     * @param {Object} leftLeg - Left leg object
     * @param {Object} rightLeg - Right leg object
     * @param {Object} leftArm - Left arm object
     * @param {Object} rightArm - Right arm object
     * @private
     */
    _applyDodgeAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm) {
        const time = this.sceneManager.clock.getElapsedTime();

        // Add slight breathing animation to prevent looking frozen
        const breathingOffset = Math.sin(time * 1.5) * 0.005;

        // Store base Y position if not already set
        if (enemy.userData.basePositionY === undefined) {
            enemy.userData.basePositionY = enemy.position.y;
        }

        // Set absolute position instead of incremental - BUT NOT during knockback
        if (enemy && (!enemy.userData.aiBrain || !enemy.userData.aiBrain.isKnockedBack)) {
            enemy.position.y = enemy.userData.basePositionY + breathingOffset;
        }

        // Quick side step for dodge with slight continuous movement
        const legSway = Math.sin(time * 2.0) * 0.05;
        if (leftLeg) leftLeg.rotation.z = Math.PI / 6 + legSway; // Lean with movement
        if (rightLeg) rightLeg.rotation.z = Math.PI / 6 + legSway; // Lean same direction with movement

        // Arms out for balance with slight movement
        const armSway = Math.sin(time * 1.8) * 0.05;
        if (leftArm) leftArm.rotation.set(0, 0, Math.PI / 4 + armSway);
        if (rightArm) rightArm.rotation.set(0, 0, -Math.PI / 4 - armSway);
    }

    /**
     * Apply hit reaction animation
     * @param {Object} enemy - Enemy object
     * @param {Object} leftLeg - Left leg object
     * @param {Object} rightLeg - Right leg object
     * @param {Object} leftArm - Left arm object
     * @param {Object} rightArm - Right arm object
     * @private
     */
    _applyHitReactionAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm) {
        const time = this.sceneManager.clock.getElapsedTime();

        // Add slight breathing animation to prevent looking frozen
        const breathingOffset = Math.sin(time * 1.5) * 0.005;

        // Store base Y position if not already set
        if (enemy.userData.basePositionY === undefined) {
            enemy.userData.basePositionY = enemy.position.y;
        }

        // Set absolute position instead of incremental - BUT NOT during knockback
        if (enemy && (!enemy.userData.aiBrain || !enemy.userData.aiBrain.isKnockedBack)) {
            enemy.position.y = enemy.userData.basePositionY + breathingOffset;
        }

        // Stagger backwards
        if (leftLeg) leftLeg.rotation.x = -Math.PI / 8; // Leg back
        if (rightLeg) rightLeg.rotation.x = -Math.PI / 6; // Leg further back

        // Arms flail slightly
        if (leftArm) {
            leftArm.rotation.x = Math.PI / 4; // Arm up
            leftArm.rotation.z = -Math.PI / 8; // Arm out
        }

        if (rightArm) {
            rightArm.rotation.x = Math.PI / 3; // Arm up higher
            rightArm.rotation.z = Math.PI / 6; // Arm out
        }

        // Apply a slight tilt to the whole enemy if possible, but not for skeletons
        const isSkeletonModel = enemy.name.includes('skeleton') ||
                               enemy.name.includes('Skeleton') ||
                               enemy.userData?.type?.includes('skeleton');

        if (enemy.rotation && !isSkeletonModel) {
            // Only apply tilt for non-skeleton enemies
            enemy.rotation.z = Math.PI / 24; // Slight tilt
        } else if (enemy.rotation && isSkeletonModel) {
            // For skeletons, ensure they stay upright
            enemy.rotation.z = 0;

            // Also ensure the enemy's AI brain applies rotation correction
            if (enemy.userData.aiBrain && typeof enemy.userData.aiBrain._ensureEnemyIsUpright === 'function') {
                enemy.userData.aiBrain._ensureEnemyIsUpright();
            }
        }
    }

    /**
     * Apply fly animation
     * @param {Object} enemy - The enemy object
     * @param {Object} leftLeg - Left leg object
     * @param {Object} rightLeg - Right leg object
     * @param {Object} leftArm - Left arm object
     * @param {Object} rightArm - Right arm object
     * @private
     */
    _applyFlyAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm) {
        const time = this.sceneManager.clock.getElapsedTime();

        // Wings flapping (arms)
        if (leftArm) leftArm.rotation.z = Math.sin(time * 5) * Math.PI / 4;
        if (rightArm) rightArm.rotation.z = -Math.sin(time * 5) * Math.PI / 4;

        // Legs tucked up
        if (leftLeg) leftLeg.rotation.x = -Math.PI / 4;
        if (rightLeg) rightLeg.rotation.x = -Math.PI / 4;

        // Bobbing up and down - BUT NOT during knockback
        if (!enemy.userData.aiBrain || !enemy.userData.aiBrain.isKnockedBack) {
            const hoverOffset = Math.sin(time * 2) * 0.1;
            enemy.position.y += hoverOffset;
        }
    }

    // --- Enemy Spawn Helper ---
    _spawnEnemy(enemyType) {
        console.log(`\n=== SPAWNING ENEMY ===`);
        console.log(`Type: ${enemyType}`);

        const enemyDefinition = getEnemyData(enemyType);
        if (!enemyDefinition) {
            console.error(`Failed to spawn enemy: Type "${enemyType}" definition not found.`);
            return;
        }

        // Check if this is a boss enemy
        const isBoss = enemyDefinition.aiType === AI_BRAIN_TYPES.BOSS;
        if (isBoss) {
            console.log(`Spawning boss enemy: ${enemyType}`);
            // Set boss battle flag
            this.isBossBattle = true;
        }

        // Track creation time for new enemies
        const creationTime = this.sceneManager.clock.getElapsedTime();

        // --- Create Enemy Mesh/Group ---
        let enemy;
        if (enemyDefinition.modelPrefab) { // <<< Use enemyDefinition
            console.log(`Creating enemy model using prefab function for type: ${enemyType}`);
            // Apply special scale for zombies and bosses
            let desiredScale = 2.5; // Default scale
            if (enemyType === 'zombie') {
                desiredScale = 3.5; // Increased scale for zombies
            } else if (enemyType === 'catacombs_overlord') {
                desiredScale = 12.5; // 5x larger scale for Catacomb Overlord boss
            }
            enemy = enemyDefinition.modelPrefab(desiredScale); // <<< Use enemyDefinition
            enemy.name = `Enemy_${enemyType}_${Date.now()}`;
            // Apply castShadow/receiveShadow to children if it's a Group
            enemy.traverse(child => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                }
            });

            // Initialize animation handler for specific enemy types
            if (enemyType === 'bat') {
                console.log(`Creating bat animation handler for enemy ${enemy.name}`);
                enemy.userData.animationHandler = new BatAnimationHandler(enemy);
                console.log(`Created animation handler for ${enemyType}:`, enemy.userData.animationHandler);
            } else if (enemyType === 'zombie') {
                console.log(`[ANIM DEBUG] Creating zombie animation handler for enemy ${enemy.name}`);
                console.log(`[ANIM DEBUG] Zombie model structure:`, {
                    hasBody: !!enemy.getObjectByName('body'),
                    hasHead: !!enemy.getObjectByName('head'),
                    hasLeftArm: !!enemy.getObjectByName('leftArm'),
                    hasRightArm: !!enemy.getObjectByName('rightArm'),
                    hasLeftLeg: !!enemy.getObjectByName('leftLeg'),
                    hasRightLeg: !!enemy.getObjectByName('rightLeg'),
                    type: enemy.userData.type
                });

                try {
                    // Use SimpleZombieAnimationHandler instead of the complex one
                    enemy.userData.animationHandler = new SimpleZombieAnimationHandler(enemy);
                    console.log(`[ANIM DEBUG] Created SimpleZombieAnimationHandler:`, enemy.userData.animationHandler);
                } catch (error) {
                    console.error(`[ANIM ERROR] Failed to create SimpleZombieAnimationHandler:`, error);
                    console.log(`[ANIM RECOVERY] Attempting to fix zombie animation handler...`);

                    // Try to fix the animation handler
                    const fixed = fixZombieAnimationHandler(enemy);
                    if (fixed) {
                        console.log(`[ANIM RECOVERY] Successfully fixed animation handler for ${enemy.name}`);
                    } else {
                        console.warn(`[ANIM RECOVERY] Failed to fix animation handler for ${enemy.name}`);
                    }
                }
            } else if (enemyType === 'magma_golem') {
                console.log(`[ANIM DEBUG] Creating magma golem animation handler for enemy ${enemy.name}`);
                console.log(`[ANIM DEBUG] Magma golem model structure:`, {
                    hasBody: !!enemy.getObjectByName('body'),
                    hasHead: !!enemy.getObjectByName('head'),
                    hasLeftArm: !!enemy.getObjectByName('leftArm'),
                    hasRightArm: !!enemy.getObjectByName('rightArm'),
                    hasLeftLeg: !!enemy.getObjectByName('leftLeg'),
                    hasRightLeg: !!enemy.getObjectByName('rightLeg')
                });

                try {
                    // Use SimpleMagmaGolemAnimationHandler instead of the complex one
                    enemy.userData.animationHandler = new SimpleMagmaGolemAnimationHandler(enemy);
                    console.log(`[ANIM DEBUG] Created SimpleMagmaGolemAnimationHandler:`, enemy.userData.animationHandler);
                } catch (error) {
                    console.error(`[ANIM ERROR] Failed to create SimpleMagmaGolemAnimationHandler:`, error);
                    console.log(`[ANIM RECOVERY] Attempting to fix magma golem animation handler...`);

                    // Try to fix the animation handler
                    const fixed = fixMagmaGolemAnimationHandler(enemy);
                    if (fixed) {
                        console.log(`[ANIM RECOVERY] Successfully fixed animation handler for ${enemy.name}`);
                    } else {
                        console.warn(`[ANIM RECOVERY] Failed to fix animation handler for ${enemy.name}`);
                    }
                }
            } else if (enemyType === 'catacombs_overlord') {
                console.log(`Creating catacombs overlord animation handler for enemy ${enemy.name}`);
                enemy.userData.animationHandler = new CatacombOverlordAnimationHandler(enemy);
                console.log(`Created animation handler for ${enemyType}`);
            }
        } else if (enemyDefinition.geometry && enemyDefinition.material) { // <<< Use enemyDefinition
            // Fallback to simple geometry/material (legacy or for other types)
            console.log(`Creating enemy using simple geometry/material for type: ${enemyType}`);
            enemy = new THREE.Mesh(enemyDefinition.geometry, enemyDefinition.material); // <<< Use enemyDefinition
        enemy.castShadow = true;
        enemy.receiveShadow = true;
        } else {
            console.error(`Cannot create enemy: No modelPrefab or geometry/material found for type: ${enemyType}`);
            return;
        }

        // --- CRITICAL FIX: Spawn position logic using floor detection ---
        const minSpawnDist = 2.0; // Minimum distance from room center
        let spawnPos = new THREE.Vector3();

        // SIMPLIFIED: Direct raycast to cave floor for spawn position
        const spawnPosition = this._findCaveFloorSpawnPosition(minSpawnDist);

        if (spawnPosition) {
            // Set Y position higher for zombies and magma golems to prevent them from being stuck in the ground
            const yOffset =
                enemyType === 'zombie' ?
                    (enemyDefinition.size || 0.6) * 0.25 : // Reduced from 0.5 to 0.25
                enemyType === 'magma_golem' ?
                    (enemyDefinition.size || 0.6) * 0.3 : // Reduced from 0.75 to 0.3
                    (enemyDefinition.size || 0.6) / 2;    // Normal Y for other enemies

            spawnPos.set(spawnPosition.x, spawnPosition.y + yOffset, spawnPosition.z);

            console.log(`[EnemySpawn] Using cave floor position: (${spawnPos.x.toFixed(2)}, ${spawnPos.y.toFixed(2)}, ${spawnPos.z.toFixed(2)})`);
        } else {
            console.warn("[EnemySpawn] Could not find cave floor for spawning. Using fallback position.");

            // Simple fallback - spawn near room center at ground level
            const angle = Math.random() * Math.PI * 2;
            const radius = minSpawnDist + Math.random() * 5; // Small radius around center
            const yPosition = (enemyDefinition.size || 0.6) / 2;

            spawnPos.set(Math.cos(angle) * radius, yPosition, Math.sin(angle) * radius);
        }

        enemy.position.copy(spawnPos);

        // Log position for debugging
        if (enemyType === 'zombie') {
            console.log(`[ZOMBIE DEBUG] Spawned zombie at position:`, enemy.position.clone());
        }
        // --- End Spawn position logic ---

        // <<< Assign unique ID to enemy's userData >>>
        enemy.userData = {
            id: enemy.uuid, // <<< Initialize userData with unique ID
            isVisible: true, // Visibility flag for frustum culling
            creationTime: creationTime, // Track when this enemy was created
            // AI Update Rate Throttling properties
            lastUpdateTime: 0,
            updateFrequency: 1, // Default: update every frame
            inCurrentRoom: true, // Flag to indicate this enemy is in the current room
            roomId: this.currentRoomId // Store the room ID this enemy belongs to
        }

        // Set userData based on definition and add variation
        enemy.userData.aiType = enemyDefinition.aiType; // <<< Use enemyDefinition
        enemy.userData.type = enemyType === 'skeleton' ? 'skeleton_archer' : enemyType; // Set type for group coordination
        enemy.userData.health = enemyDefinition.health; // <<< Use enemyDefinition
        enemy.userData.speed = enemyDefinition.baseSpeed * (1.0 - enemyDefinition.speedVariation / 2 + Math.random() * enemyDefinition.speedVariation); // <<< Use enemyDefinition
        // AI State - Use specific values from definition
        enemy.userData.timeSinceLastShot = Math.random() * (enemyDefinition.shootCooldown || 2.0); // Use definition, fallback
        enemy.userData.shootCooldown = enemyDefinition.shootCooldown || 2.0; // <<< Use enemyDefinition

        // Set melee damage for melee enemies
        if (enemyDefinition.meleeDamage) {
            enemy.userData.meleeDamage = enemyDefinition.meleeDamage;
        }
        enemy.userData.preferredRange = enemyDefinition.preferredRange || 8.0; // <<< Use enemyDefinition
        enemy.userData.moveAwayRange = enemyDefinition.moveAwayRange || 6.0; // <<< Use enemyDefinition
        // Dodge state (same initial values)
        enemy.userData.isDodging = false;
        enemy.userData.dodgeTimer = 0;
        enemy.userData.dodgeCooldownTimer = 0;
        // <<< ADD Dodge properties from definition >>>
        enemy.userData.dodgeTriggerDistance = enemyDefinition.dodgeTriggerDistance || DODGE_ACTIVATION_DISTANCE; // Use definition or constant
        enemy.userData.dodgeSpeed = enemyDefinition.dodgeSpeed || DODGE_SPEED;
        enemy.userData.dodgeDuration = enemyDefinition.dodgeDuration || DODGE_DURATION;
        enemy.userData.dodgeCooldown = enemyDefinition.dodgeCooldown || DODGE_COOLDOWN;

        enemy.userData.logHelperUpdate = true; // <<< Initialize flag for logging helper updates

        // --- Create Rotation Helpers for the enemy ---
        enemy.userData.boneHelpers = {}; // Initialize helper map
        const enemyBonesToVisualize = {};
        // Find bones (adjust names based on actual skeleton model)
        console.log(`[SpawnEnemy ${enemy.name}] Traversing to find bones for helpers...`); // <<< Log start
        enemy.traverse((child) => { // <<< Traverse the created enemy mesh/group
            // <<< Refined Bone Name Check >>>
            const boneNameCheck = child.name.toLowerCase();


            if (child.isObject3D && (boneNameCheck.includes('arm') || boneNameCheck.includes('leg') || boneNameCheck === 'head' || boneNameCheck === 'body' || boneNameCheck.includes('foot'))) {
                let boneName = child.name;
                console.log(`  - Found potential enemy bone: '${boneName}' (checking name: '${boneNameCheck}')`); // <<< Log potential bone name
                enemyBonesToVisualize[boneName] = child;
            }
        });

        console.log(`[SpawnEnemy ${enemy.name}] Final Bones selected for helpers:`, Object.keys(enemyBonesToVisualize)); // <<< Log final selected bones

        console.log(`[SpawnEnemy ${enemy.name}] --- Entering loop to create helpers ---`); // <<< ADD LOG BEFORE LOOP
        for (const name in enemyBonesToVisualize) {
             console.log(`[SpawnEnemy ${enemy.name}]   Processing bone: '${name}'`);
            const bone = enemyBonesToVisualize[name];
            if (bone) {
                // <<< REVERT TO AxesHelper >>>
                const axesHelper = new THREE.AxesHelper(AXES_HELPER_SIZE); // Use constant size
                axesHelper.visible = this.isEspEnabled; // Only visible when ESP is enabled

                // <<< NEW: Add Bone Box Helper >>>
                const boneBoxHelper = new THREE.BoxHelper(bone, 0xff00ff); // Magenta
                boneBoxHelper.matrixAutoUpdate = false;
                boneBoxHelper.visible = this.isEspEnabled; // Only visible when ESP is enabled
                // <<< END NEW >>>

                // <<< Update variable names and visibility logic >>>
                this.enemyRotationHelpersGroup.add(axesHelper); // Add axesHelper to group
                this.enemyRotationHelpersGroup.add(boneBoxHelper); // <<< ADD boneBoxHelper to group
                // Store both helpers
                enemy.userData.boneHelpers[name] = {
                    axes: axesHelper,
                    box: boneBoxHelper
                };
                console.log(`    -> Added AxesHelper and BoxHelper for bone: ${name}`); // <<< Updated log
                // <<< END Update >>>
            } else {
                 console.warn(`Enemy bone '${name}' somehow evaluated as null/undefined after traverse.`);
            }
        }
        console.log(`[SpawnEnemy ${enemy.name}] --- Exited loop for creating helpers ---`);
        // -------------------------------------------

        // --- NEW: Add Overall Bounding Box Helper ---
        const overallBoxHelper = new THREE.BoxHelper(enemy, 0x00ffff); // Cyan color
        overallBoxHelper.matrixAutoUpdate = false; // Update manually or rely on enemy matrix
        overallBoxHelper.visible = this.isEspEnabled; // Only visible when ESP is enabled
        enemy.userData.boundingBoxHelper = overallBoxHelper; // Store reference
        this.enemyRotationHelpersGroup.add(overallBoxHelper); // Add to the same group
        console.log(`[SpawnEnemy ${enemy.name}] Added overall BoxHelper.`);
        // --- END NEW ---

        // --- NEW: Add Skeleton Hierarchy Lines ---
        const lineMaterial = new THREE.LineBasicMaterial({ color: 0xffffff, linewidth: 1 }); // White lines
        const lineGeometry = new THREE.BufferGeometry();
        const skeletonLines = new THREE.LineSegments(lineGeometry, lineMaterial);
        skeletonLines.visible = this.isEspEnabled; // Only visible when ESP is enabled
        enemy.userData.skeletonLines = skeletonLines; // Store reference
        this.enemyRotationHelpersGroup.add(skeletonLines); // Add to group
        console.log(`[SpawnEnemy ${enemy.name}] Initialized SkeletonLines object.`);
        // --- END NEW ---

        // --- Create AI Brain ---
        enemy.userData.aiBrain = createAIBrain(enemy, enemy.userData, this.scene, this.player, enemyDefinition.difficulty || 1);

        // --- Set up event handlers for AI actions ---
        enemy.userData.onShoot = (direction, projectileCount = 1) => {
            this._handleEnemyShoot(enemy, direction, projectileCount);
        };

        enemy.userData.onMeleeAttack = (attackHitbox) => {
            this._handleEnemyMeleeAttack(enemy, attackHitbox);
        };

        enemy.userData.onPhaseChange = (phase) => {
            this._handleEnemyPhaseChange(enemy, phase);
        };

        enemy.userData.takeDamage = (damage) => {
            this._handleEnemyTakeDamage(enemy, damage);
        };

        // --- Initialize Enemy State ---
        // Ensure enemy.userData.id exists before setting state
        if (enemy.userData && enemy.userData.id) {
            this.enemyStateTimers.set(enemy.userData.id, { state: AIStates.IDLE, timer: 0 });
        } else {
            console.error(`[SpawnEnemy] Failed to set initial state: Enemy userData or id missing!`, enemy);
        }

        this.scene.add(enemy);
        this.activeEnemies.push(enemy);

        // If this is a boss, add to active bosses list and initialize timeline
        if (isBoss) {
            this.activeBosses.push(enemy);
            console.log(`[${enemy.name}] Added to activeBosses. Initializing boss timeline.`);

            // Enable music reactive flag for boss
            if (enemy.userData) {
                enemy.userData.musicReactive = true;
            }

            // --- Initialize Boss Timeline ---
            this.bossFightStartTime = performance.now();
            this.bossFightElapsedTime = 0;
            this.bossTimeline = []; // Clear any previous timeline

            // TODO: Define the actual boss timeline events here!
            // Example placeholder event:
            this.bossTimeline.push({
                id: 'boss_phase_1_spawn_projectile',
                time: 5, // Time in seconds
                action: (handler, boss) => {
                    console.log("Timeline Event: Spawning a projectile after 5 seconds!");
                    if (boss && handler.player) {
                        const direction = handler.player.position.clone().sub(boss.position).normalize();
                        // Use handler's spawnEnemyProjectile method
                        handler.spawnEnemyProjectile(boss.position, direction, getEnemyData(boss.userData.type));
                    }
                },
                triggered: false
            });
            // Add another example event at 15 seconds
            this.bossTimeline.push({
                id: 'boss_phase_1_charge_attack',
                time: 15, // Time in seconds
                action: (handler, boss) => {
                    console.log("Timeline Event: Triggering Charge Attack after 15 seconds!");
                    if (boss && boss.userData && boss.userData.aiBrain && typeof boss.userData.aiBrain.triggerCharge === 'function') {
                        // Example: Call a specific function on the boss's AI brain
                        boss.userData.aiBrain.triggerCharge();
                    } else {
                        console.warn("Boss AI Brain or triggerCharge function not found for timeline event.");
                    }
                },
                triggered: false
            });
            // ---------------------------------

        }
    }
    // --- End Enemy Spawn Helper ---

    // Method to display area name with fade effect
    _displayAreaName(name) {
        if (!this.areaNameElement) {
             console.error("_displayAreaName: Element not found! Should have been found in init.");
             return;
        }

        // NEW: Check for dark room flag on current room data
        let displayName = name;
        const currentRoom = this.floorLayout.get(this.currentRoomId);
        if (currentRoom && currentRoom.isDark) {
            displayName += " (Dark Room)";
        }
        // END NEW

        console.log(`Displaying area name: "${displayName}"`); // Use potentially modified name

        this.areaNameElement.textContent = displayName; // Use potentially modified name

        // --- Restore FADE LOGIC ---
        // Clear any existing fade-out timer
        if (this.areaNameFadeTimeout) {
            clearTimeout(this.areaNameFadeTimeout);
            this.areaNameFadeTimeout = null;
        }

        // Make sure element is ready for fade-in
        this.areaNameElement.style.visibility = 'visible';
        this.areaNameElement.style.opacity = '0'; // Ensure opacity starts at 0 before fade

        // Use a minimal timeout/rAF to allow the initial opacity=0 to render before starting transition
        requestAnimationFrame(() => {
            requestAnimationFrame(() => { // Double rAF for extra safety?
                if (!this.areaNameElement) return; // Check again before setting opacity
                this.areaNameElement.style.opacity = '1';
                console.log("Area name: Fade-in started (opacity -> 1)");
            });
        });

        // Set timer to fade out after 2 seconds (relative to when fade-in *starts*)
        const displayDuration = 2000; // 2 seconds display time
        const fadeDuration = 500; // 0.5 seconds (should match CSS transition)

        this.areaNameFadeTimeout = setTimeout(() => {
            if (!this.areaNameElement) return; // Check element still exists
            console.log("Area name: Fade-out starting (opacity -> 0)");
            this.areaNameElement.style.opacity = '0';
             // Set visibility to hidden after transition ends
             setTimeout(() => {
                 if (this.areaNameElement && this.areaNameElement.style.opacity === '0') { // Check if still faded out
                     this.areaNameElement.style.visibility = 'hidden';
                     console.log("Area name: Visibility hidden after fade-out.");
                 }
             }, fadeDuration);
            this.areaNameFadeTimeout = null; // Clear the timeout ID
        }, displayDuration); // Start fade-out after displayDuration
       // --- END RESTORE ---
    }

    _updateTotalRoomCountDisplay(isEspActive) {
        if (!this.totalRoomCountElement || !this.floorLayout) return;

        if (isEspActive) {
            const totalRooms = this.floorLayout.size;
            this.totalRoomCountElement.textContent = `Total Rooms: ${totalRooms}`;
            this.totalRoomCountElement.style.opacity = '1';
            this.totalRoomCountElement.style.visibility = 'visible';
        } else {
            this.totalRoomCountElement.style.opacity = '0';
            this.totalRoomCountElement.style.visibility = 'hidden';
        }
    }

    _handleEnemyDeath(deadEnemy, index) {
        console.log(`Handling death for enemy: ${deadEnemy.name}`);

        // Update music system when an enemy is killed
        if (this.audioManager && this.audioManager.musicConductor) {
            // Count remaining enemies in the current room (excluding the one being killed)
            const remainingEnemyCount = this.activeEnemies.filter(enemy =>
                enemy.userData.inCurrentRoom && enemy !== deadEnemy
            ).length;

            // Get player health
            const currentHealth = this.playerController ? this.playerController.currentHealth : 1;
            const maxHealth = this.playerController ? this.playerController.maxHealth : 1;

            // Update music system with enemy killed flag
            this.audioManager.updateCombatState(
                remainingEnemyCount,
                currentHealth,
                maxHealth,
                true,  // enemyKilled
                false  // enteredRoom
            );
        }

        // Determine enemy type and set appropriate group names
        const enemyType = deadEnemy.userData?.type || 'skeleton_archer';
        let groupNames;

        if (enemyType === 'bat') {
            // Bat-specific group names
            groupNames = ['body', 'leftWing', 'rightWing'];
        } else {
            // Default skeleton group names
            groupNames = ['head', 'core', 'leftArm', 'rightArm', 'leftLeg', 'rightLeg'];
        }

        // Set destruction parameters based on enemy type
        const destructionParams = this._getDestructionParamsForEnemy(enemyType);

        const explosionForce = destructionParams.explosionForce || 1.8;
        const upwardForce = destructionParams.upwardForce || 1.5;
        const tumbleSpeed = destructionParams.tumbleSpeed || 2.0;
        const debrisPerGroup = destructionParams.fallbackDebrisPerGroup || 3;
        const debrisExplosionForce = destructionParams.fallbackDebrisExplosionForce || 1.5;
        const debrisUpwardForce = destructionParams.fallbackDebrisUpwardForce || 1.2;
        const debrisTumbleSpeed = destructionParams.fallbackDebrisTumbleSpeed || 2.5;

        // Track creation time for bone pieces
        const creationTime = this.sceneManager.clock.getElapsedTime();

        // --- CRITICAL FIX: Get enemy data for soul orb drop with floor validation ---
        const enemyData = deadEnemy.userData;
        // Use the already declared enemyType variable
        let enemyPosition = deadEnemy.position.clone();

        // Validate that the enemy position has valid floor beneath it
        if (!this._hasValidFloor(enemyPosition)) {
            console.warn("[SoulOrb] Enemy died on invalid floor, finding nearest valid position");
            // Find the nearest valid spawn position for the soul orb
            const nearestValidPos = this._findNearestValidFloorPosition(enemyPosition);
            if (nearestValidPos) {
                enemyPosition = nearestValidPos;
                console.log(`[SoulOrb] Using nearest valid floor position: (${enemyPosition.x.toFixed(2)}, ${enemyPosition.y.toFixed(2)}, ${enemyPosition.z.toFixed(2)})`);
            } else {
                console.warn("[SoulOrb] No valid floor position found for soul orb, using original position");
            }
        }

        enemyPosition.y = 0.5; // Adjust height for soul orb drop

        // Roll for soul orb drop
        simpleSoulDropManager.rollForDrop(
            enemyData.type || 'default',
            this.currentRoom?.type || 'normal',
            enemyPosition,
            this.scene
        );

        // <<< Define debris geometry (Use standard VOXEL_SIZE) >>>
        const debrisGeo = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);
        // FIXED: Deterministic bone material selection with persistent material cache
        const boneColors = [0xF8E8C8, 0xE0C8A0, 0xC8A078, 0x987858];

        // Use cached materials or create new ones if they don't exist
        if (!this.cachedBoneMaterials) {
            this.cachedBoneMaterials = boneColors.map(color => new THREE.MeshLambertMaterial({ color }));
            console.log(`[MaterialCache] Created ${this.cachedBoneMaterials.length} cached bone materials`);
        }
        const tempBoneMaterials = this.cachedBoneMaterials;

        // FIXED: Deterministic material selection based on debris ID/position
        const getDeterministicBoneMat = (seed) => {
            // Create a simple hash from the seed to get consistent material selection
            let hash = 0;
            const seedStr = String(seed);
            for (let i = 0; i < seedStr.length; i++) {
                const char = seedStr.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }
            const index = Math.abs(hash) % tempBoneMaterials.length;
            const selectedMaterial = tempBoneMaterials[index];
            console.log(`[DeterministicMaterial] Seed: ${seed}, Hash: ${hash}, Index: ${index}, Color: 0x${selectedMaterial.color.getHex().toString(16).padStart(6, '0')}, MaterialID: ${selectedMaterial.id}`);

            // VERIFICATION: Test hash consistency
            if (seed === `debris_${deadEnemy.userData.id}_${groupNames[0]}_0_${creationTime}`) {
                console.log(`[HashVerification] Testing consistency for first debris piece...`);
                const testHash1 = this._calculateDebrisHash(seed);
                const testHash2 = this._calculateDebrisHash(seed);
                console.log(`[HashVerification] Hash1: ${testHash1}, Hash2: ${testHash2}, Consistent: ${testHash1 === testHash2}`);
            }

            return selectedMaterial;
        };
        // <<< ----------------------------- >>>

        // Update world matrices to get latest positions
        deadEnemy.updateMatrixWorld(true);

        groupNames.forEach(groupName => {
            const originalGroup = deadEnemy.getObjectByName(groupName);
            if (!originalGroup) {
                console.warn(`Could not find group "${groupName}" on dead enemy.`);
                return;
            }

            // Important: Get world position/rotation of the GROUP before processing children
            const groupWorldPos = new THREE.Vector3();
            const groupWorldQuat = new THREE.Quaternion();
            originalGroup.getWorldPosition(groupWorldPos);
            originalGroup.getWorldQuaternion(groupWorldQuat);

            originalGroup.children.forEach(originalMesh => {
                if (originalMesh.isMesh) {
                    console.log(`[Death] Found mesh in group ${groupName}:`, originalMesh.name || originalMesh.uuid);
                    const pieceMesh = originalMesh.clone(); // Clone the mesh
                    // <<< Explicitly clone material >>>
                    if (originalMesh.material) {
                        pieceMesh.material = originalMesh.material.clone();
                    }
                    // <<< -------------------------- >>>
                    pieceMesh.name = `bonePiece_${groupName}_${Date.now()}`;

                    // Apply the original mesh's world position/rotation/scale to the clone
                    const originalMeshWorldPos = new THREE.Vector3();
                    const originalMeshWorldQuat = new THREE.Quaternion();
                    const originalMeshWorldScale = new THREE.Vector3(); // <<< Get scale too
                    originalMesh.matrixWorld.decompose(originalMeshWorldPos, originalMeshWorldQuat, originalMeshWorldScale); // <<< Decompose matrixWorld

                    pieceMesh.position.copy(originalMeshWorldPos);
                    pieceMesh.quaternion.copy(originalMeshWorldQuat);
                    pieceMesh.scale.copy(originalMeshWorldScale); // <<< Apply scale explicitly

                    console.log(`[Death] Cloning ${groupName} part. World Pos:`, originalMeshWorldPos);
                    console.log(`[Death] Cloned piece scale:`, pieceMesh.scale); // <<< Log scale

                    // --- Restore Original Physics Force for LARGE pieces ---
                    // const debugExplosionForce = 0.2;
                    // const debugUpwardForce = 0.5;
                    // const debugTumbleSpeed = 0.5;
                    // Using the main constants defined at the start of _handleEnemyDeath
                    // (explosionForce, upwardForce, tumbleSpeed)
                    // --- ----------------------------------------------------

                    // --- Physics ---
                    const direction = new THREE.Vector3(
                        Math.random() - 0.5,
                        0.2 + Math.random() * 0.4, // Reduced upward bias (was 0.4 + rnd*0.6)
                        Math.random() - 0.5
                    ).normalize();
                    // <<< USE ORIGINAL FORCES (defined at function start) >>>
                    const velocity = direction.multiplyScalar(explosionForce + (Math.random() - 0.5) * 0.4);
                    velocity.y += upwardForce * (0.6 + Math.random() * 0.4); // Reduced initial upward boost slightly (was 0.8 + rnd*0.4)

                    const angularVelocity = new THREE.Vector3(
                        (Math.random() - 0.5) * tumbleSpeed * 1.2, // Increased tumble slightly
                        (Math.random() - 0.5) * tumbleSpeed * 1.2,
                        (Math.random() - 0.5) * tumbleSpeed * 1.2
                    );

                    // Add to scene and tracked pieces
                    this.scene.add(pieceMesh); // <<< ADD BACK TO SCENE
                    this.currentRoomObjects.push(pieceMesh); // <<< ADD to currentRoomObjects

                    // Store physics data in userData
                    const bodyPartId = `bodyPart_${deadEnemy.userData.id}_${groupName}_${Date.now()}`;
                    pieceMesh.userData = {
                        isBonePiece: true,
                        isDebrisPiece: true, // Mark as debris for persistence
                        debrisId: bodyPartId,
                        sourceEnemyId: deadEnemy.userData.id,
                        sourceRoomId: this.currentRoomId,
                        bodyPartType: groupName, // Store which body part this is
                        velocity: velocity,
                        angularVelocity: angularVelocity,
                        isFalling: true,
                        creationTime: creationTime, // Track when this piece was created
                        persistUntilRoomChange: true // Prevent automatic cleanup
                    };

                    // Store body part in room state for persistence
                    this._storeDebrisInRoomState(bodyPartId, pieceMesh);
                    // this.activeBonePieces.push({...}); // <<< REMOVE
                }
            }); // <<< End of originalGroup.children.forEach loop

            // --- FIXED: Spawn Small Debris Pieces with deterministic materials ---
            for (let i = 0; i < debrisPerGroup; i++) {
                // FIXED: Create deterministic debris ID for consistent material selection
                const debrisId = `debris_${deadEnemy.userData.id}_${groupName}_${i}_${creationTime}`;
                const debrisMaterial = getDeterministicBoneMat(debrisId);
                console.log(`[DebrisCreation] Created debris ${debrisId} with material color: 0x${debrisMaterial.color.getHex().toString(16).padStart(6, '0')}, MaterialID: ${debrisMaterial.id}`);
                const debrisMesh = new THREE.Mesh(debrisGeo, debrisMaterial);
                debrisMesh.name = `debris_${groupName}_${i}_${Date.now()}`;

                // Start at the group's world position + small random offset
                const randomOffset = new THREE.Vector3(
                    (Math.random() - 0.5) * VOXEL_SIZE * 3,
                    (Math.random() - 0.5) * VOXEL_SIZE * 3,
                    (Math.random() - 0.5) * VOXEL_SIZE * 3
                );
                debrisMesh.position.copy(groupWorldPos).add(randomOffset);
                debrisMesh.quaternion.copy(groupWorldQuat); // Initial rotation same as group

                // Physics for debris
                const debrisDirection = new THREE.Vector3(
                    Math.random() - 0.5,
                    0.4 + Math.random() * 0.6, // Bias upwards slightly less?
                    Math.random() - 0.5
                ).normalize();
                const debrisVelocity = debrisDirection.multiplyScalar(debrisExplosionForce + (Math.random() - 0.5) * 0.4);
                debrisVelocity.y += debrisUpwardForce * (0.7 + Math.random() * 0.6);

                const debrisAngularVelocity = new THREE.Vector3(
                    (Math.random() - 0.5) * debrisTumbleSpeed,
                    (Math.random() - 0.5) * debrisTumbleSpeed,
                    (Math.random() - 0.5) * debrisTumbleSpeed
                );

                // Add to scene and tracked pieces
                this.scene.add(debrisMesh); // <<< ADD BACK TO SCENE
                this.currentRoomObjects.push(debrisMesh); // <<< ADD to currentRoomObjects

                // Store physics data in userData
                debrisMesh.userData = {
                    isBonePiece: true,
                    isDebrisPiece: true,
                    debrisId: debrisId,
                    sourceEnemyId: deadEnemy.userData.id,
                    sourceRoomId: this.currentRoomId,
                    velocity: debrisVelocity,
                    angularVelocity: debrisAngularVelocity,
                    isFalling: true,
                    creationTime: creationTime, // Track when this piece was created
                    persistUntilRoomChange: true, // Prevent automatic cleanup
                    // FIXED: Store material info for deterministic restoration
                    materialSeed: debrisId // Store the seed used for material selection
                };

                // Store debris in room state for persistence
                this._storeDebrisInRoomState(debrisId, debrisMesh);
                // this.activeBonePieces.push({...}); // <<< REMOVE
            }
            // --- END Spawn Small Debris Pieces ---

        }); // <<< End of groupNames.forEach loop

        // --- Original Enemy Cleanup ---
        // <<< AGGRESSIVE CLEANUP >>>
        console.log(`[Death] Applying aggressive cleanup for: ${deadEnemy.name}`);
        deadEnemy.traverse(child => {
            if (child.isMesh) {
                if (child.geometry) child.geometry.dispose();
                // Material disposal might be tricky if shared, but let's try
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(mat => { if(mat && mat.dispose) mat.dispose(); });
                    } else if (child.material.dispose) {
                        child.material.dispose();
                    }
                }
            }
        });
        deadEnemy.removeFromParent(); // Explicitly remove from parent
        // <<< END AGGRESSIVE CLEANUP >>>

        // Remove from scene and lists AFTER aggressive cleanup
        this.scene.remove(deadEnemy); // Remove original enemy group
        this.activeEnemies.splice(index, 1); // Remove from active list
        this.enemyStateTimers.delete(deadEnemy.userData.id); // Remove state timer
        // Note: collisionObjects handling might need refinement if pieces should collide

        // --- Stage Complete Check (Boss Defeated) ---
        const currentRoomData = this.floorLayout.get(this.currentRoomId);
        const isBossRoom = currentRoomData?.type === 'Boss';
        const defeatedEnemyAiType = deadEnemy.userData?.aiType;
        const isBossDefeated = defeatedEnemyAiType === AI_BRAIN_TYPES.BOSS;

        // Remove from active bosses list if it was a boss
        if (isBossDefeated) {
            const bossIndex = this.activeBosses.findIndex(boss => boss === deadEnemy);
            if (bossIndex !== -1) {
                this.activeBosses.splice(bossIndex, 1);
            }

            // If no more bosses, stop boss music
            if (this.activeBosses.length === 0 && this.bossMusicPlaying) {
                this.bossMusicPlaying = false;

                // Transition back to regular music
                if (this.audioManager && this.audioManager.musicConductor) {
                    console.log("Boss defeated - transitioning back to regular music");
                    this.audioManager.startAreaMusic(this.previousAreaId || 'catacombs');
                }
            }
        }

        const noEnemiesRemain = this.activeEnemies.length === 0;

        if (isBossRoom && isBossDefeated && noEnemiesRemain) {
            console.log("!!! BOSS DEFEATED - STAGE COMPLETE !!!");
            this._handleStageComplete(); // Call new function
        } else if (this.activeEnemies.length === 0 && currentRoomData && !isBossRoom) {
            // Optionally, still mark non-boss rooms as cleared for other purposes (like preventing respawns)
            // but don't trigger the main stage complete logic.
            if (!currentRoomData.state.enemiesCleared) { // Only log/set if not already cleared
                console.log(`--- Room ${this.currentRoomId} cleared of normal enemies ---`);
                currentRoomData.state.enemiesCleared = true;
            }
        }
        // --- END STAGE COMPLETE CHECK ---

        // <<< Dispose debris geometry >>>
        debrisGeo.dispose();
        // CRITICAL FIX: DO NOT dispose bone materials - they need to persist for visual consistency
        // tempBoneMaterials.forEach(mat => mat.dispose()); // REMOVED - materials must persist

        // --- Handle Item Drop ---
        // Use the currentRoomData variable that's already declared above
        if (currentRoomData) {
            // Get room type and biome for drop context
            const roomType = currentRoomData.type.toLowerCase() || 'normal';
            const biome = currentRoomData.areaId || 'catacombs';

            // Determine enemy tier for drop system
            let enemyTier = 'normal';
            if (enemyData?.aiType === AI_BRAIN_TYPES.BOSS) {
                enemyTier = 'boss';
            } else if (enemyData?.difficulty >= 3) {
                enemyTier = 'mini_boss';
            } else if (enemyData?.difficulty >= 2) {
                enemyTier = 'elite';
            }

            // Generate a seed based on enemy ID and room ID for deterministic drops
            const dropSeed = (enemyData?.id || 0) * 1000 + (this.currentRoomId || 0);

            // Get a random item drop
            const itemType = itemDropManager.getRandomDrop(enemyTier, roomType, biome, dropSeed);

            // If an item was selected, spawn it
            if (itemType) {
                console.log(`Spawning item drop: ${itemType} at position:`, enemyPosition);
                const item = itemDropManager.spawnVoxelItem(this.scene, itemType, enemyPosition);

                // Add the item to the active items list
                if (item) {
                    if (!this.activeItems) this.activeItems = [];
                    this.activeItems.push(item);
                }
            }

            // --- Roll for soul orb drop ---
            // Use the already declared enemyType variable from the beginning of the function
            simpleSoulDropManager.rollForDrop(enemyType, roomType, enemyPosition, this.scene);
        }
    }

    // --- NEW Placeholder Function ---
    _handleStageComplete() {
        console.log("[DungeonHandler] _handleStageComplete called. Implement final logic here (e.g., unlock exit, win screen).");
        // TODO: Add logic like:
        // - Unlock final exit door
        // - Display victory message
        // - Transition to next level or win screen (e.g., this.sceneManager.changeState(STATE.WIN_SCREEN))
    }
    // --- End Placeholder ---

    /**
     * Apply camera shake effect
     * @param {Number} intensity - Shake intensity (0-1)
     * @param {Number} duration - Shake duration in milliseconds
     */
    cameraShake(intensity, duration) {
        // Reduce the intensity by half as requested
        intensity = intensity * 0.5;

        if (!this.camera) return;

        // Track when the last shake occurred to prevent too frequent shaking
        const now = Date.now();
        if (!this._lastShakeTime) {
            this._lastShakeTime = 0;
        }

        // Enforce a minimum time between shakes (1 second)
        // This ensures shakes only happen on significant pattern changes
        const minTimeBetweenShakes = 1000; // 1 second
        if (now - this._lastShakeTime < minTimeBetweenShakes) {
            console.log("[DungeonHandler] Skipping camera shake - too soon after previous shake");
            return;
        }

        // Update last shake time
        this._lastShakeTime = now;

        // Store original camera position if not already stored
        if (!this._originalCameraPosition) {
            this._originalCameraPosition = this.camera.position.clone();
        }

        // Create a shake animation
        const startTime = now;
        const endTime = startTime + duration;

        // Clear any existing shake interval
        if (this._shakeInterval) {
            clearInterval(this._shakeInterval);
        }

        // Set up shake interval
        this._shakeInterval = setInterval(() => {
            const currentTime = Date.now();

            // Check if shake should end
            if (currentTime >= endTime) {
                clearInterval(this._shakeInterval);
                this._shakeInterval = null;

                // Reset camera position
                if (this._originalCameraPosition) {
                    this.camera.position.copy(this._originalCameraPosition);
                }
                return;
            }

            // Calculate remaining shake intensity (fade out toward the end)
            const progress = (currentTime - startTime) / duration;
            const remainingIntensity = intensity * (1 - progress);

            // Apply random offset to camera position
            if (this._originalCameraPosition) {
                const offsetX = (Math.random() * 2 - 1) * remainingIntensity;
                const offsetY = (Math.random() * 2 - 1) * remainingIntensity * 0.5; // Less vertical shake
                const offsetZ = (Math.random() * 2 - 1) * remainingIntensity;

                this.camera.position.set(
                    this._originalCameraPosition.x + offsetX,
                    this._originalCameraPosition.y + offsetY,
                    this._originalCameraPosition.z + offsetZ
                );
            }
        }, 16); // ~60fps
    }

    // Method called by PlayerController to toggle the view
    toggleCameraView() {
        this.isTopDownView = !this.isTopDownView;
        console.log(`Camera view toggled. Top-down: ${this.isTopDownView}`);

        // Immediately update camera position based on the new view
        if (this.player) {
            const targetPosition = new THREE.Vector3();

            if (this.isTopDownView) {
                // Top-down view: Position camera directly above player
                targetPosition.set(
                    this.player.position.x,
                    this.topDownCameraOffset.y, // Use the defined height for consistency
                    this.player.position.z
                );

                // Set camera position directly
                this.camera.position.copy(targetPosition);

                // Set rotation to look straight down
                this.camera.rotation.x = -Math.PI/2;
                this.camera.rotation.y = 0;
                this.camera.rotation.z = 0;

                // Set the correct up vector for top-down view
                this.camera.up.set(0, 0, -1);

                // Set zoom for top-down view
                this.currentZoom = 0.9;
                this.camera.zoom = this.currentZoom;
            } else {
                // Normal view: Position camera at an angle behind player
                targetPosition.set(
                    this.player.position.x,
                    this.originalCameraOffset.y,
                    this.player.position.z + this.originalCameraOffset.z
                );
                // Keep standard up vector
                this.camera.up.set(0, 1, 0);
                // Look at player from behind
                this.camera.position.copy(targetPosition);
                this.camera.lookAt(new THREE.Vector3(this.player.position.x, 0, this.player.position.z));

                // Set zoom for normal view
                this.currentZoom = this.defaultZoom;
                this.camera.zoom = this.currentZoom;
            }
        }

        // Force update the projection matrix
        this.camera.updateProjectionMatrix();

        // Force the CRT effect to recognize the camera change
        if (this.sceneManager && this.sceneManager.crtEffect && this.sceneManager.crtEffect.manager) {
            this.sceneManager.crtEffect.manager.updateResolution();
        }
    }

    // <<< NEW method called by PlayerController >>>
    toggleDebugLight() {
        console.log("[DungeonHandler] toggleDebugLight called.");
        if (this.debugGlobalLight) {
            this.isDebugLightOn = !this.isDebugLightOn;
            this.debugGlobalLight.visible = this.isDebugLightOn;
            console.log(`   -> Debug Global Light Toggled: ${this.isDebugLightOn ? 'ON' : 'OFF'}`);
        } else {
            console.warn("   -> Debug Global Light object not found!");
        }
    }

    // --- Add method to set ESP rotation view state ---
    setEspRotationViewActive(isActive) {
        // <<< ADD LOGGING >>>
        console.log(`[DungeonHandler] setEspRotationViewActive called with: ${isActive}`);
        // <<< END LOGGING >>>
        this.isEspRotationViewActive = isActive;
        if (this.enemyRotationHelpersGroup) {
            this.enemyRotationHelpersGroup.visible = isActive;
            // <<< ADD LOGGING >>>
            console.log(`   -> Enemy rotation helpers group visibility set to: ${this.enemyRotationHelpersGroup.visible}`);
            // <<< END LOGGING >>>
        }
    }
    // -------------------------------------------------

    // --- Add method to update enemy rotation helpers transforms ---
    _updateEnemyRotationHelpers() {
        // ... (Entry log remains) ...
        if (!this.isEspRotationViewActive) {
             return;
        }
        // ... (Group check remains) ...
        if (!this.enemyRotationHelpersGroup) {
             return;
        }

        // ... (Counters, temp vars remain) ...
        let logCounter = 0;
        const maxLogsPerFrame = 1; // Log just the first one again now we know the issue
        const worldPosition = new THREE.Vector3();
        const worldQuaternion = new THREE.Quaternion();

        this.activeEnemies.forEach(enemy => {
            if (!enemy || !enemy.userData.boneHelpers) {
                 // if (logCounter < maxLogsPerFrame) console.log(`[UpdateEnemyHelpers] Skipping enemy ${enemy?.name || enemy?.uuid} - no boneHelpers.`); // Reduce spam
                 return;
            }

            // Skip enemies that are not visible (if frustum culling is enabled)
            if (this.sceneManager.enableFrustumCulling && !enemy.userData.isVisible) {
                 return;
            }

            // <<< Update Enemy World Matrix FIRST >>>
            enemy.updateMatrixWorld(true);

            // <<< Update Overall Bounding Box Helper >>>
            if (enemy.userData.boundingBoxHelper) {
                enemy.userData.boundingBoxHelper.update();
            }
            // <<< END Overall Box Update >>>

            // --- Update Skeleton Hierarchy Lines ---
            if (enemy.userData.skeletonLines) {
                const skeletonLines = enemy.userData.skeletonLines;
                const geometry = skeletonLines.geometry;
                const lineVertices = [];
                const processedBones = new Set(); // Avoid duplicate lines
                let foundParentChildPair = false; // <<< ADD Log flag



                enemy.traverse((child) => {
                    // Only process bones that have helpers
                    if (child.isBone && enemy.userData.boneHelpers[child.name]) {
                        const parent = child.parent;

                        // <<< MODIFY: Only require parent to be a Bone, not necessarily have a helper >>>
                        // Check if parent is also a bone (Removed check for parent helper: && enemy.userData.boneHelpers[parent.name])
                        if (parent && parent.isBone) {
                             // Create unique key for this connection to avoid duplicates
                             const key1 = `${parent.uuid}-${child.uuid}`;
                             const key2 = `${child.uuid}-${parent.uuid}`;

                             if (!processedBones.has(key1) && !processedBones.has(key2)) {
                                 // <<< Log modification >>>
                                 // console.log(`    [LineUpdate ${enemy.name}] FOUND connection: ${parent.name} (isBone) -> ${child.name} (has helper)`);
                                 foundParentChildPair = true; // <<< Set flag

                                 const childPos = new THREE.Vector3();
                                 const parentPos = new THREE.Vector3();
                                 // Ensure world matrices are up-to-date before getting world position
                                 child.updateWorldMatrix(true, false);
                                 parent.updateWorldMatrix(true, false);
                                 child.getWorldPosition(childPos);
                                 parent.getWorldPosition(parentPos);
                                 lineVertices.push(parentPos, childPos);
                                 processedBones.add(key1); // Mark connection as processed using UUIDs
                             }
                        }
                    }
                });

                // <<< ADD Log: Before setFromPoints >>>
                if (logCounter < maxLogsPerFrame) console.log(`[LineUpdate ${enemy.name}] Before setFromPoints. Vertices count: ${lineVertices.length}. Found Pair: ${foundParentChildPair}`);

                geometry.setFromPoints(lineVertices);
                geometry.computeBoundingSphere(); // Needed for frustum culling
            }
            // --- END Skeleton Lines Update ---

            for (const boneName in enemy.userData.boneHelpers) {
                 const shouldLog = false; // <<< Set to false to disable spam

                 // <<< Get helper data object >>>
                 const helperData = enemy.userData.boneHelpers[boneName];
                 const axesHelper = helperData?.axes;
                 const boxHelper = helperData?.box;
                 // <<< ---------------------- >>>

                 const bone = enemy.getObjectByName(boneName);

                 // <<< Check for bone and BOTH helpers >>>
                 if (bone && axesHelper && boxHelper &&
                     axesHelper instanceof THREE.AxesHelper &&
                     boxHelper instanceof THREE.BoxHelper) {
                 // <<< ------------------------------ >>>

                     // if (shouldLog) console.log(`      -> Bone & Helpers OK. Proceeding.`); // Commented out

                     // Update logic
                     bone.updateWorldMatrix(true, false);
                     bone.getWorldPosition(worldPosition);
                     bone.getWorldQuaternion(worldQuaternion);

                     // Update Axes Helper
                     axesHelper.position.copy(worldPosition);
                     axesHelper.quaternion.copy(worldQuaternion);

                     // Update Bone Box Helper
                     boxHelper.update();

                     // if (shouldLog) console.log(`         -> Updated Helpers for bone: ${boneName}`); // Commented out
                     logCounter++;
                  } else if (shouldLog) {
                     // Log failure reasons
                     if (!bone) console.log(`      -> Condition FAILED: Bone '${boneName}' not found.`);
                     else if (!axesHelper) console.log(`      -> Condition FAILED: AxesHelper missing for bone '${boneName}'.`);
                     else if (!boxHelper) console.log(`      -> Condition FAILED: BoxHelper missing for bone '${boneName}'.`);
                     else if (!(axesHelper instanceof THREE.AxesHelper)) console.log(`      -> Condition FAILED: axesHelper is not AxesHelper.`);
                     else if (!(boxHelper instanceof THREE.BoxHelper)) console.log(`      -> Condition FAILED: boxHelper is not BoxHelper.`);
                     else console.log(`      -> Condition FAILED: Unknown reason.`);
                     logCounter++;
                  }
            }
        });
    }
    // --------------------------------------------------------

    // DEPRECATED: Old pre-generation system replaced by predictive pre-loading
    // This method is kept for compatibility but should not be used
    async _preGenerateAllRooms() {
        console.warn("[DungeonHandler] _preGenerateAllRooms is deprecated. Using predictive pre-loading instead.");
        // The new system automatically pre-loads rooms as needed
        return;
    }

    /**
     * Get destruction parameters for a specific enemy type
     * @param {string} enemyType - The type of enemy
     * @returns {Object} - Destruction parameters
     * @private
     */
    _getDestructionParamsForEnemy(enemyType) {
        // Default destruction parameters
        let destructionParams = {
            explosionForce: 1.2,
            upwardForce: 1.5,
            tumbleSpeed: 2.0,
            fallbackExplosionForce: 1.0,
            fallbackUpwardForce: 1.8,
            fallbackTumbleSpeed: 1.5,
            fallbackDebrisPerGroup: 6,
            fallbackDebrisExplosionForce: 1.0,
            fallbackDebrisUpwardForce: 1.5,
            fallbackDebrisTumbleSpeed: 1.8,
            debrisLifeTime: [3, 5]
        };

        // Set specific parameters based on enemy type
        switch(enemyType) {
            case 'bat':
                destructionParams = {
                    explosionForce: 2.0,  // Higher force for dramatic effect
                    upwardForce: 2.5,    // Higher upward force
                    tumbleSpeed: 3.5,    // Faster tumbling
                    fallbackExplosionForce: 1.8,
                    fallbackUpwardForce: 3.0,
                    fallbackTumbleSpeed: 3.0,
                    fallbackDebrisPerGroup: 8,  // Fewer but larger debris pieces
                    fallbackDebrisExplosionForce: 1.7,
                    fallbackDebrisUpwardForce: 2.5,
                    fallbackDebrisTumbleSpeed: 3.2,
                    debrisLifeTime: [3, 6]  // Slightly shorter lifetime than bones
                };
                break;
            case 'skeleton_archer':
            case 'skeleton':
                destructionParams = {
                    explosionForce: 1.8,  // Keep high force for dramatic effect
                    upwardForce: 2.2,
                    tumbleSpeed: 3.0,
                    fallbackExplosionForce: 1.6,
                    fallbackUpwardForce: 2.8,
                    fallbackTumbleSpeed: 2.5,
                    fallbackDebrisPerGroup: 10,  // More bone fragments
                    fallbackDebrisExplosionForce: 1.5,
                    fallbackDebrisUpwardForce: 2.2,
                    fallbackDebrisTumbleSpeed: 2.8,
                    debrisLifeTime: [4, 7]  // Longer lifetime for bones
                };
                break;
        }

        return destructionParams;
    }

    _handleObjectDestruction(objectToDestroy, pointOfImpact = null, projectileVelocity = null) {
        console.log(`[_handleObjectDestruction] Handling destruction for: ${objectToDestroy.name || objectToDestroy.uuid}`);
        const objUserData = objectToDestroy.userData;

        if (!objUserData?.isDestructible) {
            console.warn(`Object ${objectToDestroy.name} is not marked as destructible (isDestructible check).`);
            return;
        }

        // --- Object-Specific Destruction Parameters ---
        let destructionParams;
        const objectType = objUserData.objectType || 'default';

        switch(objectType) {
            case 'stone_vase':
                destructionParams = {
                    explosionForce: 0.5,  // Significantly reduced from 0.8
                    upwardForce: 0.8,     // Significantly reduced from 1.2
                    tumbleSpeed: 1.5,     // Slightly reduced from 2.0
                    fallbackExplosionForce: 0.4,  // Reduced from 0.7
                    fallbackUpwardForce: 0.6,     // Reduced from 1.5
                    fallbackTumbleSpeed: 1.2,     // Reduced from 1.5
                    fallbackDebrisPerGroup: 4,    // Fewer debris pieces (was 6)
                    fallbackDebrisExplosionForce: 0.5,  // Reduced from 0.8
                    fallbackDebrisUpwardForce: 0.8,     // Reduced from 1.2
                    fallbackDebrisTumbleSpeed: 1.5,     // Slightly reduced from 1.8
                    debrisLifeTime: [2, 4]  // Shorter lifetime [min, max] seconds (was [3, 5])
                };
                break;
            case 'skeleton':
                destructionParams = {
                    explosionForce: 1.8,  // Keep high force for dramatic effect
                    upwardForce: 2.2,
                    tumbleSpeed: 3.0,
                    fallbackExplosionForce: 1.6,
                    fallbackUpwardForce: 2.8,
                    fallbackTumbleSpeed: 2.5,
                    fallbackDebrisPerGroup: 10,  // More bone fragments
                    fallbackDebrisExplosionForce: 1.5,
                    fallbackDebrisUpwardForce: 2.2,
                    fallbackDebrisTumbleSpeed: 2.8,
                    debrisLifeTime: [4, 7]  // Longer lifetime for bones
                };
                break;
            case 'bat':
                destructionParams = {
                    explosionForce: 2.0,  // Higher force for dramatic effect
                    upwardForce: 2.5,    // Higher upward force
                    tumbleSpeed: 3.5,    // Faster tumbling
                    fallbackExplosionForce: 1.8,
                    fallbackUpwardForce: 3.0,
                    fallbackTumbleSpeed: 3.0,
                    fallbackDebrisPerGroup: 8,  // Fewer but larger debris pieces
                    fallbackDebrisExplosionForce: 1.7,
                    fallbackDebrisUpwardForce: 2.5,
                    fallbackDebrisTumbleSpeed: 3.2,
                    debrisLifeTime: [3, 6]  // Slightly shorter lifetime than bones
                };
                break;
            default:
                destructionParams = {
                    explosionForce: 1.2,      // Moderate default (was 1.5)
                    upwardForce: 1.5,         // Moderate default (was 2.0)
                    tumbleSpeed: 2.0,         // Moderate default (was 2.5)
                    fallbackExplosionForce: 1.0,
                    fallbackUpwardForce: 1.8,
                    fallbackTumbleSpeed: 1.5,
                    fallbackDebrisPerGroup: 6,
                    fallbackDebrisExplosionForce: 1.0,
                    fallbackDebrisUpwardForce: 1.5,
                    fallbackDebrisTumbleSpeed: 1.8,
                    debrisLifeTime: [3, 5]
                };
        }

        // --- Physics Parameters (Now using object-specific values) ---
        const explosionForce = destructionParams.explosionForce;
        const upwardForce = destructionParams.upwardForce;
        const tumbleSpeed = destructionParams.tumbleSpeed;

        // --- Update World Matrix of the original object ONCE ---
        objectToDestroy.updateMatrixWorld(true);
        const objectWorldMatrix = objectToDestroy.matrixWorld.clone(); // Store its world matrix
        const objectCenterPos = new THREE.Vector3();
        objectToDestroy.getWorldPosition(objectCenterPos); // Get world center for explosion origin

        // --- Method 1: True Voxel Destruction (if data available) ---
        if (objUserData.originalVoxels && objUserData.voxelScale) {
            console.log(`[Destruction] Using TRUE VOXEL destruction for ${objectToDestroy.name}. Count: ${objUserData.originalVoxels.length}`);
            const voxelScale = objUserData.voxelScale;
            const baseVoxelGeo = getOrCreateGeometry('destruction_voxel', () => new THREE.BoxGeometry(voxelScale, voxelScale, voxelScale));
            const tempVec = new THREE.Vector3();

            objUserData.originalVoxels.forEach((voxelData, index) => {
                // 1. Create the individual voxel mesh
                const material = _getMaterialByHex_Cached(voxelData.c); // Get material by original color
                if (!material) {
                    console.warn(`[VoxelDestruction] Could not get material for hex ${voxelData.c}`);
                    return; // Skip if material fails
                }
                const voxelMesh = new THREE.Mesh(baseVoxelGeo, material.clone()); // Use shared geo, cloned material
                voxelMesh.name = `voxelDebris_${objectToDestroy.name}_${index}_${Date.now()}`;
                voxelMesh.castShadow = true;
                voxelMesh.receiveShadow = true;

                // 2. Calculate Initial World Position
                // Start with relative position, scale it, then apply object's world matrix
                tempVec.set(voxelData.x * voxelScale, voxelData.y * voxelScale, voxelData.z * voxelScale);
                tempVec.applyMatrix4(objectWorldMatrix); // Transform relative pos to world pos
                voxelMesh.position.copy(tempVec);

                // Use original object's world rotation initially (can tumble from there)
                const worldQuat = new THREE.Quaternion();
                objectWorldMatrix.decompose(new THREE.Vector3(), worldQuat, new THREE.Vector3());
                voxelMesh.quaternion.copy(worldQuat);

                // 3. Apply Physics
                const explosionDir = voxelMesh.position.clone().sub(objectCenterPos).normalize(); // Base direction: outwards from center
                let finalDir = explosionDir.clone(); // Start with the base direction

                const impactBlendFactor = 0.4; // How much the impact point direction influences the explosion direction (0 to 1)
                const impactVelocityFactor = 0.3; // How much of the projectile's velocity is transferred (0 to 1)

                if (pointOfImpact) {
                    const impactDir = voxelMesh.position.clone().sub(pointOfImpact).normalize();
                    // Blend the base explosion direction with the direction away from the impact point
                    finalDir.lerp(impactDir, impactBlendFactor).normalize();
                }

                // Add some randomness/upward bias to the potentially blended direction
                finalDir.x += (Math.random() - 0.5) * 0.6;
                finalDir.y += 0.3 + Math.random() * 0.5; // Add upward bias
                finalDir.z += (Math.random() - 0.5) * 0.6;
                finalDir.normalize();

                // Calculate initial velocity based on the final direction and explosion force
                const velocity = finalDir.clone().multiplyScalar(explosionForce + (Math.random() - 0.5) * 1.0); // Add force variation
                velocity.y += upwardForce * (0.5 + Math.random() * 0.5); // Extra upward kick

                // Add influence from projectile velocity if available
                if (projectileVelocity) {
                    const projVelNorm = projectileVelocity.clone().normalize();
                    // Calculate how much the final explosion direction aligns with the projectile's direction
                    const dotProduct = Math.max(0, finalDir.dot(projVelNorm)); // Use max(0, ...) to prevent pull-back
                    // Add a portion of the projectile's velocity, scaled by alignment and factor
                    velocity.addScaledVector(projectileVelocity, dotProduct * impactVelocityFactor);
                }

                const angularVelocity = new THREE.Vector3(
                    (Math.random() - 0.5) * tumbleSpeed,
                    (Math.random() - 0.5) * tumbleSpeed,
                    (Math.random() - 0.5) * tumbleSpeed
                );

                // 4. Add to Scene and Simulation
                this.scene.add(voxelMesh);
                this.currentRoomObjects.push(voxelMesh); // Track for cleanup

                const debrisId = `voxelDebris_${objectToDestroy.name}_${index}_${Date.now()}`;
                voxelMesh.userData = {
                    isDebrisPiece: true, // Mark as debris for physics loop
                    debrisId: debrisId,
                    sourceObjectId: objectToDestroy.userData?.id || objectToDestroy.name,
                    sourceRoomId: this.currentRoomId,
                    velocity: velocity,
                    angularVelocity: angularVelocity,
                    isFalling: true,
                    persistUntilRoomChange: true // Persist until room change instead of lifeTime
                };

                // Store debris in room state for persistence
                this._storeDebrisInRoomState(debrisId, voxelMesh);
            });

             // Dispose the shared geometry AFTER the loop
             baseVoxelGeo.dispose();

        } else {
            // --- Method 2: Fallback - Clone Parts + Generic Debris (Previous Logic) ---
            console.warn(`[Destruction] Voxel data not found for ${objectToDestroy.name}. Using fallback destruction.`);
            // (Keep previous logic here as a fallback if needed for other object types)
            // --- Get Physics Parameters ---
            const fallbackExplosionForce = 1.3;
            const fallbackUpwardForce = 2.5;
            const fallbackTumbleSpeed = 1.8;
            const fallbackDebrisPerGroup = 8;
            const fallbackDebrisExplosionForce = 1.2;
            const fallbackDebrisUpwardForce = 2.0;
            const fallbackDebrisTumbleSpeed = 2.2;
            const fallbackDebrisGeo = new THREE.BoxGeometry(VOXEL_SIZE * 0.8, VOXEL_SIZE * 0.8, VOXEL_SIZE * 0.8);
            let fallbackPieceMaterial = objectToDestroy.material;
            if (Array.isArray(fallbackPieceMaterial)) fallbackPieceMaterial = fallbackPieceMaterial[0];
            if (!fallbackPieceMaterial || !fallbackPieceMaterial.isMaterial) fallbackPieceMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
            else fallbackPieceMaterial = fallbackPieceMaterial.clone();
            const fallbackDebrisMaterial = fallbackPieceMaterial.clone();

            const meshesToProcess = [];
            if (objectToDestroy.isMesh) meshesToProcess.push(objectToDestroy);
            else objectToDestroy.traverse((child) => { if (child.isMesh) meshesToProcess.push(child); });

            if (meshesToProcess.length === 0) console.warn("No meshes found in fallback.");

            meshesToProcess.forEach(originalMesh => {
                const pieceMesh = originalMesh.clone();
                 if (originalMesh.material) {
                    if(Array.isArray(pieceMesh.material)) pieceMesh.material.forEach(m => m?.dispose());
                    else if (pieceMesh.material?.dispose) pieceMesh.material.dispose();
                    if (Array.isArray(originalMesh.material)) pieceMesh.material = originalMesh.material.map(m => m.clone());
                    else pieceMesh.material = originalMesh.material.clone();
                 } else pieceMesh.material = new THREE.MeshLambertMaterial({ color: 0x999999 });
                pieceMesh.name = `fallbackDebrisPiece_${originalMesh.name}_${Date.now()}`;
                const originalMeshWorldPos = new THREE.Vector3();
                const originalMeshWorldQuat = new THREE.Quaternion();
                const originalMeshWorldScale = new THREE.Vector3();
                originalMesh.matrixWorld.decompose(originalMeshWorldPos, originalMeshWorldQuat, originalMeshWorldScale);
                pieceMesh.position.copy(originalMeshWorldPos);
                pieceMesh.quaternion.copy(originalMeshWorldQuat);
                pieceMesh.scale.copy(originalMeshWorldScale);
                const direction = new THREE.Vector3(Math.random() - 0.5, 0.2 + Math.random() * 0.4, Math.random() - 0.5).normalize();
                const velocity = direction.multiplyScalar(fallbackExplosionForce + (Math.random() - 0.5) * 0.4);
                velocity.y += fallbackUpwardForce * (0.6 + Math.random() * 0.4);
                const angularVelocity = new THREE.Vector3((Math.random() - 0.5) * fallbackTumbleSpeed * 1.2, (Math.random() - 0.5) * fallbackTumbleSpeed * 1.2, (Math.random() - 0.5) * fallbackTumbleSpeed * 1.2);
                this.scene.add(pieceMesh);
                this.currentRoomObjects.push(pieceMesh);
                pieceMesh.userData = { isDebrisPiece: true, velocity: velocity, angularVelocity: angularVelocity, isFalling: true, persistUntilRoomChange: true };
            });

            for (let i = 0; i < fallbackDebrisPerGroup * meshesToProcess.length; i++) {
                 let currentDebrisMat = fallbackDebrisMaterial;
                 if(!currentDebrisMat || !currentDebrisMat.isMaterial) currentDebrisMat = new THREE.MeshLambertMaterial({ color: 0x777777 });
                 else currentDebrisMat = currentDebrisMat.clone();
                const smallDebrisMesh = new THREE.Mesh(fallbackDebrisGeo, currentDebrisMat);
                smallDebrisMesh.name = `fallbackSmallDebris_${i}_${Date.now()}`;
                const randomOffset = new THREE.Vector3((Math.random() - 0.5) * VOXEL_SIZE * 1.5, (Math.random() - 0.5) * VOXEL_SIZE * 1.5, (Math.random() - 0.5) * VOXEL_SIZE * 1.5);
                smallDebrisMesh.position.copy(objectCenterPos).add(randomOffset);
                const debrisDirection = new THREE.Vector3(Math.random() - 0.5, 0.3 + Math.random() * 0.4, Math.random() - 0.5).normalize();
                const debrisVelocity = debrisDirection.multiplyScalar(fallbackDebrisExplosionForce + (Math.random() - 0.5) * 0.3);
                debrisVelocity.y += fallbackDebrisUpwardForce * (0.5 + Math.random() * 0.6);
                const debrisAngularVelocity = new THREE.Vector3((Math.random() - 0.5) * fallbackDebrisTumbleSpeed, (Math.random() - 0.5) * fallbackDebrisTumbleSpeed, (Math.random() - 0.5) * fallbackDebrisTumbleSpeed);
                this.scene.add(smallDebrisMesh);
                this.currentRoomObjects.push(smallDebrisMesh);

                const debrisId = `fallbackDebris_${objectToDestroy.name}_${i}_${Date.now()}`;
                smallDebrisMesh.userData = {
                    isDebrisPiece: true,
                    debrisId: debrisId,
                    sourceObjectId: objectToDestroy.userData?.id || objectToDestroy.name,
                    sourceRoomId: this.currentRoomId,
                    velocity: debrisVelocity,
                    angularVelocity: debrisAngularVelocity,
                    isFalling: true,
                    persistUntilRoomChange: true,
                    // FIXED: Store material info for deterministic restoration (fallback debris uses original material)
                    materialSeed: null // Fallback debris uses cloned original material, not deterministic selection
                };

                // Store debris in room state for persistence
                this._storeDebrisInRoomState(debrisId, smallDebrisMesh);
            }
            if (fallbackPieceMaterial?.dispose) fallbackPieceMaterial.dispose();
            if (fallbackDebrisMaterial?.dispose) fallbackDebrisMaterial.dispose();
            fallbackDebrisGeo.dispose();
        }

        // --- Remove Original Object from scene and lists (Common to both methods) ---
        // <<< AGGRESSIVE CLEANUP >>>
        console.log(`[Destruction] Applying aggressive cleanup for: ${objectToDestroy.name || objectToDestroy.uuid}`);
        objectToDestroy.traverse(child => {
            if (child.isMesh) {
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(mat => { if(mat && mat.dispose) mat.dispose(); });
                    } else if (child.material.dispose) {
                        child.material.dispose();
                    }
                }
            }
        });
        objectToDestroy.removeFromParent(); // Explicitly remove from parent
        // <<< END AGGRESSIVE CLEANUP >>>

        // (Logging added previously)
        console.log(`[Destruction] Attempting to remove original object: ${objectToDestroy.name || objectToDestroy.uuid}`, objectToDestroy);
        console.log(`[Destruction] Is object in scene before remove? ${this.scene.children.includes(objectToDestroy)}`);
        objectToDestroy.visible = false; // <<< Keep this too, doesn't hurt
        this.scene.remove(objectToDestroy);
        console.log(`[Destruction] Is object in scene AFTER remove? ${this.scene.children.includes(objectToDestroy)}`);

        const enemyIndex = this.activeEnemies.findIndex(e => e === objectToDestroy);
        if (enemyIndex !== -1) {
            this.activeEnemies.splice(enemyIndex, 1);
            this.enemyStateTimers.delete(objUserData?.id);
        }
        const collisionIndex = this.collisionObjects.findIndex(c => c === objectToDestroy || c.parent === objectToDestroy); // Check object itself or if it's a child in collision list
        if (collisionIndex !== -1) {
            this.collisionObjects.splice(collisionIndex, 1);
            console.log("Removed destroyed object/child from collisionObjects list.");
        } else {
            console.warn("Destroyed object or its children not found in collisionObjects list.");
        }
        const roomObjIndex = this.currentRoomObjects.findIndex(o => o === objectToDestroy);
         if (roomObjIndex !== -1) {
            this.currentRoomObjects.splice(roomObjIndex, 1);
         }

        // <<< Dispose Original Object's Geometry/Materials >>>
        objectToDestroy.traverse(child => {
            if (child.isMesh) {
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(mat => { if(mat.dispose) mat.dispose(); });
                    } else if (child.material.dispose) {
                        child.material.dispose();
                    }
                }
            }
        });

        console.log(`[_handleObjectDestruction] Completed destruction for: ${objectToDestroy.name || objectToDestroy.uuid}`);
    }

    /**
     * Handle object destruction events from projectiles
     * @param {CustomEvent} event - The destruction event
     * @private
     */
    _handleObjectDestroyedEvent(event) {
        console.log(`[DungeonHandler] Received objectDestroyed event!`);
        console.log(`[DungeonHandler] Event detail:`, event.detail);

        const { object, pointOfImpact, projectileVelocity } = event.detail;
        console.log(`[DungeonHandler] Processing destruction for: ${object.name || object.uuid}`);
        console.log(`[DungeonHandler] Object userData:`, object.userData);

        // Call the existing destruction handler
        this._handleObjectDestruction(object, pointOfImpact, projectileVelocity);
    }

    // Method to toggle ESP mode
    toggleEspMode() {
        this.isEspEnabled = !this.isEspEnabled;
        console.log(`ESP Mode: ${this.isEspEnabled ? 'ON' : 'OFF'}`);

        // Update player controller ESP mode
        if (this.playerController) {
            this.playerController.isEspEnabled = this.isEspEnabled;
        }

        // Update enemy ESP helpers visibility
        this._updateEnemyEspHelpersVisibility();
    }

    // Method to update enemy ESP helpers visibility
    _updateEnemyEspHelpersVisibility() {
        // Update door triggers visibility
        this.doorTriggers.forEach(trigger => {
            if (trigger && trigger.material) {
                trigger.material.visible = this.isEspEnabled;
            }
        });

        // Update enemy helpers visibility
        this.activeEnemies.forEach(enemy => {
            // Update bone helpers
            if (enemy.userData && enemy.userData.boneHelpers) {
                for (const name in enemy.userData.boneHelpers) {
                    const helpers = enemy.userData.boneHelpers[name];
                    if (helpers.axes) helpers.axes.visible = this.isEspEnabled;
                    if (helpers.box) helpers.box.visible = this.isEspEnabled;
                }
            }

            // Update bounding box helper
            if (enemy.userData && enemy.userData.boundingBoxHelper) {
                enemy.userData.boundingBoxHelper.visible = this.isEspEnabled;
            }

            // Update skeleton lines
            if (enemy.userData && enemy.userData.skeletonLines) {
                enemy.userData.skeletonLines.visible = this.isEspEnabled;
            }
        });
    }

    /**
     * Display the room shape name
     */
    displayRoomShape() {
        if (!this.roomShapeElement) {
            console.warn("Cannot display room shape: Element not found");
            return;
        }

        // Get the current room data
        const roomData = this.floorLayout?.get(this.currentRoomId);
        if (!roomData) {
            console.warn("Cannot display room shape: Room data not found");
            return;
        }

        // Format the shape name for display (convert from SNAKE_CASE to Title Case)
        const shapeKey = roomData.shapeKey || 'SQUARE_1X1';
        const formattedShapeName = shapeKey
            .replace(/_/g, ' ')
            .toLowerCase()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');

        // Display room information
        this.roomShapeElement.textContent = `Room Shape: ${formattedShapeName}`;
        this.roomShapeElement.style.visibility = 'visible';
        this.roomShapeElement.style.opacity = '1';

        // Hide after a delay
        setTimeout(() => {
            this.hideRoomShape();
        }, 3000); // Display for 3 seconds
    }

    /**
     * Hide the room shape display
     */
    hideRoomShape() {
        if (!this.roomShapeElement) return;

        this.roomShapeElement.style.opacity = '0';
        setTimeout(() => {
            this.roomShapeElement.style.visibility = 'hidden';
        }, 500); // Match transition duration
    }

    /**
     * Find a room with a boss in the dungeon
     * @returns {Object|null} The room with a boss, or null if no boss room is found
     */
    findBossRoom() {
        console.log("[DungeonHandler] findBossRoom method called");

        // Check if we have rooms
        if (!this.rooms || this.rooms.length === 0) {
            console.error("[DungeonHandler] No rooms available");
            return null;
        }

        console.log(`[DungeonHandler] Searching through ${this.rooms.length} rooms for a boss...`);
        console.log(`[DungeonHandler] Active bosses: ${this.activeBosses ? this.activeBosses.length : 'none'}`);
        console.log(`[DungeonHandler] Active enemies: ${this.activeEnemies ? this.activeEnemies.length : 'none'}`);

        // First try to find a room with an active boss
        for (const room of this.rooms) {
            console.log(`[DungeonHandler] Checking room:`, room);
            if (room.boss) {
                console.log(`[DungeonHandler] Found boss in room at ${room.position.x}, ${room.position.y}`);
                return room;
            }
        }

        // If no room has a boss property, look for a room with type 'Boss' in the floorLayout
        console.log(`[DungeonHandler] Checking floorLayout for boss rooms...`);
        for (const [roomId, roomData] of this.floorLayout.entries()) {
            console.log(`[DungeonHandler] Room ${roomId} type: ${roomData.type}`);
            if (roomData.type === 'Boss') {
                console.log(`[DungeonHandler] Found boss room with ID: ${roomId}`);
                // Find the corresponding room object
                const roomIndex = roomData.roomIndex;
                console.log(`[DungeonHandler] Room index: ${roomIndex}`);
                if (roomIndex !== undefined && this.rooms[roomIndex]) {
                    console.log(`[DungeonHandler] Found room object at index ${roomIndex}`);
                    return this.rooms[roomIndex];
                }
            }
        }

        // If still not found, look for a room with boss enemies
        console.log(`[DungeonHandler] Checking for rooms with boss enemies...`);
        for (const [roomId, roomData] of this.floorLayout.entries()) {
            if (roomData.state && roomData.state.initialEnemies) {
                console.log(`[DungeonHandler] Room ${roomId} has initialEnemies:`, roomData.state.initialEnemies);
                for (const enemyType of roomData.state.initialEnemies) {
                    if (enemyType.includes('boss') || enemyType.includes('overlord')) {
                        console.log(`[DungeonHandler] Found room with boss enemy: ${enemyType}`);
                        const roomIndex = roomData.roomIndex;
                        if (roomIndex !== undefined && this.rooms[roomIndex]) {
                            return this.rooms[roomIndex];
                        }
                    }
                }
            }
        }

        // If we have active bosses, try to find the room they're in
        if (this.activeBosses && this.activeBosses.length > 0) {
            console.log(`[DungeonHandler] We have active bosses, trying to find their room...`);
            // Just return the current room since that's where the active boss must be
            if (this.currentRoom) {
                console.log(`[DungeonHandler] Returning current room as it must contain the active boss`);
                return this.currentRoom;
            }
        }

        // If all else fails, just return the first room as a fallback
        if (this.rooms.length > 0) {
            console.log(`[DungeonHandler] No boss room found, returning first room as fallback`);
            return this.rooms[0];
        }

        console.log("[DungeonHandler] No boss room found in the dungeon");
        return null;
    }

    /**
     * Teleport the player to a specific room
     * @param {Object} targetRoom - The room to teleport to
     */
    teleportToRoom(targetRoom) {
        console.log("[DungeonHandler] teleportToRoom called");

        if (!targetRoom) {
            console.error("[DungeonHandler] Cannot teleport to null room");
            return;
        }

        console.log("[DungeonHandler] Target room:", targetRoom);

        // Find the room ID from the room object
        let targetRoomId = null;

        // First try to find by direct reference
        for (let i = 0; i < this.rooms.length; i++) {
            if (this.rooms[i] === targetRoom) {
                console.log(`[DungeonHandler] Found room at index ${i}`);
                // Find the corresponding room ID in the floorLayout
                for (const [id, roomData] of this.floorLayout.entries()) {
                    if (roomData.roomIndex === i) {
                        targetRoomId = id;
                        console.log(`[DungeonHandler] Found room ID: ${targetRoomId}`);
                        break;
                    }
                }
                break;
            }
        }

        // If we couldn't find by reference, try to find by position
        if (targetRoomId === null && targetRoom.position) {
            console.log(`[DungeonHandler] Trying to find room by position: ${targetRoom.position.x}, ${targetRoom.position.y}, ${targetRoom.position.z}`);
            for (let i = 0; i < this.rooms.length; i++) {
                const room = this.rooms[i];
                if (room.position &&
                    room.position.x === targetRoom.position.x &&
                    room.position.z === targetRoom.position.z) {
                    console.log(`[DungeonHandler] Found room at index ${i} by position`);
                    // Find the corresponding room ID in the floorLayout
                    for (const [id, roomData] of this.floorLayout.entries()) {
                        if (roomData.roomIndex === i) {
                            targetRoomId = id;
                            console.log(`[DungeonHandler] Found room ID: ${targetRoomId}`);
                            break;
                        }
                    }
                    break;
                }
            }
        }

        // If we still couldn't find it, just use the first room as a fallback
        if (targetRoomId === null) {
            console.log("[DungeonHandler] Could not find room ID for the target room, using fallback");
            // Get the first room ID as a fallback
            for (const [id, roomData] of this.floorLayout.entries()) {
                targetRoomId = id;
                console.log(`[DungeonHandler] Using fallback room ID: ${targetRoomId}`);
                break;
            }

            if (targetRoomId === null) {
                console.error("[DungeonHandler] No rooms available for teleport");
                return;
            }
        }

        console.log(`[DungeonHandler] Teleporting to room ID: ${targetRoomId}`);

        try {
            // Use the existing transition method to load the room
            // We'll use 'north' as a default entry direction
            this._transitionToRoom(targetRoomId, 'north');
            console.log(`[DungeonHandler] Teleported to room ID: ${targetRoomId}`);
        } catch (error) {
            console.error("[DungeonHandler] Error in _transitionToRoom:", error);
        }
    }
    // --- PREDICTIVE PRE-LOADING SYSTEM METHODS ---

    /**
     * Generate and cache dungeon metadata for efficient room management
     * @private
     */
    _generateDungeonMetadata() {
        console.log("[PreLoading] Generating dungeon metadata...");

        // SAFETY: Check if dungeon generator and floor layout exist
        if (!this.dungeonGenerator || !this.dungeonGenerator.floorLayout) {
            console.warn("[PreLoading] Cannot generate metadata - dungeon generator or floor layout not available");
            return;
        }

        const startTime = performance.now();
        const rooms = Array.from(this.dungeonGenerator.floorLayout.values());

        // Build connectivity map
        const connectivityMap = new Map();
        const roomTypes = new Map();
        const roomPositions = new Map();

        rooms.forEach(roomData => {
            const roomId = roomData.id;

            // Store room type and position
            roomTypes.set(roomId, roomData.type);
            roomPositions.set(roomId, { x: roomData.x, y: roomData.y });

            // Build adjacency list
            const connections = [];
            const roomConnections = roomData.connections || {};

            ['n', 's', 'e', 'w'].forEach(direction => {
                const neighborId = roomConnections[direction];
                if (neighborId !== null && neighborId !== undefined) {
                    connections.push({
                        roomId: neighborId,
                        direction: direction
                    });
                }
            });

            connectivityMap.set(roomId, connections);
        });

        // Find critical path (entrance to exit)
        const criticalPath = this._findCriticalPath(connectivityMap, 0);

        this.dungeonMetadata = {
            totalRooms: rooms.length,
            connectivityMap,
            roomTypes,
            roomPositions,
            criticalPath,
            generatedAt: Date.now()
        };

        const endTime = performance.now();
        console.log(`[PreLoading] Dungeon metadata generated in ${(endTime - startTime).toFixed(2)}ms`);
        console.log(`[PreLoading] Total rooms: ${rooms.length}, Critical path length: ${criticalPath.length}`);
    }

    /**
     * Find the critical path from start room to exit room
     * @param {Map} connectivityMap - Room connectivity map
     * @param {number} startRoomId - Starting room ID
     * @returns {Array} Array of room IDs representing the critical path
     * @private
     */
    _findCriticalPath(connectivityMap, startRoomId) {
        // Simple BFS to find path to any exit room (boss room or room with exit)
        const visited = new Set();
        const queue = [{ roomId: startRoomId, path: [startRoomId] }];

        while (queue.length > 0) {
            const { roomId, path } = queue.shift();

            if (visited.has(roomId)) continue;
            visited.add(roomId);

            const roomType = this.dungeonMetadata?.roomTypes?.get(roomId) ||
                           this.dungeonGenerator.floorLayout.get(roomId)?.type;

            // Check if this is an exit room (boss room or special exit)
            if (roomType === 'BOSS' || roomType === 'EXIT') {
                return path;
            }

            // Add connected rooms to queue
            const connections = connectivityMap.get(roomId) || [];
            connections.forEach(connection => {
                if (!visited.has(connection.roomId)) {
                    queue.push({
                        roomId: connection.roomId,
                        path: [...path, connection.roomId]
                    });
                }
            });
        }

        // Fallback: return path to the highest room ID (likely the boss room)
        const allRooms = Array.from(connectivityMap.keys());
        const maxRoomId = Math.max(...allRooms);
        return [startRoomId, maxRoomId];
    }

    /**
     * Start the predictive pre-loading system
     * @private
     */
    _startPredictivePreLoading() {
        console.log("[PreLoading] Starting predictive pre-loading system...");

        if (!this.dungeonMetadata) {
            console.error("[PreLoading] Cannot start pre-loading without dungeon metadata");
            return;
        }

        // Pre-load the starting room and its immediate neighbors
        this._queueAdjacentRooms(this.currentRoomId);

        // Start background processing
        this._processPreLoadQueue();

        console.log("[PreLoading] Predictive pre-loading system started");
    }

    /**
     * Queue adjacent rooms for pre-loading based on current player position
     * @param {number} currentRoomId - Current room ID
     * @private
     */
    _queueAdjacentRooms(currentRoomId) {
        if (!this.dungeonMetadata || !this.dungeonMetadata.connectivityMap) {
            return;
        }

        const connections = this.dungeonMetadata.connectivityMap.get(currentRoomId) || [];

        connections.forEach(connection => {
            const neighborId = connection.roomId;

            // Only queue if not already pre-loaded and not currently being processed
            if (!this.preLoadedRooms.has(neighborId) &&
                !this.preLoadInProgress.has(neighborId) &&
                !this.preLoadQueue.has(neighborId)) {

                this.preLoadQueue.add(neighborId);
                console.log(`[PreLoading] Queued room ${neighborId} for pre-loading`);
            }
        });
    }

    /**
     * Process the pre-load queue in the background
     * @private
     */
    async _processPreLoadQueue() {
        if (this.preLoadQueue.size === 0) {
            return;
        }

        // Process one room at a time to avoid blocking
        const roomId = this.preLoadQueue.values().next().value;
        this.preLoadQueue.delete(roomId);
        this.preLoadInProgress.add(roomId);

        try {
            await this._preLoadRoom(roomId);
        } catch (error) {
            console.error(`[PreLoading] Error pre-loading room ${roomId}:`, error);
        } finally {
            this.preLoadInProgress.delete(roomId);
        }

        // Schedule next room processing
        if (this.preLoadQueue.size > 0) {
            setTimeout(() => this._processPreLoadQueue(), 50); // Small delay to prevent blocking
        }
    }

    /**
     * Pre-load a specific room in the background
     * @param {number} roomId - Room ID to pre-load
     * @private
     */
    async _preLoadRoom(roomId) {
        const startTime = performance.now();

        const roomData = this.dungeonGenerator.floorLayout.get(roomId);
        if (!roomData) {
            console.warn(`[PreLoading] Room data not found for room ${roomId}`);
            return;
        }

        console.log(`[PreLoading] Pre-loading room ${roomId} (${roomData.type})...`);

        // Set pre-loading flag
        this.isPreLoading = true;

        try {
            // Set temporary bounds for this room during generation
            this._setTemporaryRoomBounds(roomData);

            // Generate room visuals
            const result = generateRoomVisuals(roomData, this.currentArea);

            // Generate floor data
            const floorData = this._generateRoomFloorData(roomId, result.collisionMeshes, result.boundingBox);

            // Store pre-loaded room data
            this.preLoadedRooms.set(roomId, {
                roomGroup: result.roomGroup,
                collisionMeshes: result.collisionMeshes,
                lights: result.lights,
                boundingBox: result.boundingBox,
                doorCenterPoints: { ...result.doorCenterPoints },
                floorData
            });

            // Initialize room state if not exists
            if (!this.roomStates.has(roomId)) {
                this._initializeRoomState(roomId, roomData);
            }

            const endTime = performance.now();
            console.log(`[PreLoading] Room ${roomId} pre-loaded in ${(endTime - startTime).toFixed(2)}ms`);

        } finally {
            // Clear temporary data
            this._clearTemporaryRoomBounds();
            this._clearCurrentRoomWallSegments();
            this._clearCurrentRoomFloorMeshes();
            this.isPreLoading = false;
        }
    }

    /**
     * Initialize room state for persistence
     * @param {number} roomId - Room ID
     * @param {Object} roomData - Room data
     * @private
     */
    _initializeRoomState(roomId, roomData) {
        this.roomStates.set(roomId, {
            enemies: new Map(), // enemyId -> {position, health, state}
            items: new Map(),   // itemId -> {collected, position, type, value}
            events: new Set(),  // triggered event IDs
            environmental: new Map(), // objectId -> {destroyed, state}
            debris: new Map(),  // debrisId -> {position, rotation, velocity, angularVelocity, material, geometry, userData}
            soulOrbs: new Map(), // orbId -> {position, value, creationTime}
            lastVisited: null,
            initialized: true
        });

        console.log(`[PreLoading] Initialized state for room ${roomId}`);
    }

    /**
     * Store debris in room state for persistence across room transitions
     * @param {string} debrisId - Unique debris identifier
     * @param {THREE.Mesh} debrisMesh - The debris mesh object
     * @private
     */
    _storeDebrisInRoomState(debrisId, debrisMesh) {
        // Ensure current room state exists
        if (!this.roomStates.has(this.currentRoomId)) {
            this._initializeRoomState(this.currentRoomId, this.floorLayout.get(this.currentRoomId));
        }

        const roomState = this.roomStates.get(this.currentRoomId);

        // ENHANCED: Store debris data for persistence with quaternion
        const debrisData = {
            position: {
                x: debrisMesh.position.x,
                y: debrisMesh.position.y,
                z: debrisMesh.position.z
            },
            rotation: {
                x: debrisMesh.rotation.x,
                y: debrisMesh.rotation.y,
                z: debrisMesh.rotation.z
            },
            // ENHANCED: Store quaternion for exact rotation preservation
            quaternion: {
                x: debrisMesh.quaternion.x,
                y: debrisMesh.quaternion.y,
                z: debrisMesh.quaternion.z,
                w: debrisMesh.quaternion.w
            },
            scale: {
                x: debrisMesh.scale.x,
                y: debrisMesh.scale.y,
                z: debrisMesh.scale.z
            },
            velocity: debrisMesh.userData.velocity ? {
                x: debrisMesh.userData.velocity.x,
                y: debrisMesh.userData.velocity.y,
                z: debrisMesh.userData.velocity.z
            } : null,
            angularVelocity: debrisMesh.userData.angularVelocity ? {
                x: debrisMesh.userData.angularVelocity.x,
                y: debrisMesh.userData.angularVelocity.y,
                z: debrisMesh.userData.angularVelocity.z
            } : null,
            userData: {
                isBonePiece: debrisMesh.userData.isBonePiece,
                isDebrisPiece: debrisMesh.userData.isDebrisPiece,
                debrisId: debrisMesh.userData.debrisId,
                sourceEnemyId: debrisMesh.userData.sourceEnemyId,
                sourceObjectId: debrisMesh.userData.sourceObjectId,
                sourceRoomId: debrisMesh.userData.sourceRoomId,
                isFalling: debrisMesh.userData.isFalling,
                creationTime: debrisMesh.userData.creationTime,
                persistUntilRoomChange: debrisMesh.userData.persistUntilRoomChange,
                // CRITICAL FIX: Store materialSeed for deterministic material recreation
                materialSeed: debrisMesh.userData.materialSeed,
                // Store additional properties for body parts if they exist
                bodyPartType: debrisMesh.userData.bodyPartType,
                originalEnemyData: debrisMesh.userData.originalEnemyData
            },
            materialData: this._serializeMaterial(debrisMesh.material),
            geometryData: this._serializeGeometry(debrisMesh.geometry),
            name: debrisMesh.name,
            castShadow: debrisMesh.castShadow,
            receiveShadow: debrisMesh.receiveShadow
        };

        roomState.debris.set(debrisId, debrisData);
        console.log(`[DebrisStorage] Stored debris ${debrisId} in room ${this.currentRoomId} with materialSeed: ${debrisData.userData.materialSeed}, isBonePiece: ${debrisData.userData.isBonePiece}, material color: 0x${debrisMesh.material.color.getHex().toString(16).padStart(6, '0')}, MaterialID: ${debrisMesh.material.id}`);
    }

    /**
     * ENHANCED: Serialize material data for storage with comprehensive visual properties
     * @param {THREE.Material} material - Material to serialize
     * @returns {Object} Serialized material data
     * @private
     */
    _serializeMaterial(material) {
        if (!material) return null;

        const materialData = {
            type: material.type,
            color: material.color ? material.color.getHex() : 0xffffff,
            transparent: material.transparent,
            opacity: material.opacity,
            // ENHANCED: Additional visual properties
            emissive: material.emissive ? material.emissive.getHex() : 0x000000,
            emissiveIntensity: material.emissiveIntensity || 0,
            roughness: material.roughness !== undefined ? material.roughness : 1,
            metalness: material.metalness !== undefined ? material.metalness : 0,
            side: material.side !== undefined ? material.side : THREE.FrontSide,
            flatShading: material.flatShading || false,
            wireframe: material.wireframe || false,
            vertexColors: material.vertexColors || false,
            fog: material.fog !== undefined ? material.fog : true,
            alphaTest: material.alphaTest || 0,
            depthTest: material.depthTest !== undefined ? material.depthTest : true,
            depthWrite: material.depthWrite !== undefined ? material.depthWrite : true,
            blending: material.blending !== undefined ? material.blending : THREE.NormalBlending
        };

        // ENHANCED: Store texture information more comprehensively
        if (material.map) {
            materialData.mapUrl = material.map.image?.src || null;
            materialData.mapRepeat = material.map.repeat ? { x: material.map.repeat.x, y: material.map.repeat.y } : null;
            materialData.mapOffset = material.map.offset ? { x: material.map.offset.x, y: material.map.offset.y } : null;
            materialData.mapWrapS = material.map.wrapS || THREE.ClampToEdgeWrapping;
            materialData.mapWrapT = material.map.wrapT || THREE.ClampToEdgeWrapping;
        }

        // Store normal map if present
        if (material.normalMap) {
            materialData.normalMapUrl = material.normalMap.image?.src || null;
            materialData.normalScale = material.normalScale ? { x: material.normalScale.x, y: material.normalScale.y } : null;
        }

        // Store environment map if present
        if (material.envMap) {
            materialData.envMapIntensity = material.envMapIntensity || 1;
            materialData.reflectivity = material.reflectivity || 1;
        }

        return materialData;
    }

    /**
     * Serialize geometry data for storage
     * @param {THREE.BufferGeometry} geometry - Geometry to serialize
     * @returns {Object} Serialized geometry data
     * @private
     */
    _serializeGeometry(geometry) {
        if (!geometry) return null;

        // For simple geometries, store basic parameters
        if (geometry.type === 'BoxGeometry') {
            return {
                type: 'BoxGeometry',
                width: geometry.parameters?.width || 1,
                height: geometry.parameters?.height || 1,
                depth: geometry.parameters?.depth || 1
            };
        }

        // For sphere geometries
        if (geometry.type === 'SphereGeometry') {
            return {
                type: 'SphereGeometry',
                radius: geometry.parameters?.radius || 1,
                widthSegments: geometry.parameters?.widthSegments || 8,
                heightSegments: geometry.parameters?.heightSegments || 6
            };
        }

        // For cylinder geometries
        if (geometry.type === 'CylinderGeometry') {
            return {
                type: 'CylinderGeometry',
                radiusTop: geometry.parameters?.radiusTop || 1,
                radiusBottom: geometry.parameters?.radiusBottom || 1,
                height: geometry.parameters?.height || 1,
                radialSegments: geometry.parameters?.radialSegments || 8
            };
        }

        // For BufferGeometry (complex meshes like enemy body parts), store vertex data
        if (geometry.type === 'BufferGeometry' && geometry.attributes.position) {
            // Compute bounding box for size reference
            geometry.computeBoundingBox();
            const box = geometry.boundingBox;

            // Store vertex positions for detailed reconstruction
            const positions = geometry.attributes.position.array;
            const normals = geometry.attributes.normal ? geometry.attributes.normal.array : null;
            const uvs = geometry.attributes.uv ? geometry.attributes.uv.array : null;
            const indices = geometry.index ? geometry.index.array : null;

            return {
                type: 'DetailedBufferGeometry',
                positions: Array.from(positions),
                normals: normals ? Array.from(normals) : null,
                uvs: uvs ? Array.from(uvs) : null,
                indices: indices ? Array.from(indices) : null,
                boundingBox: {
                    min: { x: box.min.x, y: box.min.y, z: box.min.z },
                    max: { x: box.max.x, y: box.max.y, z: box.max.z }
                }
            };
        }

        // For complex geometries (fallback), store basic bounding box
        if (geometry.boundingBox || geometry.computeBoundingBox) {
            if (!geometry.boundingBox) geometry.computeBoundingBox();
            const box = geometry.boundingBox;
            return {
                type: 'ComplexGeometry',
                boundingBox: {
                    min: { x: box.min.x, y: box.min.y, z: box.min.z },
                    max: { x: box.max.x, y: box.max.y, z: box.max.z }
                }
            };
        }

        // Fallback for unknown geometries
        return {
            type: geometry.type || 'UnknownGeometry',
            fallback: true
        };
    }

    /**
     * Restore debris for a room from stored state
     * @param {number} roomId - Room ID to restore debris for
     * @private
     */
    _restoreRoomDebris(roomId) {
        const roomState = this.roomStates.get(roomId);
        if (!roomState || !roomState.debris || roomState.debris.size === 0) {
            return; // No debris to restore
        }

        console.log(`[DebrisPersistence] Restoring ${roomState.debris.size} debris pieces for room ${roomId}`);

        roomState.debris.forEach((debrisData, debrisId) => {
            try {
                // Only restore debris that belongs to this room
                if (debrisData.userData.sourceRoomId !== roomId) {
                    console.warn(`[DebrisPersistence] Skipping debris ${debrisId} - belongs to room ${debrisData.userData.sourceRoomId}, not ${roomId}`);
                    return;
                }

                let debrisMesh;

                // Try to recreate body part using original enemy data, with fallback to geometry deserialization
                if (debrisData.userData.bodyPartType && debrisData.userData.originalEnemyData) {
                    // Attempt to recreate body part using original enemy data
                    debrisMesh = this._recreateBodyPart(debrisData);

                    if (!debrisMesh) {
                        console.warn(`[DebrisPersistence] Body part recreation failed for ${debrisId}, falling back to geometry deserialization`);
                    }
                }

                // If body part recreation failed or this is regular debris, use geometry/material deserialization
                if (!debrisMesh) {
                    const geometry = this._deserializeGeometry(debrisData.geometryData);
                    if (!geometry) {
                        console.warn(`[DebrisPersistence] Failed to recreate geometry for debris ${debrisId}`);
                        return;
                    }

                    // FIXED: Use deterministic material recreation for bone debris
                    let material;
                    if (debrisData.userData.materialSeed && debrisData.userData.isBonePiece) {
                        // Recreate bone material deterministically using the stored seed
                        material = this._getDeterministicBoneMaterial(debrisData.userData.materialSeed);
                        console.log(`[DebrisRestoration] Using deterministic bone material for debris ${debrisId} with seed: ${debrisData.userData.materialSeed}, color: 0x${material.color.getHex().toString(16).padStart(6, '0')}, MaterialID: ${material.id}`);
                    } else {
                        // Use standard material deserialization for other debris types
                        console.log(`[DebrisRestoration] Using standard material deserialization for debris ${debrisId} (materialSeed: ${debrisData.userData.materialSeed}, isBonePiece: ${debrisData.userData.isBonePiece})`);
                        material = this._deserializeMaterial(debrisData.materialData);
                        if (!material) {
                            console.warn(`[DebrisPersistence] Failed to recreate material for debris ${debrisId}`);
                            return;
                        }
                        console.log(`[DebrisRestoration] Standard material color: 0x${material.color.getHex().toString(16).padStart(6, '0')}`);
                    }

                    debrisMesh = new THREE.Mesh(geometry, material);
                }

                if (!debrisMesh) {
                    console.warn(`[DebrisPersistence] Failed to recreate debris ${debrisId} with all methods`);
                    return;
                }

                // Set basic properties
                debrisMesh.name = debrisData.name;
                debrisMesh.castShadow = debrisData.castShadow;
                debrisMesh.receiveShadow = debrisData.receiveShadow;

                // ENHANCED: Restore position, rotation, and scale with exact precision
                debrisMesh.position.set(
                    debrisData.position.x,
                    debrisData.position.y,
                    debrisData.position.z
                );
                debrisMesh.rotation.set(
                    debrisData.rotation.x,
                    debrisData.rotation.y,
                    debrisData.rotation.z
                );

                // ENHANCED: Restore exact quaternion if available for precise rotation
                if (debrisData.quaternion) {
                    debrisMesh.quaternion.set(
                        debrisData.quaternion.x,
                        debrisData.quaternion.y,
                        debrisData.quaternion.z,
                        debrisData.quaternion.w
                    );
                }

                // Only apply scale if this is not a recreated body part (which already has scale baked into geometry)
                if (!debrisData.userData.bodyPartType || !debrisData.userData.originalEnemyData) {
                    debrisMesh.scale.set(
                        debrisData.scale.x,
                        debrisData.scale.y,
                        debrisData.scale.z
                    );
                } else {
                    // For recreated body parts, scale is already incorporated into geometry, so keep it at 1,1,1
                    debrisMesh.scale.set(1, 1, 1);
                }

                // Restore userData
                debrisMesh.userData = { ...debrisData.userData };

                // Restore velocity if it exists
                if (debrisData.velocity) {
                    debrisMesh.userData.velocity = new THREE.Vector3(
                        debrisData.velocity.x,
                        debrisData.velocity.y,
                        debrisData.velocity.z
                    );
                }

                // Restore angular velocity if it exists
                if (debrisData.angularVelocity) {
                    debrisMesh.userData.angularVelocity = new THREE.Vector3(
                        debrisData.angularVelocity.x,
                        debrisData.angularVelocity.y,
                        debrisData.angularVelocity.z
                    );
                }

                // Add to scene and track
                this.scene.add(debrisMesh);
                this.currentRoomObjects.push(debrisMesh);

                console.log(`[DebrisPersistence] Restored debris ${debrisId} at position (${debrisData.position.x.toFixed(2)}, ${debrisData.position.y.toFixed(2)}, ${debrisData.position.z.toFixed(2)})`);

            } catch (error) {
                console.error(`[DebrisPersistence] Error restoring debris ${debrisId}:`, error);
            }
        });
    }

    /**
     * Recreate a body part using original enemy data
     * @param {Object} debrisData - Stored debris data
     * @returns {THREE.Mesh} Recreated body part mesh
     * @private
     */
    _recreateBodyPart(debrisData) {
        try {
            const { bodyPartType, originalEnemyData } = debrisData.userData;

            // Get the stored scale to match the original size
            const storedScale = debrisData.scale || { x: 1, y: 1, z: 1 };

            // For now, create a simple body part based on the type
            // This is a simplified approach that's more reliable than trying to recreate complex enemy structures
            let geometry, material;

            // Create appropriate geometry based on body part type, incorporating the stored scale
            switch (bodyPartType) {
                case 'head':
                    geometry = new THREE.BoxGeometry(
                        0.8 * storedScale.x,
                        0.8 * storedScale.y,
                        0.8 * storedScale.z
                    );
                    material = new THREE.MeshLambertMaterial({ color: 0xf0f0f0 }); // Light bone color
                    break;
                case 'core':
                    geometry = new THREE.BoxGeometry(
                        1.0 * storedScale.x,
                        1.5 * storedScale.y,
                        0.6 * storedScale.z
                    );
                    material = new THREE.MeshLambertMaterial({ color: 0xe8e8e8 }); // Slightly darker bone
                    break;
                case 'leftArm':
                case 'rightArm':
                    geometry = new THREE.BoxGeometry(
                        0.4 * storedScale.x,
                        1.2 * storedScale.y,
                        0.4 * storedScale.z
                    );
                    material = new THREE.MeshLambertMaterial({ color: 0xf0f0f0 }); // Light bone color
                    break;
                case 'leftLeg':
                case 'rightLeg':
                    geometry = new THREE.BoxGeometry(
                        0.4 * storedScale.x,
                        1.4 * storedScale.y,
                        0.4 * storedScale.z
                    );
                    material = new THREE.MeshLambertMaterial({ color: 0xf0f0f0 }); // Light bone color
                    break;
                default:
                    geometry = new THREE.BoxGeometry(
                        0.5 * storedScale.x,
                        0.5 * storedScale.y,
                        0.5 * storedScale.z
                    );
                    material = new THREE.MeshLambertMaterial({ color: 0xcccccc }); // Default gray
            }

            const bodyPartMesh = new THREE.Mesh(geometry, material);
            bodyPartMesh.name = bodyPartType;
            bodyPartMesh.castShadow = true;
            bodyPartMesh.receiveShadow = true;

            // Set scale to 1,1,1 since we've already incorporated the scale into the geometry
            bodyPartMesh.scale.set(1, 1, 1);

            console.log(`[DebrisPersistence] Successfully recreated simplified body part: ${bodyPartType} with scale (${storedScale.x.toFixed(2)}, ${storedScale.y.toFixed(2)}, ${storedScale.z.toFixed(2)})`);
            return bodyPartMesh;

        } catch (error) {
            console.error(`[DebrisPersistence] Error recreating body part:`, error);
            return null;
        }
    }

    /**
     * Deserialize geometry data to create THREE.js geometry
     * @param {Object} geometryData - Serialized geometry data
     * @returns {THREE.BufferGeometry} Recreated geometry
     * @private
     */
    _deserializeGeometry(geometryData) {
        if (!geometryData) return null;

        switch (geometryData.type) {
            case 'BoxGeometry':
                return new THREE.BoxGeometry(
                    geometryData.width || 1,
                    geometryData.height || 1,
                    geometryData.depth || 1
                );

            case 'SphereGeometry':
                return new THREE.SphereGeometry(
                    geometryData.radius || 1,
                    geometryData.widthSegments || 8,
                    geometryData.heightSegments || 6
                );

            case 'CylinderGeometry':
                return new THREE.CylinderGeometry(
                    geometryData.radiusTop || 1,
                    geometryData.radiusBottom || 1,
                    geometryData.height || 1,
                    geometryData.radialSegments || 8
                );

            case 'DetailedBufferGeometry':
                // Recreate detailed geometry from stored vertex data
                try {
                    const geometry = new THREE.BufferGeometry();

                    // Restore positions
                    if (geometryData.positions) {
                        geometry.setAttribute('position', new THREE.Float32BufferAttribute(geometryData.positions, 3));
                    }

                    // Restore normals
                    if (geometryData.normals) {
                        geometry.setAttribute('normal', new THREE.Float32BufferAttribute(geometryData.normals, 3));
                    } else {
                        // Compute normals if not stored
                        geometry.computeVertexNormals();
                    }

                    // Restore UVs
                    if (geometryData.uvs) {
                        geometry.setAttribute('uv', new THREE.Float32BufferAttribute(geometryData.uvs, 2));
                    }

                    // Restore indices
                    if (geometryData.indices) {
                        geometry.setIndex(geometryData.indices);
                    }

                    console.log(`[DebrisPersistence] Restored detailed geometry with ${geometryData.positions.length / 3} vertices`);
                    return geometry;
                } catch (error) {
                    console.error(`[DebrisPersistence] Error restoring detailed geometry:`, error);
                    // Fallback to bounding box geometry
                    if (geometryData.boundingBox) {
                        const box = geometryData.boundingBox;
                        const width = box.max.x - box.min.x;
                        const height = box.max.y - box.min.y;
                        const depth = box.max.z - box.min.z;
                        return new THREE.BoxGeometry(
                            Math.max(width, 0.1),
                            Math.max(height, 0.1),
                            Math.max(depth, 0.1)
                        );
                    }
                    return new THREE.BoxGeometry(1, 1, 1);
                }

            case 'ComplexGeometry':
                // For complex geometries, create a box that matches the bounding box
                if (geometryData.boundingBox) {
                    const box = geometryData.boundingBox;
                    const width = box.max.x - box.min.x;
                    const height = box.max.y - box.min.y;
                    const depth = box.max.z - box.min.z;
                    return new THREE.BoxGeometry(
                        Math.max(width, 0.1),
                        Math.max(height, 0.1),
                        Math.max(depth, 0.1)
                    );
                }
                // Fallback if no bounding box
                return new THREE.BoxGeometry(1, 1, 1);

            case 'UnknownGeometry':
            default:
                // Fallback to a simple box geometry
                console.warn(`[DebrisPersistence] Unknown geometry type: ${geometryData.type}, using fallback`);
                return new THREE.BoxGeometry(1, 1, 1);
        }
    }

    /**
     * ENHANCED: Deserialize material data to create THREE.js material with full visual fidelity
     * @param {Object} materialData - Serialized material data
     * @returns {THREE.Material} Recreated material
     * @private
     */
    _deserializeMaterial(materialData) {
        if (!materialData) {
            return new THREE.MeshLambertMaterial({ color: 0x888888 });
        }

        // ENHANCED: Create material based on original type for better fidelity
        let material;
        const materialConfig = {
            color: materialData.color || 0xffffff,
            transparent: materialData.transparent || false,
            opacity: materialData.opacity || 1.0,
            side: materialData.side !== undefined ? materialData.side : THREE.FrontSide,
            flatShading: materialData.flatShading || false,
            wireframe: materialData.wireframe || false,
            vertexColors: materialData.vertexColors || false,
            fog: materialData.fog !== undefined ? materialData.fog : true,
            alphaTest: materialData.alphaTest || 0,
            depthTest: materialData.depthTest !== undefined ? materialData.depthTest : true,
            depthWrite: materialData.depthWrite !== undefined ? materialData.depthWrite : true,
            blending: materialData.blending !== undefined ? materialData.blending : THREE.NormalBlending
        };

        // ENHANCED: Add emissive properties if available
        if (materialData.emissive !== undefined) {
            materialConfig.emissive = new THREE.Color(materialData.emissive);
            materialConfig.emissiveIntensity = materialData.emissiveIntensity || 0;
        }

        // Create material based on original type
        switch (materialData.type) {
            case 'MeshStandardMaterial':
                materialConfig.roughness = materialData.roughness !== undefined ? materialData.roughness : 1;
                materialConfig.metalness = materialData.metalness !== undefined ? materialData.metalness : 0;
                material = new THREE.MeshStandardMaterial(materialConfig);
                break;
            case 'MeshPhongMaterial':
                material = new THREE.MeshPhongMaterial(materialConfig);
                break;
            case 'MeshBasicMaterial':
                material = new THREE.MeshBasicMaterial(materialConfig);
                break;
            case 'MeshLambertMaterial':
            default:
                material = new THREE.MeshLambertMaterial(materialConfig);
                break;
        }

        // ENHANCED: Restore texture properties if available
        // Note: Actual texture loading would require the texture to be available
        // For now, we preserve the material properties that don't require external resources

        console.log(`[DebrisPersistence] Restored ${materialData.type || 'MeshLambertMaterial'} with color 0x${(materialData.color || 0xffffff).toString(16).padStart(6, '0')}`);
        return material;
    }

    /**
     * Save current room debris state before room cleanup
     * @private
     */
    _saveCurrentRoomDebrisState() {
        if (this.currentRoomId === null || this.currentRoomId === undefined) {
            return; // No current room to save
        }

        // Ensure room state exists
        if (!this.roomStates.has(this.currentRoomId)) {
            this._initializeRoomState(this.currentRoomId, this.floorLayout.get(this.currentRoomId));
        }

        const roomState = this.roomStates.get(this.currentRoomId);
        let debrisCount = 0;

        // Update debris state for all current debris pieces
        this.currentRoomObjects.forEach(obj => {
            if (obj.userData && obj.userData.isDebrisPiece && obj.userData.debrisId) {
                const debrisId = obj.userData.debrisId;

                // ENHANCED: Update the stored debris data with current state including quaternion
                const debrisData = {
                    position: {
                        x: obj.position.x,
                        y: obj.position.y,
                        z: obj.position.z
                    },
                    rotation: {
                        x: obj.rotation.x,
                        y: obj.rotation.y,
                        z: obj.rotation.z
                    },
                    // ENHANCED: Store quaternion for exact rotation preservation
                    quaternion: {
                        x: obj.quaternion.x,
                        y: obj.quaternion.y,
                        z: obj.quaternion.z,
                        w: obj.quaternion.w
                    },
                    scale: {
                        x: obj.scale.x,
                        y: obj.scale.y,
                        z: obj.scale.z
                    },
                    velocity: obj.userData.velocity ? {
                        x: obj.userData.velocity.x,
                        y: obj.userData.velocity.y,
                        z: obj.userData.velocity.z
                    } : null,
                    angularVelocity: obj.userData.angularVelocity ? {
                        x: obj.userData.angularVelocity.x,
                        y: obj.userData.angularVelocity.y,
                        z: obj.userData.angularVelocity.z
                    } : null,
                    userData: { ...obj.userData },
                    materialData: this._serializeMaterial(obj.material),
                    geometryData: this._serializeGeometry(obj.geometry),
                    name: obj.name,
                    castShadow: obj.castShadow,
                    receiveShadow: obj.receiveShadow
                };

                roomState.debris.set(debrisId, debrisData);
                debrisCount++;
            }
        });

        if (debrisCount > 0) {
            console.log(`[DebrisPersistence] Saved state for ${debrisCount} debris pieces in room ${this.currentRoomId}`);
        }
    }

    /**
     * Save current room soul orb state before room cleanup
     * @private
     */
    _saveCurrentRoomSoulOrbState() {
        if (this.currentRoomId === null || this.currentRoomId === undefined) {
            return; // No current room to save
        }

        // Ensure room state exists
        if (!this.roomStates.has(this.currentRoomId)) {
            this._initializeRoomState(this.currentRoomId, this.floorLayout.get(this.currentRoomId));
        }

        const roomState = this.roomStates.get(this.currentRoomId);
        let orbCount = 0;

        // Get soul orbs from the SimpleSoulDropManager
        if (simpleSoulDropManager && simpleSoulDropManager.activeSoulOrbs) {
            simpleSoulDropManager.activeSoulOrbs.forEach(orb => {
                if (orb.object && orb.object.position) {
                    const orbId = `soulOrb_${this.currentRoomId}_${orb.creationTime}_${Date.now()}`;

                    const orbData = {
                        position: {
                            x: orb.object.position.x,
                            y: orb.object.position.y,
                            z: orb.object.position.z
                        },
                        value: orb.value || 1,
                        creationTime: orb.creationTime,
                        sourceRoomId: this.currentRoomId,
                        userData: orb.object.userData ? { ...orb.object.userData } : {}
                    };

                    roomState.soulOrbs.set(orbId, orbData);
                    orbCount++;
                }
            });
        }

        if (orbCount > 0) {
            console.log(`[SoulOrbPersistence] Saved state for ${orbCount} soul orbs in room ${this.currentRoomId}`);
        }
    }

    /**
     * Restore soul orbs for a room from stored state
     * @param {number} roomId - Room ID to restore soul orbs for
     * @private
     */
    _restoreRoomSoulOrbs(roomId) {
        const roomState = this.roomStates.get(roomId);
        if (!roomState || !roomState.soulOrbs || roomState.soulOrbs.size === 0) {
            return; // No soul orbs to restore
        }

        console.log(`[SoulOrbPersistence] Restoring ${roomState.soulOrbs.size} soul orbs for room ${roomId}`);

        roomState.soulOrbs.forEach((orbData, orbId) => {
            try {
                // Only restore orbs that belong to this room
                if (orbData.sourceRoomId !== roomId) {
                    console.warn(`[SoulOrbPersistence] Skipping orb ${orbId} - belongs to room ${orbData.sourceRoomId}, not ${roomId}`);
                    return;
                }

                // Create soul orb using the SimpleSoulDropManager
                const position = new THREE.Vector3(orbData.position.x, orbData.position.y, orbData.position.z);
                const orb = simpleSoulDropManager.createSoulOrb(position, this.scene, orbData.value);

                if (orb) {
                    // Update the orb's creation time to match the stored data
                    orb.creationTime = orbData.creationTime;
                    if (orb.object && orb.object.userData) {
                        orb.object.userData = { ...orb.object.userData, ...orbData.userData };
                    }

                    console.log(`[SoulOrbPersistence] Restored soul orb ${orbId} with value ${orbData.value} at position (${orbData.position.x.toFixed(2)}, ${orbData.position.y.toFixed(2)}, ${orbData.position.z.toFixed(2)})`);
                } else {
                    console.error(`[SoulOrbPersistence] Failed to restore soul orb ${orbId}`);
                }

            } catch (error) {
                console.error(`[SoulOrbPersistence] Error restoring soul orb ${orbId}:`, error);
            }
        });
    }

    /**
     * Clean up distant rooms to manage memory
     * @param {number} currentRoomId - Current room ID
     * @private
     */
    _cleanupDistantRooms(currentRoomId) {
        if (!this.dungeonMetadata) return;

        const maxDistance = 3; // Keep rooms within 3 connections
        const roomsToKeep = new Set();

        // BFS to find rooms within maxDistance
        const queue = [{ roomId: currentRoomId, distance: 0 }];
        const visited = new Set();

        while (queue.length > 0) {
            const { roomId, distance } = queue.shift();

            if (visited.has(roomId) || distance > maxDistance) continue;
            visited.add(roomId);
            roomsToKeep.add(roomId);

            const connections = this.dungeonMetadata.connectivityMap.get(roomId) || [];
            connections.forEach(connection => {
                if (!visited.has(connection.roomId)) {
                    queue.push({
                        roomId: connection.roomId,
                        distance: distance + 1
                    });
                }
            });
        }

        // Remove distant rooms
        const roomsToRemove = [];
        this.preLoadedRooms.forEach((_, roomId) => {
            if (!roomsToKeep.has(roomId)) {
                roomsToRemove.push(roomId);
            }
        });

        roomsToRemove.forEach(roomId => {
            this.preLoadedRooms.delete(roomId);
            console.log(`[PreLoading] Cleaned up distant room ${roomId}`);
        });

        if (roomsToRemove.length > 0) {
            console.log(`[PreLoading] Cleaned up ${roomsToRemove.length} distant rooms`);
        }
    }

    /**
     * Calculate collision normal for debris wall collision
     * @param {THREE.Vector3} debrisPosition - Position of debris
     * @param {THREE.Object3D} collisionObject - Object being collided with
     * @returns {THREE.Vector3} - Collision normal vector
     * @private
     */
    _calculateDebrisCollisionNormal(debrisPosition, collisionObject) {
        try {
            // Get the closest point on the collision object to the debris
            const objBox = new THREE.Box3().setFromObject(collisionObject);
            const closestPoint = objBox.clampPoint(debrisPosition, new THREE.Vector3());

            // Calculate normal from closest point to debris position
            const normal = debrisPosition.clone().sub(closestPoint);

            // If debris is inside the object, use a default upward normal
            if (normal.lengthSq() < 0.001) {
                return new THREE.Vector3(0, 1, 0);
            }

            return normal.normalize();
        } catch (error) {
            console.warn('[Debris] Error calculating collision normal:', error);
            return new THREE.Vector3(0, 1, 0); // Default upward normal
        }
    }

    /**
     * Calculate penetration depth between two bounding boxes for debris
     * @param {THREE.Box3} debrisBox - Debris bounding box
     * @param {THREE.Box3} colliderBox - Collider bounding box
     * @returns {Number} - Penetration depth
     * @private
     */
    _calculateDebrisPenetrationDepth(debrisBox, colliderBox) {
        try {
            // Calculate overlap in each axis
            const overlapX = Math.min(debrisBox.max.x, colliderBox.max.x) - Math.max(debrisBox.min.x, colliderBox.min.x);
            const overlapY = Math.min(debrisBox.max.y, colliderBox.max.y) - Math.max(debrisBox.min.y, colliderBox.min.y);
            const overlapZ = Math.min(debrisBox.max.z, colliderBox.max.z) - Math.max(debrisBox.min.z, colliderBox.min.z);

            // Return the minimum overlap (smallest penetration)
            return Math.max(0, Math.min(overlapX, overlapY, overlapZ));
        } catch (error) {
            console.warn('[Debris] Error calculating penetration depth:', error);
            return 0.1; // Default small penetration
        }
    }

    /**
     * Get deterministic bone material for consistent debris appearance
     * @param {string} seed - Seed for deterministic material selection
     * @returns {THREE.Material} - Bone material
     * @private
     */
    _getDeterministicBoneMaterial(seed) {
        // Use the same bone colors and logic as in enemy death handling
        const boneColors = [0xF8E8C8, 0xE0C8A0, 0xC8A078, 0x987858];

        // Ensure cached materials exist
        if (!this.cachedBoneMaterials) {
            this.cachedBoneMaterials = boneColors.map(color => new THREE.MeshLambertMaterial({ color }));
            console.log(`[MaterialCache] Created ${this.cachedBoneMaterials.length} cached bone materials in restoration`);
        }

        // Create a simple hash from the seed to get consistent material selection
        let hash = 0;
        const seedStr = String(seed);
        for (let i = 0; i < seedStr.length; i++) {
            const char = seedStr.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        const index = Math.abs(hash) % this.cachedBoneMaterials.length;

        // Return the cached material instance
        const material = this.cachedBoneMaterials[index];
        console.log(`[DeterministicBoneMaterial] Seed: ${seed}, Hash: ${hash}, Index: ${index}, Color: 0x${material.color.getHex().toString(16).padStart(6, '0')}, MaterialID: ${material.id}`);
        return material;
    }

    /**
     * Calculate hash for debris seed (for verification purposes)
     * @param {string} seed - Seed string
     * @returns {number} - Hash value
     * @private
     */
    _calculateDebrisHash(seed) {
        let hash = 0;
        const seedStr = String(seed);
        for (let i = 0; i < seedStr.length; i++) {
            const char = seedStr.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash;
    }
}

// Helper to get opposite direction (Needed for transition logic)
function getOppositeDirection(dir) {
    // Convert to lowercase to handle both uppercase and lowercase directions
    const direction = dir.toLowerCase();
    switch (direction) {
        case 'n': return 's';
        case 's': return 'n';
        case 'e': return 'w';
        case 'w': return 'e';
        default: return null;
    }
}

export default DungeonHandler;