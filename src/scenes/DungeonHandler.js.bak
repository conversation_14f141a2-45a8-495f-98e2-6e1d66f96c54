import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js'; // Use namespace import
import { STATE } from '../constants.js';
import PlayerController from '../core/PlayerController.js';
// --- Import constants from shared.js ---
import { VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE } from '../generators/prefabs/shared.js';
import {
    getOrCreateGeometry, // Added getOrCreateGeometry
    _getMaterialByHex_Cached // Keep internal access if needed, or remove if unused
} from '../generators/prefabs/shared.js';
// --- Import prefab functions from index.js ---
import { getEnemyData } from '../entities/EnemyTypes.js'; // <<< Import enemy data getter
import { getAreaData } from '../gameData/areas.js'; // NEW
import Enemy from '../entities/Enemy.js';
import Projectile from '../entities/Projectile.js';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
// --- Import the new room generator ---
import { generateRoomVisuals } from '../scenes/roomGenerator.js';
// --- Import necessary constants from roomGenerator ---
import { ROOM_WORLD_SIZE, WALL_HEIGHT, DOOR_WIDTH } from '../scenes/roomGenerator.js';
// --- Import prefab functions from index.js ---
import { createArrowProjectileModel } from '../generators/prefabs/index.js'; // <<< Import arrow model

// --- Helper Function: Integer to Roman Numerals ---
function intToRoman(num) {
    if (num <= 0) return ''; // Handle non-positive numbers
    const lookup = {
        M: 1000, CM: 900, D: 500, CD: 400, C: 100, XC: 90, L: 50, XL: 40, X: 10, IX: 9, V: 5, IV: 4, I: 1
    };
    let roman = '';
    for (let i in lookup) {
        while (num >= lookup[i]) {
            roman += i;
            num -= lookup[i];
        }
    }
    return roman;
}
// --------------------------------------------------

// --- Constants ---
// Constants related to room dimensions (ROOM_WORLD_SIZE, WALL_HEIGHT, WALL_DEPTH, DOOR_HEIGHT, ARCH_VOXEL_SIZE) moved to roomGenerator.js

const DOOR_TRIGGER_SIZE = 1.5; // Size of the trigger box for doors (Increased from 1.0)
const PLAYER_SPAWN_OFFSET = 1.5; // How far from the entry wall the player spawns (Reduced back from 3.0)
// REMOVED: const DOOR_WIDTH = 2.0;
// REMOVED: const WALL_HEIGHT = 3;

// --- Door Placeholder --- (Keep for debug/potential use)
const DOOR_COLOR = 0xffff00; // Yellow
const doorPlaceholderMaterial = new THREE.MeshStandardMaterial({
    color: DOOR_COLOR,
    emissive: DOOR_COLOR,
    emissiveIntensity: 0.3,
    roughness: 0.8,
    metalness: 0.2,
    transparent: true,
    opacity: 0.65
});
// --- End Door Placeholder ---

// --- Dynamic Lighting Constants --- (Keep, used by _updateDynamicLighting)
const BASE_HEALTH = 5;
const MAX_BRIGHTNESS_HEALTH = 20;
const BASE_AMBIENT_INTENSITY = 0.2;
const MIN_AMBIENT_INTENSITY = 0.02;
const MAX_AMBIENT_INTENSITY = 1.2;
const INTENSITY_LERP_FACTOR = 3.0;
const BASE_TORCH_INTENSITY = 0.5;
const MAX_TORCH_INTENSITY = 3.5;
const BASE_TORCH_RANGE = 5.0;
const MAX_TORCH_RANGE = 12.0;
// ----------------------------------

// --- Projectile/Arrow/Dodge/Axes Constants --- (Keep, used by various methods)
const PROJECTILE_SIZE = 0.15;
const PROJECTILE_SPEED = 10.0;
const PROJECTILE_COLOR = 0xa0ffff;
const PROJECTILE_EMISSIVE_INTENSITY = 1.5;
const PROJECTILE_GRAVITY = -4.9;

const ARROW_COLOR = 0xaaaaaa;
const ARROW_GRAVITY = -7.0;

const DODGE_ACTIVATION_DISTANCE = 2.0;
const DODGE_SPEED = 5.0;
const DODGE_DURATION = 0.3;
const DODGE_COOLDOWN = 1.5;

const AXES_HELPER_SIZE = 1.5;
// --------------------------

// --- Enemy Constants --- (Mostly superseded by EnemyTypes.js)
// ----------------------

class DungeonHandler {
    constructor(sceneManager, params) {
        this.sceneManager = sceneManager;
        this.audioManager = sceneManager.audioManager;
        this.initParams = params;
        this.scene = null;
        this.camera = null;
        this.userAnswers = params.userAnswers || [];

        this.player = null;
        this.playerController = null;
        this.auraParticles = null;
        this.debugGlobalLight = null;
        this.isDebugLightOn = false;
        this.ambientLight = null;

        // Camera state
        this.isTopDownView = false;
        this.originalCameraOffset = new THREE.Vector3(0, 10, 18);
        this.topDownCameraOffset = new THREE.Vector3(0, 25, 0);

        // Zoom state
        this.defaultZoom = 0.75;
        this.currentZoom = this.defaultZoom;
        this.minZoom = 0.5;
        this.maxZoom = 2.5;
        this.zoomSpeed = 1.0;

        this.floorLayout = null;
        this.currentRoomId = null;
        this.currentRoomObjects = [];
        this.doorTriggers = [];
        this.transitionCooldown = 0;
        this.transitionDebounce = 0.2;

        this.collisionObjects = [];
        this.activeProjectiles = [];
        this.activeEnemies = [];

        // --- Object Pools ---
        this.playerProjectilePool = [];
        this.enemyProjectilePool = [];
        // --------------------

        // Minimap DOM reference
        this.minimapGridElement = null;
        this.areaNameElement = null;
        this.totalRoomCountElement = null;

        this.currentArea = null; // NEW - Stores the data object for the current room's area

        this.isEspEnabled = false;

        this.enemies = []; // ??? Is this used? activeEnemies seems to be the one. Keep for now.
        this.teleportTriggerMap = new Map();
        this.enemyStateTimers = new Map();
        // REMOVED: this.currentPrefabData = null;

        // --- Rotation Helpers State (Enemies) ---
        this.isEspRotationViewActive = false;
        this.enemyRotationHelpersGroup = new THREE.Group();
        // ----------------------------------------
        
        // --- NEW: Store pre-generated room visuals ---
        this.preGeneratedRooms = new Map(); // Map<RoomID, {roomGroup, collisionMeshes, lights, boundingBox}>
        this.preGenerationComplete = false;
        this.preGenerationInProgress = false;
        // -------------------------------------------
    }

    async init(scene) {
        console.log("DungeonHandler: Initializing (Graph Mode)...");
        this.scene = scene;
        this.scene.background = new THREE.Color(0x050510);
        this.scene.fog = new THREE.FogExp2(0x050510, 0.03);

        // --- Get DOM Element References directly ---
        console.log("Attempting DOM lookup directly in init...");
        this.minimapGridElement = document.getElementById('minimap-grid');
        this.areaNameElement = document.getElementById('area-name-display');
        this.totalRoomCountElement = document.getElementById('total-room-count-display');

        if (!this.areaNameElement) {
            console.warn("Area name display element (#area-name-display) not found in init!");
        } else {
            console.log("Area name display element FOUND in init.");
            this.areaNameElement.style.opacity = '0';
            this.areaNameElement.style.visibility = 'hidden';
        }
        if (!this.minimapGridElement) {
            console.warn("Minimap grid element (#minimap-grid) not found in init!");
        }
        if (!this.totalRoomCountElement) {
            console.warn("Total room count element (#total-room-count-display) not found in init!");
        } else {
            console.log("Total room count element FOUND in init.");
            this._updateTotalRoomCountDisplay(false);
        }
        // ---------------------------------------------

        this._setupCamera();
        this._setupLighting();

        // --- Use Pre-generated Data ---
        this.floorLayout = this.initParams.floorLayout;
        this.player = this.initParams.playerMesh;

        // ... (fallback generation logic) ...
        if (!this.floorLayout || this.floorLayout.size === 0) {
            console.error("DungeonHandler: Received invalid floor layout data!");
            console.warn("Attempting fallback dungeon generation...");
            // NOTE: Needs DungeonGenerator class if fallback is desired
            // const fallbackGenerator = new DungeonGenerator();
            // this.floorLayout = fallbackGenerator.generateLayout();
            // if (!this.floorLayout || this.floorLayout.size === 0) {
                console.error("CRITICAL: Fallback dungeon generation also failed! (DungeonGenerator missing?)");
                this.sceneManager.changeState(STATE.HERO_PAGE);
                return;
            // }
        }
        if (!this.player) {
            console.error("DungeonHandler: Received invalid player mesh data!");
            // Handle error - create fallback player
            console.warn("Creating fallback player model...");
            this.player = createElementalPlayerModel();
        }
        // --------------------------------

        // Add pre-created Player to scene
        this.player.name = "player";
        const playerScale = 0.7;
        this.player.scale.set(playerScale, playerScale, playerScale);
        this.scene.add(this.player);

        // Create Player Controller (using the pre-created player mesh)
        this.playerController = new PlayerController(
            this.player, 
            this.camera, 
            this.scene, 
            this.sceneManager.clock, 
            this, // Pass DungeonHandler instance
            [], // Initial collision objects (empty)
            { minX: -Infinity, maxX: Infinity, minZ: -Infinity, maxZ: Infinity } // No bounds initially
        );

        // Load the starting room (ID 0) immediately
        this.currentRoomId = 0; 
        this.loadRoom(this.currentRoomId);
        
        // Enable player controls
        this.playerController.enable(); 
        
        // Create Aura and other elements
        this._createAura();
        this.debugGlobalLight = new THREE.HemisphereLight(0xffffff, 0x444444, 1.5);
        this.debugGlobalLight.position.set(0, 50, 0);
        this.debugGlobalLight.visible = false;
        this.debugGlobalLight.name = "debugGlobalLight";
        this.scene.add(this.debugGlobalLight);

        // Initial minimap update (may show only start room)
        this._updateMinimap();
        
        // --- NEW: Start background pre-generation after player can move ---
        console.log("Starting background pre-generation of room visuals...");
        // Use setTimeout to allow the first room to fully load before starting pre-generation
        setTimeout(() => {
            this._preGenerateAllRooms();
        }, 500);
        // ------------------------------------------

        console.log("DungeonHandler: Initialized successfully.");

        this.ambientLight = new THREE.AmbientLight(0xffffff, BASE_AMBIENT_INTENSITY);
        this.scene.add(this.ambientLight);
        
        this.enemyRotationHelpersGroup.visible = true;
        this.scene.add(this.enemyRotationHelpersGroup);
    }

    _setupCamera() {
        const aspect = window.innerWidth / window.innerHeight;
        const frustumSize = 12; // Base frustum size, zoom will modify apparent size
        this.camera = new THREE.OrthographicCamera( 
            frustumSize * aspect / - 2, 
            frustumSize * aspect / 2, 
            frustumSize / 2, 
            frustumSize / - 2, 
            1,
            1000
        );
        // Set initial position based on the default offset relative to origin (player not yet loaded)
        this.camera.position.copy(this.originalCameraOffset); 
        this.camera.lookAt(0, 1, 0); // Look towards origin initially
        
        this.camera.up.set(0, 1, 0); 
        
        // Set initial zoom
        this.currentZoom = this.defaultZoom;
        this.camera.zoom = this.currentZoom;
        this.camera.updateProjectionMatrix(); // IMPORTANT: Update projection after setting zoom
        
        this.sceneManager.camera = this.camera; // Assign to SceneManager AFTER creation
        console.log("DungeonHandler: Orthographic camera set up.");
    }

    _setupLighting() {
        const globalDirLight = this.scene.getObjectByName("globalDirectionalLight");
        if (globalDirLight) this.scene.remove(globalDirLight);
        const globalAmbLight = this.scene.getObjectByName("globalAmbientLight");
        if (globalAmbLight) this.scene.remove(globalAmbLight);
        const oldAmbLight = this.scene.getObjectByName("dungeonAmbientLight");
        if (oldAmbLight) this.scene.remove(oldAmbLight);
        const oldKeyLight = this.scene.getObjectByName("dungeonKeyLight");
        if (oldKeyLight) this.scene.remove(oldKeyLight);
        const oldDirLightDungeon = this.scene.getObjectByName("dungeonDirectionalLight");
        if (oldDirLightDungeon) this.scene.remove(oldDirLightDungeon);

        // Store the ambient light reference
        this.ambientLight = new THREE.AmbientLight(0x404080, BASE_AMBIENT_INTENSITY);
        this.ambientLight.name = "dungeonAmbientLight";
        this.scene.add(this.ambientLight);
        console.log(`Dungeon ambient light set up with base intensity: ${BASE_AMBIENT_INTENSITY}`);
    }

    async loadRoom(roomId, entryDirection = null) {
        console.log(`DungeonHandler: Loading room ID: ${roomId} (Entering from: ${entryDirection || 'Start'})`);
        this.cleanupRoom();

        const roomData = this.floorLayout.get(roomId);
        if (!roomData) {
            console.error(`DungeonHandler: Room data not found for ID ${roomId}`);
            return;
        }

        this.currentRoomId = roomId;
        roomData.visited = true;

        // --- Get Area Data --- // UPDATED BLOCK
        const areaId = roomData.state.area || 'catacombs'; // Get areaId, fallback
        this.currentArea = getAreaData(areaId);
        if (!this.currentArea) {
            console.warn(`Area data for "${areaId}" not found for room ${roomId}. Using fallback data.`);
            // Fallback data (using dummy prefabs and basic structure)
            const dummyPrefab = () => new THREE.Group();
            this.currentArea = {
                name: areaId.replace(/_/g, ' ').toUpperCase(), // Basic name from ID
                // displayName: areaId.replace(/_/g, ' ').toUpperCase(), // Use name instead
                walls: [dummyPrefab],
                floors: [dummyPrefab],
                // Add other minimal required properties if needed by generateRoomVisuals
            };
        } else {
            console.log(`   - Applying Area: ${this.currentArea.name} (ID: ${areaId})`);
        }
        // --- End Get Area Data ---

        // --- Display Area Name --- // UPDATED BLOCK
        if (this.currentArea && this.currentArea.name) { // Use name from area data
            let displayString = this.currentArea.name;
            if (roomId > 0) {
                const romanNumeral = intToRoman(roomId);
                if (romanNumeral) displayString += ` ${romanNumeral}`;
            }
            const displayDelay = 100;
            setTimeout(() => this._displayAreaName(displayString), displayDelay);
        }
        // --- End Display Area Name ---

        // --- Use pre-generated room visuals or generate on demand ---
        let roomGroup, collisionMeshes, lights, boundingBox, doorCenterPoints;
        
        if (this.preGeneratedRooms.has(roomId)) {
            console.log(`   - Using pre-generated visuals for room ${roomId}`);
            const preGenData = this.preGeneratedRooms.get(roomId);
            roomGroup = preGenData.roomGroup;
            collisionMeshes = preGenData.collisionMeshes;
            lights = preGenData.lights;
            boundingBox = preGenData.boundingBox;
            doorCenterPoints = preGenData.doorCenterPoints; // <<< Get points from cache
        } else {
            // Show loading indicator if generating on-demand
            const messageElement = document.getElementById('loading-message');
            if (messageElement) {
                messageElement.textContent = 'Generating room...';
                messageElement.style.display = 'block';
            }
            
            console.log(`   - Pre-generated visuals NOT FOUND for room ${roomId}. Generating on demand...`);
            const result = generateRoomVisuals(roomData, this.currentArea); // NEW - Pass currentArea
            roomGroup = result.roomGroup;
            collisionMeshes = result.collisionMeshes;
            lights = result.lights;
            boundingBox = result.boundingBox;
            doorCenterPoints = result.doorCenterPoints; // <<< Get points from generation
            
            // Hide loading indicator
            if (messageElement) {
                messageElement.style.display = 'none';
            }
            
            // Cache the generated room for future use, including door points
            if (roomGroup) {
                this.preGeneratedRooms.set(roomId, {
                    roomGroup, // Direct reference is likely okay for the group itself
                    collisionMeshes, // Direct reference okay
                    lights, // Direct reference okay
                    boundingBox, // Direct reference okay
                    doorCenterPoints: { ...doorCenterPoints } // <<< Store a shallow clone of the map
                });
                console.log(`Pre-generated room ${roomId} (${roomData.type})`);
            }
        }
        
        if (!roomGroup) {
            console.error("   - Failed to get or generate room visuals!");
            return; // Cannot proceed without visuals
        }

        // Add the room group to the scene
        this.scene.add(roomGroup);
        this.currentRoomObjects.push(roomGroup); // Add for cleanup

        // Assign collision meshes
        this.collisionObjects = collisionMeshes || [];

        // Add lights to the scene
        lights.forEach(light => {
            if (light instanceof THREE.PointLight) {
                this.scene.add(light);
                this.currentRoomObjects.push(light); // Add for cleanup
            } else {
                console.warn("   - Invalid light object in lights array:", light);
            }
        });
        // --- End Visual Setup ---

        // --- Create Door Triggers ---
        this.doorTriggers = []; // Clear existing triggers
        const connections = roomData.connections;

        // Use precise door positions if available, otherwise fallback (though fallback is less likely now)
        const usePreciseDoorPositions = doorCenterPoints && Object.values(doorCenterPoints).some(p => p !== null);

        for (const dirKey in connections) {
            const dirLower = dirKey.toLowerCase();
            if (!['n', 's', 'e', 'w'].includes(dirLower)) continue;

            const neighborId = connections[dirKey];
            if (neighborId !== null && neighborId !== undefined) {
                let connectionPos = null;
                
                if (usePreciseDoorPositions && doorCenterPoints[dirLower]) {
                    connectionPos = doorCenterPoints[dirLower].clone();
                    // Keep Y position low for ground-based player collision
                    connectionPos.y = (WALL_HEIGHT || 3) * 0.1; 
                } else {
                     // Fallback using bounding box (less accurate)
                     const doorTriggerY = (WALL_HEIGHT || 3) / 2; 
                     const fallbackPoints = {
                         n: new THREE.Vector3(0, doorTriggerY, boundingBox.minZ),
                         s: new THREE.Vector3(0, doorTriggerY, boundingBox.maxZ),
                         w: new THREE.Vector3(boundingBox.minX, doorTriggerY, 0),
                         e: new THREE.Vector3(boundingBox.maxX, doorTriggerY, 0)
                     };
                     connectionPos = fallbackPoints[dirLower];
                     console.warn(`[loadRoom] Using fallback bounding box position for door trigger ${dirLower.toUpperCase()}`);
                }

                if (!connectionPos) {
                     console.error(`[loadRoom] CRITICAL: Could not determine connection point for dir: ${dirLower.toUpperCase()}`);
                     continue;
                }
                console.log(`[loadRoom] Creating trigger for dir: ${dirLower.toUpperCase()} targeting ${neighborId} at`, connectionPos);

                // Create Invisible Trigger Mesh
                let triggerGeo;
                const triggerHeight = (WALL_HEIGHT || 3) * 1.5; // Make trigger taller
                const triggerThickness = DOOR_TRIGGER_SIZE * 1.5; // Make trigger thicker
                const triggerWidth = (DOOR_WIDTH || 2.0) * 1.1; // Make trigger wider

                if (dirLower === 'n' || dirLower === 's') {
                    triggerGeo = new THREE.BoxGeometry(triggerWidth, triggerHeight, triggerThickness);
                } else { // dir === 'e' || dir === 'w'
                    triggerGeo = new THREE.BoxGeometry(triggerThickness, triggerHeight, triggerWidth);
                }

                const triggerMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00, wireframe: true, visible: this.isEspEnabled });
                const doorTrigger = new THREE.Mesh(triggerGeo, triggerMaterial);

                // Position trigger AT the precise door center (adjusting Y slightly if needed)
                doorTrigger.position.copy(connectionPos);
                doorTrigger.position.y = triggerHeight / 2; // Center the taller trigger vertically
                
                // Apply a smaller offset INTO the room from the precise door center
                const offsetFactor = triggerThickness * 0.2; 
                const offsetDir = new THREE.Vector3();
                if (dirLower === 'n') offsetDir.set(0, 0, 1);  // Move trigger South (into room)
                else if (dirLower === 's') offsetDir.set(0, 0, -1); // Move trigger North (into room)
                else if (dirLower === 'w') offsetDir.set(1, 0, 0);  // Move trigger East (into room)
                else if (dirLower === 'e') offsetDir.set(-1, 0, 0); // Move trigger West (into room)
                doorTrigger.position.addScaledVector(offsetDir, offsetFactor);

                doorTrigger.userData = { isDoorTrigger: true, direction: dirLower, targetRoomId: neighborId };
                doorTrigger.name = `doorTrigger_${dirLower.toUpperCase()}`;
                this.scene.add(doorTrigger);
                this.currentRoomObjects.push(doorTrigger);
                this.doorTriggers.push(doorTrigger);
            }
        }
        // --- End Door Trigger Creation ---

        // 2. Populate Room Content (Enemies, Items)
        this._populateRoom(roomData);

        // 3. Set Player Position using the boundingBox from generateRoomVisuals
        // <<< PASS doorCenterPoints >>>
        this._positionPlayerForEntry(entryDirection, boundingBox, doorCenterPoints);

        // 4. Update Player Controller collisions and bounds
        if (this.playerController) {
            this.playerController.updateCollisionObjects(this.collisionObjects);

            // --- USE boundingBox FROM generateRoomVisuals ---
            if (boundingBox && boundingBox.minX !== undefined) { // Check if valid boundingBox
                const playerSize = this.playerController.playerSize;
                const playerHalfWidth = playerSize.x / 2;
                const playerHalfDepth = playerSize.z / 2;

                this.playerController.floorBounds = {
                    minX: boundingBox.minX + playerHalfWidth,
                    maxX: boundingBox.maxX - playerHalfWidth,
                    minZ: boundingBox.minZ + playerHalfDepth,
                    maxZ: boundingBox.maxZ - playerHalfDepth
                };
                console.log("   - Updated player controller bounds:", this.playerController.floorBounds);
            } else {
                 console.warn("   - Bounding box not available from generateRoomVisuals. Using default large bounds.");
                 this.playerController.floorBounds = { minX: -50, maxX: 50, minZ: -50, maxZ: 50 };
            }
            // --- END BOUNDING BOX USAGE ---

            this.playerController._updatePlayerBounds(); // Recalculate internal derived bounds
            this.playerController._resolveGroundCollision(); // Ensure player is on the ground
        }

        // 5. Update ESP helpers visibility
        this.playerController?._updateEspHelpersVisibility();

        // 6. Update Minimap
        this._updateMinimap();

        console.log(`DungeonHandler: Room ${roomId} (${roomData.type}) loaded successfully.`);
    }

    cleanupRoom() {
        console.log(`DungeonHandler: Cleaning up previous room (ID: ${this.currentRoomId})...`);
        // --- Return Active Projectiles to Pools (don't destroy) --- 
        for (let i = this.activeProjectiles.length - 1; i >= 0; i--) {
             const projectile = this.activeProjectiles[i];
             projectile.visible = false; // Hide it
             // Move to appropriate pool based on owner
             if (projectile.userData.owner === 'player') {
                 this.playerProjectilePool.push(projectile);
             } else if (projectile.userData.owner === 'enemy') {
                 this.enemyProjectilePool.push(projectile);
             }
             // Don't remove from scene, just hide and pool
             // if (projectile.geometry) projectile.geometry.dispose();
             // if (projectile.material) { ... }
             // this.scene.remove(projectile);
        }
        this.activeProjectiles = []; // Clear active list
        // --- End Projectile Cleanup ---

        // --- Cleanup Enemies & their Rotation Helpers --- 
        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {
            const enemy = this.activeEnemies[i];

            // <<< START Rotation Helper Cleanup >>>
            if (enemy.userData.boneHelpers) {
                 console.log(`   -> Cleaning up helpers for enemy ${enemy.name || enemy.uuid}`);
                 Object.values(enemy.userData.boneHelpers).forEach(helperData => {
                     // Now helperData is { axes: ..., box: ... }
                     if (helperData) {
                         if (helperData.axes) {
                             this.enemyRotationHelpersGroup.remove(helperData.axes);
                             if (helperData.axes.geometry) helperData.axes.geometry.dispose();
                             if (helperData.axes.material) helperData.axes.material.dispose();
                         }
                         if (helperData.box) {
                             this.enemyRotationHelpersGroup.remove(helperData.box);
                             if (helperData.box.geometry) helperData.box.geometry.dispose();
                             if (helperData.box.material) helperData.box.material.dispose();
                         }
                     }
                 });
                 enemy.userData.boneHelpers = {}; // Clear the map on the enemy object
            }
            // <<< NEW: Cleanup Overall Bounding Box >>>
            if (enemy.userData.boundingBoxHelper) {
                this.enemyRotationHelpersGroup.remove(enemy.userData.boundingBoxHelper);
                if (enemy.userData.boundingBoxHelper.geometry) enemy.userData.boundingBoxHelper.geometry.dispose();
                if (enemy.userData.boundingBoxHelper.material) enemy.userData.boundingBoxHelper.material.dispose();
                enemy.userData.boundingBoxHelper = null; // Clear reference
            }
            // <<< NEW: Cleanup Skeleton Lines >>>
            if (enemy.userData.skeletonLines) {
                this.enemyRotationHelpersGroup.remove(enemy.userData.skeletonLines);
                if (enemy.userData.skeletonLines.geometry) enemy.userData.skeletonLines.geometry.dispose();
                if (enemy.userData.skeletonLines.material) enemy.userData.skeletonLines.material.dispose();
                enemy.userData.skeletonLines = null;
            }
            // <<< END NEW >>>
            // <<< END Rotation Helper Cleanup >>>

            // --- Original Enemy Mesh Cleanup ---
            enemy.traverse(child => {
                 if (child.isMesh) {
                     if (child.geometry) child.geometry.dispose();
                     if (child.material) {
                         if (Array.isArray(child.material)) child.material.forEach(m => m.dispose());
                         else child.material.dispose();
                     }
                 }
             });
            this.scene.remove(enemy);
            // <<< Remove enemy state timer here too >>>
            if (enemy.userData && enemy.userData.id) {
                 this.enemyStateTimers.delete(enemy.userData.id);
            }
        }
        this.activeEnemies = [];
        // --- End Enemy Cleanup ---

        // --- Handle Room Objects (Don't Dispose Pre-Generated Visuals) ---
        this.currentRoomObjects.forEach(obj => {
            // Only remove objects from the scene, don't dispose of them if they're 
            // part of pre-generated room visuals
            if (obj.userData && obj.userData.isDoorTrigger) {
                // Fully dispose of door triggers as they're recreated each time
                obj.traverse((child) => {
                    if (child.isMesh) {
                        if (child.geometry) child.geometry.dispose();
                        if (child.material) {
                            if (Array.isArray(child.material)) child.material.forEach(m => m.dispose());
                            else if (child.material.dispose) child.material.dispose();
                        }
                    }
                });
            }
            
            // Simply remove all objects from the scene
            this.scene.remove(obj);
        });
        
        this.currentRoomObjects = [];
        this.collisionObjects = []; // Clear collisions
        this.doorTriggers = []; // Clear door triggers
        console.log("DungeonHandler: Previous room cleaned.");

        // --- Dispose Object Pools --- 
        const disposePool = (pool) => {
             pool.forEach(obj => {
                 if (obj.geometry) obj.geometry.dispose();
                 if (obj.material) {
                     if (Array.isArray(obj.material)) obj.material.forEach(m => m.dispose());
                     else if (obj.material.dispose) obj.material.dispose();
                 }
            });
            pool.length = 0;
        };

        // Empty enemy projectile pool only
        disposePool(this.enemyProjectilePool);
        // Keep playerProjectilePool to avoid re-creating player projectiles
    }

    _populateRoom(roomData) {
        console.log(` -> Populating room ${roomData.id} (${roomData.type})`);
        // --- Enemy Spawning --- 
        // Check state: If enemies are NOT cleared AND there are initial enemies defined
        if (!roomData.state.enemiesCleared && roomData.state.initialEnemies && roomData.state.initialEnemies.length > 0) {
            // const enemyCount = 3; // Example: Spawn 3 enemies <<< Remove fixed count
            console.log(`   - Spawning ${roomData.state.initialEnemies.length} enemies from definition:`, roomData.state.initialEnemies);
            // Iterate through the defined list
            for (const enemyType of roomData.state.initialEnemies) {
                // Specify enemy type to spawn
                this._spawnEnemy(enemyType); 
            }
        } else if (roomData.state.enemiesCleared) {
            console.log("   - Enemies already cleared.");
        } else {
            console.log("   - No enemies defined or room type doesn't spawn enemies.");
        }
        // --- End Enemy Spawning ---
        
        if (roomData.type === 'Item'){
            // console.log("   - Item already taken."); // Removed redundant log
        } else {
             // console.log("   - Spawning item (TODO)"); // Removed redundant log
             // Example state update (would happen when player picks up):
             // roomData.state.itemTaken = true;
        }
        // Add logic for other room types (Boss, Shop etc.)
    }

    // --- REWRITTEN Player Positioning Logic ---
    _positionPlayerForEntry(entryDirection, roomBounds, doorCenterPoints) {
        if (!this.player) return;

        const playerY = Math.max(0.5, this.player.position.y); // Maintain current Y or minimum
        let spawnPos = new THREE.Vector3(0, playerY, 0); // Default spawn point (center)
        let lookAtPos = new THREE.Vector3(0, playerY, 0); // Look towards center by default

        const roomData = this.floorLayout?.get(this.currentRoomId);
        const shapeKey = roomData?.shapeKey || 'SQUARE_1X1'; // Get shape for logging/potential fallback

        // --- Determine Room Center (Used for LookAt and Fallback) ---
        const minX = roomBounds?.minX ?? -ROOM_WORLD_SIZE / 2;
        const maxX = roomBounds?.maxX ?? ROOM_WORLD_SIZE / 2;
        const minZ = roomBounds?.minZ ?? -ROOM_WORLD_SIZE / 2;
        const maxZ = roomBounds?.maxZ ?? ROOM_WORLD_SIZE / 2;
        const roomCenterX = (minX + maxX) / 2;
        const roomCenterZ = (minZ + maxZ) / 2;
        lookAtPos.set(roomCenterX, playerY, roomCenterZ); // Default lookAt is room center

        // --- Use Precise Door Position if available ---
        const entryDirLower = entryDirection?.toLowerCase();
        const doorPos = doorCenterPoints && entryDirLower ? doorCenterPoints[entryDirLower] : null;

        if (doorPos) {
            console.log(`[PositionPlayer] Using precise door position for entry '${entryDirLower}' at`, doorPos);
            spawnPos.copy(doorPos); // Start at the door's center point
            spawnPos.y = playerY; // Ensure correct Y level

            // Offset INTO the room based on entry direction
            const offsetVector = new THREE.Vector3();
            switch (entryDirLower) {
                case 'n': offsetVector.set(0, 0, 1); break;  // Came from North, move South
                case 's': offsetVector.set(0, 0, -1); break; // Came from South, move North
                case 'w': offsetVector.set(1, 0, 0); break;  // Came from West, move East
                case 'e': offsetVector.set(-1, 0, 0); break; // Came from East, move West
                default: console.warn(`[PositionPlayer] Unknown entry direction: ${entryDirLower}`); break; // Should not happen
            }
            spawnPos.addScaledVector(offsetVector, PLAYER_SPAWN_OFFSET);
            
            // Look towards the room center from the spawn position
            lookAtPos.set(roomCenterX, playerY, roomCenterZ);

        } else if (entryDirLower) {
             // --- Fallback to Bounding Box Logic (Original Method, slightly adapted) ---
            console.warn(`[PositionPlayer] Precise door position for '${entryDirLower}' not found. Using bounding box fallback for shape ${shapeKey}.`);
            switch (entryDirLower) {
                case 'n': // Came from North, spawn near North wall (min Z)
                    spawnPos.z = minZ + PLAYER_SPAWN_OFFSET;
                    spawnPos.x = roomCenterX;
                    lookAtPos.z = maxZ; // Look South
                    lookAtPos.x = roomCenterX;
                    break;
                case 's': // Came from South, spawn near South wall (max Z)
                    spawnPos.z = maxZ - PLAYER_SPAWN_OFFSET;
                    spawnPos.x = roomCenterX;
                    lookAtPos.z = minZ; // Look North
                    lookAtPos.x = roomCenterX;
                    break;
                case 'w': // Came from West, spawn near West wall (min X)
                    spawnPos.x = minX + PLAYER_SPAWN_OFFSET;
                    spawnPos.z = roomCenterZ;
                    lookAtPos.x = maxX; // Look East
                    lookAtPos.z = roomCenterZ;
                    break;
                case 'e': // Came from East, spawn near East wall (max X)
                    spawnPos.x = maxX - PLAYER_SPAWN_OFFSET;
                    spawnPos.z = roomCenterZ;
                    lookAtPos.x = minX; // Look West
                    lookAtPos.z = roomCenterZ;
                    break;
                default: // Start of dungeon or error
                    console.log("[PositionPlayer] Positioning player at center (Start or unknown entry/fallback)");
                    spawnPos.x = roomCenterX;
                    spawnPos.z = roomCenterZ;
                    lookAtPos.z = minZ; // Look North by default
                    lookAtPos.x = roomCenterX;
                    break;
            }
            spawnPos.y = playerY; // Ensure correct Y level in fallback too
            
        } else {
            // --- Initial Spawn (No entry direction) ---
            console.log("[PositionPlayer] Positioning player at center (Initial dungeon entry)");
            spawnPos.set(roomCenterX, playerY, roomCenterZ);
            lookAtPos.set(roomCenterX, playerY, minZ); // Look North initially
        }

        // --- Collision Safety Check (Keep for now, but should be less needed) ---
        const playerSize = this.playerController?.playerSize || { x: 0.8, y: 1.6, z: 0.8 };
        const tempPlayerBox = new THREE.Box3(
            new THREE.Vector3(spawnPos.x - playerSize.x/2, 0, spawnPos.z - playerSize.z/2),
            new THREE.Vector3(spawnPos.x + playerSize.x/2, playerSize.y, spawnPos.z + playerSize.z/2)
        );

        let hasCollision = false;
        for (const collObj of this.collisionObjects) {
             // Check if collObj and its geometry/children exist before creating Box3
            if (collObj && (collObj.geometry || (collObj.children && collObj.children.length > 0))) {
                try {
                    const collBox = new THREE.Box3().setFromObject(collObj);
                    if (tempPlayerBox.intersectsBox(collBox)) {
                        hasCollision = true;
                        console.warn("Player spawn position intersects collision object. Applying safe offset.");
                        // Move slightly towards the room center
                        const safeOffset = new THREE.Vector3(roomCenterX - spawnPos.x, 0, roomCenterZ - spawnPos.z).normalize().multiplyScalar(0.5);
                        spawnPos.add(safeOffset);
                        lookAtPos.set(roomCenterX, playerY, roomCenterZ); // Look at center after adjustment
                        break; // Only apply one offset
                    }
                } catch (error) {
                     console.error(`[PositionPlayer] Error creating Box3 for collision object: ${collObj.name || collObj.uuid}`, error);
                     // Continue checking other objects
                }
            }
        }
        // --- End Collision Safety Check ---

        // --- Final Application ---
        this.player.position.copy(spawnPos);
        this.player.lookAt(lookAtPos);

        if(this.playerController) this.playerController._updatePlayerBounds(); // Update bounds based on new position
        console.log(`[PositionPlayer] Final Player Position: ${spawnPos.x.toFixed(2)}, ${spawnPos.y.toFixed(2)}, ${spawnPos.z.toFixed(2)}`);
    }
    // --- END REWRITTEN FUNCTION ---

    _createAura() {
        console.log("[_createAura] Creating aura particles...");
        const particleCount = 100;
        const positions = new Float32Array(particleCount * 3);
        const opacities = new Float32Array(particleCount);
        const auraColor = new THREE.Color(0x66DDFF);

        const radius = 0.8;

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            const r = radius * Math.pow(Math.random(), 1.5); 
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.acos(2 * Math.random() - 1);

            positions[i3] = r * Math.sin(phi) * Math.cos(theta);
            positions[i3 + 1] = r * Math.cos(phi);
            positions[i3 + 2] = r * Math.sin(phi) * Math.sin(theta);

            opacities[i] = 1.0 - (r / radius);
        }

        const geometry = new THREE.BufferGeometry();
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('opacityAttr', new THREE.BufferAttribute(opacities, 1));

        const material = new THREE.PointsMaterial({
            color: auraColor,
            size: 0.15,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending,
            depthWrite: false,
            sizeAttenuation: true
        });

        this.auraParticles = new THREE.Points(geometry, material);
        this.auraParticles.name = "playerAura";
        this.scene.add(this.auraParticles);
        console.log("[_createAura] Aura particles created and added to SCENE.", this.auraParticles);
    }

    cleanup() {
        console.log("DungeonHandler: Cleaning up...");
        this.cleanupRoom();

        // --- Dispose Pre-Generated Rooms ---
        console.log(`Disposing of ${this.preGeneratedRooms.size} pre-generated rooms...`);
        this.preGeneratedRooms.forEach((preGenData, roomId) => {
            const { roomGroup, collisionMeshes, lights } = preGenData;
            
            // Dispose room group
            if (roomGroup) {
                roomGroup.traverse(child => {
                    if (child.isMesh) {
                        if (child.geometry) child.geometry.dispose();
                        if (child.material) {
                            if (Array.isArray(child.material)) child.material.forEach(m => m.dispose());
                            else if (child.material.dispose) child.material.dispose();
                        }
                    }
                });
            }
            
            // Dispose lights
            if (lights) {
                lights.forEach(light => {
                    if (light.dispose) light.dispose();
                });
            }
        });
        this.preGeneratedRooms.clear();
        // --- End Pre-Generated Rooms Cleanup ---

        // Dispose player model
        if (this.player) {
            // Dispose aura first if it's attached or related
            if (this.auraParticles) {
                 if(this.auraParticles.geometry) this.auraParticles.geometry.dispose();
                 if(this.auraParticles.material) this.auraParticles.material.dispose();
                 this.scene.remove(this.auraParticles);
                 this.auraParticles = null;
            }
            // Traverse and dispose player geometry/materials
            this.player.traverse((child) => {
                 if (child.isMesh) {
                     if (child.geometry) child.geometry.dispose();
                     if (child.material) {
                          if (Array.isArray(child.material)) child.material.forEach(m => m.dispose());
                          else if (child.material.dispose) child.material.dispose();
                     }
                 }
                 // Remove lights attached to player?
                 if (child.isPointLight) child.removeFromParent(); 
             });
            this.scene.remove(this.player);
            this.player = null;
        }
        this.playerController = null; // Nullify controller reference

        // Remove lights
         const lightNames = ["dungeonAmbientLight", "debugGlobalLight"];
         lightNames.forEach(name => {
             const light = this.scene.getObjectByName(name);
             if (light) this.scene.remove(light);
         });
        this.ambientLight = null;
        this.debugGlobalLight = null;

        // Clear layout data
        this.floorLayout = null;
        // this.dungeonGenerator = null; // Allow garbage collection
        console.log("DungeonHandler: Cleanup complete.");
    }

    update(deltaTime, scene, camera) { // Note: 'camera' argument might be redundant if using this.camera
        // Update cooldown timer
        if (this.transitionCooldown > 0) {
            this.transitionCooldown -= deltaTime;
        }

        // --- Update Player Controller FIRST --- 
        this.playerController?.update(deltaTime);

        // --- Process Camera Zoom Input --- 
        let zoomChanged = false;
        if (this.playerController && this.camera) {
            const zoomDirection = (this.playerController.keys.zoomIn ? 1 : 0) - (this.playerController.keys.zoomOut ? 1 : 0);
            if (zoomDirection !== 0) {
                const newZoom = this.currentZoom + zoomDirection * this.zoomSpeed * deltaTime;
                const clampedZoom = THREE.MathUtils.clamp(newZoom, this.minZoom, this.maxZoom);
                if (clampedZoom !== this.currentZoom) {
                    this.currentZoom = clampedZoom;
                    this.camera.zoom = this.currentZoom;
                    zoomChanged = true;
                }
            }
        }
        // ------------------------------- 

        // --- Update Aura --- 
        const time = this.sceneManager.clock.getElapsedTime(); // Ensure time is available
        if (this.auraParticles && this.player) {
            this.auraParticles.position.copy(this.player.position);
            // Adjust Y offset to center the aura slightly above the player model's base
            const playerHeight = this.playerController?.playerSize?.y || 1.0; // Get player height if available
            this.auraParticles.position.y += playerHeight * 0.5; // Center around player's vertical middle

            // Restore animation
            const sizeSpeed = 2.5;
            const minSize = 0.1;
            const maxSize = 0.2;
            const calculatedSize = minSize + (maxSize - minSize) * (Math.sin(time * sizeSpeed) + 1) / 2;
            this.auraParticles.material.size = calculatedSize;
            this.auraParticles.material.opacity = 0.8; 
            this.auraParticles.rotation.y += deltaTime * 0.2;
        }
        // ------------------- 

        // Update dynamic lighting
        // const time = this.sceneManager.clock.getElapsedTime(); // Time is already declared above
        this._updateDynamicLighting(deltaTime, time);

        // --- Check for Door Trigger Collisions --- 
        if (this.playerController && this.doorTriggers.length > 0 && !this.sceneManager.isFading && this.transitionCooldown <= 0) {
            for (const trigger of this.doorTriggers) {
                const triggerBox = new THREE.Box3().setFromObject(trigger);
                if (this.player && triggerBox.containsPoint(this.player.position)) { 
                    console.log(`Player entered door trigger: ${trigger.name}`);
                    // Get target room ID and entry direction for the *next* room
                    const targetRoomId = trigger.userData.targetRoomId;
                    // Normalize direction to lowercase for getOppositeDirection
                    const direction = trigger.userData.direction.toLowerCase();
                    const entryDirForNextRoom = getOppositeDirection(direction);
                    
                    // Initiate transition (now asynchronous)
                    this._transitionToRoom(targetRoomId, entryDirForNextRoom);
                    break; // Exit loop after first trigger hit
                }
            }
        }
        // --- End Door Trigger Check ---
        
        // --- Update Enemies ---
        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {
            const enemy = this.activeEnemies[i];
            const enemyData = enemy.userData;

            // --- AI Logic --- 
            if (enemyData.aiType === 'skeleton') {
                if (!this.player) continue; // Skip if player doesn't exist

                // Cooldowns
                enemyData.timeSinceLastShot += deltaTime;
                if (enemyData.dodgeCooldownTimer > 0) enemyData.dodgeCooldownTimer -= deltaTime;

                const directionToPlayer = this.player.position.clone().sub(enemy.position);
                directionToPlayer.y = 0; // Ignore vertical difference
                const distanceToPlayer = directionToPlayer.length();
                directionToPlayer.normalize();

                // --- State Machine Logic --- 
                const enemyStateData = this.enemyStateTimers.get(enemyData.id) || { state: 'IDLE', timer: 0 }; // Get state

                // Health Check
                if (enemyData.health <= 0) {
                    console.log(`Enemy ${enemyData.id} defeated!`);
                    if (enemy.geometry) enemy.geometry.dispose();
                    if (enemy.material) enemy.material.dispose();
                    this.scene.remove(enemy);
                    this.activeEnemies.splice(i, 1);
                    this.enemyStateTimers.delete(enemyData.id);
                    continue;
                }

                // --- Transitions based on current state and conditions ---
                const currentState = enemyStateData.state;
                let nextState = currentState;
                enemyStateData.timer -= deltaTime; // Decrement timer

                if (currentState === 'AIMING') {
                    if (enemyStateData.timer <= 0) {
                        nextState = 'SHOOTING';
                    }
                } else if (currentState === 'SHOOTING') {
                    // Shoot action happens below, transition immediately after
                    nextState = 'IDLE'; // Or MOVING based on distance?
                    // Re-evaluate distance to see if should move or stay idle
                    if (distanceToPlayer < enemyData.moveAwayRange || distanceToPlayer > enemyData.preferredRange) {
                        nextState = 'MOVING';
                    }
                } else { // IDLE or MOVING
                    // Check if should start aiming
                    if (distanceToPlayer <= enemyData.preferredRange * 1.1 && 
                        enemyData.timeSinceLastShot >= enemyData.shootCooldown && 
                        !enemyData.isDodging) // Don't aim while dodging
                    {
                        nextState = 'AIMING';
                        enemyStateData.timer = 0.5; // Set aim duration (e.g., 0.5 seconds)
                        enemyData.timeSinceLastShot = 0; // Reset shot cooldown *when aiming starts*
                    }
                }
                // --- End State Transitions ---

                // --- Update State if Changed ---
                if (nextState !== currentState) {
                    // console.log(`Enemy ${enemyData.id} changing state: ${currentState} -> ${nextState}`);
                    enemyStateData.state = nextState;
                    // Reset timer if entering a state that doesn't use it immediately
                    if (nextState !== 'AIMING') {
                        enemyStateData.timer = 0;
                    }
                }
                this.enemyStateTimers.set(enemyData.id, enemyStateData); // Update map
                // --- End State Machine ---

                // --- Dodge Check (Placeholder - should probably integrate with state) ---
                // Simple: Only allow dodging if IDLE or MOVING
                if (!enemyData.isDodging && enemyData.dodgeCooldownTimer <= 0 && 
                    (enemyStateData.state === 'IDLE' || enemyStateData.state === 'MOVING')) 
                {
                    // Check projectiles near the enemy (simplified check)
                    for (const projectile of this.activeProjectiles) {
                        if (projectile.userData.owner === 'player') {
                            const distSq = enemy.position.distanceToSquared(projectile.position);
                            if (distSq < (enemyData.dodgeTriggerDistance * enemyData.dodgeTriggerDistance)) {
                                console.log(`Enemy ${enemyData.id} triggering dodge!`);
                                // Initiate Dodge
                                enemyData.isDodging = true;
                                enemyData.dodgeTimer = enemyData.dodgeDuration;
                                enemyData.dodgeCooldownTimer = enemyData.dodgeCooldown;
                                // Dodge perpendicular to projectile direction
                                const projectileVelNorm = projectile.userData.velocity.clone().normalize();
                                const dodgeDirOption1 = new THREE.Vector3(-projectileVelNorm.z, 0, projectileVelNorm.x);
                                const dodgeDirOption2 = new THREE.Vector3(projectileVelNorm.z, 0, -projectileVelNorm.x);
                                enemyData.dodgeDirection = (Math.random() < 0.5) ? dodgeDirOption1 : dodgeDirOption2;
                                break; // Only dodge one projectile at a time
                            }
                        }
                    }
                }

                // --- Execute Dodge (if active) --- 
                if (enemyData.isDodging) {
                    enemyData.dodgeTimer -= deltaTime;
                    if (enemyData.dodgeTimer <= 0) {
                        enemyData.isDodging = false;
                } else { 
                        // Move in dodge direction
                        const dodgeAmount = enemyData.dodgeSpeed * deltaTime;
                        const dodgeVector = enemyData.dodgeDirection.clone().multiplyScalar(dodgeAmount);
                        // Simple move, no collision check during dodge for now
                        enemy.position.add(dodgeVector);
                        // Maybe add a dodge animation state/pose?
                    }
                } else { // --- Normal Actions Based on State (if not dodging) --- 

                    // --- Look At Player (During Idle, Moving, Aiming) ---
                    if (enemyStateData.state !== 'SHOOTING' && this.player) { // Don't rotate during the brief shoot action
                        const lookTarget = this.player.position.clone();
                        lookTarget.y = enemy.position.y; // Look horizontally
                        enemy.lookAt(lookTarget);
                    }
                    // -----------------------------------------------------

                    // --- Movement (Only if IDLE or MOVING state requests it) --- 
                    let moved = false;
                    let moveDirection = null; // Determine desired move direction based on range
                    if (distanceToPlayer < enemyData.moveAwayRange) {
                        moveDirection = directionToPlayer.clone().negate();
                    } else if (distanceToPlayer > enemyData.preferredRange) {
                        moveDirection = directionToPlayer.clone();
                    } 

                    // Only execute move if in MOVING state and a direction is set
                    if (enemyStateData.state === 'MOVING' && moveDirection) {
                        const moveAmount = enemyData.speed * deltaTime;
                        const moveVector = moveDirection.multiplyScalar(moveAmount); 
                        const originalPosition = enemy.position.clone(); 
                        const finalMove = new THREE.Vector3(0, 0, 0); 

                        // --- Collision Check (Adapted from PlayerController) ---
                        const enemySize = getEnemyData(enemyData.aiType)?.size || 0.6;
                        const enemyHeight = getEnemyData(enemyData.aiType)?.size || 0.6; // Assuming square enemy for now
                        const enemySizeVec = new THREE.Vector3(enemySize, enemyHeight, enemySize);

                        // Function to check collision at a potential position for this enemy
                        const checkEnemyCollision = (potentialEnemyPos) => {
                            // Floor bounds check
                            const halfFloor = ROOM_WORLD_SIZE / 2;
                            const enemyHalfWidth = enemySizeVec.x / 2;
                            const enemyHalfDepth = enemySizeVec.z / 2;
                            if (potentialEnemyPos.x < -halfFloor + enemyHalfWidth ||
                                potentialEnemyPos.x > halfFloor - enemyHalfWidth ||
                                potentialEnemyPos.z < -halfFloor + enemyHalfDepth ||
                                potentialEnemyPos.z > halfFloor - enemyHalfDepth) {
                                // console.log(`Enemy ${enemy.uuid} hit floor boundary.`);
                                return true; // Collision with floor boundary
                            }

                            // Wall collision check
                            const tempBoxCenter = potentialEnemyPos.clone();
                            tempBoxCenter.y += enemySizeVec.y * 0.5; // Center the box vertically
                            const tempEnemyBox = new THREE.Box3();
                            tempEnemyBox.setFromCenterAndSize(tempBoxCenter, enemySizeVec);

                            for (const collisionObject of this.collisionObjects) {
                                if (collisionObject.geometry || (collisionObject.children && collisionObject.children.length > 0)) {
                                    const objectBox = new THREE.Box3().setFromObject(collisionObject);
                                    if (tempEnemyBox.intersectsBox(objectBox)) {
                                        // console.log(`Enemy ${enemy.uuid} collided with wall ${collisionObject.name || collisionObject.uuid}`);
                                        return true; // Collision with a wall
                                    }
                                }
                            }
                            return false; // No collision
                        };

                        // Check X movement independently
                        const potentialPositionX = originalPosition.clone();
                        potentialPositionX.x += moveVector.x;
                        if (!checkEnemyCollision(potentialPositionX)) {
                            finalMove.x = moveVector.x;
                        }

                        // Check Z movement independently (starting from original position + allowed X move)
                        const potentialPositionZ = originalPosition.clone();
                        potentialPositionZ.x += finalMove.x; // Use the allowed X move
                        potentialPositionZ.z += moveVector.z;
                        if (!checkEnemyCollision(potentialPositionZ)) {
                            finalMove.z = moveVector.z;
                        }
                        // --- End Collision Check ---

                        // Apply the allowed movement
                        enemy.position.add(finalMove);
                        moved = finalMove.lengthSq() > 0.0001;
                        // Rotation is now handled by lookAt player
                    } else if (enemyStateData.state === 'MOVING' && !moveDirection) {
                        // If in MOVING state but no direction needed (in preferred range), switch to IDLE
                        enemyStateData.state = 'IDLE';
                        this.enemyStateTimers.set(enemyData.id, enemyStateData);
                    } else if (enemyStateData.state === 'IDLE' && moveDirection) {
                        // If IDLE but should be moving, switch to MOVING
                         enemyStateData.state = 'MOVING';
                         this.enemyStateTimers.set(enemyData.id, enemyStateData);
                    }
                    // --- End Movement Block --- 

                    // --- Animation Decision (Based on State) ---
                    const leftLeg = enemy.getObjectByName('leftLeg');
                    const rightLeg = enemy.getObjectByName('rightLeg');
                    const leftArm = enemy.getObjectByName('leftArm');
                    const rightArm = enemy.getObjectByName('rightArm');

                    if (enemyStateData.state === 'MOVING' && moved) {
                        // Walk Animation
                        const walkSpeed = enemyData.speed * 2.0; 
                        const walkAmplitude = Math.PI / 10; // <<< Reduced Amplitude
                        if (leftLeg && rightLeg) {
                            const time = this.sceneManager.clock.getElapsedTime();
                            leftLeg.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude;
                            rightLeg.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude;
                        }
                        // Reset arm pose during walk
                        if(leftArm) leftArm.rotation.set(0,0,0); 
                        if(rightArm) rightArm.rotation.set(0,0,0);
                    } else if (enemyStateData.state === 'AIMING') { // <<< Added 'else if' for clarity
                        // Aiming Animation (Hold Pose) - Consolidated & Corrected
                        const time = this.sceneManager.clock.getElapsedTime();
                        const idleSwayAmplitude = Math.PI / 64; // Reduced sway
                        const swayOffset = Math.sin(time * 1.0) * idleSwayAmplitude; // Slower sway
                        
                        // Find limb groups
                        const leftArm = enemy.getObjectByName("leftArm");
                        const rightArm = enemy.getObjectByName("rightArm");
                        const leftLeg = enemy.getObjectByName("leftLeg");
                        const rightLeg = enemy.getObjectByName("rightLeg");
                        
                        if(leftArm) {
                            // Keep left arm relatively still, maybe slightly raised/forward
                            leftArm.rotation.set(-Math.PI / 16, 0, swayOffset); // Slight raise, minimal sway, no Y rotation
                        }
                        if(rightArm) {
                            // Calculate pullback using rotation.y based on timer
                            // Assuming enemyStateData.timer counts down from 0.5 to 0 during AIMING
                            const pullbackProgress = Math.min(1.0, 1.0 - (enemyStateData.timer / 0.5)); // Ensure progress is 0 to 1
                            const pullbackAngleY = pullbackProgress * -Math.PI / 32; // Apply the minimal pullback angle
                            
                            rightArm.rotation.set(0, pullbackAngleY, -swayOffset); // No X rotation, apply Y pullback, minimal sway
                        }
                        // Keep legs neutral during aiming
                        if (leftLeg) leftLeg.rotation.set(0,0,0);
                        if (rightLeg) rightLeg.rotation.set(0,0,0);
                    } else if (enemyStateData.state === 'IDLE' || enemyStateData.state === 'WALKING') {
                        // Existing idle/walking animation logic...
                        const idleSpeed = 1.5;
                        const idleBobAmplitude = 0.05; 
                        const idleSwayAmplitude = Math.PI / 32; 
                        if (leftLeg && rightLeg && leftArm && rightArm) {
                            const time = this.sceneManager.clock.getElapsedTime();
                            const bobOffset = Math.sin(time * idleSpeed) * idleBobAmplitude;
                            const swayOffset = Math.sin(time * idleSpeed * 0.7) * idleSwayAmplitude;

                            leftLeg.rotation.x = bobOffset * 0.5;
                            rightLeg.rotation.x = bobOffset * 0.5;
                            leftLeg.rotation.z = swayOffset;
                            rightLeg.rotation.z = -swayOffset;
                            // Apply only sway to arms during idle
                            leftArm.rotation.x = 0; leftArm.rotation.y = 0; 
                            rightArm.rotation.x = 0; rightArm.rotation.y = 0;
                            leftArm.rotation.z = swayOffset * 0.8;
                            rightArm.rotation.z = -swayOffset * 0.8;
                        }
                    } else { // SHOOTING or DODGING or other states - reset pose quickly
                         if (leftLeg) leftLeg.rotation.set(0,0,0);
                         if (rightLeg) rightLeg.rotation.set(0,0,0);
                         if (leftArm) leftArm.rotation.set(0,0,0);
                         if (rightArm) rightArm.rotation.set(0,0,0);
                    }
                    // --- End Animation --- 

                    // --- Shooting (Triggered by State) --- 
                    if (enemyStateData.state === 'SHOOTING') 
                    {
                        // --- Spawn Arrow (only once per SHOOTING state entry) ---
                        // Calculate arrow start position relative to bow/hand
                        let arrowStartPos = new THREE.Vector3();
                        
                        // <<< UPDATE ENEMY MATRIX WORLD FIRST >>>
                        enemy.updateMatrixWorld(true);
                        // <<< ------------------------------ >>>
                        
                        const bowGroup = enemy.getObjectByName('SkeletonBow'); // Find the bow group
                        
                        if (bowGroup && this.player) { // Need bow and player
                            // Get world position of the bow's center (approx grip)
                            bowGroup.getWorldPosition(arrowStartPos);
                            // Aim from bow position towards player
                            const aimDirection = this.player.position.clone().sub(arrowStartPos).normalize();
                             // Offset start slightly forward along aim direction
                             arrowStartPos.addScaledVector(aimDirection, VOXEL_SIZE * 4); // <<< Use imported VOXEL_SIZE

                        const definition = getEnemyData(enemyData.aiType);
                        if (definition) {
                                // Pass true for the isArrowFlag
                                this.spawnProjectile(arrowStartPos, aimDirection, definition);
                            }
                        } else { // Fallback if bow/player missing
                           console.warn("Could not find SkeletonBow or player to spawn arrow correctly.");
                           // Fallback spawn from enemy center
                            arrowStartPos.copy(enemy.position);
                            const definition = getEnemyData(enemyData.aiType);
                            // Use definition.size if available for fallback height
                            arrowStartPos.y += (definition?.size || 0.6) * 0.7; 
                             const aimDirection = directionToPlayer.clone(); // Use pre-calculated direction
                            if (definition) {
                                // Pass true for the isArrowFlag in fallback too
                                this.spawnProjectile(arrowStartPos, aimDirection, definition);
                            }
                        }
                        // State transition happens at top of next frame
                        // ---------------------------------------------
                    }
                    // --- End Shooting ---
                } // End Normal Actions Block
            } else {
                 // Handle other AI types
            }
            // --- End AI Logic ---
        } // --- End Enemy Update --- 

        // --- Refactor: Update ALL Projectiles ---
        for (let i = this.activeProjectiles.length - 1; i >= 0; i--) {
            const projectile = this.activeProjectiles[i];
            const projectileData = projectile.userData;

            // --- Movement --- 
            const horizontalMove = projectileData.velocity.clone().multiplyScalar(deltaTime);
            projectile.position.add(horizontalMove);
            projectileData.distanceTraveled += horizontalMove.length();
            // Use ARROW_GRAVITY if owner is enemy, otherwise PROJECTILE_GRAVITY
            const gravity = (projectileData.owner === 'enemy') ? ARROW_GRAVITY : PROJECTILE_GRAVITY;
            projectileData.verticalVelocity += gravity * deltaTime;
            projectile.position.y += projectileData.verticalVelocity * deltaTime;
            const floorY = 0;
            // Calculate radius differently for spheres (player mesh) and arrows (enemy group)
            let radius = 0.1; // Default small radius
            if (projectile.isMesh && projectile.geometry?.parameters) { // Player sphere projectile
                 radius = projectile.geometry.parameters.radius || 0.1; // Use radius if available
            } else if (projectile.isGroup) { // Enemy arrow projectile (Group)
                 // Approximate radius based on voxel size used for arrows (VOXEL_SIZE * 0.8)
                 const approxArrowRadius = VOXEL_SIZE * 0.8 * 2; // Estimate: 2x arrow voxel size
                 radius = approxArrowRadius;
            }
            // --- End Radius Calculation ---
            
            if (projectile.position.y < floorY + radius) {
                 projectileData.distanceTraveled = projectileData.range + 1; // Mark for removal
            }
            // --- End Movement ---

            // --- Range Check --- 
            const outOfRange = projectileData.distanceTraveled >= projectileData.range;

            // --- Collision Checks (Wall, Enemy, Player) --- 
            let collided = false;
            let hitTarget = null; // Store what was hit
            const projectileBox = new THREE.Box3().setFromObject(projectile);

            if (!outOfRange) { 
                // --- REFACTORED COLLISION CHECKS --- 
                
                // 1. Check IMPASSABLE objects first (Walls, Floors, Doors that ARE NOT destructible)
                for (const obstacle of this.collisionObjects) {
                    if (obstacle && obstacle.isObject3D && 
                        (obstacle.geometry || (obstacle.children && obstacle.children.length > 0)) &&
                        !obstacle.userData?.isDestructible) { // <<< Check if NOT destructible
                        
                        const obstacleBox = new THREE.Box3().setFromObject(obstacle);
                        if (projectileBox.intersectsBox(obstacleBox)) {
                            collided = true;
                            hitTarget = 'wall'; // Treat non-destructible hits as walls
                            // console.log("Projectile hit non-destructible wall/obstacle");
                            break; // Projectile stops here
                        }
                    }
                }

                // 2. Check DESTRUCTIBLE objects *only if* no wall was hit
                if (!collided) {
                    for (const destructible of this.collisionObjects) {
                         if (destructible && destructible.isObject3D && 
                             (destructible.geometry || (destructible.children && destructible.children.length > 0)) &&
                             destructible.userData?.isDestructible) { // <<< Check if IS destructible
                            
                            const destructibleBox = new THREE.Box3().setFromObject(destructible);
                            if (projectileBox.intersectsBox(destructibleBox)) {
                                collided = true;
                                hitTarget = 'destructible';
                                console.log(`Projectile hit destructible object: ${destructible.name || destructible.userData.objectType || destructible.uuid}`);
                                
                                // Apply damage (Only player projectiles damage objects for now)
                                if (projectileData.owner === 'player') {
                                    destructible.userData.health = (destructible.userData.health || 1) - projectileData.damage;
                                    console.log(`Object health: ${destructible.userData.health}`);
                                    if (destructible.userData.health <= 0) {
                                        console.log(`Destructible object ${destructible.name || destructible.userData.objectType} destroyed!`);
                                        // <<< Capture impact data >>>
                                        const pointOfImpact = projectile.position.clone();
                                        const projectileVelocity = projectileData.velocity.clone();
                                        // <<< Pass impact data to handler >>>
                                        this._handleObjectDestruction(destructible, pointOfImpact, projectileVelocity);
                                    }
                                } else {
                                    // Optional: Log if enemy projectile hits destructible (maybe ricochet later?)
                                    // console.log("Enemy projectile hit destructible, ignoring damage.");
                                }
                                break; // Projectile stops here
                            }
                         }
                    }
                }

                // 3. Check ACTORS (Enemies/Player) *only if* nothing else was hit
                if (!collided) {
                    if (projectileData.owner === 'player') {
                        // Check collision with enemies
                        for (let j = this.activeEnemies.length - 1; j >= 0; j--) {
                             const enemy = this.activeEnemies[j];
                             const enemyBox = new THREE.Box3().setFromObject(enemy);
                             if (projectileBox.intersectsBox(enemyBox)) {
                                collided = true;
                                hitTarget = 'enemy';
                                console.log("Player projectile hit enemy!");
                                // Damage Enemy
                                enemy.userData.health -= projectileData.damage;
                                console.log(`Enemy health: ${enemy.userData.health}`);
                                if (enemy.userData.health <= 0) {
                                    console.log("Enemy defeated!");
                                    this._handleEnemyDeath(enemy, j); // Use the existing handler
                                    // Room clear check is implicitly handled after _handleEnemyDeath now
                                }
                                break; // Stop checking other enemies
                             }
                        }
                    } else if (projectileData.owner === 'enemy' && this.player) {
                        // Check collision with player
                        const playerBox = new THREE.Box3().setFromObject(this.player); // Use player mesh directly
                        if (projectileBox.intersectsBox(playerBox)) {
                            collided = true;
                            hitTarget = 'player';
                            console.log("Enemy projectile hit player!");
                            if (this.playerController) {
                                this.playerController.takeDamage(projectileData.damage);
                            }
                            // No break needed as there's only one player
                        }
                    }
                }
                // --- END REFACTORED COLLISION CHECKS ---
            }

            // Remove projectile if out of range or collided
            if (outOfRange || collided) {
                // --- Return to Pool instead of disposing ---
                projectile.visible = false; 
                if (projectile.userData.owner === 'player') {
                     this.playerProjectilePool.push(projectile);
                 } else if (projectile.userData.owner === 'enemy') {
                     this.enemyProjectilePool.push(projectile);
                 }
                 // Remove from active list
                this.activeProjectiles.splice(i, 1); 
                
                // --- REMOVE Disposal --- 
                // if (projectile.geometry) projectile.geometry.dispose();
                // if (projectile.material) projectile.material.dispose();
                // this.scene.remove(projectile);
                // -----------------------
            }
        }
        // --- End Projectile Update ---

        // --- Update Active Bone Pieces (Iterate room objects) --- 
        // <<< DEFINE PHYSICS CONSTANTS HERE >>>
        const gravity = -9.8;
        const groundLevel = 0.0;
        const tempQuaternion = new THREE.Quaternion();
        const tempAxis = new THREE.Vector3();
        const tempBox = new THREE.Box3();
        const groundReactionFactor = 15.0;
        // <<< FINAL TUNING FOR SPREAD >>>
        const groundDragFactor = 0.995;     // Almost no sliding drag
        const groundAngularDragFactor = 0.99; // Almost no angular drag
        const groundVerticalDamping = 0.95;   // Minimal vertical damping (Max bounce)
        // <<< END TUNING >>>
        const maxReactionVelocity = 1.0;
        // Resting thresholds
        const restLinearThresholdSq = 0.5 * 0.5;
        const restAngularThresholdSq = 1.0 * 1.0;
        const groundThreshold = VOXEL_SIZE * 0.5;   
        const wallBounceDamping = 0.5;
        // Room bounds for debris containment
        const roomHalfSize = ROOM_WORLD_SIZE / 2;
        const roomBounds = new THREE.Box3(
            new THREE.Vector3(-roomHalfSize, -1, -roomHalfSize),
            new THREE.Vector3(roomHalfSize, 10, roomHalfSize)
        );

        // Temporary vectors for calculations
        const tempDebrisBox = new THREE.Box3();
        const tempColliderBox = new THREE.Box3();
        const tempDebrisCenter = new THREE.Vector3();
        const tempColliderCenter = new THREE.Vector3();
        const tempCollisionNormal = new THREE.Vector3();
        const tempMTV = new THREE.Vector3();
        const tempVelocity = new THREE.Vector3();

        // <<< START Refactored Loop >>>
        for (let i = this.currentRoomObjects.length - 1; i >= 0; i--) {
            const obj = this.currentRoomObjects[i];
            
            // Process both bone pieces and debris
            if (obj.userData?.isBonePiece || obj.userData?.isDebrisPiece) { 
                const pieceMesh = obj; 
                const pieceData = pieceMesh.userData;

                if (pieceData.isFalling) {
                    // Store original position for collision resolution
                    const originalPosition = pieceMesh.position.clone();
                    
                    // Apply gravity and update velocity
                    pieceData.velocity.y += gravity * deltaTime;
                    
                    // Calculate potential new position
                    const potentialPosition = originalPosition.clone().addScaledVector(pieceData.velocity, deltaTime);
                    
                    // Check room bounds first
                    if (!roomBounds.containsPoint(potentialPosition)) {
                        // Clamp position to room bounds and reflect velocity
                        if (potentialPosition.x < -roomHalfSize) {
                            potentialPosition.x = -roomHalfSize;
                            pieceData.velocity.x = Math.abs(pieceData.velocity.x) * wallBounceDamping;
                        } else if (potentialPosition.x > roomHalfSize) {
                            potentialPosition.x = roomHalfSize;
                            pieceData.velocity.x = -Math.abs(pieceData.velocity.x) * wallBounceDamping;
                        }
                        
                        if (potentialPosition.z < -roomHalfSize) {
                            potentialPosition.z = -roomHalfSize;
                            pieceData.velocity.z = Math.abs(pieceData.velocity.z) * wallBounceDamping;
                        } else if (potentialPosition.z > roomHalfSize) {
                            potentialPosition.z = roomHalfSize;
                            pieceData.velocity.z = -Math.abs(pieceData.velocity.z) * wallBounceDamping;
                        }
                        
                        if (potentialPosition.y < groundLevel) {
                            potentialPosition.y = groundLevel;
                            pieceData.velocity.y = 0;
                            pieceData.isFalling = false;
                        }
                    }
                    
                    // Update mesh position temporarily for collision check
                    pieceMesh.position.copy(potentialPosition);
                    pieceMesh.updateMatrixWorld(true);
                    tempDebrisBox.setFromObject(pieceMesh);
                    
                    // Check collisions with walls
                    let hasCollision = false;
                    for (const wall of this.collisionObjects) {
                        if (!wall.visible || !wall.userData?.isWallSegment) continue;
                        
                        tempColliderBox.setFromObject(wall);
                        if (tempDebrisBox.intersectsBox(tempColliderBox)) {
                            hasCollision = true;
                            
                            // Calculate collision normal and penetration depth
                            tempDebrisBox.getCenter(tempDebrisCenter);
                            tempColliderBox.getCenter(tempColliderCenter);
                            
                            // Determine collision axis (X or Z) based on penetration depth
                            const overlapX = (tempDebrisBox.max.x - tempDebrisBox.min.x + tempColliderBox.max.x - tempColliderBox.min.x) / 2 
                                           - Math.abs(tempDebrisCenter.x - tempColliderCenter.x);
                            const overlapZ = (tempDebrisBox.max.z - tempDebrisBox.min.z + tempColliderBox.max.z - tempColliderBox.min.z) / 2
                                           - Math.abs(tempDebrisCenter.z - tempColliderCenter.z);
                            
                            if (overlapX < overlapZ) {
                                // X-axis collision
                                tempCollisionNormal.set(Math.sign(tempDebrisCenter.x - tempColliderCenter.x), 0, 0);
                                potentialPosition.x = originalPosition.x; // Revert X position
                            } else {
                                // Z-axis collision
                                tempCollisionNormal.set(0, 0, Math.sign(tempDebrisCenter.z - tempColliderCenter.z));
                                potentialPosition.z = originalPosition.z; // Revert Z position
                            }
                            
                            // Reflect and dampen velocity
                            tempVelocity.copy(pieceData.velocity);
                            pieceData.velocity.reflect(tempCollisionNormal).multiplyScalar(wallBounceDamping);
                            
                            // Apply angular impulse for more realistic collision response
                            const impulseMagnitude = tempVelocity.length() * 0.5;
                            pieceData.angularVelocity.addScaledVector(tempCollisionNormal, impulseMagnitude);
                            
                            break; // Handle one collision at a time
                        }
                    }
                    
                    // Update final position
                    pieceMesh.position.copy(potentialPosition);
                    
                    // Update rotation
                    if (pieceData.angularVelocity.lengthSq() > 0.001) {
                        const angle = pieceData.angularVelocity.length() * deltaTime;
                        tempAxis.copy(pieceData.angularVelocity).normalize();
                        tempQuaternion.setFromAxisAngle(tempAxis, angle);
                        pieceMesh.quaternion.premultiply(tempQuaternion);
                        
                        // Dampen angular velocity
                        pieceData.angularVelocity.multiplyScalar(0.98);
                    }
                    
                    // Check if piece should stop moving
                    if (!pieceData.isFalling && pieceData.velocity.lengthSq() < restLinearThresholdSq 
                        && pieceData.angularVelocity.lengthSq() < restAngularThresholdSq) {
                        pieceData.velocity.set(0, 0, 0);
                        pieceData.angularVelocity.set(0, 0, 0);
                    }
                }
            }
        }
        // --- END NEW Collision Check ---

        // Ground Collision Response
        if (potentialPenetration > -groundThreshold) {
            isOnGroundNow = true;
            pieceData.isFalling = false; 
            pieceData.velocity.set(0, 0, 0);
            pieceData.angularVelocity.set(0, 0, 0);
            finalPosition.y = currentPosition.y + (groundLevel - currentLowestPoint); 
        } else {
            // Still airborne, apply gravity effect (already done before)
            pieceData.velocity.y += gravity * deltaTime;
        }

        // Apply final position
        pieceMesh.position.copy(finalPosition);

        // Update rotation (only if still falling/tumbling)
        if (pieceData.isFalling) {
            const angle = pieceData.angularVelocity.length() * deltaTime;
            if (angle > 0.001) { 
                tempAxis.copy(pieceData.angularVelocity).normalize();
                tempQuaternion.setFromAxisAngle(tempAxis, angle);
                pieceMesh.quaternion.premultiply(tempQuaternion);
            }
        }
    } // End if (pieceData.isFalling) initially check
} // End if (obj.userData?.isBonePiece || obj.userData?.isDebrisPiece) 
} // <<< END Refactored Loop >>>
// --- End Bone Piece Update ---

// --- Camera Update ---
// Smooth Camera Movement Towards Player
if (this.player && this.camera) {
    const targetCameraPosition = new THREE.Vector3();
    // Calculate the desired camera position based on player, maintaining offset
    const cameraOffset = new THREE.Vector3(0, 10, 18); // Your original offset
    targetCameraPosition.copy(this.player.position).add(cameraOffset);
    
    // Use LERP to smoothly move the camera towards the target position
    const lerpFactor = 0.05; // Adjust this value for faster/slower camera movement (0.01 = slow, 0.1 = fast)
    this.camera.position.lerp(targetCameraPosition, lerpFactor);
    
    // Keep the camera looking at a point slightly in front of the player's feet
    const lookAtTarget = this.player.position.clone();
    lookAtTarget.y = 0; // Look at the ground level where the player is
    // Optional: Add a small forward offset based on player rotation if you want camera to lead slightly
    // const forward = new THREE.Vector3(0, 0, -1).applyQuaternion(this.player.quaternion);
    // lookAtTarget.addScaledVector(forward, 1.0); 
    
    this.camera.lookAt(lookAtTarget);
    // No need to call updateProjectionMatrix unless zoom/frustum changes
}

// --- Camera Update ---
if (this.player && this.camera) {
    const targetCameraPosition = new THREE.Vector3();
    const lookAtTarget = this.player.position.clone();
    const lerpFactor = 0.05; // Keep consistent lerp factor

    if (this.isTopDownView) {
        // Top-down view: Directly above the player
        targetCameraPosition.copy(this.player.position).add(this.topDownCameraOffset);
        lookAtTarget.y = this.player.position.y; // Look directly at the player's y-level
        
        // Apply LERP to position for smooth follow
        this.camera.position.lerp(targetCameraPosition, lerpFactor);
        // For top-down, we might want an instant lookAt or a very fast lerp for the lookAt target
        // For now, let's use the same lookAt lerping as the position for consistency,
        // but apply it to a temporary vector to avoid modifying the camera's lookAt directly
        // if the camera uses a lookAt target internally that shouldn't be lerped.
        // Since THREE.Camera.lookAt sets the rotation quaternion, lerping the lookAt point
        // indirectly lerps the rotation.
        const currentLookAt = new THREE.Vector3();
        // Get the current lookAt point (approximation)
        const forward = new THREE.Vector3(0, 0, -1).applyQuaternion(this.camera.quaternion);
        currentLookAt.copy(this.camera.position).add(forward); 
        
        const lerpedLookAt = currentLookAt.lerp(lookAtTarget, lerpFactor * 2); // Faster lookAt lerp maybe?
        this.camera.lookAt(lerpedLookAt); 

    } else {
        // Default view: Offset from the player
        targetCameraPosition.copy(this.player.position).add(this.originalCameraOffset);
        lookAtTarget.y = 0; // Look at the ground level slightly ahead/at player's feet

        // Use LERP to smoothly move the camera towards the target position
        this.camera.position.lerp(targetCameraPosition, lerpFactor);

        // Smoothly lerp the lookAt target as well
         const currentLookAt = new THREE.Vector3();
         const forward = new THREE.Vector3(0, 0, -1).applyQuaternion(this.camera.quaternion);
         currentLookAt.copy(this.camera.position).add(forward); 
        
         const lerpedLookAt = currentLookAt.lerp(lookAtTarget, lerpFactor); // Same lerp factor for lookAt
         this.camera.lookAt(lerpedLookAt);
    }
     // Ensure the up vector is correct, especially after large rotation changes
     // Orthographic cameras often need manual 'up' management if not looking mostly horizontal
     if (this.isTopDownView) {
        // If looking straight down, 'up' should point along Z or X axis
        // Which axis depends on the desired orientation of the top-down view.
        // Let's assume we want North to be 'up' on the screen (negative Z in world space)
         this.camera.up.set(0, 0, -1); 
     } else {
         this.camera.up.set(0, 1, 0); // Reset to default 'up' for the standard view
     }

    // No need to call updateProjectionMatrix unless zoom/frustum changes
}
// ... rest of update method ...

// --- Update Projection Matrix if Zoom Changed (or if view toggled) ---
// The toggleCameraView function already calls updateProjectionMatrix, 
// so we only need to call it here if zoom changed via keys.
if (zoomChanged) {
    this.camera.updateProjectionMatrix();
}
// -----------------------------------------------------------------

// --- Update Enemy Rotation Helpers ---
this._updateEnemyRotationHelpers();
// -----------------------------------

try {
    if (this.isDebugLightOn) {
        // Optionally update debug light position if needed
    }
} catch (error) {
    console.error("Error in DungeonHandler update:", error);
}
}

_transitionToRoom(targetRoomId, entryDirection) {
    // Prevent double transitions if already fading
    if (this.sceneManager.isFading) return; 

    // Prevent triggering immediately if already cooling down (safety)
    if (this.transitionCooldown > 0) return;

    console.log(`Transitioning to Room ID: ${targetRoomId}, entering from ${entryDirection}`);
    
    // Disable player input during transition
    this.playerController?.disable(); 

    // Start fade out, load room in callback
    this.sceneManager.startFade(() => {
        // This code runs when screen is black
        this.loadRoom(targetRoomId, entryDirection);
        // SceneManager.endFade() is automatically called after this callback completes
        
        // Start the cooldown AFTER the new room starts loading
        this.transitionCooldown = 0.5; // Set cooldown duration (e.g., 0.5 seconds)

        // Re-enable player input AFTER new room is loaded and fade starts/ends
        // Need to ensure fade completes before enabling
        // For now, enable slightly delayed after load starts
        // A better way would be a callback on sceneManager.endFade completion
        setTimeout(() => { 
            this.playerController?.enable();
        }, 50); // Small delay, adjust if needed
    });
}

// --- _updateDynamicLighting (Extracted from previous update) ---
_updateDynamicLighting(deltaTime, time) {
     // Update Ambient Light
     if (this.ambientLight && this.playerController && typeof this.playerController.currentHealth === 'number') {
        const currentHealth = this.playerController.currentHealth;
        const maxHealth = this.playerController.maxHealth;
        let targetIntensity = BASE_AMBIENT_INTENSITY;
        if (currentHealth <= 0) {
            targetIntensity = MIN_AMBIENT_INTENSITY;
        } else if (currentHealth <= BASE_HEALTH) {
            const healthRatio = currentHealth / BASE_HEALTH;
            const easedRatio = Math.pow(healthRatio, 2.5);
            targetIntensity = MIN_AMBIENT_INTENSITY + (BASE_AMBIENT_INTENSITY - MIN_AMBIENT_INTENSITY) * easedRatio;
        } else {
            const clampedHealth = Math.min(currentHealth, MAX_BRIGHTNESS_HEALTH);
            targetIntensity = THREE.MathUtils.mapLinear(
                clampedHealth, 
                BASE_HEALTH, MAX_BRIGHTNESS_HEALTH, 
                BASE_AMBIENT_INTENSITY, MAX_AMBIENT_INTENSITY
            );
        }
        this.ambientLight.intensity = THREE.MathUtils.lerp(
            this.ambientLight.intensity, 
            targetIntensity, 
            INTENSITY_LERP_FACTOR * deltaTime
        );
    }

    // Update Torch Lights
    // --- OPTIMIZED: Iterate currentRoomObjects --- 
    this.currentRoomObjects.forEach((object) => {
    // this.scene.traverse((object) => { // <<< OLD inefficient way
        if (object.isPointLight && object.name === 'torchLight') {
            if (this.playerController && typeof this.playerController.currentHealth === 'number') {
                const currentHealth = this.playerController.currentHealth;
                if (currentHealth < 3) {
                    object.intensity = 0;
                    object.distance = 0;
                } else if (currentHealth <= BASE_HEALTH) {
                    // Use BASE_TORCH_INTENSITY and BASE_TORCH_RANGE
                    object.intensity = BASE_TORCH_INTENSITY * (0.8 + Math.sin(time * 6 + object.position.x) * 0.2 + Math.random() * 0.15);
                    object.distance = BASE_TORCH_RANGE;
                    // object.shadow.camera.far = BASE_TORCH_RANGE + 1; // Shadows disabled
                } else {
                     // Calculate intensity/range based on health between BASE and MAX
                     const highHealthRatio = Math.min(1, (currentHealth - BASE_HEALTH) / (MAX_BRIGHTNESS_HEALTH - BASE_HEALTH));
                     const targetIntensity = BASE_TORCH_INTENSITY + (MAX_TORCH_INTENSITY - BASE_TORCH_INTENSITY) * highHealthRatio;
                     const targetRange = BASE_TORCH_RANGE + (MAX_TORCH_RANGE - BASE_TORCH_RANGE) * highHealthRatio;
                     object.intensity = targetIntensity * (0.8 + Math.sin(time * 6 + object.position.x) * 0.2 + Math.random() * 0.15);
                     object.distance = targetRange;
                     // object.shadow.camera.far = targetRange + 1; // Shadows disabled
                }
            } else {
                // Fallback if player health not available (shouldn't happen often)
                object.intensity = BASE_TORCH_INTENSITY * (0.8 + Math.sin(time * 6 + object.position.x) * 0.2 + Math.random() * 0.15);
                object.distance = BASE_TORCH_RANGE;
                // object.shadow.camera.far = BASE_TORCH_RANGE + 1; // Shadows disabled
            }
            // ... (end logic) ...
        }
    });
    // --- END OPTIMIZED LOOP ---
}

onResize() {
     if (this.camera instanceof THREE.OrthographicCamera) {
        const aspect = window.innerWidth / window.innerHeight;
        const frustumSize = 12;
        this.camera.left = frustumSize * aspect / -2;
        this.camera.right = frustumSize * aspect / 2;
        this.camera.top = frustumSize / 2;
        this.camera.bottom = frustumSize / -2;
        this.camera.updateProjectionMatrix();
     } else if (this.camera instanceof THREE.PerspectiveCamera) {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
     }
}

// --- Minimap Update Logic ---
_updateMinimap() {
    if (!this.minimapGridElement || !this.floorLayout) {
        console.warn("Minimap element or floor layout not found, cannot update minimap.");
        return;
    }

    // Clear previous minimap
    this.minimapGridElement.innerHTML = '';

    // Find bounds of visited rooms
    let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
    let visitedRoomsExist = false;
    this.floorLayout.forEach(room => {
        if (room.visited) {
            visitedRoomsExist = true;
            minX = Math.min(minX, room.coords.x);
            maxX = Math.max(maxX, room.coords.x);
            minY = Math.min(minY, room.coords.y);
            maxY = Math.max(maxY, room.coords.y);
        }
    });

    if (!visitedRoomsExist) return; // Don't draw anything if no rooms visited

    const mapWidth = maxX - minX + 1;
    const mapHeight = maxY - minY + 1;

    // Adjust grid template columns based on calculated width
    this.minimapGridElement.style.gridTemplateColumns = `repeat(${mapWidth}, auto)`;

    // Create grid elements
    for (let y = minY; y <= maxY; y++) {
        for (let x = minX; x <= maxX; x++) {
            const roomDiv = document.createElement('div');
            roomDiv.classList.add('minimap-room');

            // Find if a room exists at these coords in the layout
            let roomAtCoords = null;
            for (const room of this.floorLayout.values()) {
                if (room.coords.x === x && room.coords.y === y) {
                    roomAtCoords = room;
                    break;
                }
            }

            if (roomAtCoords && roomAtCoords.visited) {
                roomDiv.classList.add('visited');
                if (roomAtCoords.id === this.currentRoomId) {
                    roomDiv.classList.add('current');
                }
                // TODO: Add classes for other room types (Boss, Item, etc.)
                // if (roomAtCoords.type === 'Boss') roomDiv.classList.add('boss');
            } else {
                // Keep default background for non-visited or non-existent rooms within bounds
                roomDiv.style.backgroundColor = 'transparent'; // Make empty grid cells transparent
                roomDiv.style.borderColor = 'transparent';
            }
            this.minimapGridElement.appendChild(roomDiv);
        }
    }
}

// --- Function to spawn projectiles (player or enemy) ---
spawnProjectile(startPos, direction, ownerData) { // <<< REMOVE isArrowFlag parameter
    // --- Determine Owner and Projectile Type --- 
    const isPlayerProjectile = !ownerData?.aiType; 
    let projectileType = 'sphere'; // Default to sphere
    let enemyDefinition = null; // Store definition if enemy

    if (!isPlayerProjectile) {
        enemyDefinition = getEnemyData(ownerData.aiType || 'unknown'); 
        projectileType = enemyDefinition?.projectileType || 'sphere'; // Get type from definition, default sphere
    }
    const isArrow = projectileType === 'arrow'; // <<< Determine based on type string
    // -------------------------------------------

    // --- Get Projectile Properties based on Owner --- 
    let projectileDamage, projectileRange, projectileSize, projectileSpeed, projectileColor;
    if (isPlayerProjectile) {
        // Player properties
        projectileDamage = 10; 
        projectileRange = 12;
        projectileSize = PROJECTILE_SIZE; 
        projectileSpeed = PROJECTILE_SPEED; 
        projectileColor = PROJECTILE_COLOR; 
    } else if (enemyDefinition) { 
        // Enemy properties (use previously fetched definition)
        projectileDamage = enemyDefinition.projectileDamage || 1; 
        projectileRange = enemyDefinition.projectileRange || 15;
        projectileSize = enemyDefinition.projectileSize || VOXEL_SIZE * 1.5;
        projectileSpeed = enemyDefinition.projectileSpeed || 10;
        projectileColor = enemyDefinition.projectileColor || (isArrow ? ARROW_COLOR : 0xff0000); // Use definition color or type default
    } else {
        // Fallback for unknown enemy (shouldn't happen often)
        console.warn("Spawning projectile for unknown enemy type");
        projectileDamage = 1;
        projectileRange = 10;
        projectileSize = VOXEL_SIZE * 1.5;
        projectileSpeed = 5;
        projectileColor = 0xff0000;
    }
    // --- End Property Determination ---

    // --- Create Geometry & Material based on calculated type --- 
    const projectileGeometry = isArrow ? null : new THREE.SphereGeometry(projectileSize, 8, 8);
    const projectileMaterial = isArrow ? null : new THREE.MeshBasicMaterial({ 
        color: projectileColor // Use determined color
    });
    // --- End Geometry/Material --- 

    // --- Create Mesh/Group --- 
    const projectileMesh = isArrow 
        ? createArrowProjectileModel() // Returns a Group
        : new THREE.Mesh(projectileGeometry, projectileMaterial); // Returns a Mesh
    // --- End Mesh/Group --- 
    
    projectileMesh.position.copy(startPos);
    
    // --- Orient the projectile --- 
    if (isArrow) {
        const lookAtPos = projectileMesh.position.clone().add(direction);
        projectileMesh.lookAt(lookAtPos);
    }
    // --------------------------

    const velocity = direction.clone().multiplyScalar(projectileSpeed);
    
    // Store projectile data in userData
    projectileMesh.userData = new Projectile(
        this.scene,                 // scene
        startPos,                 // startPosition
        velocity,                 // <<< PASS calculated velocity instead
        projectileDamage,         // damage
        projectileRange,          // range
        projectileSize,           // size
        !!ownerData.aiType        // isEnemyProjectile (true if owner has aiType)
    );

    this.scene.add(projectileMesh);
    this.activeProjectiles.push(projectileMesh);

    // Cleanup geometry if not an arrow group
    if (!isArrow) {
        projectileGeometry.dispose();
    }
}
// Renamed function for clarity
spawnEnemyProjectile(startPos, direction, enemyDefinition) {
    this.spawnProjectile(startPos, direction, enemyDefinition);
}

// --- Enemy Spawn Helper ---
_spawnEnemy(enemyType) {
    const enemyDefinition = getEnemyData(enemyType);
    if (!enemyDefinition) {
        console.error(`Failed to spawn enemy: Type "${enemyType}" definition not found.`);
        return;
    }

    // --- Create Enemy Mesh/Group --- 
    let enemy;
    if (enemyDefinition.modelPrefab) { // <<< Use enemyDefinition
        console.log(`Creating enemy model using prefab function for type: ${enemyType}`);
        const desiredScale = 2.5; // <<< Increased scale further
        enemy = enemyDefinition.modelPrefab(desiredScale); // <<< Use enemyDefinition
        enemy.name = `Enemy_${enemyType}_${Date.now()}`;
        // Apply castShadow/receiveShadow to children if it's a Group
        enemy.traverse(child => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
            }
        });
    } else if (enemyDefinition.geometry && enemyDefinition.material) { // <<< Use enemyDefinition
        // Fallback to simple geometry/material (legacy or for other types)
        console.log(`Creating enemy using simple geometry/material for type: ${enemyType}`);
        enemy = new THREE.Mesh(enemyDefinition.geometry, enemyDefinition.material); // <<< Use enemyDefinition
    enemy.castShadow = true;
    enemy.receiveShadow = true; 
    } else {
        console.error(`Cannot create enemy: No modelPrefab or geometry/material found for type: ${enemyType}`);
        return;
    }

    // --- Spawn position logic (Adjust Y position) ---
    const halfRoom = ROOM_WORLD_SIZE / 2 - (enemyDefinition.size || 0.6); // <<< Use enemyDefinition size 
    const minSpawnDist = 2.0; 
    let spawnPos = new THREE.Vector3();
    let attempts = 0;
    do {
        const angle = Math.random() * Math.PI * 2;
        const radius = minSpawnDist + Math.random() * (halfRoom - minSpawnDist);
        // Set initial Y based on definition size (half height approx)
        spawnPos.set(Math.cos(angle) * radius, (enemyDefinition.size || 0.6) / 2, Math.sin(angle) * radius); // <<< Use enemyDefinition size
        attempts++;
        // Removed TODO check
    } while (attempts < 20); 
    
    enemy.position.copy(spawnPos);
    // --- End Spawn position logic --- 

    // <<< Assign unique ID to enemy's userData >>>
    enemy.userData = { id: enemy.uuid }; // <<< Initialize userData with unique ID

    // Set userData based on definition and add variation
    enemy.userData.aiType = enemyDefinition.aiType; // <<< Use enemyDefinition
    enemy.userData.health = enemyDefinition.health; // <<< Use enemyDefinition
    enemy.userData.speed = enemyDefinition.baseSpeed * (1.0 - enemyDefinition.speedVariation / 2 + Math.random() * enemyDefinition.speedVariation); // <<< Use enemyDefinition
    // AI State - Use specific values from definition
    enemy.userData.timeSinceLastShot = Math.random() * (enemyDefinition.shootCooldown || 2.0); // Use definition, fallback
    enemy.userData.shootCooldown = enemyDefinition.shootCooldown || 2.0; // <<< Use enemyDefinition
    enemy.userData.preferredRange = enemyDefinition.preferredRange || 8.0; // <<< Use enemyDefinition
    enemy.userData.moveAwayRange = enemyDefinition.moveAwayRange || 6.0; // <<< Use enemyDefinition
    // Dodge state (same initial values)
    enemy.userData.isDodging = false;
    enemy.userData.dodgeTimer = 0;
    enemy.userData.dodgeCooldownTimer = 0;
    // <<< ADD Dodge properties from definition >>>
    enemy.userData.dodgeTriggerDistance = enemyDefinition.dodgeTriggerDistance || DODGE_ACTIVATION_DISTANCE; // Use definition or constant
    enemy.userData.dodgeSpeed = enemyDefinition.dodgeSpeed || DODGE_SPEED;
    enemy.userData.dodgeDuration = enemyDefinition.dodgeDuration || DODGE_DURATION;
    enemy.userData.dodgeCooldown = enemyDefinition.dodgeCooldown || DODGE_COOLDOWN;

    enemy.userData.logHelperUpdate = true; // <<< Initialize flag for logging helper updates

    // --- Create Rotation Helpers for the enemy --- 
    enemy.userData.boneHelpers = {}; // Initialize helper map
    const enemyBonesToVisualize = {};
    // Find bones (adjust names based on actual skeleton model)
    console.log(`[SpawnEnemy ${enemy.name}] Traversing to find bones for helpers...`); // <<< Log start
    enemy.traverse((child) => { // <<< Traverse the created enemy mesh/group
        // <<< Refined Bone Name Check >>>
        const boneNameCheck = child.name.toLowerCase();
        // <<< LOG every child name for debugging >>>
        // console.log(`  - Traversing child: '${child.name}' (Type: ${child.type})`);

        if (child.isObject3D && (boneNameCheck.includes('arm') || boneNameCheck.includes('leg') || boneNameCheck === 'head' || boneNameCheck === 'body' || boneNameCheck.includes('foot'))) {
            let boneName = child.name;
            console.log(`  - Found potential enemy bone: '${boneName}' (checking name: '${boneNameCheck}')`); // <<< Log potential bone name
            enemyBonesToVisualize[boneName] = child;
        }
    });

    console.log(`[SpawnEnemy ${enemy.name}] Final Bones selected for helpers:`, Object.keys(enemyBonesToVisualize)); // <<< Log final selected bones

    console.log(`[SpawnEnemy ${enemy.name}] --- Entering loop to create helpers ---`); // <<< ADD LOG BEFORE LOOP
    for (const name in enemyBonesToVisualize) {
         console.log(`[SpawnEnemy ${enemy.name}]   Processing bone: '${name}'`); 
        const bone = enemyBonesToVisualize[name];
        if (bone) {
            // <<< REVERT TO AxesHelper >>>
            const axesHelper = new THREE.AxesHelper(AXES_HELPER_SIZE); // Use constant size
            // <<< NEW: Add Bone Box Helper >>>
            const boneBoxHelper = new THREE.BoxHelper(bone, 0xff00ff); // Magenta
            boneBoxHelper.matrixAutoUpdate = false;
            // <<< END NEW >>>
            
            // <<< Update variable names and visibility logic >>>
            this.enemyRotationHelpersGroup.add(axesHelper); // Add axesHelper to group
            this.enemyRotationHelpersGroup.add(boneBoxHelper); // <<< ADD boneBoxHelper to group
            // Store both helpers
            enemy.userData.boneHelpers[name] = { 
                axes: axesHelper, 
                box: boneBoxHelper 
            }; 
            console.log(`    -> Added AxesHelper and BoxHelper for bone: ${name}`); // <<< Updated log
            // <<< END Update >>>
        } else {
             console.warn(`Enemy bone '${name}' somehow evaluated as null/undefined after traverse.`);
        }
    }
    console.log(`[SpawnEnemy ${enemy.name}] --- Exited loop for creating helpers ---`);
    // -------------------------------------------

    // --- NEW: Add Overall Bounding Box Helper ---
    const overallBoxHelper = new THREE.BoxHelper(enemy, 0x00ffff); // Cyan color
    overallBoxHelper.matrixAutoUpdate = false; // Update manually or rely on enemy matrix
    enemy.userData.boundingBoxHelper = overallBoxHelper; // Store reference
    this.enemyRotationHelpersGroup.add(overallBoxHelper); // Add to the same group
    console.log(`[SpawnEnemy ${enemy.name}] Added overall BoxHelper.`);
    // --- END NEW ---

    // --- NEW: Add Skeleton Hierarchy Lines --- 
    const lineMaterial = new THREE.LineBasicMaterial({ color: 0xffffff, linewidth: 1 }); // White lines
    const lineGeometry = new THREE.BufferGeometry();
    const skeletonLines = new THREE.LineSegments(lineGeometry, lineMaterial);
    enemy.userData.skeletonLines = skeletonLines; // Store reference
    this.enemyRotationHelpersGroup.add(skeletonLines); // Add to group
    console.log(`[SpawnEnemy ${enemy.name}] Initialized SkeletonLines object.`);
    // --- END NEW ---

    // --- NEW: Initialize Enemy State --- 
    // <<< Ensure enemy.userData.id exists before setting state >>>
    if (enemy.userData && enemy.userData.id) { 
        this.enemyStateTimers.set(enemy.userData.id, { state: 'IDLE', timer: 0 });
    } else {
        console.error(`[SpawnEnemy] Failed to set initial state: Enemy userData or id missing!`, enemy);
    }
    // --------------------------------

    this.scene.add(enemy);
    this.activeEnemies.push(enemy);
}
// --- End Enemy Spawn Helper ---

// Method to display area name with fade effect
_displayAreaName(name) {
    if (!this.areaNameElement) {
         console.error("_displayAreaName: Element not found! Should have been found in init.");
         return; 
    }

    // NEW: Check for dark room flag on current room data
    let displayName = name;
    const currentRoom = this.floorLayout.get(this.currentRoomId);
    if (currentRoom && currentRoom.isDark) { 
        displayName += " (Dark Room)";
    }
    // END NEW

    console.log(`Displaying area name: "${displayName}"`); // Use potentially modified name

    this.areaNameElement.textContent = displayName; // Use potentially modified name
    
    // --- Restore FADE LOGIC --- 
    // Clear any existing fade-out timer
    if (this.areaNameFadeTimeout) {
        clearTimeout(this.areaNameFadeTimeout);
        this.areaNameFadeTimeout = null;
    }
    
    // Make sure element is ready for fade-in
    this.areaNameElement.style.visibility = 'visible';
    this.areaNameElement.style.opacity = '0'; // Ensure opacity starts at 0 before fade
    
    // Use a minimal timeout/rAF to allow the initial opacity=0 to render before starting transition
    requestAnimationFrame(() => {
        requestAnimationFrame(() => { // Double rAF for extra safety?
            if (!this.areaNameElement) return; // Check again before setting opacity
            this.areaNameElement.style.opacity = '1'; 
            console.log("Area name: Fade-in started (opacity -> 1)");
        });
    });
    
    // Set timer to fade out after 2 seconds (relative to when fade-in *starts*)
    const displayDuration = 2000; // 2 seconds display time
    const fadeDuration = 500; // 0.5 seconds (should match CSS transition)
    
    this.areaNameFadeTimeout = setTimeout(() => {
        if (!this.areaNameElement) return; // Check element still exists
        console.log("Area name: Fade-out starting (opacity -> 0)");
        this.areaNameElement.style.opacity = '0';
         // Set visibility to hidden after transition ends
         setTimeout(() => {
             if (this.areaNameElement && this.areaNameElement.style.opacity === '0') { // Check if still faded out
                 this.areaNameElement.style.visibility = 'hidden';
                 console.log("Area name: Visibility hidden after fade-out.");
             }
         }, fadeDuration);
        this.areaNameFadeTimeout = null; // Clear the timeout ID
    }, displayDuration); // Start fade-out after displayDuration
   // --- END RESTORE --- 
}

_updateTotalRoomCountDisplay(isEspActive) {
    if (!this.totalRoomCountElement || !this.floorLayout) return;
    
    if (isEspActive) {
        const totalRooms = this.floorLayout.size;
        this.totalRoomCountElement.textContent = `Total Rooms: ${totalRooms}`;
        this.totalRoomCountElement.style.opacity = '1';
        this.totalRoomCountElement.style.visibility = 'visible';
    } else {
        this.totalRoomCountElement.style.opacity = '0';
        this.totalRoomCountElement.style.visibility = 'hidden';
    }
}

_handleEnemyDeath(deadEnemy, index) {
    console.log(`Handling death for enemy: ${deadEnemy.name}`);
    const groupNames = ['head', 'core', 'leftArm', 'rightArm', 'leftLeg', 'rightLeg'];
    const explosionForce = 1.8; 
    const upwardForce = 1.5; 
    const tumbleSpeed = 2.0; 
    const debrisPerGroup = 3; // <<< Number of small debris pieces per main part
    const debrisExplosionForce = 1.5; // Slightly less force for debris
    const debrisUpwardForce = 1.2;
    const debrisTumbleSpeed = 2.5; // Maybe tumble more?

    // <<< Define debris geometry (Use standard VOXEL_SIZE) >>>
    const debrisGeo = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);
    // Helper to get random bone material (assuming boneMaterials are accessible or redefined)
    // NOTE: This assumes boneMaterials were defined globally or passed appropriately.
    // If not, we might need to recreate them here or use a single color.
    const boneColors = [0xF8E8C8, 0xE0C8A0, 0xC8A078, 0x987858]; 
    const tempBoneMaterials = boneColors.map(color => new THREE.MeshLambertMaterial({ color }));
    const getRandomBoneMat = () => tempBoneMaterials[Math.floor(Math.random() * tempBoneMaterials.length)];
    // <<< ----------------------------- >>>

    // Update world matrices to get latest positions
    deadEnemy.updateMatrixWorld(true);

    groupNames.forEach(groupName => {
        const originalGroup = deadEnemy.getObjectByName(groupName);
        if (!originalGroup) {
            console.warn(`Could not find group "${groupName}" on dead enemy.`);
            return; 
        }
        
        // Important: Get world position/rotation of the GROUP before processing children
        const groupWorldPos = new THREE.Vector3();
        const groupWorldQuat = new THREE.Quaternion();
        originalGroup.getWorldPosition(groupWorldPos);
        originalGroup.getWorldQuaternion(groupWorldQuat);

        originalGroup.children.forEach(originalMesh => {
            if (originalMesh.isMesh) {
                console.log(`[Death] Found mesh in group ${groupName}:`, originalMesh.name || originalMesh.uuid);
                const pieceMesh = originalMesh.clone(); // Clone the mesh
                // <<< Explicitly clone material >>>
                if (originalMesh.material) { 
                    pieceMesh.material = originalMesh.material.clone();
                } 
                // <<< -------------------------- >>>
                pieceMesh.name = `bonePiece_${groupName}_${Date.now()}`;

                // Apply the original mesh's world position/rotation/scale to the clone
                const originalMeshWorldPos = new THREE.Vector3();
                const originalMeshWorldQuat = new THREE.Quaternion();
                const originalMeshWorldScale = new THREE.Vector3(); // <<< Get scale too
                originalMesh.matrixWorld.decompose(originalMeshWorldPos, originalMeshWorldQuat, originalMeshWorldScale); // <<< Decompose matrixWorld
                
                pieceMesh.position.copy(originalMeshWorldPos);
                pieceMesh.quaternion.copy(originalMeshWorldQuat);
                pieceMesh.scale.copy(originalMeshWorldScale); // <<< Apply scale explicitly
                
                console.log(`[Death] Cloning ${groupName} part. World Pos:`, originalMeshWorldPos);
                console.log(`[Death] Cloned piece scale:`, pieceMesh.scale); // <<< Log scale
                
                // --- Restore Original Physics Force for LARGE pieces --- 
                // const debugExplosionForce = 0.2; 
                // const debugUpwardForce = 0.5; 
                // const debugTumbleSpeed = 0.5;
                // Using the main constants defined at the start of _handleEnemyDeath
                // (explosionForce, upwardForce, tumbleSpeed)
                // --- ----------------------------------------------------

                // --- Physics --- 
                const direction = new THREE.Vector3(
                    Math.random() - 0.5,
                    0.2 + Math.random() * 0.4, // Reduced upward bias (was 0.4 + rnd*0.6)
                    Math.random() - 0.5
                ).normalize();
                // <<< USE ORIGINAL FORCES (defined at function start) >>>
                const velocity = direction.multiplyScalar(explosionForce + (Math.random() - 0.5) * 0.4);
                velocity.y += upwardForce * (0.6 + Math.random() * 0.4); // Reduced initial upward boost slightly (was 0.8 + rnd*0.4)
                
                const angularVelocity = new THREE.Vector3(
                    (Math.random() - 0.5) * tumbleSpeed * 1.2, // Increased tumble slightly
                    (Math.random() - 0.5) * tumbleSpeed * 1.2,
                    (Math.random() - 0.5) * tumbleSpeed * 1.2
                );

                // Add to scene and tracked pieces
                // console.log(`[Death] Adding large piece ${pieceMesh.name} to scene.`);
                this.scene.add(pieceMesh); // <<< ADD BACK TO SCENE
                this.currentRoomObjects.push(pieceMesh); // <<< ADD to currentRoomObjects
                // Store physics data in userData
                pieceMesh.userData = {
                    isBonePiece: true,
                    velocity: velocity,
                    angularVelocity: angularVelocity,
                    isFalling: true
                };
                // this.activeBonePieces.push({...}); // <<< REMOVE
            }
        }); // <<< End of originalGroup.children.forEach loop

        // --- NEW: Spawn Small Debris Pieces for this group --- 
        for (let i = 0; i < debrisPerGroup; i++) {
            const debrisMesh = new THREE.Mesh(debrisGeo, getRandomBoneMat());
            debrisMesh.name = `debris_${groupName}_${i}_${Date.now()}`;

            // Start at the group's world position + small random offset
            const randomOffset = new THREE.Vector3(
                (Math.random() - 0.5) * VOXEL_SIZE * 3,
                (Math.random() - 0.5) * VOXEL_SIZE * 3,
                (Math.random() - 0.5) * VOXEL_SIZE * 3
            );
            debrisMesh.position.copy(groupWorldPos).add(randomOffset);
            debrisMesh.quaternion.copy(groupWorldQuat); // Initial rotation same as group

            // Physics for debris
            const debrisDirection = new THREE.Vector3(
                Math.random() - 0.5,
                0.4 + Math.random() * 0.6, // Bias upwards slightly less?
                Math.random() - 0.5
            ).normalize();
            const debrisVelocity = debrisDirection.multiplyScalar(debrisExplosionForce + (Math.random() - 0.5) * 0.4);
            debrisVelocity.y += debrisUpwardForce * (0.7 + Math.random() * 0.6);
            
            const debrisAngularVelocity = new THREE.Vector3(
                (Math.random() - 0.5) * debrisTumbleSpeed,
                (Math.random() - 0.5) * debrisTumbleSpeed,
                (Math.random() - 0.5) * debrisTumbleSpeed
            );

            // Add to scene and tracked pieces
            this.scene.add(debrisMesh); // <<< ADD BACK TO SCENE
            this.currentRoomObjects.push(debrisMesh); // <<< ADD to currentRoomObjects
            // Store physics data in userData
            debrisMesh.userData = {
                isBonePiece: true,
                velocity: debrisVelocity,
                angularVelocity: debrisAngularVelocity,
                isFalling: true
            };
            // this.activeBonePieces.push({...}); // <<< REMOVE
        }
        // --- END Spawn Small Debris Pieces ---

    }); // <<< End of groupNames.forEach loop

    // --- Original Enemy Cleanup --- 
    // <<< AGGRESSIVE CLEANUP >>>
    console.log(`[Death] Applying aggressive cleanup for: ${deadEnemy.name}`);
    deadEnemy.traverse(child => {
        if (child.isMesh) {
            if (child.geometry) child.geometry.dispose();
            // Material disposal might be tricky if shared, but let's try
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(mat => { if(mat && mat.dispose) mat.dispose(); });
                } else if (child.material.dispose) {
                    child.material.dispose();
                }
            }
        }
    });
    deadEnemy.removeFromParent(); // Explicitly remove from parent
    // <<< END AGGRESSIVE CLEANUP >>>
    
    // Remove from scene and lists AFTER aggressive cleanup
    this.scene.remove(deadEnemy); // Remove original enemy group
    this.activeEnemies.splice(index, 1); // Remove from active list
    this.enemyStateTimers.delete(deadEnemy.userData.id); // Remove state timer
    // Note: collisionObjects handling might need refinement if pieces should collide

    // --- NEW: Stage Complete Check (Only if Boss Defeated) --- 
    const currentRoomData = this.floorLayout.get(this.currentRoomId);
    // <<< CHECK BOSS CONDITION HERE >>>
    const isBossRoom = currentRoomData?.type === 'Boss';
    const defeatedEnemyType = deadEnemy.userData?.aiType;
    const isBossDefeated = defeatedEnemyType === 'skeleton_boss'; // <<< ASSUME BOSS TYPE is 'skeleton_boss'
    const noEnemiesRemain = this.activeEnemies.length === 0;

    if (isBossRoom && isBossDefeated && noEnemiesRemain) {
        console.log("!!! BOSS DEFEATED - STAGE COMPLETE !!!");
        this._handleStageComplete(); // Call new function
    } else if (this.activeEnemies.length === 0 && currentRoomData && !isBossRoom) {
        // Optionally, still mark non-boss rooms as cleared for other purposes (like preventing respawns)
        // but don't trigger the main stage complete logic.
        if (!currentRoomData.state.enemiesCleared) { // Only log/set if not already cleared
            console.log(`--- Room ${this.currentRoomId} cleared of normal enemies ---`);
            currentRoomData.state.enemiesCleared = true;
        }
    }
    // --- END NEW CHECK ---

    // <<< Dispose debris geometry >>>
    debrisGeo.dispose();
    tempBoneMaterials.forEach(mat => mat.dispose()); // Dispose temporary materials
}

// --- NEW Placeholder Function --- 
_handleStageComplete() {
    console.log("[DungeonHandler] _handleStageComplete called. Implement final logic here (e.g., unlock exit, win screen).");
    // TODO: Add logic like:
    // - Unlock final exit door
    // - Display victory message
    // - Transition to next level or win screen (e.g., this.sceneManager.changeState(STATE.WIN_SCREEN))
}
// --- End Placeholder --- 

// Method called by PlayerController to toggle the view
toggleCameraView() {
    this.isTopDownView = !this.isTopDownView;
    console.log(`Camera view toggled. Top-down: ${this.isTopDownView}`);
    
    // Optional: Immediately snap camera or start a smooth transition animation
    // For now, the update loop will handle the lerping
    
    // We need to update the projection matrix if the 'up' vector changes orientation significantly
    // Or if other properties like zoom/frustum were changed specifically for one view.
    // Since we are changing 'up', let's update the projection matrix.
    this.camera.updateProjectionMatrix(); 
}

// <<< NEW method called by PlayerController >>>
toggleDebugLight() {
    console.log("[DungeonHandler] toggleDebugLight called.");
    if (this.debugGlobalLight) {
        this.isDebugLightOn = !this.isDebugLightOn;
        this.debugGlobalLight.visible = this.isDebugLightOn;
        console.log(`   -> Debug Global Light Toggled: ${this.isDebugLightOn ? 'ON' : 'OFF'}`);
    } else {
        console.warn("   -> Debug Global Light object not found!");
    }
}

// --- Add method to set ESP rotation view state ---
setEspRotationViewActive(isActive) {
    // <<< ADD LOGGING >>>
    console.log(`[DungeonHandler] setEspRotationViewActive called with: ${isActive}`);
    // <<< END LOGGING >>>
    this.isEspRotationViewActive = isActive;
    if (this.enemyRotationHelpersGroup) { 
        this.enemyRotationHelpersGroup.visible = isActive;
        // <<< ADD LOGGING >>>
        console.log(`   -> Enemy rotation helpers group visibility set to: ${this.enemyRotationHelpersGroup.visible}`);
        // <<< END LOGGING >>>
    }
}
// -------------------------------------------------

// --- Add method to update enemy rotation helpers transforms ---
_updateEnemyRotationHelpers() {
    // ... (Entry log remains) ...
    if (this.isEspRotationViewActive) {
         // console.log("[UpdateEnemyHelpers] Function called. isEspRotationViewActive=true"); // Reduce spam
    } else {
         return; 
    }
    // ... (Group check remains) ...
    if (!this.enemyRotationHelpersGroup) {
         // console.log("[UpdateEnemyHelpers] Exit: No enemyRotationHelpersGroup."); // Reduce spam
         return;
    }

    // ... (Counters, temp vars remain) ...
    let logCounter = 0; 
    const maxLogsPerFrame = 1; // Log just the first one again now we know the issue
    const worldPosition = new THREE.Vector3();
    const worldQuaternion = new THREE.Quaternion();

    this.activeEnemies.forEach(enemy => {
        if (!enemy || !enemy.userData.boneHelpers) {
             // if (logCounter < maxLogsPerFrame) console.log(`[UpdateEnemyHelpers] Skipping enemy ${enemy?.name || enemy?.uuid} - no boneHelpers.`); // Reduce spam
             return; 
        }

        // <<< Update Enemy World Matrix FIRST >>>
        enemy.updateMatrixWorld(true); 
        
        // <<< Update Overall Bounding Box Helper >>>
        if (enemy.userData.boundingBoxHelper) {
            enemy.userData.boundingBoxHelper.update();
        }
        // <<< END Overall Box Update >>>

        // --- Update Skeleton Hierarchy Lines ---
        if (enemy.userData.skeletonLines) {
            const skeletonLines = enemy.userData.skeletonLines;
            const geometry = skeletonLines.geometry;
            const lineVertices = [];
            const processedBones = new Set(); // Avoid duplicate lines
            let foundParentChildPair = false; // <<< ADD Log flag

            // <<< ADD Log: Entering line update >>>
            // console.log(`[LineUpdate ${enemy.name}] Starting traversal for lines...`);

            enemy.traverse((child) => {
                // Only process bones that have helpers
                if (child.isBone && enemy.userData.boneHelpers[child.name]) {
                    const parent = child.parent;
                    
                    // <<< MODIFY: Only require parent to be a Bone, not necessarily have a helper >>>
                    // Check if parent is also a bone (Removed check for parent helper: && enemy.userData.boneHelpers[parent.name])
                    if (parent && parent.isBone) { 
                         // Create unique key for this connection to avoid duplicates
                         const key1 = `${parent.uuid}-${child.uuid}`;
                         const key2 = `${child.uuid}-${parent.uuid}`;
                        
                         if (!processedBones.has(key1) && !processedBones.has(key2)) {
                             // <<< Log modification >>>
                             // console.log(`    [LineUpdate ${enemy.name}] FOUND connection: ${parent.name} (isBone) -> ${child.name} (has helper)`);
                             foundParentChildPair = true; // <<< Set flag
                             
                             const childPos = new THREE.Vector3();
                             const parentPos = new THREE.Vector3();
                             // Ensure world matrices are up-to-date before getting world position
                             child.updateWorldMatrix(true, false);
                             parent.updateWorldMatrix(true, false);
                             child.getWorldPosition(childPos);
                             parent.getWorldPosition(parentPos);
                             lineVertices.push(parentPos, childPos);
                             processedBones.add(key1); // Mark connection as processed using UUIDs
                         }
                    }
                }
            });

            // <<< ADD Log: Before setFromPoints >>>
            if (logCounter < maxLogsPerFrame) console.log(`[LineUpdate ${enemy.name}] Before setFromPoints. Vertices count: ${lineVertices.length}. Found Pair: ${foundParentChildPair}`);

            geometry.setFromPoints(lineVertices);
            geometry.computeBoundingSphere(); // Needed for frustum culling
        }
        // --- END Skeleton Lines Update ---

        for (const boneName in enemy.userData.boneHelpers) {
             const shouldLog = false; // <<< Set to false to disable spam
             
             // <<< Get helper data object >>>
             const helperData = enemy.userData.boneHelpers[boneName];
             const axesHelper = helperData?.axes;
             const boxHelper = helperData?.box;
             // <<< ---------------------- >>>
             
             const bone = enemy.getObjectByName(boneName); 
             
             // <<< Check for bone and BOTH helpers >>>
             if (bone && axesHelper && boxHelper && 
                 axesHelper instanceof THREE.AxesHelper && 
                 boxHelper instanceof THREE.BoxHelper) {
             // <<< ------------------------------ >>>
             
                 // if (shouldLog) console.log(`      -> Bone & Helpers OK. Proceeding.`); // Commented out
                
                 // Update logic
                 bone.updateWorldMatrix(true, false); 
                 bone.getWorldPosition(worldPosition);
                 bone.getWorldQuaternion(worldQuaternion);
                
                 // Update Axes Helper
                 axesHelper.position.copy(worldPosition);
                 axesHelper.quaternion.copy(worldQuaternion);
                 
                 // Update Bone Box Helper
                 boxHelper.update(); 

                 // if (shouldLog) console.log(`         -> Updated Helpers for bone: ${boneName}`); // Commented out
                 logCounter++; 
              } else if (shouldLog) {
                 // Log failure reasons
                 if (!bone) console.log(`      -> Condition FAILED: Bone '${boneName}' not found.`);
                 else if (!axesHelper) console.log(`      -> Condition FAILED: AxesHelper missing for bone '${boneName}'.`);
                 else if (!boxHelper) console.log(`      -> Condition FAILED: BoxHelper missing for bone '${boneName}'.`);
                 else if (!(axesHelper instanceof THREE.AxesHelper)) console.log(`      -> Condition FAILED: axesHelper is not AxesHelper.`);
                 else if (!(boxHelper instanceof THREE.BoxHelper)) console.log(`      -> Condition FAILED: boxHelper is not BoxHelper.`);
                 else console.log(`      -> Condition FAILED: Unknown reason.`); 
                 logCounter++; 
              }
        }
    });
}
// --------------------------------------------------------

// Completely rewrite the pre-generation method to be more efficient and work in batches
async _preGenerateAllRooms() {
    if (!this.floorLayout || this.floorLayout.size === 0) {
        console.error("Cannot pre-generate rooms: floor layout is empty or undefined.");
        return;
    }
    
    if (this.preGenerationInProgress) {
        console.log("Room pre-generation already in progress. Skipping request.");
        return;
    }
    
    this.preGenerationInProgress = true;
    console.log(`Pre-generating visuals for ${this.floorLayout.size} rooms...`);
    const startTime = performance.now();

    // Get room IDs and sort by distance from the current room
    const roomIds = Array.from(this.floorLayout.keys());
    
    // Function to calculate graph-based "distance" between rooms
    const getDistanceToRoom = (roomId) => {
        if (roomId === this.currentRoomId) return 0;
        
        // Simplified BFS to find shortest path
        const visited = new Set([this.currentRoomId]);
        const queue = [[this.currentRoomId, 0]]; // [roomId, distance]
        
        while (queue.length > 0) {
            const [currentId, distance] = queue.shift();
            const currentRoom = this.floorLayout.get(currentId);
            
            if (!currentRoom || !currentRoom.connections) continue;
            
            for (const dir in currentRoom.connections) {
                const neighborId = currentRoom.connections[dir];
                if (neighborId !== null && !visited.has(neighborId)) {
                    if (neighborId === roomId) return distance + 1;
                    visited.add(neighborId);
                    queue.push([neighborId, distance + 1]);
                }
            }
        }
        return Infinity; // Room not reachable
    };
    
    // Sort rooms by distance from current room
    roomIds.sort((a, b) => {
        const distA = getDistanceToRoom(a);
        const distB = getDistanceToRoom(b);
        return distA - distB;
    });
    
    // Create a message to update the user on progress
    const displayMessage = (message) => {
        // Only show if UI element exists
        const messageElement = document.getElementById('loading-message');
        if (messageElement) {
            messageElement.textContent = message;
            messageElement.style.display = 'block';
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }
    };

    // Process rooms in batches to avoid blocking the main thread for too long
    const BATCH_SIZE = 3;
    let batchIndex = 0;
    
    const processBatch = async () => {
        if (batchIndex >= roomIds.length) {
            // All rooms processed
            const endTime = performance.now();
            console.log(`Pre-generation complete. Generated ${this.preGeneratedRooms.size} rooms in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);
            this.preGenerationComplete = true;
            this.preGenerationInProgress = false;
            displayMessage("All rooms pre-generated. Performance should be improved!");
            return;
        }
        
        // Process a batch of rooms
        const batchEndIndex = Math.min(batchIndex + BATCH_SIZE, roomIds.length);
        displayMessage(`Preparing dungeon: ${Math.round((batchIndex / roomIds.length) * 100)}%`);
        
        for (let i = batchIndex; i < batchEndIndex; i++) {
            const roomId = roomIds[i];
            
            // Skip if already generated or is the current room (already loaded)
            if (this.preGeneratedRooms.has(roomId) || roomId === this.currentRoomId) {
                continue;
            }
            
            const roomData = this.floorLayout.get(roomId);
            if (!roomData) continue;
            
            // Get area data for this room // UPDATED BLOCK
            const areaId = roomData.state.area || 'catacombs'; // Get areaId, fallback
            const areaData = getAreaData(areaId);
            if (!areaData) {
                console.warn(`Area "${areaId}" not found for room ${roomId}. Skipping pre-generation.`);
                continue;
            }
            // End Area Data Fetch
            
            try {
                // Generate the visuals but don't add them to scene
                const { roomGroup, collisionMeshes, lights, boundingBox, doorCenterPoints: generatedDoorPoints } = generateRoomVisuals(roomData, areaData); // NEW - Pass areaData
                
                if (roomGroup) {
                    // Store the generated visuals for later use
                    this.preGeneratedRooms.set(roomId, {
                        roomGroup, 
                        collisionMeshes,
                        lights,
                        boundingBox,
                        doorCenterPoints: { ...generatedDoorPoints } // <<< Store shallow clone
                    });
                    console.log(`Pre-generated room ${roomId} (${roomData.type})`);
                    // <<< ADD Log before caching >>>
                    console.log(`[PreGen] Caching door points for Room ${roomId}:`, generatedDoorPoints);
                    // console.log(`Pre-generated room ${roomId} (${roomData.type})`); // Commented out the original success log for brevity
                }
            } catch (error) {
                console.error(`Error pre-generating room ${roomId}:`, error);
            }
        }
        
        batchIndex = batchEndIndex;
        
        // Give the UI thread a chance to update before processing the next batch
        return new Promise(resolve => {
            setTimeout(() => {
                requestAnimationFrame(() => {
                    processBatch().then(resolve);
                });
            }, 0);
        });
    };
    
    // Start processing batches
    await processBatch();
}

_handleObjectDestruction(objectToDestroy, pointOfImpact = null, projectileVelocity = null) {
    console.log(`[_handleObjectDestruction] Handling destruction for: ${objectToDestroy.name || objectToDestroy.uuid}`);
    const objUserData = objectToDestroy.userData;

    if (!objUserData?.isDestructible) { 
        console.warn(`Object ${objectToDestroy.name} is not marked as destructible (isDestructible check).`);
        return;
    }

    // --- Physics Parameters (Shared for both methods) ---
    const explosionForce = 1.5; // Tuned for individual voxels 
    const upwardForce = 2.0;    // Tuned for individual voxels
    const tumbleSpeed = 2.5;    // Tuned for individual voxels

    // --- Update World Matrix of the original object ONCE ---
    objectToDestroy.updateMatrixWorld(true);
    const objectWorldMatrix = objectToDestroy.matrixWorld.clone(); // Store its world matrix
    const objectCenterPos = new THREE.Vector3();
    objectToDestroy.getWorldPosition(objectCenterPos); // Get world center for explosion origin

    // --- Method 1: True Voxel Destruction (if data available) ---
    if (objUserData.originalVoxels && objUserData.voxelScale) {
        console.log(`[Destruction] Using TRUE VOXEL destruction for ${objectToDestroy.name}. Count: ${objUserData.originalVoxels.length}`);
        const voxelScale = objUserData.voxelScale;
        const baseVoxelGeo = getOrCreateGeometry('destruction_voxel', () => new THREE.BoxGeometry(voxelScale, voxelScale, voxelScale));
        const tempVec = new THREE.Vector3();

        objUserData.originalVoxels.forEach((voxelData, index) => {
            // 1. Create the individual voxel mesh
            const material = _getMaterialByHex_Cached(voxelData.c); // Get material by original color
            if (!material) {
                console.warn(`[VoxelDestruction] Could not get material for hex ${voxelData.c}`);
                return; // Skip if material fails
            }
            const voxelMesh = new THREE.Mesh(baseVoxelGeo, material.clone()); // Use shared geo, cloned material
            voxelMesh.name = `voxelDebris_${objectToDestroy.name}_${index}_${Date.now()}`;
            voxelMesh.castShadow = true;
            voxelMesh.receiveShadow = true;

            // 2. Calculate Initial World Position
            // Start with relative position, scale it, then apply object's world matrix
            tempVec.set(voxelData.x * voxelScale, voxelData.y * voxelScale, voxelData.z * voxelScale);
            tempVec.applyMatrix4(objectWorldMatrix); // Transform relative pos to world pos
            voxelMesh.position.copy(tempVec);
            
            // Use original object's world rotation initially (can tumble from there)
            const worldQuat = new THREE.Quaternion();
            objectWorldMatrix.decompose(new THREE.Vector3(), worldQuat, new THREE.Vector3());
            voxelMesh.quaternion.copy(worldQuat);

            // 3. Apply Physics
            const explosionDir = voxelMesh.position.clone().sub(objectCenterPos).normalize(); // Base direction: outwards from center
            let finalDir = explosionDir.clone(); // Start with the base direction

            const impactBlendFactor = 0.4; // How much the impact point direction influences the explosion direction (0 to 1)
            const impactVelocityFactor = 0.3; // How much of the projectile's velocity is transferred (0 to 1)

            if (pointOfImpact) {
                const impactDir = voxelMesh.position.clone().sub(pointOfImpact).normalize();
                // Blend the base explosion direction with the direction away from the impact point
                finalDir.lerp(impactDir, impactBlendFactor).normalize();
            }

            // Add some randomness/upward bias to the potentially blended direction
            finalDir.x += (Math.random() - 0.5) * 0.6;
            finalDir.y += 0.3 + Math.random() * 0.5; // Add upward bias
            finalDir.z += (Math.random() - 0.5) * 0.6;
            finalDir.normalize();

            // Calculate initial velocity based on the final direction and explosion force
            const velocity = finalDir.clone().multiplyScalar(explosionForce + (Math.random() - 0.5) * 1.0); // Add force variation
            velocity.y += upwardForce * (0.5 + Math.random() * 0.5); // Extra upward kick

            // Add influence from projectile velocity if available
            if (projectileVelocity) {
                const projVelNorm = projectileVelocity.clone().normalize();
                // Calculate how much the final explosion direction aligns with the projectile's direction
                const dotProduct = Math.max(0, finalDir.dot(projVelNorm)); // Use max(0, ...) to prevent pull-back
                // Add a portion of the projectile's velocity, scaled by alignment and factor
                velocity.addScaledVector(projectileVelocity, dotProduct * impactVelocityFactor);
            }
            
            const angularVelocity = new THREE.Vector3(
                (Math.random() - 0.5) * tumbleSpeed,
                (Math.random() - 0.5) * tumbleSpeed,
                (Math.random() - 0.5) * tumbleSpeed
            );

            // 4. Add to Scene and Simulation
            this.scene.add(voxelMesh);
            this.currentRoomObjects.push(voxelMesh); // Track for cleanup
            voxelMesh.userData = {
                isDebrisPiece: true, // Mark as debris for physics loop
                velocity: velocity,
                angularVelocity: angularVelocity,
                isFalling: true,
                lifeTime: 4 + Math.random() * 6 // Voxel lifetime
            };
        });
        
         // Dispose the shared geometry AFTER the loop
         baseVoxelGeo.dispose();

    } else {
        // --- Method 2: Fallback - Clone Parts + Generic Debris (Previous Logic) ---
        console.warn(`[Destruction] Voxel data not found for ${objectToDestroy.name}. Using fallback destruction.`);
        // (Keep previous logic here as a fallback if needed for other object types)
        // --- Get Physics Parameters ---
        const fallbackExplosionForce = 1.3; 
        const fallbackUpwardForce = 2.5; 
        const fallbackTumbleSpeed = 1.8;
        const fallbackDebrisPerGroup = 8; 
        const fallbackDebrisExplosionForce = 1.2;
        const fallbackDebrisUpwardForce = 2.0;
        const fallbackDebrisTumbleSpeed = 2.2;
        const fallbackDebrisGeo = new THREE.BoxGeometry(VOXEL_SIZE * 0.8, VOXEL_SIZE * 0.8, VOXEL_SIZE * 0.8);
        let fallbackPieceMaterial = objectToDestroy.material;
        if (Array.isArray(fallbackPieceMaterial)) fallbackPieceMaterial = fallbackPieceMaterial[0];
        if (!fallbackPieceMaterial || !fallbackPieceMaterial.isMaterial) fallbackPieceMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
        else fallbackPieceMaterial = fallbackPieceMaterial.clone();
        const fallbackDebrisMaterial = fallbackPieceMaterial.clone();
        
        const meshesToProcess = [];
        if (objectToDestroy.isMesh) meshesToProcess.push(objectToDestroy);
        else objectToDestroy.traverse((child) => { if (child.isMesh) meshesToProcess.push(child); });

        if (meshesToProcess.length === 0) console.warn("No meshes found in fallback.");

        meshesToProcess.forEach(originalMesh => {
            const pieceMesh = originalMesh.clone();
             if (originalMesh.material) {
                if(Array.isArray(pieceMesh.material)) pieceMesh.material.forEach(m => m?.dispose());
                else if (pieceMesh.material?.dispose) pieceMesh.material.dispose();
                if (Array.isArray(originalMesh.material)) pieceMesh.material = originalMesh.material.map(m => m.clone());
                else pieceMesh.material = originalMesh.material.clone(); 
             } else pieceMesh.material = new THREE.MeshLambertMaterial({ color: 0x999999 });
            pieceMesh.name = `fallbackDebrisPiece_${originalMesh.name}_${Date.now()}`;
            const originalMeshWorldPos = new THREE.Vector3();
            const originalMeshWorldQuat = new THREE.Quaternion();
            const originalMeshWorldScale = new THREE.Vector3();
            originalMesh.matrixWorld.decompose(originalMeshWorldPos, originalMeshWorldQuat, originalMeshWorldScale);
            pieceMesh.position.copy(originalMeshWorldPos);
            pieceMesh.quaternion.copy(originalMeshWorldQuat);
            pieceMesh.scale.copy(originalMeshWorldScale);
            const direction = new THREE.Vector3(Math.random() - 0.5, 0.2 + Math.random() * 0.4, Math.random() - 0.5).normalize();
            const velocity = direction.multiplyScalar(fallbackExplosionForce + (Math.random() - 0.5) * 0.4);
            velocity.y += fallbackUpwardForce * (0.6 + Math.random() * 0.4);
            const angularVelocity = new THREE.Vector3((Math.random() - 0.5) * fallbackTumbleSpeed * 1.2, (Math.random() - 0.5) * fallbackTumbleSpeed * 1.2, (Math.random() - 0.5) * fallbackTumbleSpeed * 1.2);
            this.scene.add(pieceMesh);
            this.currentRoomObjects.push(pieceMesh);
            pieceMesh.userData = { isDebrisPiece: true, velocity: velocity, angularVelocity: angularVelocity, isFalling: true, lifeTime: 5 + Math.random() * 5 };
        });

        for (let i = 0; i < fallbackDebrisPerGroup * meshesToProcess.length; i++) {
             let currentDebrisMat = fallbackDebrisMaterial;
             if(!currentDebrisMat || !currentDebrisMat.isMaterial) currentDebrisMat = new THREE.MeshLambertMaterial({ color: 0x777777 });
             else currentDebrisMat = currentDebrisMat.clone(); 
            const smallDebrisMesh = new THREE.Mesh(fallbackDebrisGeo, currentDebrisMat);
            smallDebrisMesh.name = `fallbackSmallDebris_${i}_${Date.now()}`;
            const randomOffset = new THREE.Vector3((Math.random() - 0.5) * VOXEL_SIZE * 1.5, (Math.random() - 0.5) * VOXEL_SIZE * 1.5, (Math.random() - 0.5) * VOXEL_SIZE * 1.5);
            smallDebrisMesh.position.copy(objectCenterPos).add(randomOffset);
            const debrisDirection = new THREE.Vector3(Math.random() - 0.5, 0.3 + Math.random() * 0.4, Math.random() - 0.5).normalize();
            const debrisVelocity = debrisDirection.multiplyScalar(fallbackDebrisExplosionForce + (Math.random() - 0.5) * 0.3);
            debrisVelocity.y += fallbackDebrisUpwardForce * (0.5 + Math.random() * 0.6);
            const debrisAngularVelocity = new THREE.Vector3((Math.random() - 0.5) * fallbackDebrisTumbleSpeed, (Math.random() - 0.5) * fallbackDebrisTumbleSpeed, (Math.random() - 0.5) * fallbackDebrisTumbleSpeed);
            this.scene.add(smallDebrisMesh);
            this.currentRoomObjects.push(smallDebrisMesh);
            smallDebrisMesh.userData = { isDebrisPiece: true, velocity: debrisVelocity, angularVelocity: debrisAngularVelocity, isFalling: true, lifeTime: 3 + Math.random() * 4 };
        }
        if (fallbackPieceMaterial?.dispose) fallbackPieceMaterial.dispose();
        if (fallbackDebrisMaterial?.dispose) fallbackDebrisMaterial.dispose();
        fallbackDebrisGeo.dispose();
    }

    // --- Remove Original Object from scene and lists (Common to both methods) ---
    // <<< AGGRESSIVE CLEANUP >>>
    console.log(`[Destruction] Applying aggressive cleanup for: ${objectToDestroy.name || objectToDestroy.uuid}`);
    objectToDestroy.traverse(child => {
        if (child.isMesh) {
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(mat => { if(mat && mat.dispose) mat.dispose(); });
                } else if (child.material.dispose) {
                    child.material.dispose();
                }
            }
        }
    });
    objectToDestroy.removeFromParent(); // Explicitly remove from parent
    // <<< END AGGRESSIVE CLEANUP >>>
    
    // (Logging added previously)
    console.log(`[Destruction] Attempting to remove original object: ${objectToDestroy.name || objectToDestroy.uuid}`, objectToDestroy);
    console.log(`[Destruction] Is object in scene before remove? ${this.scene.children.includes(objectToDestroy)}`);
    objectToDestroy.visible = false; // <<< Keep this too, doesn't hurt
    this.scene.remove(objectToDestroy);
    console.log(`[Destruction] Is object in scene AFTER remove? ${this.scene.children.includes(objectToDestroy)}`);
    
    const enemyIndex = this.activeEnemies.findIndex(e => e === objectToDestroy);
    if (enemyIndex !== -1) {
        this.activeEnemies.splice(enemyIndex, 1);
        this.enemyStateTimers.delete(objUserData?.id);
    }
    const collisionIndex = this.collisionObjects.findIndex(c => c === objectToDestroy || c.parent === objectToDestroy); // Check object itself or if it's a child in collision list
    if (collisionIndex !== -1) {
        this.collisionObjects.splice(collisionIndex, 1);
        console.log("Removed destroyed object/child from collisionObjects list.");
    } else {
        console.warn("Destroyed object or its children not found in collisionObjects list.");
    }
    const roomObjIndex = this.currentRoomObjects.findIndex(o => o === objectToDestroy);
     if (roomObjIndex !== -1) {
        this.currentRoomObjects.splice(roomObjIndex, 1);
     }

    // <<< Dispose Original Object's Geometry/Materials >>>
    objectToDestroy.traverse(child => {
        if (child.isMesh) {
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(mat => { if(mat.dispose) mat.dispose(); });
                } else if (child.material.dispose) {
                    child.material.dispose();
                }
            }
        }
    });

    console.log(`[_handleObjectDestruction] Completed destruction for: ${objectToDestroy.name || objectToDestroy.uuid}`);
}

}

// Helper to get opposite direction (Needed for transition logic)
function getOppositeDirection(dir) {
    // Convert to lowercase to handle both uppercase and lowercase directions
    const direction = dir.toLowerCase();
    switch (direction) {
        case 'n': return 's';
        case 's': return 'n';
        case 'e': return 'w';
        case 'w': return 'e';
        default: return null;
    }
}

export default DungeonHandler; 