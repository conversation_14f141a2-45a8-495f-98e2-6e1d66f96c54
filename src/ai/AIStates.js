/**
 * Shared AI states for all AI types
 */

export const AIStates = {
    // Common states
    IDLE: 'IDLE',           // Standing still or wandering
    MOVING: 'MOVING',       // Moving with purpose (chasing, repositioning)
    AIMING: 'AIMING',       // Preparing to attack
    ATTACKING: 'ATTACKING', // Executing attack
    SHOOTING: 'SHOOTING',   // Firing projectile (for ranged AI)
    FLEEING: 'FLEEING',     // Running away
    DODGING: 'DODGING',     // Dodging an attack
    STUNNED: 'STUNNED',     // Temporarily unable to act
    KNOCKBACK: 'KNOCKBACK', // Being knocked back
    
    // Melee specific states
    CHARGING: 'CHARGING',   // Charging towards player
    BLOCKING: 'BLOCKING',   // Blocking an attack
    
    // Ranged specific states
    STRAFING: 'STRAFING',   // Moving sideways while maintaining distance
    
    // Flying specific states
    HOVERING: 'HOVERING',   // Hovering in place
    SWOOPING: 'SWOOPING',   // Swooping down to attack
    ASCENDING: 'ASCENDING', // Gaining altitude
    
    // Assassin specific states
    STALKING: 'STALKING',   // Moving quietly behind player
    AMBUSHING: 'AMBUSHING', // Preparing for a backstab
    
    // Mirror specific states
    MIRRORING: 'MIRRORING', // Copying player's movements
    
    // Boss specific states
    PHASE_CHANGE: 'PHASE_CHANGE', // Changing attack patterns
    SPECIAL_ATTACK: 'SPECIAL_ATTACK', // Executing a special attack
};

/**
 * State transition conditions
 * These are common conditions that trigger state transitions
 */
export const TransitionConditions = {
    PLAYER_IN_RANGE: 'PLAYER_IN_RANGE',
    PLAYER_OUT_OF_RANGE: 'PLAYER_OUT_OF_RANGE',
    PLAYER_TOO_CLOSE: 'PLAYER_TOO_CLOSE',
    PLAYER_ATTACKING: 'PLAYER_ATTACKING',
    PLAYER_VISIBLE: 'PLAYER_VISIBLE',
    PLAYER_HIDDEN: 'PLAYER_HIDDEN',
    ATTACK_READY: 'ATTACK_READY',
    HEALTH_LOW: 'HEALTH_LOW',
    PROJECTILE_INCOMING: 'PROJECTILE_INCOMING',
    TIMER_EXPIRED: 'TIMER_EXPIRED',
    RANDOM_CHANCE: 'RANDOM_CHANCE',
};

/**
 * Helper function to check if a transition condition is met
 * @param {String} condition - The condition to check
 * @param {Object} params - Parameters needed for the check
 * @returns {Boolean} - Whether the condition is met
 */
export function checkCondition(condition, params) {
    switch (condition) {
        case TransitionConditions.PLAYER_IN_RANGE:
            return params.distanceToPlayer <= params.preferredRange;
            
        case TransitionConditions.PLAYER_OUT_OF_RANGE:
            return params.distanceToPlayer > params.preferredRange;
            
        case TransitionConditions.PLAYER_TOO_CLOSE:
            return params.distanceToPlayer < params.minRange;
            
        case TransitionConditions.PLAYER_ATTACKING:
            return params.playerIsAttacking;
            
        case TransitionConditions.PLAYER_VISIBLE:
            return params.canSeePlayer;
            
        case TransitionConditions.PLAYER_HIDDEN:
            return !params.canSeePlayer;
            
        case TransitionConditions.ATTACK_READY:
            return params.timeSinceLastAttack >= params.attackCooldown;
            
        case TransitionConditions.HEALTH_LOW:
            return params.health / params.maxHealth < 0.3;
            
        case TransitionConditions.PROJECTILE_INCOMING:
            return params.projectileIncoming;
            
        case TransitionConditions.TIMER_EXPIRED:
            return params.timer <= 0;
            
        case TransitionConditions.RANDOM_CHANCE:
            return Math.random() < params.chance;
            
        default:
            return false;
    }
}
