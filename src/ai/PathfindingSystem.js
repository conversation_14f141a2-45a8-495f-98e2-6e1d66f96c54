/**
 * Advanced Pathfinding System for AI
 * Provides intelligent navigation around obstacles, debris, and other enemies
 */
import * as THREE from 'three';

export class PathfindingSystem {
    /**
     * Constructor for Pathfinding System
     * @param {Object} aiBrain - The AI brain that owns this pathfinding system
     */
    constructor(aiBrain) {
        this.aiBrain = aiBrain;
        this.enemy = aiBrain.enemy;
        this.scene = aiBrain.scene;
        
        // Pathfinding parameters
        this.gridSize = 0.5; // Size of each grid cell
        this.maxPathLength = 50; // Maximum path length to prevent infinite loops
        this.pathUpdateInterval = 500; // Update path every 500ms
        this.lastPathUpdate = 0;
        
        // Current path data
        this.currentPath = [];
        this.currentPathIndex = 0;
        this.targetPosition = new THREE.Vector3();
        this.isPathValid = false;
        
        // Obstacle detection
        this.obstacleCheckRadius = 1.0;
        this.avoidanceRadius = 1.5;
        
        // Navigation grid (simplified for performance)
        this.navigationGrid = new Map();
        this.gridBounds = {
            min: new THREE.Vector3(-50, 0, -50),
            max: new THREE.Vector3(50, 0, 50)
        };
        
        // Temporary vectors for calculations
        this.tempVector1 = new THREE.Vector3();
        this.tempVector2 = new THREE.Vector3();
        this.tempVector3 = new THREE.Vector3();
    }

    /**
     * Find path to target position
     * @param {THREE.Vector3} targetPos - Target position
     * @param {Array} obstacles - Array of obstacle objects
     * @returns {Array} Array of waypoints
     */
    findPath(targetPos, obstacles = []) {
        const now = Date.now();
        
        // Check if we need to update the path
        if (now - this.lastPathUpdate < this.pathUpdateInterval && 
            this.isPathValid && 
            this.targetPosition.distanceTo(targetPos) < 1.0) {
            return this.currentPath;
        }
        
        this.lastPathUpdate = now;
        this.targetPosition.copy(targetPos);
        
        // Simple pathfinding with obstacle avoidance
        const startPos = this.enemy.position.clone();
        const directPath = this._getDirectPath(startPos, targetPos);
        
        // Check if direct path is clear
        if (this._isPathClear(directPath, obstacles)) {
            this.currentPath = directPath;
            this.currentPathIndex = 0;
            this.isPathValid = true;
            return this.currentPath;
        }
        
        // Use A* pathfinding for complex navigation
        const astarPath = this._findAStarPath(startPos, targetPos, obstacles);
        
        this.currentPath = astarPath;
        this.currentPathIndex = 0;
        this.isPathValid = astarPath.length > 0;
        
        return this.currentPath;
    }

    /**
     * Get next waypoint in current path
     * @returns {THREE.Vector3|null} Next waypoint or null if no path
     */
    getNextWaypoint() {
        if (!this.isPathValid || this.currentPathIndex >= this.currentPath.length) {
            return null;
        }
        
        const currentWaypoint = this.currentPath[this.currentPathIndex];
        const distanceToWaypoint = this.enemy.position.distanceTo(currentWaypoint);
        
        // Move to next waypoint if close enough
        if (distanceToWaypoint < 1.0) {
            this.currentPathIndex++;
            if (this.currentPathIndex >= this.currentPath.length) {
                return null;
            }
            return this.currentPath[this.currentPathIndex];
        }
        
        return currentWaypoint;
    }

    /**
     * Get steering force to avoid obstacles
     * @param {THREE.Vector3} desiredDirection - Desired movement direction
     * @param {Array} obstacles - Array of obstacles to avoid
     * @returns {THREE.Vector3} Steering force
     */
    getSteeringForce(desiredDirection, obstacles = []) {
        const steeringForce = new THREE.Vector3();
        const enemyPos = this.enemy.position;
        
        // Obstacle avoidance
        obstacles.forEach(obstacle => {
            if (!obstacle || !obstacle.position) return;
            
            const obstaclePos = obstacle.position;
            const distance = enemyPos.distanceTo(obstaclePos);
            
            if (distance < this.avoidanceRadius && distance > 0) {
                // Calculate avoidance force
                const avoidanceDirection = this.tempVector1.copy(enemyPos).sub(obstaclePos);
                avoidanceDirection.y = 0; // Keep horizontal
                avoidanceDirection.normalize();
                
                // Stronger force when closer
                const forceStrength = (this.avoidanceRadius - distance) / this.avoidanceRadius;
                avoidanceDirection.multiplyScalar(forceStrength);
                
                steeringForce.add(avoidanceDirection);
            }
        });
        
        // Enemy avoidance
        if (this.aiBrain.environmentalData && this.aiBrain.environmentalData.nearbyEnemies) {
            this.aiBrain.environmentalData.nearbyEnemies.forEach(enemyData => {
                const distance = enemyData.distance;
                if (distance < this.avoidanceRadius * 0.7 && distance > 0) {
                    const avoidanceDirection = this.tempVector2.copy(enemyPos).sub(enemyData.position);
                    avoidanceDirection.y = 0;
                    avoidanceDirection.normalize();
                    
                    const forceStrength = (this.avoidanceRadius * 0.7 - distance) / (this.avoidanceRadius * 0.7);
                    avoidanceDirection.multiplyScalar(forceStrength * 0.5); // Weaker than obstacle avoidance
                    
                    steeringForce.add(avoidanceDirection);
                }
            });
        }
        
        // Debris avoidance
        if (this.aiBrain.environmentalData && this.aiBrain.environmentalData.nearbyDebris) {
            this.aiBrain.environmentalData.nearbyDebris.forEach(debrisData => {
                const distance = debrisData.distance;
                if (distance < this.avoidanceRadius * 0.5 && distance > 0) {
                    const avoidanceDirection = this.tempVector3.copy(enemyPos).sub(debrisData.position);
                    avoidanceDirection.y = 0;
                    avoidanceDirection.normalize();
                    
                    const forceStrength = (this.avoidanceRadius * 0.5 - distance) / (this.avoidanceRadius * 0.5);
                    avoidanceDirection.multiplyScalar(forceStrength * 0.3); // Even weaker for debris
                    
                    steeringForce.add(avoidanceDirection);
                }
            });
        }
        
        // Normalize and limit steering force
        if (steeringForce.lengthSq() > 0) {
            steeringForce.normalize();
            steeringForce.multiplyScalar(Math.min(1.0, steeringForce.length()));
        }
        
        return steeringForce;
    }

    /**
     * Check if there's a clear line of sight to target
     * @param {THREE.Vector3} targetPos - Target position
     * @param {Array} obstacles - Array of obstacles
     * @returns {Boolean} True if line of sight is clear
     */
    hasLineOfSight(targetPos, obstacles = []) {
        const startPos = this.enemy.position;
        const direction = this.tempVector1.copy(targetPos).sub(startPos);
        const distance = direction.length();
        direction.normalize();
        
        // Raycast to check for obstacles
        const raycaster = new THREE.Raycaster(startPos, direction, 0, distance);
        
        for (const obstacle of obstacles) {
            if (!obstacle || !obstacle.geometry) continue;
            
            const intersections = raycaster.intersectObject(obstacle, true);
            if (intersections.length > 0) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get direct path between two points
     * @param {THREE.Vector3} start - Start position
     * @param {THREE.Vector3} end - End position
     * @returns {Array} Array of waypoints
     * @private
     */
    _getDirectPath(start, end) {
        return [start.clone(), end.clone()];
    }

    /**
     * Check if path is clear of obstacles
     * @param {Array} path - Array of waypoints
     * @param {Array} obstacles - Array of obstacles
     * @returns {Boolean} True if path is clear
     * @private
     */
    _isPathClear(path, obstacles) {
        if (path.length < 2) return true;
        
        for (let i = 0; i < path.length - 1; i++) {
            const start = path[i];
            const end = path[i + 1];
            
            if (!this._isSegmentClear(start, end, obstacles)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Check if a line segment is clear of obstacles
     * @param {THREE.Vector3} start - Start position
     * @param {THREE.Vector3} end - End position
     * @param {Array} obstacles - Array of obstacles
     * @returns {Boolean} True if segment is clear
     * @private
     */
    _isSegmentClear(start, end, obstacles) {
        const direction = this.tempVector1.copy(end).sub(start);
        const distance = direction.length();
        direction.normalize();
        
        // Check multiple points along the segment
        const checkPoints = Math.max(2, Math.floor(distance / this.obstacleCheckRadius));
        
        for (let i = 0; i <= checkPoints; i++) {
            const t = i / checkPoints;
            const checkPos = this.tempVector2.copy(start).lerp(end, t);
            
            // Check if this position collides with any obstacle
            for (const obstacle of obstacles) {
                if (!obstacle || !obstacle.position) continue;
                
                const obstacleDistance = checkPos.distanceTo(obstacle.position);
                if (obstacleDistance < this.obstacleCheckRadius) {
                    return false;
                }
            }
        }
        
        return true;
    }

    /**
     * Simple A* pathfinding implementation
     * @param {THREE.Vector3} start - Start position
     * @param {THREE.Vector3} end - End position
     * @param {Array} obstacles - Array of obstacles
     * @returns {Array} Array of waypoints
     * @private
     */
    _findAStarPath(start, end, obstacles) {
        // Simplified A* for performance
        // In a full implementation, this would use a proper grid and heap
        
        const waypoints = [];
        const currentPos = start.clone();
        const targetPos = end.clone();
        
        // Try to find intermediate waypoints around obstacles
        const maxAttempts = 10;
        let attempts = 0;
        
        while (currentPos.distanceTo(targetPos) > 1.0 && attempts < maxAttempts) {
            attempts++;
            
            // Find the best direction to move
            const bestDirection = this._findBestDirection(currentPos, targetPos, obstacles);
            
            if (bestDirection) {
                const stepSize = Math.min(3.0, currentPos.distanceTo(targetPos));
                const nextPos = currentPos.clone().add(bestDirection.multiplyScalar(stepSize));
                waypoints.push(nextPos.clone());
                currentPos.copy(nextPos);
            } else {
                // If no good direction found, try direct path
                break;
            }
        }
        
        // Add final target
        waypoints.push(targetPos.clone());
        
        return waypoints;
    }

    /**
     * Find best direction to move towards target while avoiding obstacles
     * @param {THREE.Vector3} currentPos - Current position
     * @param {THREE.Vector3} targetPos - Target position
     * @param {Array} obstacles - Array of obstacles
     * @returns {THREE.Vector3|null} Best direction or null
     * @private
     */
    _findBestDirection(currentPos, targetPos, obstacles) {
        const directDirection = this.tempVector1.copy(targetPos).sub(currentPos);
        directDirection.y = 0;
        directDirection.normalize();
        
        // Test multiple directions around the direct direction
        const testAngles = [-Math.PI/2, -Math.PI/4, 0, Math.PI/4, Math.PI/2];
        let bestDirection = null;
        let bestScore = -Infinity;
        
        for (const angle of testAngles) {
            const testDirection = this.tempVector2.copy(directDirection);
            testDirection.applyAxisAngle(new THREE.Vector3(0, 1, 0), angle);
            
            const score = this._scoreDirection(currentPos, testDirection, targetPos, obstacles);
            
            if (score > bestScore) {
                bestScore = score;
                bestDirection = testDirection.clone();
            }
        }
        
        return bestDirection;
    }

    /**
     * Score a direction based on target alignment and obstacle avoidance
     * @param {THREE.Vector3} currentPos - Current position
     * @param {THREE.Vector3} direction - Direction to test
     * @param {THREE.Vector3} targetPos - Target position
     * @param {Array} obstacles - Array of obstacles
     * @returns {Number} Direction score
     * @private
     */
    _scoreDirection(currentPos, direction, targetPos, obstacles) {
        // Score based on alignment with target
        const targetDirection = this.tempVector3.copy(targetPos).sub(currentPos).normalize();
        const alignment = direction.dot(targetDirection);
        
        // Penalty for obstacles in this direction
        let obstaclePenalty = 0;
        const testPos = currentPos.clone().add(direction.clone().multiplyScalar(2.0));
        
        for (const obstacle of obstacles) {
            if (!obstacle || !obstacle.position) continue;
            
            const distance = testPos.distanceTo(obstacle.position);
            if (distance < this.avoidanceRadius) {
                obstaclePenalty += (this.avoidanceRadius - distance) / this.avoidanceRadius;
            }
        }
        
        return alignment - obstaclePenalty;
    }
}
