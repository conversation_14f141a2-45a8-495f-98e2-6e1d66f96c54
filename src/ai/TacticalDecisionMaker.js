/**
 * Tactical Decision Maker for AI
 * Makes intelligent decisions based on combat conditions, personality, and environment
 */
import { AIStates } from './AIStates.js';

export class TacticalDecisionMaker {
    /**
     * Constructor for Tactical Decision Maker
     * @param {Object} aiBrain - The AI brain that owns this decision maker
     */
    constructor(aiBrain) {
        this.aiBrain = aiBrain;
        this.enemy = aiBrain.enemy;
        this.player = aiBrain.player;
        
        // Decision weights based on personality and situation
        this.decisionWeights = {
            aggression: 0.5,
            caution: 0.5,
            intelligence: 0.5,
            cooperation: 0.5,
            adaptability: 0.5
        };
        
        // State transition probabilities
        this.transitionMatrix = new Map();
        this._initializeTransitionMatrix();
        
        // Decision history for learning
        this.decisionHistory = [];
        this.maxHistoryLength = 20;
    }

    /**
     * Make tactical decision based on current situation
     * @param {Object} context - Decision context
     * @returns {String} Recommended AI state
     */
    makeDecision(context) {
        const {
            distanceToPlayer,
            directionToPlayer,
            currentState,
            personality,
            environmentalData,
            groupBehavior,
            combatIntelligence
        } = context;

        // Update decision weights based on personality
        this._updateDecisionWeights(personality);

        // Analyze current situation
        const situationAnalysis = this._analyzeSituation(context);

        // Calculate state scores
        const stateScores = this._calculateStateScores(situationAnalysis, context);

        // Select best state
        const bestState = this._selectBestState(stateScores, currentState);

        // Record decision for learning
        this._recordDecision(context, bestState, situationAnalysis);

        return bestState;
    }

    /**
     * Update decision weights based on personality
     * @param {Object} personality - Personality traits
     * @private
     */
    _updateDecisionWeights(personality) {
        this.decisionWeights.aggression = personality.aggression;
        this.decisionWeights.caution = personality.caution;
        this.decisionWeights.intelligence = personality.intelligence;
        this.decisionWeights.cooperation = personality.cooperation;
        this.decisionWeights.adaptability = personality.adaptability;
    }

    /**
     * Analyze current situation
     * @param {Object} context - Decision context
     * @returns {Object} Situation analysis
     * @private
     */
    _analyzeSituation(context) {
        const {
            distanceToPlayer,
            environmentalData,
            groupBehavior,
            combatIntelligence
        } = context;

        const analysis = {
            threatLevel: this._calculateThreatLevel(context),
            opportunityLevel: this._calculateOpportunityLevel(context),
            supportLevel: this._calculateSupportLevel(context),
            environmentalAdvantage: this._calculateEnvironmentalAdvantage(context),
            playerBehaviorPattern: this._analyzePlayerBehavior(context)
        };

        // Distance-based factors
        analysis.isInAttackRange = distanceToPlayer <= (this.aiBrain.attackRange || 3.0);
        analysis.isInPreferredRange = distanceToPlayer <= (this.aiBrain.preferredRange || 5.0);
        analysis.isPlayerTooClose = distanceToPlayer < (this.aiBrain.preferredRange || 5.0) * 0.5;
        analysis.isPlayerTooFar = distanceToPlayer > (this.aiBrain.preferredRange || 5.0) * 2.0;

        // Health-based factors
        const healthRatio = this.aiBrain.enemyData.health / (this.aiBrain.enemyData.maxHealth || 100);
        analysis.isLowHealth = healthRatio < 0.3;
        analysis.isCriticalHealth = healthRatio < 0.15;

        // Group factors
        analysis.hasGroupSupport = groupBehavior.isGroupMember && 
                                  environmentalData.nearbyEnemies.length > 0;
        analysis.isIsolated = !analysis.hasGroupSupport;

        return analysis;
    }

    /**
     * Calculate threat level from player
     * @param {Object} context - Decision context
     * @returns {Number} Threat level (0-1)
     * @private
     */
    _calculateThreatLevel(context) {
        const { distanceToPlayer, combatIntelligence } = context;
        
        let threatLevel = 0;

        // Distance-based threat
        const maxThreatDistance = 10.0;
        const distanceThreat = Math.max(0, 1 - (distanceToPlayer / maxThreatDistance));
        threatLevel += distanceThreat * 0.4;

        // Player aggression pattern
        if (combatIntelligence.playerPatterns.has('aggressive')) {
            threatLevel += 0.3;
        }

        // Player weapon/attack pattern
        if (combatIntelligence.lastPlayerActions.length > 0) {
            const recentAttacks = combatIntelligence.lastPlayerActions
                .filter(action => action.type === 'attack').length;
            threatLevel += Math.min(0.3, recentAttacks * 0.1);
        }

        return Math.min(1.0, threatLevel);
    }

    /**
     * Calculate opportunity level for attack
     * @param {Object} context - Decision context
     * @returns {Number} Opportunity level (0-1)
     * @private
     */
    _calculateOpportunityLevel(context) {
        const { distanceToPlayer, environmentalData } = context;
        
        let opportunityLevel = 0;

        // Distance-based opportunity
        const optimalDistance = this.aiBrain.attackRange || 3.0;
        const distanceScore = 1 - Math.abs(distanceToPlayer - optimalDistance) / optimalDistance;
        opportunityLevel += Math.max(0, distanceScore) * 0.5;

        // Environmental advantages
        if (environmentalData.coverPositions.length > 0) {
            opportunityLevel += 0.2;
        }

        // Player vulnerability (if player is not moving much)
        const playerVelocity = this.aiBrain.prediction?.playerVelocity?.length() || 0;
        if (playerVelocity < 1.0) {
            opportunityLevel += 0.3;
        }

        return Math.min(1.0, opportunityLevel);
    }

    /**
     * Calculate support level from allies
     * @param {Object} context - Decision context
     * @returns {Number} Support level (0-1)
     * @private
     */
    _calculateSupportLevel(context) {
        const { environmentalData, groupBehavior } = context;
        
        if (!groupBehavior.isGroupMember) {
            return 0;
        }

        const nearbyAllies = environmentalData.nearbyEnemies.length;
        const maxSupportAllies = 3;
        
        return Math.min(1.0, nearbyAllies / maxSupportAllies);
    }

    /**
     * Calculate environmental advantage
     * @param {Object} context - Decision context
     * @returns {Number} Environmental advantage (0-1)
     * @private
     */
    _calculateEnvironmentalAdvantage(context) {
        const { environmentalData } = context;
        
        let advantage = 0;

        // Cover availability
        if (environmentalData.coverPositions.length > 0) {
            const bestCover = Math.max(...environmentalData.coverPositions.map(c => c.coverValue));
            advantage += bestCover * 0.4;
        }

        // High ground advantage (simplified)
        const enemyY = this.enemy.position.y;
        const playerY = this.player.position.y;
        if (enemyY > playerY + 1.0) {
            advantage += 0.3;
        }

        // Debris for cover
        if (environmentalData.nearbyDebris.length > 2) {
            advantage += 0.3;
        }

        return Math.min(1.0, advantage);
    }

    /**
     * Analyze player behavior pattern
     * @param {Object} context - Decision context
     * @returns {String} Player behavior pattern
     * @private
     */
    _analyzePlayerBehavior(context) {
        const { combatIntelligence } = context;
        
        if (combatIntelligence.lastPlayerActions.length < 5) {
            return 'unknown';
        }

        const actions = combatIntelligence.lastPlayerActions.slice(-10);
        const attackCount = actions.filter(a => a.type === 'attack').length;
        const moveCount = actions.filter(a => a.type === 'move').length;
        const defendCount = actions.filter(a => a.type === 'defend').length;

        if (attackCount > moveCount && attackCount > defendCount) {
            return 'aggressive';
        } else if (moveCount > attackCount && moveCount > defendCount) {
            return 'evasive';
        } else if (defendCount > attackCount) {
            return 'defensive';
        }

        return 'balanced';
    }

    /**
     * Calculate scores for each possible state
     * @param {Object} situationAnalysis - Analysis of current situation
     * @param {Object} context - Decision context
     * @returns {Map} State scores
     * @private
     */
    _calculateStateScores(situationAnalysis, context) {
        const scores = new Map();
        const { currentState, personality } = context;

        // IDLE state score
        scores.set(AIStates.IDLE, this._calculateIdleScore(situationAnalysis, personality));

        // MOVING state score
        scores.set(AIStates.MOVING, this._calculateMovingScore(situationAnalysis, personality));

        // ATTACKING state score
        scores.set(AIStates.ATTACKING, this._calculateAttackingScore(situationAnalysis, personality));

        // STRAFING state score
        scores.set(AIStates.STRAFING, this._calculateStrafingScore(situationAnalysis, personality));

        // FLEEING state score
        scores.set(AIStates.FLEEING, this._calculateFleeingScore(situationAnalysis, personality));

        // BLOCKING state score (if applicable)
        if (this.aiBrain.isBlocking !== undefined) {
            scores.set(AIStates.BLOCKING, this._calculateBlockingScore(situationAnalysis, personality));
        }

        return scores;
    }

    /**
     * Calculate IDLE state score
     * @param {Object} analysis - Situation analysis
     * @param {Object} personality - Personality traits
     * @returns {Number} State score
     * @private
     */
    _calculateIdleScore(analysis, personality) {
        let score = 0;

        // Base idle tendency
        score += personality.caution * 0.3;

        // Reduce score if player is close
        if (analysis.isInAttackRange) {
            score -= 0.8;
        } else if (analysis.isInPreferredRange) {
            score -= 0.4;
        }

        // Increase score if low health and no support
        if (analysis.isLowHealth && analysis.isIsolated) {
            score += personality.caution * 0.5;
        }

        return Math.max(0, score);
    }

    /**
     * Calculate MOVING state score
     * @param {Object} analysis - Situation analysis
     * @param {Object} personality - Personality traits
     * @returns {Number} State score
     * @private
     */
    _calculateMovingScore(analysis, personality) {
        let score = 0;

        // Base movement tendency
        score += personality.aggression * 0.4;

        // High score if player is too far
        if (analysis.isPlayerTooFar) {
            score += 0.8;
        } else if (!analysis.isInPreferredRange) {
            score += 0.5;
        }

        // Reduce score if player is too close
        if (analysis.isPlayerTooClose) {
            score -= 0.6;
        }

        // Group coordination bonus
        if (analysis.hasGroupSupport) {
            score += personality.cooperation * 0.3;
        }

        return Math.max(0, score);
    }

    /**
     * Calculate ATTACKING state score
     * @param {Object} analysis - Situation analysis
     * @param {Object} personality - Personality traits
     * @returns {Number} State score
     * @private
     */
    _calculateAttackingScore(analysis, personality) {
        let score = 0;

        // Base attack tendency
        score += personality.aggression * 0.6;

        // High score if in attack range
        if (analysis.isInAttackRange) {
            score += 0.9;
        } else {
            score -= 0.5;
        }

        // Opportunity bonus
        score += analysis.opportunityLevel * 0.4;

        // Support bonus
        if (analysis.hasGroupSupport) {
            score += 0.3;
        }

        // Reduce score if low health and no support
        if (analysis.isLowHealth && analysis.isIsolated) {
            score -= personality.caution * 0.6;
        }

        // Check attack cooldown
        if (this.aiBrain.timeSinceLastAttack < this.aiBrain.attackCooldown) {
            score -= 0.8;
        }

        return Math.max(0, score);
    }

    /**
     * Calculate STRAFING state score
     * @param {Object} analysis - Situation analysis
     * @param {Object} personality - Personality traits
     * @returns {Number} State score
     * @private
     */
    _calculateStrafingScore(analysis, personality) {
        let score = 0;

        // Base strafing tendency
        score += personality.intelligence * 0.4;

        // Good score if in preferred range but not attacking
        if (analysis.isInPreferredRange && !analysis.isInAttackRange) {
            score += 0.6;
        }

        // Tactical movement bonus
        if (analysis.environmentalAdvantage > 0.5) {
            score += 0.4;
        }

        // Player behavior adaptation
        if (analysis.playerBehaviorPattern === 'aggressive') {
            score += personality.adaptability * 0.5;
        }

        return Math.max(0, score);
    }

    /**
     * Calculate FLEEING state score
     * @param {Object} analysis - Situation analysis
     * @param {Object} personality - Personality traits
     * @returns {Number} State score
     * @private
     */
    _calculateFleeingScore(analysis, personality) {
        let score = 0;

        // Base fleeing tendency
        score += (1 - personality.aggression) * 0.3;

        // High score if critical health
        if (analysis.isCriticalHealth) {
            score += 0.9;
        } else if (analysis.isLowHealth) {
            score += 0.6;
        }

        // High threat and no support
        if (analysis.threatLevel > 0.7 && analysis.isIsolated) {
            score += personality.caution * 0.7;
        }

        // Reduce score if has group support
        if (analysis.hasGroupSupport) {
            score -= 0.4;
        }

        return Math.max(0, score);
    }

    /**
     * Calculate BLOCKING state score
     * @param {Object} analysis - Situation analysis
     * @param {Object} personality - Personality traits
     * @returns {Number} State score
     * @private
     */
    _calculateBlockingScore(analysis, personality) {
        let score = 0;

        // Base blocking tendency
        score += personality.caution * 0.4;

        // High score if player is aggressive and close
        if (analysis.playerBehaviorPattern === 'aggressive' && analysis.isInAttackRange) {
            score += 0.7;
        }

        // Defensive personality bonus
        if (personality.caution > personality.aggression) {
            score += 0.3;
        }

        return Math.max(0, score);
    }

    /**
     * Select best state from scores
     * @param {Map} stateScores - State scores
     * @param {String} currentState - Current AI state
     * @returns {String} Best state
     * @private
     */
    _selectBestState(stateScores, currentState) {
        let bestState = currentState;
        let bestScore = stateScores.get(currentState) || 0;

        // Add small bonus to current state to prevent rapid switching
        bestScore += 0.1;

        for (const [state, score] of stateScores) {
            if (score > bestScore) {
                bestScore = score;
                bestState = state;
            }
        }

        return bestState;
    }

    /**
     * Record decision for learning
     * @param {Object} context - Decision context
     * @param {String} decision - Made decision
     * @param {Object} analysis - Situation analysis
     * @private
     */
    _recordDecision(context, decision, analysis) {
        const record = {
            timestamp: Date.now(),
            context: { ...context },
            decision,
            analysis: { ...analysis }
        };

        this.decisionHistory.push(record);

        // Limit history length
        if (this.decisionHistory.length > this.maxHistoryLength) {
            this.decisionHistory.shift();
        }
    }

    /**
     * Initialize state transition matrix
     * @private
     */
    _initializeTransitionMatrix() {
        // Define valid transitions between states
        this.transitionMatrix.set(AIStates.IDLE, [
            AIStates.MOVING, AIStates.ATTACKING, AIStates.STRAFING, AIStates.BLOCKING
        ]);
        
        this.transitionMatrix.set(AIStates.MOVING, [
            AIStates.IDLE, AIStates.ATTACKING, AIStates.STRAFING, AIStates.FLEEING
        ]);
        
        this.transitionMatrix.set(AIStates.ATTACKING, [
            AIStates.IDLE, AIStates.MOVING, AIStates.STRAFING, AIStates.BLOCKING
        ]);
        
        this.transitionMatrix.set(AIStates.STRAFING, [
            AIStates.IDLE, AIStates.MOVING, AIStates.ATTACKING, AIStates.FLEEING
        ]);
        
        this.transitionMatrix.set(AIStates.FLEEING, [
            AIStates.IDLE, AIStates.MOVING, AIStates.STRAFING
        ]);
        
        this.transitionMatrix.set(AIStates.BLOCKING, [
            AIStates.IDLE, AIStates.MOVING, AIStates.ATTACKING, AIStates.STRAFING
        ]);
    }
}
