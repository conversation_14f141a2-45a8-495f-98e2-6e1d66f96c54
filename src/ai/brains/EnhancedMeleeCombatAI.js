/**
 * Enhanced Melee Combat AI implementation
 * Combines the base melee AI with advanced intelligence systems
 */
import * as THREE from 'three';
import { EnhancedAIBrain } from '../EnhancedAIBrain.js';
import { AIStates } from '../AIStates.js';

export class EnhancedMeleeCombatAI extends EnhancedAIBrain {
    /**
     * Constructor for Enhanced Melee Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Melee specific parameters
        this.preferredRange = this._getScaledValue(enemyData.preferredRange || 2.0);
        this.attackRange = this._getScaledValue(enemyData.attackRange || 2.5);
        this.chargeRange = this._getScaledValue(enemyData.chargeRange || 6.0);

        // Enhanced melee behaviors
        this.meleeEnhancements = {
            comboAttacks: this.personality.aggression > 0.6,
            feintAttacks: this.personality.intelligence > 0.7,
            counterAttacks: this.personality.adaptability > 0.5,
            berserkerMode: this.personality.riskTolerance > 0.8,
            defensiveStance: this.personality.caution > 0.6
        };

        // Charging behavior
        this.chargeData = {
            isCharging: false,
            chargeTimer: 0,
            chargeDuration: this._getScaledValue(1.0, 0.5, 1.5),
            chargeSpeed: this._getScaledValue(enemyData.speed * 1.5, enemyData.speed * 1.2, enemyData.speed * 2.0),
            chargeChance: this._getScaledValue(0.4, 0.2, 0.6)
        };

        // Blocking behavior
        this.blockData = {
            isBlocking: false,
            blockTimer: 0,
            blockDuration: this._getScaledValue(1.0, 0.5, 1.5),
            blockChance: this._getScaledValue(0.3, 0.1, 0.5),
            perfectBlockWindow: 0.2 // Time window for perfect blocks
        };

        // Circling behavior
        this.circleData = {
            isCircling: false,
            circleDirection: 1,
            circleTimer: 0,
            circleDuration: this._getScaledValue(2.0, 1.0, 3.0),
            circleChance: this._getScaledValue(0.5, 0.3, 0.7),
            circleRadius: this.preferredRange * 0.8
        };

        // Combat adaptation
        this.combatAdaptation = {
            playerDodgePattern: 'unknown',
            playerAttackTiming: [],
            lastPlayerAction: null,
            adaptationTimer: 0
        };

        // Set group role based on personality
        this.groupBehavior.groupRole = this._determineGroupRole();
    }

    /**
     * Determine group role based on personality
     * @returns {String} Group role
     * @private
     */
    _determineGroupRole() {
        if (this.personality.aggression > 0.7) {
            return 'assault';
        } else if (this.personality.caution > 0.7) {
            return 'defender';
        } else if (this.personality.intelligence > 0.7) {
            return 'flanker';
        } else {
            return 'support';
        }
    }

    /**
     * Enhanced state update for melee combat
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Update combat adaptation
        this._updateCombatAdaptation(deltaTime);

        // Call parent enhanced state update
        super._updateState(deltaTime, distanceToPlayer, directionToPlayer);

        // Additional melee-specific state logic
        this._updateMeleeSpecificStates(deltaTime, distanceToPlayer, directionToPlayer);
    }

    /**
     * Update melee-specific states
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _updateMeleeSpecificStates(deltaTime, distanceToPlayer, directionToPlayer) {
        // Update timers
        if (this.chargeData.isCharging) {
            this.chargeData.chargeTimer -= deltaTime;
            if (this.chargeData.chargeTimer <= 0) {
                this.chargeData.isCharging = false;
            }
        }

        if (this.blockData.isBlocking) {
            this.blockData.blockTimer -= deltaTime;
            if (this.blockData.blockTimer <= 0) {
                this.blockData.isBlocking = false;
            }
        }

        if (this.circleData.isCircling) {
            this.circleData.circleTimer -= deltaTime;
            if (this.circleData.circleTimer <= 0) {
                this.circleData.isCircling = false;
            }
        }

        // Melee-specific state transitions
        switch (this.currentState) {
            case AIStates.IDLE:
                this._handleIdleToMeleeTransitions(distanceToPlayer);
                break;

            case AIStates.MOVING:
                this._handleMovingToMeleeTransitions(distanceToPlayer);
                break;

            case AIStates.ATTACKING:
                this._handleAttackingTransitions(deltaTime, distanceToPlayer);
                break;

            case AIStates.STRAFING:
                this._handleStrafingTransitions(distanceToPlayer);
                break;

            case AIStates.CHARGING:
                this._handleChargingTransitions(distanceToPlayer);
                break;

            case AIStates.BLOCKING:
                this._handleBlockingTransitions(distanceToPlayer);
                break;
        }
    }

    /**
     * Handle idle to melee state transitions
     * @param {Number} distanceToPlayer - Distance to player
     * @private
     */
    _handleIdleToMeleeTransitions(distanceToPlayer) {
        if (!this._canSeePlayer()) return;

        // Consider charging if player is in charge range
        if (distanceToPlayer <= this.chargeRange && 
            distanceToPlayer > this.attackRange &&
            Math.random() < this.chargeData.chargeChance &&
            this.personality.aggression > 0.5) {
            this.setState(AIStates.CHARGING);
        }
        // Attack if in range and ready
        else if (distanceToPlayer <= this.attackRange &&
                 this.timeSinceLastAttack >= this.attackCooldown) {
            this.setState(AIStates.ATTACKING);
        }
        // Move closer if too far
        else if (distanceToPlayer > this.preferredRange) {
            this.setState(AIStates.MOVING);
        }
        // Consider circling if in good position
        else if (Math.random() < this.circleData.circleChance) {
            this.setState(AIStates.STRAFING);
        }
    }

    /**
     * Handle moving to melee state transitions
     * @param {Number} distanceToPlayer - Distance to player
     * @private
     */
    _handleMovingToMeleeTransitions(distanceToPlayer) {
        // Consider charging if conditions are right
        if (distanceToPlayer <= this.chargeRange &&
            distanceToPlayer > this.attackRange &&
            Math.random() < this.chargeData.chargeChance &&
            this.personality.aggression > 0.6) {
            this.setState(AIStates.CHARGING);
        }
        // Attack if in range
        else if (distanceToPlayer <= this.attackRange &&
                 this.timeSinceLastAttack >= this.attackCooldown) {
            this.setState(AIStates.ATTACKING);
        }
        // Start circling if reached good position
        else if (distanceToPlayer <= this.preferredRange) {
            if (Math.random() < this.circleData.circleChance) {
                this.setState(AIStates.STRAFING);
            } else {
                this.setState(AIStates.IDLE);
            }
        }
    }

    /**
     * Handle attacking state transitions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @private
     */
    _handleAttackingTransitions(deltaTime, distanceToPlayer) {
        // Check if attack animation is complete
        if (this.attackStateTimer && this.attackStateTimer <= 0) {
            this.timeSinceLastAttack = 0;
            
            // Decide next action based on situation and personality
            if (distanceToPlayer > this.preferredRange) {
                this.setState(AIStates.MOVING);
            } else if (this.meleeEnhancements.defensiveStance && Math.random() < this.blockData.blockChance) {
                this.setState(AIStates.BLOCKING);
            } else if (Math.random() < this.circleData.circleChance) {
                this.setState(AIStates.STRAFING);
            } else {
                this.setState(AIStates.IDLE);
            }
        }
        // Cancel attack if player moves too far away
        else if (distanceToPlayer > this.attackRange * 1.5) {
            this.setState(AIStates.MOVING);
        }
    }

    /**
     * Handle strafing state transitions
     * @param {Number} distanceToPlayer - Distance to player
     * @private
     */
    _handleStrafingTransitions(distanceToPlayer) {
        // Attack if in range and ready
        if (distanceToPlayer <= this.attackRange &&
            this.timeSinceLastAttack >= this.attackCooldown) {
            const attackWhileCirclingChance = this._getScaledValue(0.6, 0.4, 0.8);
            if (Math.random() < attackWhileCirclingChance) {
                this.setState(AIStates.ATTACKING);
            }
        }
        // Chase if player moves too far
        else if (distanceToPlayer > this.preferredRange * 1.5) {
            this.setState(AIStates.MOVING);
        }
        // End circling if timer expired
        else if (!this.circleData.isCircling) {
            this.setState(AIStates.IDLE);
        }
    }

    /**
     * Handle charging state transitions
     * @param {Number} distanceToPlayer - Distance to player
     * @private
     */
    _handleChargingTransitions(distanceToPlayer) {
        // Attack if reached attack range during charge
        if (distanceToPlayer <= this.attackRange) {
            this.setState(AIStates.ATTACKING);
        }
        // End charge if timer expired
        else if (!this.chargeData.isCharging) {
            if (distanceToPlayer <= this.attackRange) {
                this.setState(AIStates.ATTACKING);
            } else {
                this.setState(AIStates.MOVING);
            }
        }
    }

    /**
     * Handle blocking state transitions
     * @param {Number} distanceToPlayer - Distance to player
     * @private
     */
    _handleBlockingTransitions(distanceToPlayer) {
        // End blocking if timer expired
        if (!this.blockData.isBlocking) {
            if (Math.random() < this.circleData.circleChance) {
                this.setState(AIStates.STRAFING);
            } else {
                this.setState(AIStates.IDLE);
            }
        }
    }

    /**
     * Enhanced behavior execution for melee combat
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Always face the player unless circling or fleeing
        if (this.currentState !== AIStates.STRAFING && this.currentState !== AIStates.FLEEING) {
            this._faceTarget(this.player.position);
        }

        // Execute behavior based on current state
        switch (this.currentState) {
            case AIStates.CHARGING:
                this._executeEnhancedCharging(deltaTime, directionToPlayer);
                break;

            case AIStates.BLOCKING:
                this._executeEnhancedBlocking(deltaTime);
                break;

            default:
                // Use parent enhanced behavior
                super._executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds);
                break;
        }
    }

    /**
     * Execute enhanced charging behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeEnhancedCharging(deltaTime, directionToPlayer) {
        if (this.isKnockedBack) return;

        // Use predictive targeting for charge
        const targetPosition = this.combatIntelligence.predictiveTargeting ? 
            this.prediction.predictedPlayerPosition : 
            this.player.position;

        const chargeDirection = targetPosition.clone().sub(this.enemy.position);
        chargeDirection.y = 0;
        chargeDirection.normalize();

        // Apply movement with charge speed
        this._applyMovement(deltaTime, chargeDirection, this.chargeData.chargeSpeed);

        // Face charge direction
        this._faceTarget(targetPosition);
    }

    /**
     * Execute enhanced blocking behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeEnhancedBlocking(deltaTime) {
        // Maintain facing towards player while blocking
        this._faceTarget(this.player.position);

        // Reduce movement while blocking
        // Block animation and effects would be handled by the animation system
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        super.onStateEnter(state);

        switch (state) {
            case AIStates.CHARGING:
                this.chargeData.isCharging = true;
                this.chargeData.chargeTimer = this.chargeData.chargeDuration;
                break;

            case AIStates.ATTACKING:
                this.timeSinceLastAttack = 0;
                this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || 2.4;
                this.attackStateTimer = this.attackStateDuration;
                break;

            case AIStates.BLOCKING:
                this.blockData.isBlocking = true;
                this.blockData.blockTimer = this.blockData.blockDuration;
                break;

            case AIStates.STRAFING:
                this.circleData.isCircling = true;
                this.circleData.circleTimer = this.circleData.circleDuration;
                // 50% chance to change circle direction
                if (Math.random() < 0.5) {
                    this.circleData.circleDirection *= -1;
                }
                break;
        }
    }

    /**
     * Update combat adaptation based on player behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updateCombatAdaptation(deltaTime) {
        this.combatAdaptation.adaptationTimer += deltaTime;

        // Analyze player dodge patterns
        if (this.combatAdaptation.adaptationTimer > 2.0) {
            this._analyzePlayerDodgePattern();
            this.combatAdaptation.adaptationTimer = 0;
        }

        // Record player attack timing
        this._recordPlayerAttackTiming();
    }

    /**
     * Analyze player dodge patterns
     * @private
     */
    _analyzePlayerDodgePattern() {
        // This would analyze player movement patterns during attacks
        // For now, simplified implementation
        const playerVelocity = this.prediction.playerVelocity.length();
        
        if (playerVelocity > 3.0) {
            this.combatAdaptation.playerDodgePattern = 'mobile';
        } else if (playerVelocity > 1.0) {
            this.combatAdaptation.playerDodgePattern = 'moderate';
        } else {
            this.combatAdaptation.playerDodgePattern = 'stationary';
        }
    }

    /**
     * Record player attack timing for adaptation
     * @private
     */
    _recordPlayerAttackTiming() {
        // This would record when player attacks to predict timing
        // Implementation would depend on how player attacks are detected
    }

    /**
     * Execute melee attack with enhancements
     * @private
     */
    _executeMeleeAttack() {
        if (!this.player) return;

        // Get attack data from enemy model
        const attackHitbox = this.enemy.userData.attackHitbox || {
            radius: this.attackRange,
            damage: this.enemyData.meleeDamage || 4,
            knockback: 5.0
        };

        // Apply combat style modifications
        if (this.personality.combatStyle === 'aggressive') {
            attackHitbox.damage *= 1.2;
            attackHitbox.knockback *= 1.3;
        } else if (this.personality.combatStyle === 'tactical') {
            // Tactical attacks have better accuracy
            attackHitbox.radius *= 1.1;
        }

        // Emit attack event
        if (this.enemy.userData.onMeleeAttack) {
            this.enemy.userData.onMeleeAttack(attackHitbox);
        }
    }
}
