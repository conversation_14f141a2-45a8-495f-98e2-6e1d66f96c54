/**
 * BossController.js
 *
 * Controls boss behavior based on music analysis.
 * Coordinates between BossMusicAnalyzer and BulletPatternManager to create
 * a music-reactive boss battle experience.
 */

import * as THREE from 'three';
import { BossMusicAnalyzer } from '../../audio/BossMusicAnalyzer.js';
import { BossMusicTimeline } from '../../audio/BossMusicTimeline.js';
import { BulletPatternManager } from '../../projectiles/BulletPatternManager.js';
import { bossTimelines } from '../../data/bossTimelines.js';
import { CustomTimelineShaker } from '../../audio/CustomTimelineShaker.js';

export class BossController {
    /**
     * Constructor for BossController
     * @param {Object} boss - The boss entity
     * @param {Object} dungeonHandler - Reference to the DungeonHandler
     * @param {Object} audioManager - Reference to the AudioManager
     * @param {Object} options - Configuration options
     */
    constructor(boss, dungeonHandler, audioManager, options = {}) {
        console.log("[<PERSON><PERSON>ontroller] constructor called");
        console.log("[Boss<PERSON><PERSON>roller] Boss parameter:", boss ? "exists" : "null");
        console.log("[BossController] DungeonHandler parameter:", dungeonHandler ? "exists" : "null");
        console.log("[BossController] AudioManager parameter:", audioManager ? "exists" : "null");

        this.boss = boss;
        this.dungeonHandler = dungeonHandler;
        this.audioManager = audioManager;

        if (boss) {
            console.log("[BossController] Boss position:", boss.position ? "exists" : "null");
            if (boss.position) {
                console.log(`[BossController] Boss position: ${boss.position.x.toFixed(2)}, ${boss.position.y.toFixed(2)}, ${boss.position.z.toFixed(2)}`);
            }
        }

        // Configuration
        this.options = {
            visualEffects: options.visualEffects !== undefined ? options.visualEffects : true,
            screenShake: options.screenShake !== undefined ? options.screenShake : true,
            debugMode: options.debugMode !== undefined ? options.debugMode : false,
            timelineName: options.timelineName || null, // Name of the timeline to use
            ...options
        };

        // State
        this.active = false;
        this.currentIntensity = 0;
        this.lastBeatTime = 0;
        this.lastPatternTime = 0;
        this.bossPhase = 0;

        // Create music analyzer
        this.musicAnalyzer = new BossMusicAnalyzer({
            smoothingFactor: 0.8,
            updateInterval: 50,
            fftSize: 1024
        });

        // Create bullet pattern manager
        this.patternManager = new BulletPatternManager(dungeonHandler, boss);

        // Store a reference to the boss controller in the boss object
        if (boss && boss.userData) {
            boss.userData.bossController = this;
            console.log("[BossController] Stored reference to boss controller in boss userData");
        }

        // Create music timeline
        this.musicTimeline = new BossMusicTimeline({
            debug: this.options.debugMode
        });

        // Create custom timeline shaker for specific timestamp transitions
        this.timelineShaker = new CustomTimelineShaker({
            dungeonHandler: this.dungeonHandler,
            debug: this.options.debugMode,
            shakeIntensity: 0.25,
            shakeDuration: 300
        });

        // Set up timeline callbacks
        this.musicTimeline.setPatternTriggerCallback(this._onTimelinePatternTrigger.bind(this));
        this.musicTimeline.setTimelineEntryChangeCallback(this._onTimelineEntryChange.bind(this));
        this.musicTimeline.setTimelineStartCallback(this._onTimelineStart.bind(this));
        this.musicTimeline.setTimelineEndCallback(this._onTimelineEnd.bind(this));

        // Current timeline name
        this.currentTimelineName = null;

        // Load timeline if specified in options
        if (this.options.timelineName && bossTimelines[this.options.timelineName]) {
            this.loadTimeline(this.options.timelineName);
        }

        // Visual effects with enhanced capabilities
        this.visualEffects = {
            backgroundPulse: 0,         // Strong pulse on beats
            backgroundPulseSubtle: 0,   // Subtle pulse based on beat phase
            glowStrength: 0,            // Boss glow effect
            screenShakeAmount: 0,        // Screen shake amount
            vignetteIntensity: 0         // Vignette effect for anticipation
        };

        // Store original values for visual effects
        this._originalBackgroundColor = null;
        this._originalCameraPosition = null;
        this._originalEmissive = null;

        // For timeline logging
        this._lastLoggedTime = -1;

        // Enhanced debug info with more musical features and note pattern detection
        this.debugInfo = {
            intensity: 50, // Start with a default value of 50%
            subBassIntensity: 30,
            bassIntensity: 40,
            lowMidIntensity: 45,
            midrangeIntensity: 60,
            upperMidIntensity: 50,
            trebleIntensity: 30,
            highFreqIntensity: 20,
            lastPattern: 'initializing',
            beatDetected: false,
            beatAnticipated: false,
            // Musical features
            isArpeggio: false,
            isBuildUp: false,
            isDrop: false,
            isTransition: false,
            // Enhanced note pattern detection
            noteChangeRate: 0,      // Rate of note changes (0-1)
            noteChangeSpeed: 0,     // Speed of note changes (0-2)
            patternComplexity: 0,   // Complexity of note patterns (0-1)
            isStaccato: false,      // Short, detached notes
            isLegato: false,        // Smooth, connected notes

            // Timeline information
            timelineActive: false,
            timelineProgress: 0,
            timelineCurrentTime: 0,
            timelineCurrentSection: 'None',
            timelineCurrentPattern: 'None',
            timelineCurrentProjectile: 'None',
            lastTimelinePattern: 'None',
            lastTimelineIntensity: 0,
            lastTimelineSpeedMultiplier: 1.0,

            // Additional musical features
            isTremolo: false,       // Rapid repetition of a single note
            isTrill: false,         // Rapid alternation between two notes
            // Tempo information
            tempo: 120,
            beatPhase: 0,
            beatConfidence: 0,      // Confidence in beat prediction (0-1)
            // Current section
            currentSection: 'intro',
            // Anticipation state
            anticipationActive: false,
            timeUntilNextBeat: 0,
            // Melody tracking
            currentNote: null,       // Current detected note
            melodyPatterns: {        // Detected melody patterns
                ascending: false,
                descending: false,
                alternating: false,
                repeating: false,
                stepwise: false,
                leaping: false
            },
            hasMelody: false,        // Whether a melody is detected
            melodyChangeRate: 0,     // Rate of melody note changes
            lastNoteChangeTime: 0,   // Time of last note change
            noteChangeDetected: false // Visual indicator for note change
        };

        // Beat anticipation state
        this._anticipationState = {
            isActive: false,
            patternQueued: false,
            queuedPattern: null,
            queuedIntensity: 0,
            queuedSpeed: 1.0,
            anticipationEffects: []
        };

        console.log("[BossController] Created");
    }

    /**
     * Initialize the boss controller
     * @returns {Boolean} Success
     */
    async init() {
        console.log("[BossController] init() method called");
        console.log("[BossController] Boss object:", this.boss ? "exists" : "null");
        if (this.boss) {
            console.log("[BossController] Boss position:", this.boss.position ? "exists" : "null");
            if (this.boss.position) {
                console.log(`[BossController] Boss position: ${this.boss.position.x.toFixed(2)}, ${this.boss.position.y.toFixed(2)}, ${this.boss.position.z.toFixed(2)}`);
            }
        }
        try {
            // Check if audio manager and boss are available
            if (!this.audioManager || !this.boss) {
                console.error("[BossController] Cannot initialize: audioManager or boss not available");
                return false;
            }

            // Load boss music if not already loaded
            const bossMusic = await this._loadBossMusic();
            if (!bossMusic) {
                console.error("[BossController] Failed to load boss music");
                return false;
            }

            // Initialize music analyzer with boss music player
            console.log("[BossController] Initializing music analyzer...");
            const initResult = this.musicAnalyzer.init(bossMusic);
            if (!initResult) {
                console.error("[BossController] Failed to initialize music analyzer");
                return false;
            }
            console.log("[BossController] Music analyzer initialized successfully");

            // Set up callbacks with enhanced beat anticipation and melody detection
            this.musicAnalyzer.setIntensityCallback(this._onIntensityChange.bind(this));
            this.musicAnalyzer.setBeatCallback(this._onBeatDetected.bind(this));
            this.musicAnalyzer.setBeatAnticipationCallback(this._onBeatAnticipated.bind(this));
            this.musicAnalyzer.setNoteChangeCallback(this._onNoteChange.bind(this));

            console.log("[BossController] Initialized successfully");
            return true;
        } catch (error) {
            console.error("[BossController] Error initializing:", error);
            return false;
        }
    }

    /**
     * Start the boss battle
     * @returns {Boolean} Success
     */
    async start() {
        console.log("[BossController] start() method called");
        if (this.active) {
            console.warn("[BossController] Already active");
            return true;
        }

        console.log("[BossController] Current state - Timeline name:", this.currentTimelineName,
                  "Music analyzer:", this.musicAnalyzer ? "Initialized" : "Not initialized",
                  "Pattern manager:", this.patternManager ? "Initialized" : "Not initialized");

        // Ensure pattern manager has a valid boss reference
        if (this.patternManager && !this.patternManager.boss && this.boss) {
            console.log("[BossController] Updating pattern manager boss reference");
            this.patternManager.boss = this.boss;
        }

        // Ensure timeline shaker has a valid dungeonHandler reference
        if (this.timelineShaker && this.dungeonHandler) {
            console.log("[BossController] Updating timeline shaker dungeonHandler reference");
            this.timelineShaker.setDungeonHandler(this.dungeonHandler);
        }

        try {
            console.log("[BossController] Starting boss battle...");

            // Force Tone.js to start if it hasn't already
            if (typeof Tone !== 'undefined' && Tone.context.state !== 'running') {
                console.log("[BossController] Starting Tone.js context...");
                await Tone.start();
                console.log("[BossController] Tone.js context started:", Tone.context.state);
            }

            // Start music analyzer
            console.log("[BossController] Starting music analyzer...");
            const analyzerStarted = this.musicAnalyzer.start();
            if (!analyzerStarted) {
                console.error("[BossController] Failed to start music analyzer");
                return false;
            }

            // Start boss music
            console.log("[BossController] Starting boss music...");
            const musicStarted = await this._startBossMusic();
            if (!musicStarted) {
                console.error("[BossController] Failed to start boss music");
                this.musicAnalyzer.stop();
                return false;
            }

            this.active = true;
            console.log("[BossController] Boss battle started successfully");

            // Force a pattern to show it's working
            setTimeout(() => {
                console.log("[BossController] Triggering initial pattern...");
                const initialPattern = "circle_ripple";

                // Ensure pattern manager has a valid boss reference
                if (this.patternManager && !this.patternManager.boss && this.boss) {
                    console.log("[BossController] Updating pattern manager boss reference before triggering pattern");
                    this.patternManager.boss = this.boss;
                }

                this.patternManager.triggerPattern(initialPattern, 0.5);

                // Update debug info with the initial pattern
                if (this.options.debugMode) {
                    this.debugInfo.lastPattern = initialPattern;
                }

                // Force another pattern after a short delay
                setTimeout(() => {
                    console.log("[BossController] Triggering second pattern...");
                    const secondPattern = "petal_spread";
                    this.patternManager.triggerPattern(secondPattern, 0.7);
                }, 2000);
            }, 1000);

            return true;
        } catch (error) {
            console.error("[BossController] Error starting boss battle:", error);
            return false;
        }
    }

    /**
     * Stop the boss battle
     */
    stop() {
        if (!this.active) {
            return;
        }

        // Stop music analyzer
        this.musicAnalyzer.stop();

        // Stop boss music
        this._stopBossMusic();

        this.active = false;
        console.log("[BossController] Boss battle stopped");
    }

    /**
     * Update the boss controller
     * @param {Number} deltaTime - Time since last update
     */
    update(deltaTime) {
        if (!this.active) {
            return;
        }

        // Update timeline with current music time if available
        if (this.musicAnalyzer.getCurrentTime) {
            const musicTime = this.musicAnalyzer.getCurrentTime();
            this.musicTimeline.update(musicTime);

            // Update custom timeline shaker with current music time
            // This will trigger screen shakes only at specific timestamps
            if (this.timelineShaker && this.options.screenShake) {
                this.timelineShaker.update(musicTime);
            }

            // Get timeline info
            const timelineInfo = this.musicTimeline.getTimelineInfo();

            // Log timeline info every 5 seconds
            if (Math.floor(musicTime) % 5 === 0 && Math.floor(musicTime) !== this._lastLoggedTime) {
                this._lastLoggedTime = Math.floor(musicTime);
                console.log(`[BossController] Timeline at ${musicTime.toFixed(1)}s: ` +
                           `Current pattern: ${timelineInfo.currentPattern || 'None'}, ` +
                           `Current projectile: ${timelineInfo.currentProjectileType || 'None'}, ` +
                           `Current section: ${timelineInfo.currentEntry?.description || 'None'}`);
            }

            // Update debug info with timeline information
            if (this.options.debugMode) {
                this.debugInfo.timelineProgress = timelineInfo.progress;
                this.debugInfo.timelineCurrentTime = timelineInfo.currentTime;
                if (timelineInfo.currentEntry) {
                    this.debugInfo.timelineCurrentSection = timelineInfo.currentEntry.description || 'Unknown';
                }
                this.debugInfo.timelineCurrentPattern = timelineInfo.currentPattern || 'None';
                this.debugInfo.timelineCurrentProjectile = timelineInfo.currentProjectileType || 'None';
            }
        }

        // Extract enhanced musical features for pattern manager
        const musicalFeatures = {
            // Basic musical features
            isArpeggio: this.debugInfo.isArpeggio,
            isBuildUp: this.debugInfo.isBuildUp,
            isDrop: this.debugInfo.isDrop,
            isTransition: this.debugInfo.isTransition,
            // Enhanced note pattern detection
            noteChangeRate: this.debugInfo.noteChangeRate,
            noteChangeSpeed: this.debugInfo.noteChangeSpeed,
            patternComplexity: this.debugInfo.patternComplexity,
            isStaccato: this.debugInfo.isStaccato,
            isLegato: this.debugInfo.isLegato,
            isTremolo: this.debugInfo.isTremolo,
            isTrill: this.debugInfo.isTrill
        };

        // Update pattern manager with current intensity and musical features
        this.patternManager.update(deltaTime, this.currentIntensity, musicalFeatures);

        // Update debug info with the last pattern type
        if (this.options.debugMode && this.patternManager.lastPatternType) {
            this.debugInfo.lastPattern = this.patternManager.lastPatternType;
        }

        // Update visual effects
        this._updateVisualEffects(deltaTime);

        // Update boss behavior based on music
        this._updateBossBehavior(deltaTime);
    }

    /**
     * Handle intensity change from music analyzer
     * @param {Number} intensity - Current music intensity (0-100)
     * @private
     */
    _onIntensityChange(intensity) {
        this.currentIntensity = intensity;

        // Update debug info
        if (this.options.debugMode) {
            const musicData = this.musicAnalyzer.getIntensity();

            // Update basic intensity values
            this.debugInfo.intensity = intensity;
            this.debugInfo.subBassIntensity = musicData.subBass * 100;
            this.debugInfo.bassIntensity = musicData.bass * 100;
            this.debugInfo.lowMidIntensity = musicData.lowMid * 100;
            this.debugInfo.midrangeIntensity = musicData.midrange * 100;
            this.debugInfo.upperMidIntensity = musicData.upperMid * 100;
            this.debugInfo.trebleIntensity = musicData.treble * 100;
            this.debugInfo.highFreqIntensity = musicData.highFreq * 100;

            // Update musical feature detection
            this.debugInfo.isArpeggio = musicData.isArpeggio;
            this.debugInfo.isBuildUp = musicData.isBuildUp;
            this.debugInfo.isDrop = musicData.isDrop;
            this.debugInfo.isTransition = musicData.isTransition;

            // Update enhanced note pattern detection
            this.debugInfo.noteChangeRate = musicData.noteChangeRate;
            this.debugInfo.noteChangeSpeed = musicData.noteChangeSpeed;
            this.debugInfo.patternComplexity = musicData.patternComplexity;
            this.debugInfo.isStaccato = musicData.isStaccato;
            this.debugInfo.isLegato = musicData.isLegato;
            this.debugInfo.isTremolo = musicData.isTremolo;
            this.debugInfo.isTrill = musicData.isTrill;

            // Update tempo information
            this.debugInfo.tempo = musicData.tempo;
            this.debugInfo.beatPhase = musicData.beatPhase;

            // Update section information
            this.debugInfo.currentSection = musicData.currentSection;

            // Log debug info update
            console.log('[BossController] Updated intensity:', intensity, 'Musical features:', {
                isArpeggio: this.debugInfo.isArpeggio,
                isBuildUp: this.debugInfo.isBuildUp,
                isDrop: this.debugInfo.isDrop,
                isTransition: this.debugInfo.isTransition
            });

            // Trigger special patterns based on musical features
            this._reactToMusicalFeatures();
        }
    }

    /**
     * React to detected musical features by triggering appropriate patterns
     * @private
     */
    _reactToMusicalFeatures() {
        // Skip if not active
        if (!this.active) return;

        // Create a musical features object to pass to pattern manager
        const musicalFeatures = {
            isArpeggio: this.debugInfo.isArpeggio,
            isBuildUp: this.debugInfo.isBuildUp,
            isDrop: this.debugInfo.isDrop,
            isTransition: this.debugInfo.isTransition,
            noteChangeRate: this.debugInfo.noteChangeRate,
            noteChangeSpeed: this.debugInfo.noteChangeSpeed,
            patternComplexity: this.debugInfo.patternComplexity,
            isStaccato: this.debugInfo.isStaccato,
            isLegato: this.debugInfo.isLegato,
            isTremolo: this.debugInfo.isTremolo,
            isTrill: this.debugInfo.isTrill
        };

        // EXTREMELY REDUCED: Only react to significant musical events
        // Add randomization to further reduce pattern triggering
        const now = Date.now();

        // Store last reaction time if not already set
        if (!this._lastReactionTime) {
            this._lastReactionTime = now;
        }

        // BALANCED: Moderate time between reactions (1.5 seconds)
        const minTimeBetweenReactions = 1500; // 1.5 seconds (was 3 seconds)
        if (now - this._lastReactionTime < minTimeBetweenReactions) {
            return; // Skip reaction if too soon
        }

        // Only react to very significant musical events (high thresholds)
        // and add randomization to further reduce frequency
        let shouldReact = false;
        let patternIntensity = 0.5;
        let patternName = '';
        let speedMultiplier = 1.0;

        // ENHANCED: Detect fast continuous parts for chain lightning with improved sensitivity
        // Lowered thresholds to detect fast notes more easily
        const isFastContinuous = this.debugInfo.noteChangeRate > 0.5 && this.debugInfo.noteChangeSpeed > 0.8;
        const isVeryFastContinuous = this.debugInfo.noteChangeRate > 0.6 && this.debugInfo.noteChangeSpeed > 1.5;
        const isExtremeFastContinuous = this.debugInfo.noteChangeRate > 0.7 && this.debugInfo.noteChangeSpeed > 2.5;

        // React to extremely fast continuous parts with rapid fire pattern and maximum speed
        if (isExtremeFastContinuous && Math.random() < 0.95) {
            // Almost guaranteed to trigger for extremely fast continuous parts
            // Maximum speed multiplier for extremely fast notes (up to 8.0x)
            speedMultiplier = 2.5 + (this.debugInfo.noteChangeSpeed * 1.8); // 2.5-10.0 range
            patternIntensity = 0.9 + (this.debugInfo.noteChangeSpeed * 0.1); // 0.9-1.0 range
            patternName = 'rapid_fire'; // Use the new rapid fire pattern for extreme speeds
            shouldReact = true;
            console.log(`[BossController] Reacting to EXTREME fast continuous part with rapid fire (speed: ${speedMultiplier.toFixed(2)}x)`);
        }
        // React to very fast continuous parts with lightning chains and significantly increased speed
        else if (isVeryFastContinuous && Math.random() < 0.85) {
            // Much higher chance to trigger for very fast continuous parts
            // Dramatically increase speed multiplier for very fast notes
            speedMultiplier = 1.8 + (this.debugInfo.noteChangeSpeed * 1.2); // 1.8-5.0 range
            patternIntensity = 0.7 + (this.debugInfo.noteChangeSpeed * 0.2); // 0.7-1.0 range
            patternName = 'lightning_chain';
            shouldReact = true;
            console.log(`[BossController] Reacting to VERY fast continuous part with lightning chain (speed: ${speedMultiplier.toFixed(2)}x)`);
        }
        // React to fast continuous parts with lightning chains
        else if (isFastContinuous && Math.random() < 0.8) {
            // Higher chance to trigger for fast continuous parts
            speedMultiplier = 1.2 + (this.debugInfo.noteChangeSpeed * 1.0); // 1.2-4.0 range
            patternIntensity = 0.6 + (this.debugInfo.noteChangeSpeed * 0.2); // 0.6-0.9 range
            patternName = 'lightning_chain';
            shouldReact = true;
            console.log(`[BossController] Reacting to fast continuous part with lightning chain (speed: ${speedMultiplier.toFixed(2)}x)`);
        }
        // React to moderately fast note changes with appropriate patterns
        else if (this.debugInfo.noteChangeRate > 0.6 && Math.random() < 0.5) {
            // Calculate speed multiplier based on note change speed
            speedMultiplier = 1.0 + (this.debugInfo.noteChangeSpeed * 0.5); // 1.0-2.0 range
            patternIntensity = 0.5 + (this.debugInfo.noteChangeSpeed * 0.1); // 0.5-0.7 range

            if (this.debugInfo.isTrill) {
                patternName = 'spiral_wave';
                shouldReact = true;
            }
            else if (this.debugInfo.isTremolo) {
                patternName = 'circle_ripple';
                shouldReact = true;
            }
            else if (this.debugInfo.isStaccato) {
                patternName = 'laser_grid';
                shouldReact = true;
            }
        }
        // ENHANCED: React to arpeggios with chain lightning for fast arpeggios
        else if (this.debugInfo.isArpeggio && this.debugInfo.noteChangeRate > 0.5 && Math.random() < 0.6) {
            patternIntensity = 0.6 + (this.debugInfo.noteChangeSpeed * 0.1); // 0.6-0.8 range
            speedMultiplier = 1.0 + (this.debugInfo.noteChangeSpeed * 0.5); // 1.0-2.0 range

            // Use lightning_chain for fast arpeggios, otherwise use spiral_wave or laser_grid
            if (this.debugInfo.noteChangeSpeed > 1.0) {
                patternName = 'lightning_chain'; // Chain lightning for fast arpeggios
                console.log(`[BossController] Fast arpeggio detected, using lightning chain`);
            } else {
                // Randomly choose between spiral_wave and laser_grid for slower arpeggios
                patternName = Math.random() < 0.5 ? 'spiral_wave' : 'laser_grid';
            }
            shouldReact = true;
        }
        // BALANCED: React to drops (highest priority)
        else if (this.debugInfo.isDrop && Math.random() < 0.8) {
            // Fast speed for drops
            speedMultiplier = 1.5;
            patternIntensity = 0.8;
            patternName = 'hellburst';
            shouldReact = true;
        }
        // BALANCED: React to build-ups
        else if (this.debugInfo.isBuildUp && this.debugInfo.intensity > 50 && Math.random() < 0.6) {
            // Gradually increasing speed for build-ups
            speedMultiplier = 1.0 + (this.debugInfo.intensity / 200); // 1.0-1.5 range
            patternIntensity = 0.6;
            patternName = 'circle_ripple';
            shouldReact = true;
        }
        // BALANCED: React to transitions
        else if (this.debugInfo.isTransition && Math.random() < 0.5) {
            // Variable speed for transitions
            speedMultiplier = 1.2;
            patternIntensity = 0.6;
            patternName = 'boomerang_arcs';
            shouldReact = true;
        }
        // ADDED: Periodically trigger laser_grid patterns to ensure white projectiles appear
        else if (Math.random() < 0.3) {
            // This ensures laser_grid patterns appear regularly regardless of musical features
            speedMultiplier = 1.0 + (Math.random() * 0.5); // 1.0-1.5 range
            patternIntensity = 0.6;
            patternName = 'laser_grid';
            shouldReact = true;
            console.log('[BossController] Periodically triggering laser grid pattern');
        }

        // Trigger pattern if we decided to react
        if (shouldReact) {
            console.log(`[BossController] Reacting to musical feature with ${patternName} pattern (speed: ${speedMultiplier.toFixed(2)}x)`);
            this.patternManager.triggerPattern(patternName, patternIntensity, speedMultiplier);
            this._lastReactionTime = now; // Update last reaction time
        }
    }

    /**
     * Handle beat anticipation from music analyzer
     * @param {Number} timeUntilBeat - Time until the beat in ms
     * @param {Number} confidence - Confidence in the prediction (0-1)
     * @private
     */
    _onBeatAnticipated(timeUntilBeat, confidence) {
        // Update debug info
        if (this.options.debugMode) {
            this.debugInfo.beatAnticipated = true;
            this.debugInfo.timeUntilNextBeat = timeUntilBeat;
            this.debugInfo.beatConfidence = confidence;
            console.log(`[BossController] Beat anticipated in ${timeUntilBeat}ms with confidence ${confidence.toFixed(2)}`);

            // Reset after a short delay
            setTimeout(() => {
                this.debugInfo.beatAnticipated = false;
            }, 100);
        }

        // Only react if confidence is high enough and we're not already in anticipation
        if (confidence < 0.6 || this._anticipationState.isActive) {
            return;
        }

        // Start anticipation effects
        this._anticipationState.isActive = true;

        // Queue a pattern to be triggered exactly on the beat
        if (!this._anticipationState.patternQueued && timeUntilBeat > 30 && timeUntilBeat < 200) {
            // Select pattern based on current musical features
            const { patternName, intensity, speedMultiplier } = this._selectPatternForAnticipation();

            // Queue the pattern
            this._anticipationState.patternQueued = true;
            this._anticipationState.queuedPattern = patternName;
            this._anticipationState.queuedIntensity = intensity;
            this._anticipationState.queuedSpeed = speedMultiplier;

            console.log(`[BossController] Queued pattern ${patternName} to trigger in ${timeUntilBeat}ms`);

            // Schedule the pattern to trigger exactly on the beat
            setTimeout(() => {
                if (this._anticipationState.patternQueued) {
                    console.log(`[BossController] Triggering anticipated pattern: ${this._anticipationState.queuedPattern}`);
                    this.patternManager.triggerPattern(
                        this._anticipationState.queuedPattern,
                        this._anticipationState.queuedIntensity,
                        this._anticipationState.queuedSpeed
                    );

                    // Reset queue
                    this._anticipationState.patternQueued = false;
                    this._anticipationState.queuedPattern = null;
                }
            }, timeUntilBeat);

            // Add visual anticipation effects
            this._addAnticipationEffects(timeUntilBeat);
        }
    }

    /**
     * Add visual anticipation effects that build up to the beat
     * @param {Number} timeUntilBeat - Time until the beat in ms
     * @private
     */
    _addAnticipationEffects(timeUntilBeat) {
        // Boss glow effect that intensifies as the beat approaches
        if (this.boss && this.boss.userData) {
            // Start with subtle glow
            if (this.boss.userData.glowIntensity !== undefined) {
                this.boss.userData.glowIntensity = 0.3;
            }

            // Schedule glow intensification
            const glowSteps = Math.min(3, Math.floor(timeUntilBeat / 30));
            for (let i = 1; i <= glowSteps; i++) {
                const stepTime = timeUntilBeat * (i / glowSteps);
                const glowIntensity = 0.3 + (i / glowSteps) * 0.7; // 0.3 to 1.0

                setTimeout(() => {
                    if (this.boss && this.boss.userData && this.boss.userData.glowIntensity !== undefined) {
                        this.boss.userData.glowIntensity = glowIntensity;
                    }
                }, stepTime);
            }

            // Reset glow after the beat
            setTimeout(() => {
                if (this.boss && this.boss.userData && this.boss.userData.glowIntensity !== undefined) {
                    this.boss.userData.glowIntensity = 0.2;
                }
                this._anticipationState.isActive = false;
            }, timeUntilBeat + 100);
        }

        // Add screen effects that build up to the beat
        if (this.options.visualEffects) {
            // Subtle vignette effect
            this.visualEffects.vignetteIntensity = 0.2;

            // Increase vignette as beat approaches
            setTimeout(() => {
                this.visualEffects.vignetteIntensity = 0.4;
            }, timeUntilBeat * 0.5);

            // Reset after beat
            setTimeout(() => {
                this.visualEffects.vignetteIntensity = 0;
            }, timeUntilBeat + 100);
        }
    }

    /**
     * Select an appropriate pattern based on current musical features
     * @returns {Object} Pattern selection { patternName, intensity, speedMultiplier }
     * @private
     */
    _selectPatternForAnticipation() {
        // Base intensity on current music intensity
        const baseIntensity = this.currentIntensity / 100;
        let patternIntensity = 0.4 + (baseIntensity * 0.6); // 0.4-1.0 range

        // Base speed on note change speed with enhanced sensitivity to fast notes
        const noteChangeSpeed = this.debugInfo.noteChangeSpeed || 1.0;
        // Increase the speed multiplier range significantly for fast notes
        let speedMultiplier = noteChangeSpeed < 1.2 ?
            0.9 + (noteChangeSpeed * 0.5) : // 0.9-1.5 range for normal speeds
            1.5 + (noteChangeSpeed - 1.2) * 1.5; // 1.5-5.7 range for fast notes

        // Select pattern based on musical features
        let patternName = '';

        // For arpeggios, use spiral_wave, lightning_chain, or rapid_fire based on speed
        if (this.debugInfo.isArpeggio) {
            if (this.debugInfo.noteChangeSpeed > 2.5) {
                patternName = 'rapid_fire'; // Use rapid fire for extremely fast arpeggios
                speedMultiplier *= 1.5; // Even faster for extreme arpeggios
            } else if (this.debugInfo.noteChangeSpeed > 1.5) {
                patternName = 'lightning_chain'; // Use lightning chain for very fast arpeggios
                speedMultiplier *= 1.3; // Faster for very fast arpeggios
            } else {
                patternName = 'spiral_wave'; // Use spiral wave for normal arpeggios
                speedMultiplier *= 1.2; // Faster for arpeggios
            }
        }
        // For build-ups, use circle_ripple
        else if (this.debugInfo.isBuildUp) {
            patternName = 'circle_ripple';
            // Gradually increase intensity for build-ups
            patternIntensity = Math.min(1.0, patternIntensity * 1.2);
        }
        // For drops, use hellburst
        else if (this.debugInfo.isDrop) {
            patternName = 'hellburst';
            speedMultiplier *= 1.3; // Faster for drops
            patternIntensity = Math.min(1.0, patternIntensity * 1.3); // More intense
        }
        // For transitions, use laser_grid
        else if (this.debugInfo.isTransition) {
            patternName = 'laser_grid';
        }
        // Otherwise select based on intensity
        else {
            if (this.currentIntensity < 30) {
                patternName = 'petal_spread';
            } else if (this.currentIntensity < 60) {
                patternName = Math.random() < 0.6 ? 'spiral_wave' : 'boomerang_arcs';
            } else {
                patternName = Math.random() < 0.7 ? 'laser_grid' : 'inhale_exhale';
            }
        }

        return { patternName, intensity: patternIntensity, speedMultiplier };
    }

    /**
     * Handle beat detection from music analyzer
     * @param {Number} strength - Beat strength
     * @private
     */
    _onBeatDetected(strength) {
        this.lastBeatTime = Date.now();

        // Update debug info
        if (this.options.debugMode) {
            this.debugInfo.beatDetected = true;
            console.log('[BossController] Beat detected, strength:', strength);

            setTimeout(() => {
                this.debugInfo.beatDetected = false;
                console.log('[BossController] Beat detection reset');
            }, 100);
        }

        // Apply immediate visual effects on beat
        this._applyBeatVisualEffects(strength);

        // If we're using beat anticipation, we may already have queued a pattern
        // Only trigger a new pattern if we didn't anticipate this beat
        if (this._anticipationState.patternQueued) {
            return;
        }

        // BALANCED: React to moderately strong beats with randomization
        // Reduced threshold to make it more responsive
        if (strength > 1.6 && Math.random() < 0.6) {
            console.log('[BossController] Strong beat detected, triggering pattern');

            // Calculate intensity and speed based on beat strength
            const patternIntensity = 0.5 + (strength * 0.1); // 0.5-0.8 range

            // Increase speed multiplier and consider current note change speed
            const baseSpeedMultiplier = 1.0 + (strength * 0.4); // 1.0-2.2 range (increased for more responsiveness)
            // Enhanced note speed bonus with more aggressive scaling
            const noteSpeedBonus = this.debugInfo.noteChangeSpeed > 1.0 ?
                (this.debugInfo.noteChangeSpeed - 1.0) * 1.2 : 0; // Larger bonus starting at lower threshold
            const speedMultiplier = baseSpeedMultiplier + noteSpeedBonus;

            // Choose a pattern based on timeline or defaults
            let patternName = '';

            // ALWAYS use the timeline pattern if available
            if (this.currentTimelineName && this.musicTimeline.currentPattern) {
                patternName = this.musicTimeline.currentPattern;
                console.log(`[BossController] Using timeline pattern ${patternName} for beat response`);
            } else {
                // Only use these fallbacks if no timeline is active
                // For extremely strong beats with fast notes, use rapid_fire
                if (strength > 2.5 && this.debugInfo.noteChangeSpeed > 2.0) {
                    patternName = 'rapid_fire';
                    console.log('[BossController] Extremely strong beat with fast notes detected, using rapid fire pattern');
                }
                // For very strong beats, use lightning_chain to create continuous chains
                else if (strength > 2.2) {
                    patternName = 'lightning_chain';
                    console.log('[BossController] Very strong beat detected, using lightning chain pattern');
                }
                // 40% chance of laser_grid for medium-strong beats
                else if (Math.random() < 0.4) {
                    patternName = 'laser_grid';
                    console.log('[BossController] Medium-strong beat detected, using laser_grid pattern');
                }
                // Otherwise choose based on intensity
                else if (this.currentIntensity < 30) {
                    patternName = 'petal_spread';
                }
                else if (this.currentIntensity < 60) {
                    patternName = 'spiral_wave';
                }
                else {
                    patternName = 'inhale_exhale';
                }
            }

            // Only trigger if we have a pattern name
            if (patternName) {
                console.log(`[BossController] Beat-synced pattern: ${patternName} (speed: ${speedMultiplier.toFixed(2)}x)`);

                // If we have a timeline loaded and a projectile type, use it
                if (this.currentTimelineName && this.musicTimeline.currentProjectileType) {
                    this.patternManager.triggerPattern(patternName, patternIntensity, speedMultiplier, this.musicTimeline.currentProjectileType);
                } else if (!this.currentTimelineName) {
                    // Only use non-timeline patterns if no timeline is active
                    this.patternManager.triggerPattern(patternName, patternIntensity, speedMultiplier);
                }
            }

            // Update last beat reaction time
            if (!this._lastBeatReactionTime) {
                this._lastBeatReactionTime = Date.now();
            }
        }
    }

    /**
     * Handle note changes from music analyzer
     * @param {Object} noteInfo - Information about the note change
     * @private
     */
    _onNoteChange(noteInfo) {
        // Update debug info
        if (this.options.debugMode) {
            this.debugInfo.currentNote = noteInfo.note;
            this.debugInfo.melodyPatterns = noteInfo.patterns;
            this.debugInfo.melodyChangeRate = noteInfo.changeRate;
            this.debugInfo.noteChangeDetected = true;
            this.debugInfo.lastNoteChangeTime = Date.now();

            // Reset note change indicator after a short delay
            setTimeout(() => {
                this.debugInfo.noteChangeDetected = false;
            }, 100);

            console.log(`[BossController] Note change detected: ${noteInfo.note.name}${noteInfo.note.octave}`);
        }

        // Track consecutive fast note changes for combo system
        const now = Date.now();
        if (!this._lastNoteChangeTime) {
            this._lastNoteChangeTime = now;
            this._consecutiveFastNotes = 0;
            return;
        }

        // Calculate time since last note change
        const timeSinceLastNote = now - this._lastNoteChangeTime;
        this._lastNoteChangeTime = now;

        // If notes are changing very quickly, increment combo counter
        if (timeSinceLastNote < 300) { // Less than 300ms between notes is considered fast
            this._consecutiveFastNotes++;
            console.log(`[BossController] Fast note detected! Combo: ${this._consecutiveFastNotes}`);

            // After 3 consecutive fast notes, trigger a special burst
            if (this._consecutiveFastNotes >= 3 && this._consecutiveFastNotes % 3 === 0) {
                this._triggerFastNoteCombo();
            }
        } else {
            // Reset combo if notes aren't changing quickly
            this._consecutiveFastNotes = 0;
        }

        // Only react to note changes if we're not already reacting to a beat
        // or if it's been a while since the last beat
        const timeSinceLastBeat = now - this.lastBeatTime;

        // Skip if we recently reacted to a beat
        if (timeSinceLastBeat < 200) {
            return;
        }

        // Skip if we recently triggered a pattern
        if (this._lastReactionTime && now - this._lastReactionTime < 500) {
            return;
        }

        // Determine if we should react to this note change
        let shouldReact = false;
        let patternName = '';
        let patternIntensity = 0.5;
        let speedMultiplier = 1.0;

        // React based on melody patterns
        if (noteInfo.patterns.ascending && Math.random() < 0.7) {
            // Ascending melody - use spiral wave or circle ripple
            patternName = Math.random() < 0.6 ? 'spiral_wave' : 'circle_ripple';
            patternIntensity = 0.6;
            speedMultiplier = 1.2;
            shouldReact = true;
            console.log('[BossController] Reacting to ascending melody');
        }
        else if (noteInfo.patterns.descending && Math.random() < 0.7) {
            // Descending melody - use inhale_exhale or boomerang_arcs
            patternName = Math.random() < 0.5 ? 'inhale_exhale' : 'boomerang_arcs';
            patternIntensity = 0.6;
            speedMultiplier = 1.1;
            shouldReact = true;
            console.log('[BossController] Reacting to descending melody');
        }
        else if (noteInfo.patterns.alternating && Math.random() < 0.8) {
            // Alternating melody - use laser_grid
            patternName = 'laser_grid';
            patternIntensity = 0.7;
            speedMultiplier = 1.3;
            shouldReact = true;
            console.log('[BossController] Reacting to alternating melody');
        }
        else if (noteInfo.patterns.leaping && Math.random() < 0.8) {
            // Leaping melody (large intervals) - use lightning_chain
            patternName = 'lightning_chain';
            patternIntensity = 0.7;
            speedMultiplier = 1.4;
            shouldReact = true;
            console.log('[BossController] Reacting to leaping melody');
        }
        else if (noteInfo.patterns.stepwise && Math.random() < 0.6) {
            // Stepwise melody (small intervals) - use petal_spread
            patternName = 'petal_spread';
            patternIntensity = 0.5;
            speedMultiplier = 1.0;
            shouldReact = true;
            console.log('[BossController] Reacting to stepwise melody');
        }
        else if (noteInfo.patterns.repeating && Math.random() < 0.5) {
            // Repeating notes - use hellburst
            patternName = 'hellburst';
            patternIntensity = 0.6;
            speedMultiplier = 1.2;
            shouldReact = true;
            console.log('[BossController] Reacting to repeating notes');
        }

        // Trigger pattern if we decided to react
        if (shouldReact) {
            // ALWAYS use the timeline pattern and projectile if available
            if (this.currentTimelineName && this.musicTimeline.currentPattern) {
                patternName = this.musicTimeline.currentPattern;
                console.log(`[BossController] Using timeline pattern ${patternName} for melody response`);

                // If we have a timeline loaded and a projectile type, use it
                if (this.musicTimeline.currentProjectileType) {
                    console.log(`[BossController] Triggering pattern ${patternName} in response to melody`);
                    this.patternManager.triggerPattern(patternName, patternIntensity, speedMultiplier, this.musicTimeline.currentProjectileType);
                } else {
                    console.log(`[BossController] Triggering pattern ${patternName} in response to melody`);
                    this.patternManager.triggerPattern(patternName, patternIntensity, speedMultiplier);
                }
            } else if (!this.currentTimelineName) {
                // Only use non-timeline patterns if no timeline is active
                console.log(`[BossController] Triggering pattern ${patternName} in response to melody`);
                this.patternManager.triggerPattern(patternName, patternIntensity, speedMultiplier);
            }

            this._lastReactionTime = now;

            // Apply visual effects
            this._applyNoteChangeVisualEffects(noteInfo);
        }
    }

    /**
     * Trigger a special combo attack after consecutive fast notes
     * @private
     */
    _triggerFastNoteCombo() {
        console.log(`[BossController] Triggering fast note combo! (${this._consecutiveFastNotes} consecutive fast notes)`);

        // Calculate combo level (1-3) based on consecutive fast notes
        const comboLevel = Math.min(3, Math.floor(this._consecutiveFastNotes / 3));

        // Calculate intensity and speed based on combo level
        const comboIntensity = 0.7 + (comboLevel * 0.1); // 0.7-1.0 range
        const comboSpeed = 1.5 + (comboLevel * 0.5); // 1.5-3.0 range

        // Select pattern based on combo level
        let patternName;
        if (comboLevel === 3) {
            // Level 3 combo (9+ consecutive fast notes) - use rapid fire
            patternName = 'rapid_fire';
        } else if (comboLevel === 2) {
            // Level 2 combo (6+ consecutive fast notes) - use lightning chain
            patternName = 'lightning_chain';
        } else {
            // Level 1 combo (3+ consecutive fast notes) - use hellburst
            patternName = 'hellburst';
        }

        // Trigger the pattern with combo parameters
        this.patternManager.triggerPattern(patternName, comboIntensity, comboSpeed);

        // Apply visual effects
        if (this.options.visualEffects) {
            // Apply screen shake occasionally for combos
            const now = Date.now();
            if (!this._lastScreenShakeTime) {
                this._lastScreenShakeTime = 0;
            }

            // Apply screen shake with moderate cooldown
            const shakeCooldown = 2000; // 2 second cooldown (2x the original 1000ms)

            // Shake for all combo levels with 25% probability
            const shouldShake = Math.random() < 0.25;

            if (now - this._lastScreenShakeTime > shakeCooldown && shouldShake) {
                // Moderate screen shake based on combo level
                this.visualEffects.screenShakeAmount = 0.15 + (comboLevel * 0.05); // 0.15-0.3 range
                this._lastScreenShakeTime = now;

                if (this.options.debugMode) {
                    console.log(`[BossController] Combo camera shake triggered, level: ${comboLevel}`);
                }
            }

            // Background pulse based on combo level
            this.visualEffects.backgroundPulse = 0.3 + (comboLevel * 0.2); // 0.3-0.9 range

            // Create a light flash if available
            if (this.dungeonHandler && this.dungeonHandler.createLightFlash && this.boss) {
                const flashColors = [0x88ffff, 0xffaa00, 0xff3300]; // Blue, orange, red for levels 1-3
                const flashIntensity = 0.5 + (comboLevel * 0.2); // 0.5-1.1 range
                const flashDuration = 200 + (comboLevel * 100); // 200-500ms range

                this.dungeonHandler.createLightFlash(
                    this.boss.position,
                    flashIntensity,
                    flashColors[comboLevel - 1],
                    flashDuration
                );
            }
        }
    }

    /**
     * Apply visual effects for note changes
     * @param {Object} noteInfo - Information about the note change
     * @private
     */
    _applyNoteChangeVisualEffects(noteInfo) {
        // Only apply if visual effects are enabled
        if (!this.options.visualEffects) return;

        // Calculate effect strength based on note properties
        // Higher notes get stronger effects
        const normalizedPitch = Math.min(1.0, noteInfo.note.number / 84); // Normalize to 0-1 (C0 to C7)
        const effectStrength = 0.3 + (normalizedPitch * 0.4); // 0.3-0.7 range

        // Apply color effect based on note
        if (this.boss && this.boss.material && this.boss.material.emissive) {
            // Store original emissive if not already stored
            if (!this._originalEmissive) {
                this._originalEmissive = this.boss.material.emissive.clone();
            }

            // Map note to color (simple mapping using note number)
            const hue = (noteInfo.note.number % 12) / 12; // 0-1 based on note
            const saturation = 0.7 + (normalizedPitch * 0.3); // 0.7-1.0
            const lightness = 0.4 + (effectStrength * 0.3); // 0.4-0.7

            // Convert HSL to RGB (simplified conversion)
            const color = this._hslToRgb(hue, saturation, lightness);

            // Apply color to boss
            this.boss.material.emissive.setRGB(color.r, color.g, color.b);

            // Reset after a short delay
            setTimeout(() => {
                if (this.boss && this.boss.material && this.boss.material.emissive && this._originalEmissive) {
                    this.boss.material.emissive.copy(this._originalEmissive);
                }
            }, 300);
        }

        // Apply subtle screen effects
        this.visualEffects.backgroundPulse = effectStrength * 0.5;
    }

    /**
     * Convert HSL color to RGB
     * @param {Number} h - Hue (0-1)
     * @param {Number} s - Saturation (0-1)
     * @param {Number} l - Lightness (0-1)
     * @returns {Object} RGB color object
     * @private
     */
    _hslToRgb(h, s, l) {
        let r, g, b;

        if (s === 0) {
            r = g = b = l; // achromatic
        } else {
            const hue2rgb = (p, q, t) => {
                if (t < 0) t += 1;
                if (t > 1) t -= 1;
                if (t < 1/6) return p + (q - p) * 6 * t;
                if (t < 1/2) return q;
                if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                return p;
            };

            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;

            r = hue2rgb(p, q, h + 1/3);
            g = hue2rgb(p, q, h);
            b = hue2rgb(p, q, h - 1/3);
        }

        return { r, g, b };
    }

    /**
     * Apply visual effects on beat detection
     * @param {Number} strength - Beat strength
     * @private
     */
    _applyBeatVisualEffects(strength) {
        // Only apply if visual effects are enabled
        if (!this.options.visualEffects) return;

        // Add a cooldown for screen shake to prevent too frequent shaking
        const now = Date.now();
        if (!this._lastScreenShakeTime) {
            this._lastScreenShakeTime = 0;
        }

        // Scale effects based on beat strength
        const effectStrength = Math.min(1.0, strength / 2.5);

        // Background pulse effect
        this.visualEffects.backgroundPulse = effectStrength * 0.8;

        // Screen shake effect with moderate cooldown and threshold
        if (this.options.screenShake) {
            // Apply screen shake for moderately strong beats with reasonable cooldown
            const minStrengthForShake = 1.9; // Reduced threshold from 2.2 to 1.9
            const shakeCooldown = 1600; // 1.6 second cooldown (2x the original 800ms)

            // Add a probability check to trigger shakes 25% of the time
            const shouldShake = Math.random() < 0.25;

            if (strength > minStrengthForShake && now - this._lastScreenShakeTime > shakeCooldown && shouldShake) {
                // Use moderate shake amount
                const shakeStrength = Math.min(0.3, (strength - minStrengthForShake) / 3.0);
                this.visualEffects.screenShakeAmount = shakeStrength * 0.35; // Increased from 0.25 to 0.35
                this._lastScreenShakeTime = now;

                // Log when we actually trigger a shake (for debugging)
                if (this.options.debugMode) {
                    console.log(`[BossController] Camera shake triggered, strength: ${shakeStrength.toFixed(2)}`);
                }
            }
        }

        // Boss visual effects
        if (this.boss && this.boss.userData) {
            // Pulse boss size
            if (this.boss.userData.originalScale) {
                const pulseAmount = 1.0 + (effectStrength * 0.15); // 1.0-1.15x scale
                this.boss.scale.set(
                    this.boss.userData.originalScale.x * pulseAmount,
                    this.boss.userData.originalScale.y * pulseAmount,
                    this.boss.userData.originalScale.z * pulseAmount
                );

                // Reset scale after pulse
                setTimeout(() => {
                    if (this.boss && this.boss.userData && this.boss.userData.originalScale) {
                        this.boss.scale.set(
                            this.boss.userData.originalScale.x,
                            this.boss.userData.originalScale.y,
                            this.boss.userData.originalScale.z
                        );
                    }
                }, 200);
            }
        }
    }

    /**
     * Update visual effects based on music
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updateVisualEffects(deltaTime) {
        if (!this.options.visualEffects) {
            return;
        }

        // Decay background pulse
        this.visualEffects.backgroundPulse *= 0.9;

        // Decay screen shake at a moderate rate to reduce frequency but ensure visibility
        this.visualEffects.screenShakeAmount *= 0.85; // Adjusted decay rate from 0.7 to 0.85

        // Add a moderate maximum cap to screen shake amount
        this.visualEffects.screenShakeAmount = Math.min(this.visualEffects.screenShakeAmount, 0.25); // Increased from 0.2 to 0.25

        // Zero out very small shake values but with a lower threshold
        if (this.visualEffects.screenShakeAmount < 0.01) {
            this.visualEffects.screenShakeAmount = 0;
        }

        // Decay vignette effect
        if (this.visualEffects.vignetteIntensity > 0) {
            this.visualEffects.vignetteIntensity *= 0.95;
        }

        // Update beat phase based on tempo
        if (this.debugInfo && this.debugInfo.tempo > 0 && this.debugInfo.beatConfidence > 0.5) {
            // Calculate beat phase (0-1) based on current time and tempo
            const beatDuration = 60000 / this.debugInfo.tempo; // ms per beat
            const timeSinceLastBeat = Date.now() - this.lastBeatTime;
            this.debugInfo.beatPhase = (timeSinceLastBeat % beatDuration) / beatDuration;

            // Pulse subtle background effects based on beat phase
            const pulseFactor = Math.sin(this.debugInfo.beatPhase * Math.PI * 2) * 0.5 + 0.5;
            this.visualEffects.backgroundPulseSubtle = pulseFactor * 0.2;
        }

        // Apply visual effects
        this._applyVisualEffects();
    }

    /**
     * Apply visual effects to the game
     * @private
     */
    _applyVisualEffects() {
        // Apply background pulse (strong beats)
        if (this.visualEffects.backgroundPulse > 0.01) {
            // Apply to scene background if available
            if (window.scene && window.scene.background) {
                // Store original color if not already stored
                if (!this._originalBackgroundColor && window.scene.background.isColor) {
                    this._originalBackgroundColor = window.scene.background.clone();
                }

                // Brighten background based on pulse amount
                if (this._originalBackgroundColor) {
                    const pulseColor = this._originalBackgroundColor.clone();
                    const pulseFactor = 1.0 + this.visualEffects.backgroundPulse * 0.5;
                    pulseColor.r = Math.min(1, pulseColor.r * pulseFactor);
                    pulseColor.g = Math.min(1, pulseColor.g * pulseFactor);
                    pulseColor.b = Math.min(1, pulseColor.b * pulseFactor);
                    window.scene.background.copy(pulseColor);
                }
            }

            // Pulse ambient light based on beat
            if (this.dungeonHandler && this.dungeonHandler.ambientLight) {
                const baseIntensity = 0.4;
                const pulseAmount = this.visualEffects.backgroundPulse * 0.3;
                this.dungeonHandler.ambientLight.intensity = baseIntensity + pulseAmount;
            }
        }

        // Apply subtle background pulse (beat phase)
        if (this.visualEffects.backgroundPulseSubtle > 0.01) {
            // Apply subtle color shift to boss if available
            if (this.boss && this.boss.material && this.boss.material.emissive) {
                // Store original emissive if not already stored
                if (!this._originalEmissive) {
                    this._originalEmissive = this.boss.material.emissive.clone();
                }

                // Pulse emissive based on beat phase
                const emissiveFactor = 0.1 + this.visualEffects.backgroundPulseSubtle * 0.2;
                this.boss.material.emissive.set(emissiveFactor, emissiveFactor * 0.5, emissiveFactor * 0.8);
            }
        }

        // Apply screen shake with reduced intensity and smoother motion
        // Remove the frame-by-frame probability check to ensure shakes are visible
        if (this.options.screenShake && this.visualEffects.screenShakeAmount > 0.01) {
            // Apply to camera if available
            const camera = this.dungeonHandler && this.dungeonHandler.camera ?
                this.dungeonHandler.camera : window.camera;

            if (camera) {
                // Store original position if not already stored
                if (!this._originalCameraPosition) {
                    this._originalCameraPosition = camera.position.clone();
                }

                // Use a smoother shake algorithm with moderate intensity
                // Increase the multiplier to make shakes more noticeable
                const shakeAmount = this.visualEffects.screenShakeAmount * 0.15; // Increased from 0.05 to 0.15

                // Use sine waves for smoother camera motion instead of pure random
                const time = Date.now() / 1000;
                const frequency = 14; // Increased from 12 to 14 for more noticeable shake

                // Calculate offsets using sine waves with different phases
                // Increase the multipliers for each axis to make shake more visible
                const offsetX = Math.sin(time * frequency) * shakeAmount * 0.9;
                const offsetY = Math.sin(time * frequency + 1.3) * shakeAmount * 0.4; // Moderate vertical shake
                const offsetZ = Math.sin(time * frequency + 2.7) * shakeAmount * 0.9;

                camera.position.set(
                    this._originalCameraPosition.x + offsetX,
                    this._originalCameraPosition.y + offsetY,
                    this._originalCameraPosition.z + offsetZ
                );
            }
        } else if (this._originalCameraPosition) {
            // Reset camera position when shake ends
            const camera = this.dungeonHandler && this.dungeonHandler.camera ?
                this.dungeonHandler.camera : window.camera;

            if (camera) {
                camera.position.copy(this._originalCameraPosition);
            }
        }

        // Apply vignette effect
        if (this.visualEffects.vignetteIntensity > 0.01) {
            // Apply to post-processing if available
            if (window.postProcessor && window.postProcessor.setVignette) {
                window.postProcessor.setVignette(this.visualEffects.vignetteIntensity);
            }
        }
    }

    /**
     * Update boss behavior based on music
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updateBossBehavior(deltaTime) {
        if (!this.boss || !this.boss.userData) {
            return;
        }

        // Update boss speed based on intensity
        const baseSpeed = this.boss.userData.baseSpeed || 1.6;
        const speedMultiplier = 0.8 + (this.currentIntensity / 100) * 0.6; // 0.8-1.4x speed
        this.boss.userData.speed = baseSpeed * speedMultiplier;

        // Update boss attack cooldown based on intensity
        if (this.boss.userData.aiController) {
            const baseCooldown = 1.5;
            const cooldownMultiplier = 1.0 - (this.currentIntensity / 100) * 0.5; // 0.5-1.0x cooldown
            this.boss.userData.aiController.attackCooldown = baseCooldown * cooldownMultiplier;
        }

        // Update boss phase based on health
        if (this.boss.userData.aiController) {
            const aiController = this.boss.userData.aiController;
            const healthPercent = aiController.currentHealth / aiController.maxHealth;

            // Determine phase based on health
            let newPhase = 0;
            if (healthPercent < 0.15) newPhase = 3;
            else if (healthPercent < 0.4) newPhase = 2;
            else if (healthPercent < 0.7) newPhase = 1;

            // Phase transition
            if (newPhase !== this.bossPhase) {
                this.bossPhase = newPhase;
                this._onPhaseChange(newPhase);
            }
        }
    }

    /**
     * Handle boss phase change
     * @param {Number} phase - New phase (0-3)
     * @private
     */
    _onPhaseChange(phase) {
        console.log(`[BossController] Boss entering phase ${phase}`);

        // Trigger special attack on phase change
        if (phase > 0) {
            // Select pattern based on phase
            let patternName;
            switch (phase) {
                case 1: patternName = "spiral_wave"; break;
                case 2: patternName = "laser_grid"; break;
                case 3: patternName = "hellburst"; break;
                default: patternName = "circle_ripple";
            }

            // Trigger pattern with high intensity
            this.patternManager.triggerPattern(patternName, 0.8 + (phase * 0.05));

            // Update debug info with the pattern name
            if (this.options.debugMode) {
                this.debugInfo.lastPattern = patternName;
            }

            // Trigger screen shake for all phase changes, with stronger effect for higher phases
            if (this.options.screenShake) {
                // Always allow phase change shakes
                this._lastScreenShakeTime = Date.now(); // Update the timestamp

                // Use moderate shake amount that increases with phase
                const shakeAmount = 0.2 + (phase * 0.05); // 0.2-0.35 range
                this.visualEffects.screenShakeAmount = shakeAmount;

                if (this.options.debugMode) {
                    console.log(`[BossController] Phase change camera shake triggered, phase: ${phase}, amount: ${shakeAmount.toFixed(2)}`);
                }

                // Also trigger the DungeonHandler camera shake for more direct control
                if (this.dungeonHandler && this.dungeonHandler.cameraShake) {
                    console.log(`[BossController] Phase change - triggering direct camera shake`);
                    this.dungeonHandler.cameraShake(0.2 + (phase * 0.05), 500); // Longer duration for phase changes
                }
            }
        }
    }

    /**
     * Load boss music
     * @returns {Object} Tone.js Player or null
     * @private
     */
    async _loadBossMusic() {
        try {
            // Check if Tone.js is available
            if (typeof Tone === 'undefined') {
                console.error("[BossController] Tone.js is not loaded");
                return null;
            }

            console.log("[BossController] Loading boss music...");

            // Create a new player for boss music
            const bossMusic = new Tone.Player({
                url: "assets/music/catacombs/catacomb_boss.wav",
                loop: true,
                autostart: false,
                volume: 0, // dB (increased from -10 to make it more audible)
                onload: () => console.log("[BossController] Boss music loaded successfully")
            }).toDestination();

            // Wait for player to load with timeout
            await Promise.race([
                new Promise((resolve) => {
                    if (bossMusic.loaded) {
                        console.log("[BossController] Boss music already loaded");
                        resolve();
                    } else {
                        bossMusic.onstop = resolve;
                        bossMusic.onload = () => {
                            console.log("[BossController] Boss music loaded via onload");
                            resolve();
                        };
                    }
                }),
                new Promise((resolve) => setTimeout(() => {
                    console.log("[BossController] Boss music load timeout - continuing anyway");
                    resolve();
                }, 5000))
            ]);

            console.log("[BossController] Boss music load state:", bossMusic.loaded ? "Loaded" : "Not loaded");

            this.bossMusic = bossMusic;
            return bossMusic;
        } catch (error) {
            console.error("[BossController] Error loading boss music:", error);
            return null;
        }
    }

    /**
     * Start playing boss music
     * @returns {Boolean} Success
     * @private
     */
    async _startBossMusic() {
        console.log("[BossController] _startBossMusic called");
        try {
            // Check if boss music is loaded
            if (!this.bossMusic) {
                console.error("[BossController] Boss music not loaded");
                return false;
            }

            console.log("[BossController] Boss music state before starting:",
                      "State:", this.bossMusic.state,
                      "Loaded:", this.bossMusic.loaded,
                      "Volume:", this.bossMusic.volume ? this.bossMusic.volume.value : "N/A");

            // Fade out current music if playing
            if (this.audioManager && this.audioManager.musicConductor) {
                console.log("[BossController] Stopping current music...");
                // Try multiple methods to stop the current music
                try {
                    // Try different methods to stop the music
                    if (typeof this.audioManager.musicConductor.stopMusic === 'function') {
                        this.audioManager.musicConductor.stopMusic();
                    } else if (typeof this.audioManager.musicConductor.stop === 'function') {
                        this.audioManager.musicConductor.stop();
                    } else if (typeof this.audioManager.musicConductor.forceTransition === 'function') {
                        // Force transition to nothing
                        this.audioManager.musicConductor.forceTransition('none');
                    } else {
                        // Direct method to stop all players if available
                        if (typeof this.audioManager.musicConductor._stopAllPlayers === 'function') {
                            this.audioManager.musicConductor._stopAllPlayers();
                        }
                    }
                    console.log("[BossController] Current music stopped");
                } catch (e) {
                    console.error("[BossController] Error stopping current music:", e);
                }
            }

            // Start boss music with a small delay to ensure audio context is running
            await new Promise(resolve => setTimeout(resolve, 500));

            console.log("[BossController] Starting boss music now...");
            this.bossMusic.start();
            console.log("[BossController] Boss music started");

            // Verify it's actually playing
            setTimeout(() => {
                console.log("[BossController] Boss music state check:",
                    this.bossMusic.state,
                    "Volume:", this.bossMusic.volume.value,
                    "Muted:", this.bossMusic.mute);
            }, 1000);

            return true;
        } catch (error) {
            console.error("[BossController] Error starting boss music:", error);
            return false;
        }
    }

    /**
     * Stop boss music
     * @private
     */
    _stopBossMusic() {
        try {
            // Check if boss music is loaded
            if (!this.bossMusic) {
                return;
            }

            // Stop boss music
            this.bossMusic.stop();
            console.log("[BossController] Boss music stopped");

            // Resume regular music
            if (this.audioManager.musicConductor) {
                // TODO: Implement proper fade back to regular music
                // For now, just restart area music
                // this.audioManager.musicConductor.fadeIn();
            }
        } catch (error) {
            console.error("[BossController] Error stopping boss music:", error);
        }
    }

    /**
     * Get debug information
     * @returns {Object} Debug info
     */
    getDebugInfo() {
        // Log debug info when requested
        console.log('[BossController] Debug info requested:', this.debugInfo);
        return { ...this.debugInfo };
    }

    /**
     * Clean up resources
     */
    dispose() {
        this.stop();

        if (this.musicAnalyzer) {
            this.musicAnalyzer.dispose();
            this.musicAnalyzer = null;
        }

        if (this.patternManager) {
            this.patternManager.dispose();
            this.patternManager = null;
        }

        if (this.bossMusic) {
            this.bossMusic.dispose();
            this.bossMusic = null;
        }

        this.boss = null;
        this.dungeonHandler = null;
        this.audioManager = null;

        console.log("[BossController] Disposed");
    }

    /**
     * Load a timeline for the boss battle
     * @param {String} timelineName - Name of the timeline to load
     * @param {Boolean} useOnlyTimelinePatterns - If false (default), timeline patterns will be triggered by music sync.
     *                                           If true, only timeline patterns will be used with timeline-based timing.
     * @returns {Boolean} Success
     */
    loadTimeline(timelineName, useOnlyTimelinePatterns = false) { // Default to integrated mode
        console.log(`[BossController] loadTimeline called with timelineName=${timelineName}, useOnlyTimelinePatterns=${useOnlyTimelinePatterns}`);
        console.log(`[BossController] Current state: patternManager=${this.patternManager ? 'exists' : 'null'}, musicTimeline=${this.musicTimeline ? 'exists' : 'null'}`);

        // Ensure pattern manager has a valid boss reference
        if (this.patternManager && !this.patternManager.boss && this.boss) {
            console.log("[BossController] Updating pattern manager boss reference in loadTimeline");
            this.patternManager.boss = this.boss;
        }

        // Check if timeline exists
        if (!bossTimelines[timelineName]) {
            console.error(`[BossController] Timeline '${timelineName}' not found`);
            return false;
        }

        // Load timeline
        this.musicTimeline.loadTimeline(bossTimelines[timelineName]);
        this.currentTimelineName = timelineName;

        // Set the flag to use only timeline patterns
        // This ensures the boss will ONLY use the patterns specified in the timeline
        this.patternManager.useOnlyTimelinePatterns = useOnlyTimelinePatterns;

        // Update debug info
        if (this.options.debugMode) {
            this.debugInfo.timelineActive = true;
        }

        console.log(`[BossController] Loaded timeline '${timelineName}' with ${bossTimelines[timelineName].length} entries`);

        // Log the mode we're using
        if (useOnlyTimelinePatterns) {
            console.log(`[BossController] Using EXCLUSIVE timeline mode - patterns will be triggered by timeline timing only`);
        } else {
            console.log(`[BossController] Using INTEGRATED timeline mode - patterns will be triggered by music sync`);
        }

        // Force a log of the first timeline entry to verify it's loaded correctly
        if (bossTimelines[timelineName].length > 0) {
            const firstEntry = bossTimelines[timelineName][0];
            console.log(`[BossController] First timeline entry: ${firstEntry.description || 'No description'}, ` +
                       `Patterns: ${firstEntry.patterns.join(', ')}, ` +
                       `Projectiles: ${firstEntry.projectileTypes ? firstEntry.projectileTypes.join(', ') : 'None'}`);
        }

        return true;
    }

    /**
     * Handle pattern trigger from timeline
     * @param {Object} triggerInfo - Information about the triggered pattern
     * @private
     */
    _onTimelinePatternTrigger(triggerInfo) {
        console.log(`[BossController] _onTimelinePatternTrigger called with:`, JSON.stringify(triggerInfo));
        const { pattern, projectileType, intensity, speedMultiplier, fromTimeline } = triggerInfo;

        // Ensure pattern manager has a valid boss reference
        if (this.patternManager && !this.patternManager.boss && this.boss) {
            console.log("[BossController] Updating pattern manager boss reference in _onTimelinePatternTrigger");
            this.patternManager.boss = this.boss;
        }

        // Store the current timeline pattern in the pattern manager
        this.patternManager.currentTimelinePattern = pattern;
        this.patternManager.currentTimelineProjectileType = projectileType;

        // Trigger the pattern with the specified projectile type
        console.log(`[BossController] Calling patternManager.triggerPattern with: pattern=${pattern}, intensity=${intensity}, speedMultiplier=${speedMultiplier}, projectileType=${projectileType}`);
        this.patternManager.triggerPattern(pattern, intensity, speedMultiplier, projectileType);
        console.log(`[BossController] Pattern triggered from timeline: ${pattern}`);

        if (this.options.debugMode) {
            console.log(`[BossController] Timeline triggered pattern '${pattern}' with projectile '${projectileType}', intensity ${intensity.toFixed(2)} and speed ${speedMultiplier.toFixed(2)}`);
            this.debugInfo.lastTimelinePattern = pattern;
            this.debugInfo.lastTimelineIntensity = intensity;
            this.debugInfo.lastTimelineSpeedMultiplier = speedMultiplier;
        }
    }

    /**
     * Handle timeline entry change
     * @param {Object} entry - New timeline entry
     * @private
     */
    _onTimelineEntryChange(entry) {
        if (this.options.debugMode) {
            console.log(`[BossController] Timeline entry changed: ${entry.description || 'Unnamed'} (${entry.startTime}s - ${entry.endTime}s)`);
        }

        // We're not triggering camera shake here anymore
        // Instead, the CustomTimelineShaker handles shakes at specific timestamps
        // This comment is kept for reference
    }

    /**
     * Handle timeline start
     * @private
     */
    _onTimelineStart() {
        if (this.options.debugMode) {
            console.log(`[BossController] Timeline started: ${this.currentTimelineName}`);
            this.debugInfo.timelineActive = true;
        }
    }

    /**
     * Handle timeline end
     * @private
     */
    _onTimelineEnd() {
        if (this.options.debugMode) {
            console.log(`[BossController] Timeline ended: ${this.currentTimelineName}`);
            this.debugInfo.timelineActive = false;
        }
    }
}
