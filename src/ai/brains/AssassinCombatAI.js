/**
 * Assassin Combat AI implementation
 * Specializes in attacking the player from behind for bonus damage
 */
import * as THREE from 'three';
import { AIBrain } from '../AIBrain.js';
import { AIStates, TransitionConditions, checkCondition } from '../AIStates.js';

export class AssassinCombatAI extends AIBrain {
    /**
     * Constructor for Assassin Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Assassin specific parameters
        this.preferredRange = this._getScaledValue(enemyData.preferredRange || 2.0);
        this.attackRange = this._getScaledValue(enemyData.attackRange || 2.5);
        this.stalkRange = this._getScaledValue(enemyData.stalkRange || 10.0);
        this.backstabAngle = this._getScaledValue(Math.PI / 3, Math.PI / 4, Math.PI / 2); // Angle considered "behind" player
        this.backstabDamageMultiplier = this._getScaledValue(2.0, 1.5, 2.5); // Damage multiplier for backstab

        // Stalking behavior
        this.isStalking = false;
        this.stalkTimer = 0;
        this.stalkDuration = this._getScaledValue(3.0, 2.0, 5.0);
        this.stealthLevel = this._getScaledValue(0.7, 0.5, 0.9); // Higher = harder to detect

        // Ambush behavior
        this.isAmbushing = false;
        this.ambushTimer = 0;
        this.ambushDuration = this._getScaledValue(1.0, 0.5, 1.5);
        this.ambushSpeed = this._getScaledValue(enemyData.speed * 2.0, enemyData.speed * 1.5, enemyData.speed * 2.5);

        // Vanish behavior
        this.isVanished = false;
        this.vanishTimer = 0;
        this.vanishDuration = this._getScaledValue(2.0, 1.0, 3.0);
        this.vanishCooldown = this._getScaledValue(10.0, 15.0, 5.0); // Longer cooldown for lower difficulty
        this.vanishCooldownTimer = 0;

        // Initialize state
        this.setState(AIStates.IDLE);
    }

    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Update cooldown timers
        if (this.vanishCooldownTimer > 0) {
            this.vanishCooldownTimer -= deltaTime;
        }

        // Handle state transitions
        switch (this.currentState) {
            case AIStates.IDLE:
                // Transition to STALKING if player is visible and in range
                if (this._canSeePlayer() && distanceToPlayer <= this.stalkRange) {
                    this.setState(AIStates.STALKING);
                }
                break;

            case AIStates.STALKING:
                // If player is not visible or too far, go back to idle
                if (!this._canSeePlayer() || distanceToPlayer > this.stalkRange) {
                    this.setState(AIStates.IDLE);
                }
                // If behind player and in attack range, ambush
                else if (this._isBehindPlayer() && distanceToPlayer <= this.attackRange) {
                    this.setState(AIStates.AMBUSHING);
                }
                // If player spots assassin, consider vanishing
                else if (this._isSpottedByPlayer() && this.vanishCooldownTimer <= 0) {
                    this.setState(AIStates.VANISH);
                }
                break;

            case AIStates.AMBUSHING:
                // After ambush duration, attack
                if (this.ambushTimer <= 0) {
                    this.setState(AIStates.ATTACKING);
                }
                break;

            case AIStates.ATTACKING:
                // After attacking, vanish or go back to stalking
                if (this.vanishCooldownTimer <= 0 && Math.random() < 0.7) {
                    this.setState(AIStates.VANISH);
                } else {
                    this.setState(AIStates.STALKING);
                }
                break;

            case AIStates.VANISH:
                // After vanish duration, go back to stalking
                if (this.vanishTimer <= 0) {
                    this.setState(AIStates.STALKING);
                }
                break;
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Execute behavior based on state
        switch (this.currentState) {
            case AIStates.IDLE:
                // Idle behavior (slight random movement)
                this._executeIdleBehavior(deltaTime);
                break;

            case AIStates.STALKING:
                // Stalk player (move to position behind player)
                this._executeStalkingBehavior(deltaTime, distanceToPlayer, directionToPlayer);
                break;

            case AIStates.AMBUSHING:
                // Ambush player (quick dash to attack)
                this._executeAmbushBehavior(deltaTime, directionToPlayer);
                break;

            case AIStates.ATTACKING:
                // Execute backstab attack
                this._executeBackstabAttack();
                break;

            case AIStates.VANISH:
                // Vanish from player's sight
                this._executeVanishBehavior(deltaTime, directionToPlayer);
                break;
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        switch (state) {
            case AIStates.STALKING:
                // Initialize stalking
                this.isStalking = true;
                this.stalkTimer = this.stalkDuration;
                break;

            case AIStates.AMBUSHING:
                // Initialize ambushing
                this.isAmbushing = true;
                this.ambushTimer = this.ambushDuration;
                break;

            case AIStates.ATTACKING:
                // Reset attack timer
                this.timeSinceLastAttack = 0;
                break;

            case AIStates.VANISH:
                // Initialize vanishing
                this.isVanished = true;
                this.vanishTimer = this.vanishDuration;
                this.vanishCooldownTimer = this.vanishCooldown;

                // Make enemy partially transparent
                this._setEnemyVisibility(0.3);
                break;
        }
    }

    /**
     * Check if assassin is behind player
     * @returns {Boolean} - True if behind player
     * @private
     */
    _isBehindPlayer() {
        if (!this.player) return false;

        // Get player's forward direction
        let playerForward = new THREE.Vector3(0, 0, -1);
        if (this.player.quaternion) {
            playerForward.applyQuaternion(this.player.quaternion);
            playerForward.y = 0;
            playerForward.normalize();
        }

        // Get direction from player to enemy
        const playerToEnemy = this.enemy.position.clone().sub(this.player.position);
        playerToEnemy.y = 0;
        playerToEnemy.normalize();

        // Calculate angle between directions
        const angle = Math.acos(playerForward.dot(playerToEnemy));

        // Check if angle is within backstab range
        return angle > Math.PI - this.backstabAngle;
    }

    /**
     * Check if player has spotted the assassin
     * @returns {Boolean} - True if spotted
     * @private
     */
    _isSpottedByPlayer() {
        if (!this.player) return false;

        // Get player's forward direction
        let playerForward = new THREE.Vector3(0, 0, -1);
        if (this.player.quaternion) {
            playerForward.applyQuaternion(this.player.quaternion);
            playerForward.y = 0;
            playerForward.normalize();
        }

        // Get direction from player to enemy
        const playerToEnemy = this.enemy.position.clone().sub(this.player.position);
        playerToEnemy.y = 0;
        playerToEnemy.normalize();

        // Calculate angle between directions
        const angle = Math.acos(playerForward.dot(playerToEnemy));

        // Check if player is facing enemy and close enough
        const distanceToPlayer = this.player.position.distanceTo(this.enemy.position);
        const maxSpotDistance = this.stealthLevel * 10; // Scale with stealth level

        return angle < Math.PI / 3 && distanceToPlayer < maxSpotDistance;
    }

    /**
     * Set enemy visibility
     * @param {Number} opacity - Opacity value (0-1)
     * @private
     */
    _setEnemyVisibility(opacity) {
        // Apply opacity to all meshes in enemy
        this.enemy.traverse(child => {
            if (child.isMesh && child.material) {
                // Handle array of materials
                if (Array.isArray(child.material)) {
                    child.material.forEach(mat => {
                        mat.transparent = opacity < 1;
                        mat.opacity = opacity;
                    });
                } else {
                    child.material.transparent = opacity < 1;
                    child.material.opacity = opacity;
                }
            }
        });
    }

    /**
     * Execute stalking behavior
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeStalkingBehavior(deltaTime, distanceToPlayer, directionToPlayer) {
        if (!this.player) return;

        // Update stalk timer
        this.stalkTimer -= deltaTime;
        if (this.stalkTimer <= 0) {
            this.stalkTimer = this.stalkDuration;
        }

        // Get player's forward direction
        let playerForward = new THREE.Vector3(0, 0, -1);
        if (this.player.quaternion) {
            playerForward.applyQuaternion(this.player.quaternion);
            playerForward.y = 0;
            playerForward.normalize();
        }

        // Calculate position behind player
        const behindDistance = this.preferredRange;
        const behindPosition = this.player.position.clone().add(
            playerForward.clone().multiplyScalar(-behindDistance)
        );

        // Add some randomness to position
        const randomAngle = (Math.random() - 0.5) * Math.PI / 4; // +/- 45 degrees
        const randomOffset = new THREE.Vector3(
            Math.cos(randomAngle) * behindDistance * 0.3,
            0,
            Math.sin(randomAngle) * behindDistance * 0.3
        );
        behindPosition.add(randomOffset);

        // Calculate direction to behind position
        const directionToBehind = behindPosition.clone().sub(this.enemy.position);
        const distanceToBehind = directionToBehind.length();
        directionToBehind.normalize();

        // Move towards behind position
        if (distanceToBehind > 0.5) {
            // Calculate move amount (slower when stalking)
            const stalkSpeed = this.enemyData.speed * 0.8;
            const moveAmount = stalkSpeed * deltaTime;

            // Calculate new position
            const moveVector = directionToBehind.multiplyScalar(Math.min(moveAmount, distanceToBehind));
            const newPosition = this.enemy.position.clone().add(moveVector);

            // Check if new position is within floor bounds
            if (this.floorBounds) {
                const minX = this.floorBounds.min.x + 1.0; // Add buffer
                const maxX = this.floorBounds.max.x - 1.0;
                const minZ = this.floorBounds.min.z + 1.0;
                const maxZ = this.floorBounds.max.z - 1.0;

                // Clamp position to floor bounds
                newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
                newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
            }

            // Check for collisions with walls
            let canMove = true;
            if (this.collisionObjects) {
                const enemyRadius = this.enemyData.size || 0.5;
                for (const obj of this.collisionObjects) {
                    const objBox = new THREE.Box3().setFromObject(obj);
                    const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

                    // Simple collision check: if enemy's bounding sphere intersects with object's bounding box
                    if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                        canMove = false;
                        break;
                    }
                }
            }

            // Apply movement if no collision
            if (canMove) {
                this.enemy.position.copy(newPosition);
            }
        }

        // Always face player
        this._faceTarget(this.player.position);

        // Restore visibility if vanished
        if (this.isVanished) {
            this.isVanished = false;
            this._setEnemyVisibility(1.0);
        }
    }

    /**
     * Execute ambush behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeAmbushBehavior(deltaTime, directionToPlayer) {
        // Update ambush timer
        this.ambushTimer -= deltaTime;

        // Calculate move amount (faster when ambushing)
        const moveAmount = this.ambushSpeed * deltaTime;

        // Calculate new position
        const moveVector = directionToPlayer.clone().multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check for collisions with walls
        let canMove = true;
        if (this.collisionObjects) {
            const enemyRadius = this.enemyData.size || 0.5;
            for (const obj of this.collisionObjects) {
                const objBox = new THREE.Box3().setFromObject(obj);
                const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

                // Simple collision check: if enemy's bounding sphere intersects with object's bounding box
                if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                    canMove = false;
                    break;
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
        }

        // Face player
        this._faceTarget(this.player.position);
    }

    /**
     * Execute backstab attack
     * @private
     */
    _executeBackstabAttack() {
        if (!this.player) return;

        // Check if still behind player
        const isBehind = this._isBehindPlayer();

        // Get attack data from enemy model if available
        const baseAttackHitbox = this.enemy.userData.attackHitbox || {
            radius: this.attackRange,
            damage: this.enemyData.meleeDamage || 4,
            knockback: 5.0
        };

        // Apply backstab multiplier if behind player
        const attackHitbox = {
            ...baseAttackHitbox,
            damage: baseAttackHitbox.damage * (isBehind ? this.backstabDamageMultiplier : 1.0)
        };

        // Emit attack event with modified hitbox
        if (this.enemy.userData.onMeleeAttack) {
            this.enemy.userData.onMeleeAttack(attackHitbox);
        }
    }

    /**
     * Execute vanish behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeVanishBehavior(deltaTime, directionToPlayer) {
        // Update vanish timer
        this.vanishTimer -= deltaTime;

        // Move away from player while vanished
        const fleeDirection = directionToPlayer.clone().multiplyScalar(-1);

        // Add some randomness to direction
        const randomAngle = (Math.random() - 0.5) * Math.PI; // +/- 90 degrees
        const randomizedDirection = new THREE.Vector3(
            fleeDirection.x * Math.cos(randomAngle) - fleeDirection.z * Math.sin(randomAngle),
            0,
            fleeDirection.x * Math.sin(randomAngle) + fleeDirection.z * Math.cos(randomAngle)
        );

        // Calculate move amount (faster when vanished)
        const vanishSpeed = this.enemyData.speed * 1.5;
        const moveAmount = vanishSpeed * deltaTime;

        // Apply movement
        const moveVector = randomizedDirection.normalize().multiplyScalar(moveAmount);
        this.enemy.position.add(moveVector);

        // If vanish ended, restore visibility
        if (this.vanishTimer <= 0) {
            this.isVanished = false;
            this._setEnemyVisibility(1.0);
        }
    }
}
