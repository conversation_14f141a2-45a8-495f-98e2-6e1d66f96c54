/**
 * Boss Combat AI base class
 * Provides framework for creating complex boss behaviors with phases
 * Integrates with BossController for music-reactive bullet patterns
 */
import * as THREE from 'three';
import { AIBrain } from '../AIBrain.js';
import { AIStates, TransitionConditions, checkCondition } from '../AIStates.js';
import { <PERSON><PERSON><PERSON>roller } from './BossController.js';

export class BossCombatAI extends AIBrain {
    /**
     * Constructor for Boss Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Boss specific parameters
        this.maxHealth = enemyData.health;
        this.currentHealth = enemyData.health;
        this.phaseThresholds = [0.7, 0.4, 0.15]; // Health percentages for phase transitions
        this.currentPhase = 0; // Start at phase 0

        // Boss controller for music-reactive patterns
        this.bossController = null;
        this.isMusicReactive = enemyData.musicReactive !== false; // Enable by default

        // Phase transition
        this.isTransitioning = false;
        this.transitionTimer = 0;
        this.transitionDuration = this._getScaledValue(2.0, 1.5, 3.0);

        // Special attack
        this.specialAttackCooldown = this._getScaledValue(15.0, 20.0, 10.0); // Shorter with higher difficulty
        this.specialAttackCooldownTimer = this.specialAttackCooldown * 0.5; // Start halfway through cooldown
        this.specialAttackDuration = this._getScaledValue(3.0, 2.0, 4.0);
        this.specialAttackTimer = 0;

        // Boss movement
        this.teleportCooldown = this._getScaledValue(20.0, 30.0, 15.0); // Shorter with higher difficulty
        this.teleportCooldownTimer = this.teleportCooldown * 0.5; // Start halfway through cooldown
        this.teleportDuration = this._getScaledValue(1.0, 0.7, 1.5);
        this.teleportTimer = 0;

        // Initialize state
        this.setState(AIStates.IDLE);

        // Initialize boss controller if music reactive
        if (this.isMusicReactive && scene.userData.dungeonHandler && scene.userData.audioManager) {
            this._initBossController(scene.userData.dungeonHandler, scene.userData.audioManager);
        }
    }

    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Ensure boss controller is started
        if (this.bossController && !this.bossController.active) {
            console.log("[BossCombatAI] Boss controller not active, starting it...");
            this._startBossController();
        }
        // Update cooldown timers
        if (this.specialAttackCooldownTimer > 0) {
            this.specialAttackCooldownTimer -= deltaTime;
        }
        if (this.teleportCooldownTimer > 0) {
            this.teleportCooldownTimer -= deltaTime;
        }

        // Check for phase transitions
        this._checkPhaseTransition();

        // Handle state transitions
        if (this.isTransitioning) {
            // During transition, only update transition timer
            this.transitionTimer -= deltaTime;
            if (this.transitionTimer <= 0) {
                this.isTransitioning = false;
                this._onPhaseTransitionComplete();
            }
            return;
        }

        // State transitions based on current state
        switch (this.currentState) {
            case AIStates.IDLE:
                // Transition based on distance
                if (distanceToPlayer <= this._getPreferredRange()) {
                    // Choose between different attack types
                    this._chooseAttackState();
                } else {
                    this.setState(AIStates.MOVING);
                }
                break;

            case AIStates.MOVING:
                // If reached preferred range, attack
                if (distanceToPlayer <= this._getPreferredRange()) {
                    this._chooseAttackState();
                }
                // Consider teleporting if cooldown is ready
                else if (distanceToPlayer > this._getPreferredRange() * 1.5 &&
                         this.teleportCooldownTimer <= 0) {
                    this.setState(AIStates.PHASE_CHANGE); // Use PHASE_CHANGE for teleport
                }
                break;

            case AIStates.ATTACKING:
            case AIStates.SHOOTING:
                // After attack, go back to idle
                this.setState(AIStates.IDLE);
                break;

            case AIStates.SPECIAL_ATTACK:
                // Update special attack timer
                this.specialAttackTimer -= deltaTime;
                if (this.specialAttackTimer <= 0) {
                    this.setState(AIStates.IDLE);
                }
                break;

            case AIStates.PHASE_CHANGE: // Used for teleport
                // Update teleport timer
                this.teleportTimer -= deltaTime;
                if (this.teleportTimer <= 0) {
                    this.setState(AIStates.IDLE);
                }
                break;
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Initialize boss controller if not already initialized
        if (this.isMusicReactive && !this.bossController && this.scene && this.scene.userData.dungeonHandler && this.scene.userData.audioManager) {
            console.log("[BossCombatAI] Boss controller not initialized, initializing it now...");
            this._initBossController(this.scene.userData.dungeonHandler, this.scene.userData.audioManager);
        }

        // Update boss controller if active
        this._updateBossController(deltaTime);

        // If transitioning between phases, execute transition behavior
        if (this.isTransitioning) {
            this._executePhaseTransition(deltaTime);
            return;
        }

        // Execute behavior based on state
        switch (this.currentState) {
            case AIStates.IDLE:
                // Idle behavior (slight random movement)
                this._executeIdleBehavior(deltaTime);
                break;

            case AIStates.MOVING:
                // Move towards player
                this._executeMoveTowardsPlayer(deltaTime, directionToPlayer);
                break;

            case AIStates.ATTACKING:
                // Execute melee attack
                this._executeMeleeAttack();
                break;

            case AIStates.SHOOTING:
                // Execute ranged attack
                this._executeRangedAttack();
                break;

            case AIStates.SPECIAL_ATTACK:
                // Execute special attack
                this._executeSpecialAttack(deltaTime);
                break;

            case AIStates.PHASE_CHANGE: // Used for teleport
                // Execute teleport
                this._executeTeleport(deltaTime);
                break;
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        switch (state) {
            case AIStates.ATTACKING:
            case AIStates.SHOOTING:
                // Reset attack timer
                this.timeSinceLastAttack = 0;
                break;

            case AIStates.SPECIAL_ATTACK:
                // Initialize special attack
                this.specialAttackTimer = this.specialAttackDuration;
                this.specialAttackCooldownTimer = this.specialAttackCooldown;
                break;

            case AIStates.PHASE_CHANGE: // Used for teleport
                // Initialize teleport
                this.teleportTimer = this.teleportDuration;
                this.teleportCooldownTimer = this.teleportCooldown;
                break;
        }
    }

    /**
     * Take damage and check for phase transitions
     * @param {Number} damage - Amount of damage taken
     */
    takeDamage(damage) {
        this.currentHealth -= damage;

        // Clamp health to 0
        if (this.currentHealth < 0) {
            this.currentHealth = 0;
        }

        // Check for phase transition
        this._checkPhaseTransition();
    }

    /**
     * Check if should transition to next phase
     * @private
     */
    _checkPhaseTransition() {
        if (this.isTransitioning) return;

        // Calculate health percentage
        const healthPercentage = this.currentHealth / this.maxHealth;

        // Check if crossed a phase threshold
        for (let i = this.currentPhase; i < this.phaseThresholds.length; i++) {
            if (healthPercentage <= this.phaseThresholds[i]) {
                // Transition to next phase
                this.currentPhase = i + 1;
                this._startPhaseTransition();
                break;
            }
        }
    }

    /**
     * Start phase transition
     * @private
     */
    _startPhaseTransition() {
        this.isTransitioning = true;
        this.transitionTimer = this.transitionDuration;

        // Cancel any current actions
        this.setState(AIStates.IDLE);

        // Emit phase change event (to be handled by DungeonHandler)
        if (this.enemy.userData.onPhaseChange) {
            this.enemy.userData.onPhaseChange(this.currentPhase);
        }
    }

    /**
     * Execute phase transition behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executePhaseTransition(deltaTime) {
        // This is a base implementation that should be overridden by subclasses
        // Default behavior: become invulnerable and play an animation

        // Make enemy partially transparent
        const opacity = 0.5 + 0.5 * Math.sin(this.transitionTimer * 5);
        this._setEnemyVisibility(opacity);
    }

    /**
     * Called when phase transition is complete
     * @private
     */
    _onPhaseTransitionComplete() {
        // This is a base implementation that should be overridden by subclasses
        // Default behavior: restore visibility and update parameters

        // Restore visibility
        this._setEnemyVisibility(1.0);

        // Update parameters based on phase
        this._updatePhaseParameters();

        // Trigger special attack pattern for phase transition
        if (this.bossController && this.bossController.active) {
            // Select pattern based on phase
            let patternName;
            switch (this.currentPhase) {
                case 1: patternName = "spiral_wave"; break;
                case 2: patternName = "laser_grid"; break;
                case 3: patternName = "hellburst"; break;
                default: patternName = "circle_ripple";
            }

            // Trigger pattern with high intensity
            this.bossController.patternManager.triggerPattern(patternName, 0.8 + (this.currentPhase * 0.05));
        }
    }

    /**
     * Update parameters based on current phase
     * @private
     */
    _updatePhaseParameters() {
        // This is a base implementation that should be overridden by subclasses
        // Default behavior: increase aggression and speed with each phase

        // Scale parameters based on phase
        const phaseMultiplier = 1.0 + this.currentPhase * 0.2; // 20% increase per phase

        // Update speed
        this.enemyData.speed = this.enemyData.baseSpeed * phaseMultiplier;

        // Update attack cooldown (shorter)
        this.attackCooldown = this._getScaledValue(2.0) / phaseMultiplier;

        // Update special attack cooldown (shorter)
        this.specialAttackCooldown = this._getScaledValue(15.0, 20.0, 10.0) / phaseMultiplier;
    }

    /**
     * Choose an attack state based on distance and cooldowns
     * @private
     */
    _chooseAttackState() {
        // Check if special attack is ready
        if (this.specialAttackCooldownTimer <= 0) {
            // Higher chance of special attack at higher phases
            const specialAttackChance = 0.3 + (this.currentPhase * 0.1);
            if (Math.random() < specialAttackChance) {
                this.setState(AIStates.SPECIAL_ATTACK);
                return;
            }
        }

        // Choose between melee and ranged based on distance
        const distanceToPlayer = this._getDistanceToPlayer();
        const meleeRange = this._getScaledValue(3.0);

        if (distanceToPlayer <= meleeRange) {
            this.setState(AIStates.ATTACKING);
        } else {
            this.setState(AIStates.SHOOTING);
        }
    }

    /**
     * Get preferred range based on phase
     * @returns {Number} - Preferred range
     * @private
     */
    _getPreferredRange() {
        // Base preferred range
        const baseRange = this._getScaledValue(5.0);

        // Adjust based on phase
        switch (this.currentPhase) {
            case 0: // First phase
                return baseRange;
            case 1: // Second phase
                return baseRange * 0.8; // Closer
            case 2: // Third phase
                return baseRange * 0.6; // Even closer
            default: // Final phase
                return baseRange * 0.5; // Very close
        }
    }

    /**
     * Execute move towards player behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeMoveTowardsPlayer(deltaTime, directionToPlayer) {
        // Calculate move amount
        const moveSpeed = this.enemyData.speed;
        const moveAmount = moveSpeed * deltaTime;

        // Calculate new position
        const moveVector = directionToPlayer.clone().multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check for collisions with walls
        let canMove = true;
        if (this.collisionObjects) {
            const enemyRadius = this.enemyData.size || 0.5;
            for (const obj of this.collisionObjects) {
                const objBox = new THREE.Box3().setFromObject(obj);
                const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

                // Simple collision check: if enemy's bounding sphere intersects with object's bounding box
                if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                    canMove = false;
                    break;
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
        }

        // Face player
        this._faceTarget(this.player.position);
    }

    /**
     * Execute melee attack
     * @private
     */
    _executeMeleeAttack() {
        if (!this.player) return;

        // Get attack data from enemy model if available
        const baseAttackHitbox = this.enemy.userData.attackHitbox || {
            radius: this.attackRange,
            damage: this.enemyData.meleeDamage || 4,
            knockback: 5.0
        };

        // Scale damage based on phase
        const damageMultiplier = 1.0 + (this.currentPhase * 0.2); // 20% increase per phase

        // Apply phase multiplier to damage
        const attackHitbox = {
            ...baseAttackHitbox,
            damage: baseAttackHitbox.damage * damageMultiplier
        };

        // Emit attack event with modified hitbox
        if (this.enemy.userData.onMeleeAttack) {
            this.enemy.userData.onMeleeAttack(attackHitbox);
        }
    }

    /**
     * Execute ranged attack
     * @private
     */
    _executeRangedAttack() {
        if (!this.player) return;

        // Get shoot direction
        let shootDirection;
        if (this.difficulty >= 3 || this.currentPhase >= 1) {
            // Use prediction for higher difficulties or later phases
            const predictedPosition = this._predictPlayerMovement();
            shootDirection = predictedPosition.clone().sub(this.enemy.position).normalize();
        } else {
            // Direct shooting for lower difficulties
            shootDirection = this.player.position.clone().sub(this.enemy.position).normalize();
        }

        // Emit shoot event (to be handled by DungeonHandler)
        if (this.enemy.userData.onShoot) {
            // Scale projectile count based on phase
            const projectileCount = 1 + Math.floor(this.currentPhase / 2); // More projectiles in later phases
            this.enemy.userData.onShoot(shootDirection, projectileCount);
        }
    }

    /**
     * Execute special attack
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeSpecialAttack(deltaTime) {
        // If using music-reactive boss controller, delegate to it
        if (this.bossController && this.bossController.active) {
            // Use music intensity to determine attack pattern
            const intensity = this.bossController.currentIntensity;

            // Calculate attack progress (0 to 1)
            const attackProgress = 1 - (this.specialAttackTimer / this.specialAttackDuration);

            // Select pattern based on intensity and phase
            let patternName;
            if (intensity < 30) {
                patternName = "petal_spread";
            } else if (intensity < 50) {
                patternName = "circle_ripple";
            } else if (intensity < 70) {
                patternName = "spiral_wave";
            } else {
                patternName = "hellburst";
            }

            // Trigger pattern at specific points in the progress
            if (attackProgress >= 0.2 && attackProgress < 0.25) {
                this.bossController.patternManager.triggerPattern(patternName, intensity / 100);
            } else if (attackProgress >= 0.5 && attackProgress < 0.55) {
                this.bossController.patternManager.triggerPattern(patternName, intensity / 100);
            } else if (attackProgress >= 0.8 && attackProgress < 0.85) {
                this.bossController.patternManager.triggerPattern(patternName, intensity / 100);
            }

            return;
        }

        // Fallback to default behavior if no boss controller
        // Calculate attack progress (0 to 1)
        const attackProgress = 1 - (this.specialAttackTimer / this.specialAttackDuration);

        // Execute attack at specific points in the progress
        if (attackProgress >= 0.2 && attackProgress < 0.25) {
            this._executeSpecialAttackWave(0);
        } else if (attackProgress >= 0.5 && attackProgress < 0.55) {
            this._executeSpecialAttackWave(1);
        } else if (attackProgress >= 0.8 && attackProgress < 0.85) {
            this._executeSpecialAttackWave(2);
        }
    }

    /**
     * Execute a wave of the special attack
     * @param {Number} waveIndex - Index of the wave
     * @private
     */
    _executeSpecialAttackWave(waveIndex) {
        if (!this.player) return;

        // Number of projectiles based on phase and wave
        const baseCount = 4 + this.currentPhase * 2;
        const projectileCount = baseCount + waveIndex;

        // Emit special attack event (to be handled by DungeonHandler)
        if (this.enemy.userData.onSpecialAttack) {
            this.enemy.userData.onSpecialAttack(waveIndex, projectileCount);
        }
    }

    /**
     * Execute teleport behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeTeleport(deltaTime) {
        // Calculate teleport progress (0 to 1)
        const teleportProgress = 1 - (this.teleportTimer / this.teleportDuration);

        if (teleportProgress < 0.5) {
            // First half: fade out
            const opacity = 1 - (teleportProgress * 2);
            this._setEnemyVisibility(opacity);
        } else {
            // Second half: fade in at new position
            if (teleportProgress === 0.5) {
                // At exactly halfway, move to new position
                this._teleportToNewPosition();
            }

            // Fade in
            const opacity = (teleportProgress - 0.5) * 2;
            this._setEnemyVisibility(opacity);
        }
    }

    /**
     * Teleport to a new position
     * @private
     */
    _teleportToNewPosition() {
        if (!this.player) return;

        // Choose teleport position based on phase
        let teleportPosition;

        if (this.currentPhase >= 2) {
            // Later phases: teleport behind player
            const playerForward = new THREE.Vector3(0, 0, -1);
            if (this.player.quaternion) {
                playerForward.applyQuaternion(this.player.quaternion);
            }

            const behindDistance = 3.0;
            teleportPosition = this.player.position.clone().add(
                playerForward.clone().multiplyScalar(-behindDistance)
            );
        } else {
            // Earlier phases: teleport to preferred range
            const preferredRange = this._getPreferredRange();
            const randomAngle = Math.random() * Math.PI * 2;

            teleportPosition = this.player.position.clone().add(
                new THREE.Vector3(
                    Math.cos(randomAngle) * preferredRange,
                    0,
                    Math.sin(randomAngle) * preferredRange
                )
            );
        }

        // Apply teleport
        this.enemy.position.copy(teleportPosition);

        // Face player
        this._faceTarget(this.player.position);
    }

    /**
     * Set enemy visibility
     * @param {Number} opacity - Opacity value (0-1)
     * @private
     */
    _setEnemyVisibility(opacity) {
        // Apply opacity to all meshes in enemy
        this.enemy.traverse(child => {
            if (child.isMesh && child.material) {
                // Handle array of materials
                if (Array.isArray(child.material)) {
                    child.material.forEach(mat => {
                        mat.transparent = opacity < 1;
                        mat.opacity = opacity;
                    });
                } else {
                    child.material.transparent = opacity < 1;
                    child.material.opacity = opacity;
                }
            }
        });
    }

    /**
     * Initialize boss controller for music-reactive patterns
     * @param {Object} dungeonHandler - Reference to the DungeonHandler
     * @param {Object} audioManager - Reference to the AudioManager
     * @private
     */
    _initBossController(dungeonHandler, audioManager) {
        console.log("[BossCombatAI] _initBossController called");
        console.log("[BossCombatAI] DungeonHandler:", dungeonHandler ? "exists" : "null");
        console.log("[BossCombatAI] AudioManager:", audioManager ? "exists" : "null");
        console.log("[BossCombatAI] Enemy:", this.enemy ? "exists" : "null");
        if (this.enemy) {
            console.log("[BossCombatAI] Enemy position:", this.enemy.position ? "exists" : "null");
            if (this.enemy.position) {
                console.log(`[BossCombatAI] Enemy position: ${this.enemy.position.x.toFixed(2)}, ${this.enemy.position.y.toFixed(2)}, ${this.enemy.position.z.toFixed(2)}`);
            }
        }
        try {
            // Create boss controller
            this.bossController = new BossController(
                this.enemy,
                dungeonHandler,
                audioManager,
                {
                    visualEffects: true,
                    screenShake: true,
                    debugMode: true
                }
            );

            // Initialize boss controller
            this.bossController.init().then(success => {
                if (success) {
                    console.log("[BossCombatAI] Boss controller initialized successfully");

                    // Store reference to AI controller in enemy userData
                    this.enemy.userData.aiController = this;

                    // Load the appropriate timeline for the boss type
                    if (this.enemy.userData.type === 'catacombs_overlord') {
                        console.log("[BossCombatAI] Setting up catacomb_overlord timeline for catacomb boss");
                        const success = this.bossController.loadTimeline("catacomb_overlord", false); // false = integrated mode
                        console.log(`[BossCombatAI] Timeline load ${success ? 'successful' : 'failed'}`);
                    } else {
                        console.log("[BossCombatAI] Enemy type not recognized, using default timeline");
                        const success = this.bossController.loadTimeline("catacomb_overlord", false); // Use catacomb_overlord as default
                        console.log(`[BossCombatAI] Default timeline load ${success ? 'successful' : 'failed'}`);
                    }

                    // Start boss controller when entering combat
                    this._startBossController();
                } else {
                    console.error("[BossCombatAI] Failed to initialize boss controller");
                }
            });
        } catch (error) {
            console.error("[BossCombatAI] Error initializing boss controller:", error);
        }
    }

    /**
     * Start boss controller
     * @private
     */
    _startBossController() {
        console.log("[BossCombatAI] _startBossController called");
        if (this.bossController) {
            console.log("[BossCombatAI] Boss controller exists, active state:", this.bossController.active);
            if (!this.bossController.active) {
                console.log("[BossCombatAI] Starting boss controller...");
                this.bossController.start().then(success => {
                    if (success) {
                        console.log("[BossCombatAI] Boss controller started successfully");
                    } else {
                        console.error("[BossCombatAI] Failed to start boss controller");
                    }
                }).catch(error => {
                    console.error("[BossCombatAI] Error starting boss controller:", error);
                });
            } else {
                console.log("[BossCombatAI] Boss controller already active");
            }
        } else {
            console.error("[BossCombatAI] Cannot start boss controller - not initialized");
        }
    }

    /**
     * Update boss controller
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updateBossController(deltaTime) {
        if (this.bossController && this.bossController.active) {
            this.bossController.update(deltaTime);
        }
    }

    /**
     * Clean up resources
     */
    dispose() {
        // Dispose boss controller
        if (this.bossController) {
            this.bossController.dispose();
            this.bossController = null;
        }

        // Call parent dispose if it exists
        if (super.dispose) {
            super.dispose();
        }
    }
}
