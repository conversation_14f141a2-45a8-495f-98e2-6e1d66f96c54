/**
 * Melee Combat AI implementation
 * Specializes in getting close to the player and using melee attacks
 */
import * as THREE from 'three';
import { AIBrain } from '../AIBrain.js';
import { AIStates, TransitionConditions, checkCondition } from '../AIStates.js';

export class MeleeCombatAI extends AIBrain {
    /**
     * Constructor for Melee Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Melee specific parameters
        this.preferredRange = this._getScaledValue(enemyData.preferredRange || 2.0);
        this.attackRange = this._getScaledValue(enemyData.attackRange || 2.5);
        this.chargeRange = this._getScaledValue(enemyData.chargeRange || 6.0);

        // Charging behavior
        this.isCharging = false;
        this.chargeTimer = 0;
        this.chargeDuration = this._getScaledValue(1.0, 0.5, 1.5);
        this.chargeSpeed = this._getScaledValue(enemyData.speed * 1.5, enemyData.speed * 1.2, enemyData.speed * 2.0);
        this.chargeChance = this._getScaledValue(0.4, 0.2, 0.6); // Higher with difficulty

        // Blocking behavior
        this.isBlocking = false;
        this.blockTimer = 0;
        this.blockDuration = this._getScaledValue(1.0, 0.5, 1.5);
        this.blockChance = this._getScaledValue(0.3, 0.1, 0.5); // Higher with difficulty

        // Circling behavior
        this.isCircling = false;
        this.circleDirection = 1; // 1 for clockwise, -1 for counterclockwise
        this.circleTimer = 0;
        this.circleDuration = this._getScaledValue(2.0, 1.0, 3.0);
        this.circleChance = this._getScaledValue(0.5, 0.3, 0.7); // Higher with difficulty

        // Initialize state
        this.setState(AIStates.IDLE);
    }

    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Handle state transitions
        switch (this.currentState) {
            case AIStates.IDLE:
                // Transition to MOVING if player is visible
                if (this._canSeePlayer()) {
                    // If player is in charge range, consider charging
                    if (distanceToPlayer <= this.chargeRange &&
                        distanceToPlayer > this.attackRange &&
                        Math.random() < this.chargeChance) {
                        this.setState(AIStates.CHARGING);
                    }
                    // If player is in attack range, attack
                    else if (distanceToPlayer <= this.attackRange &&
                             this.timeSinceLastAttack >= this.attackCooldown) {
                        this.setState(AIStates.ATTACKING);
                        // Set attack state duration to match animation
                        this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || 2.4;
                        this.attackStateTimer = this.attackStateDuration;
                    }
                    // If player is too far, move closer
                    else if (distanceToPlayer > this.preferredRange) {
                        this.setState(AIStates.MOVING);
                    }
                    // Otherwise, consider circling
                    else if (Math.random() < this.circleChance) {
                        this.setState(AIStates.STRAFING); // Use strafing for circling
                    }
                }
                break;

            case AIStates.MOVING:
                // If player is in charge range, consider charging
                if (distanceToPlayer <= this.chargeRange &&
                    distanceToPlayer > this.attackRange &&
                    Math.random() < this.chargeChance) {
                    this.setState(AIStates.CHARGING);
                }
                // If player is in attack range, attack
                else if (distanceToPlayer <= this.attackRange &&
                         this.timeSinceLastAttack >= this.attackCooldown) {
                    this.setState(AIStates.ATTACKING);
                    // Set attack state duration to match animation
                    this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || 2.4;
                    this.attackStateTimer = this.attackStateDuration;
                }
                // If we've reached a good position, go idle or circle
                else if (distanceToPlayer <= this.preferredRange) {
                    if (Math.random() < this.circleChance) {
                        this.setState(AIStates.STRAFING); // Use strafing for circling
                    } else {
                        this.setState(AIStates.IDLE);
                    }
                }
                break;

            case AIStates.CHARGING:
                // If charge timer expired, go to attacking if in range
                if (this.chargeTimer <= 0) {
                    if (distanceToPlayer <= this.attackRange) {
                        this.setState(AIStates.ATTACKING);
                        // Set attack state duration to match animation
                        this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || 2.4;
                        this.attackStateTimer = this.attackStateDuration;
                    } else {
                        this.setState(AIStates.MOVING);
                    }
                }
                // If we've reached attack range during charge, attack
                else if (distanceToPlayer <= this.attackRange) {
                    this.setState(AIStates.ATTACKING);
                    // Set attack state duration to match animation
                    this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || 2.4;
                    this.attackStateTimer = this.attackStateDuration;
                }
                break;

            case AIStates.ATTACKING:
                // Stay in ATTACKING state for the full animation duration
                this.attackStateTimer -= deltaTime;
                if (this.attackStateTimer <= 0) {
                    // Only transition after animation completes
                    this.timeSinceLastAttack = 0;
                    if (distanceToPlayer > this.preferredRange) {
                        this.setState(AIStates.MOVING);
                    } else if (Math.random() < this.blockChance) {
                        this.setState(AIStates.BLOCKING);
                    } else if (Math.random() < this.circleChance) {
                        this.setState(AIStates.STRAFING); // Use strafing for circling
                    } else {
                        this.setState(AIStates.IDLE);
                    }
                }
                // CRITICAL: If player moves too far away during attack, cancel and chase
                else if (distanceToPlayer > this.attackRange * 1.5) {
                    this.setState(AIStates.MOVING);
                    this.attackStateTimer = 0;
                }
                break;

            case AIStates.BLOCKING:
                // After blocking duration, go back to idle/circling
                if (this.blockTimer <= 0) {
                    if (Math.random() < this.circleChance) {
                        this.setState(AIStates.STRAFING); // Use strafing for circling
                    } else {
                        this.setState(AIStates.IDLE);
                    }
                }
                break;

            case AIStates.STRAFING: // Used for circling
                // If circling timer expired, go back to idle
                if (this.circleTimer <= 0) {
                    this.setState(AIStates.IDLE);
                }
                // If player is in attack range while circling, consider attacking
                else if (distanceToPlayer <= this.attackRange &&
                         this.timeSinceLastAttack >= this.attackCooldown) {
                    // Chance to attack while circling based on difficulty
                    const attackWhileCirclingChance = this._getScaledValue(0.6, 0.4, 0.8);
                    if (Math.random() < attackWhileCirclingChance) {
                        this.setState(AIStates.ATTACKING);
                        // Set attack state duration to match animation
                        this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || 2.4;
                        this.attackStateTimer = this.attackStateDuration;
                    }
                }
                // If player moves too far while circling, chase
                else if (distanceToPlayer > this.preferredRange * 1.5) {
                    this.setState(AIStates.MOVING);
                }
                break;
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Always face the player unless circling
        if (this.currentState !== AIStates.STRAFING) {
            // Make sure all enemies face the player, especially zombies and magma golems
            this._faceTarget(this.player.position);

            // Force zombies and magma golems to face the player
            const enemyType = this.enemy.userData.type;
            if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                // EMERGENCY FIX: Only update rotation if not frozen
                if (!this.enemy.userData.freezeRotation) {
                    // Force rotation update to ensure they face the player
                    const direction = this.player.position.clone().sub(this.enemy.position);
                    direction.y = 0; // Keep upright
                    this.enemy.lookAt(this.enemy.position.clone().add(direction));

                    // IMPORTANT: Store the direction to player for animation handlers to use
                    if (!this.enemy.userData.lastMoveDirection) {
                        this.enemy.userData.lastMoveDirection = new THREE.Vector3();
                    }
                    this.enemy.userData.lastMoveDirection.copy(direction);
                }
            }
        }

        // IMPORTANT: If we're hit reacting, make sure we set the state to HIT_REACTING
        // This ensures the animation handler plays the hit animation
        if (this.isHitReacting && this.currentState !== AIStates.HIT_REACTING) {
            this.setState(AIStates.HIT_REACTING);
        }

        // Execute behavior based on state
        switch (this.currentState) {
            case AIStates.IDLE:
                // Idle behavior (slight random movement)
                this._executeIdleBehavior(deltaTime);
                break;

            case AIStates.MOVING:
                // Move towards player
                this._moveTowardsPlayer(deltaTime, directionToPlayer);
                break;

            case AIStates.CHARGING:
                // Charge towards player
                this._executeChargeBehavior(deltaTime, directionToPlayer);
                break;

            case AIStates.ATTACKING:
                // After attacking, wait for animation to complete
                this.attackStateTimer -= deltaTime;

                // Check if player has moved too far away during attack
                if (distanceToPlayer > this.attackRange * 1.5) {
                    // Player moved out of range during attack, cancel and chase
                    this.setState(AIStates.MOVING);
                    return;
                }

                // Execute the attack at the start of the state
                if (this.attackStateTimer >= this.attackStateDuration - deltaTime) {
                    this._executeMeleeAttack();
                }

                if (this.attackStateTimer <= 0) {
                    // Only transition after animation completes
                    if (distanceToPlayer > this.preferredRange) {
                        // Player is too far away, chase them
                        this.setState(AIStates.MOVING);
                    } else if (Math.random() < this.blockChance) {
                        this.setState(AIStates.BLOCKING);
                    } else if (Math.random() < this.circleChance) {
                        this.setState(AIStates.STRAFING); // Use strafing for circling
                    } else {
                        this.setState(AIStates.IDLE);
                    }
                }
                break;

            case AIStates.BLOCKING:
                // Execute blocking behavior
                this._executeBlockingBehavior(deltaTime);
                break;

            case AIStates.STRAFING:
                // Circle around player
                this._executeCirclingBehavior(deltaTime, directionToPlayer);
                break;
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        switch (state) {
            case AIStates.CHARGING:
                // Initialize charging
                this.chargeTimer = this.chargeDuration;
                this.isCharging = true;
                break;

            case AIStates.ATTACKING:
                // Reset attack timer
                this.timeSinceLastAttack = 0;
                // Set attack state duration to match animation
                this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || 2.4;
                this.attackStateTimer = this.attackStateDuration;
                break;

            case AIStates.BLOCKING:
                // Initialize blocking
                this.blockTimer = this.blockDuration;
                this.isBlocking = true;
                break;

            case AIStates.STRAFING:
                // Initialize circling
                this.circleTimer = this.circleDuration;
                this.isCircling = true;
                // 50% chance to change circle direction
                if (Math.random() < 0.5) {
                    this.circleDirection *= -1;
                }
                break;
        }
    }

    /**
     * Move towards player
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _moveTowardsPlayer(deltaTime, directionToPlayer) {
        // CRITICAL FIX: Don't move if knocked back
        if (this.isKnockedBack) return;

        // Calculate move amount
        const moveSpeed = this.enemyData.speed;
        const moveAmount = moveSpeed * deltaTime;

        // Calculate new position
        const moveVector = directionToPlayer.clone().multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Temporarily disable collision detection for regular movement
        let canMove = true;
        // Only check floor bounds, not collisions with objects

        // Apply movement if no collision
        if (canMove) {
            // Special handling for zombies and magma golems
            const enemyType = this.enemy.userData.type;
            if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                // IMPORTANT: Make sure the enemy faces the direction of movement BEFORE moving
                // This ensures the model is properly oriented
                if (directionToPlayer.lengthSq() > 0.001 && !this.enemy.userData.freezeRotation) {
                    // EMERGENCY FIX: Only update rotation if not frozen
                    // Create a normalized direction vector in the XZ plane
                    const moveDir = new THREE.Vector3(directionToPlayer.x, 0, directionToPlayer.z).normalize();
                    // Calculate the target position to look at
                    const targetPos = this.enemy.position.clone().add(moveDir);
                    // Make the enemy look at the target position
                    this.enemy.lookAt(targetPos);
                }

                // Force position update for these enemy types
                // This ensures the position change takes effect regardless of animation handlers
                this.enemy.position.x = newPosition.x;
                this.enemy.position.z = newPosition.z;
                // Don't modify Y position to avoid floor collision issues

                // Store the position in userData to prevent animation handlers from overriding it
                if (!this.enemy.userData.lastAIPosition) {
                    this.enemy.userData.lastAIPosition = new THREE.Vector3();
                }
                this.enemy.userData.lastAIPosition.copy(this.enemy.position);

                // Store the movement direction for animation handlers to use
                if (!this.enemy.userData.lastMoveDirection) {
                    this.enemy.userData.lastMoveDirection = new THREE.Vector3();
                }
                this.enemy.userData.lastMoveDirection.copy(directionToPlayer);
            } else {
                // Normal position update for other enemy types
                this.enemy.position.copy(newPosition);
            }
        }
    }

    /**
     * Execute charge behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeChargeBehavior(deltaTime, directionToPlayer) {
        // CRITICAL FIX: Don't move if knocked back
        if (this.isKnockedBack) return;

        // Update charge timer
        this.chargeTimer -= deltaTime;

        // Calculate move amount (faster when charging)
        const moveAmount = this.chargeSpeed * deltaTime;

        // Calculate new position
        const moveVector = directionToPlayer.clone().multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Temporarily disable collision detection for regular movement
        let canMove = true;
        // Only check floor bounds, not collisions with objects

        // Apply movement if no collision
        if (canMove) {
            // Special handling for zombies and magma golems
            const enemyType = this.enemy.userData.type;
            if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                // IMPORTANT: Make sure the enemy faces the direction of movement BEFORE moving
                // This ensures the model is properly oriented
                if (directionToPlayer.lengthSq() > 0.001 && !this.enemy.userData.freezeRotation) {
                    // EMERGENCY FIX: Only update rotation if not frozen
                    // Create a normalized direction vector in the XZ plane
                    const moveDir = new THREE.Vector3(directionToPlayer.x, 0, directionToPlayer.z).normalize();
                    // Calculate the target position to look at
                    const targetPos = this.enemy.position.clone().add(moveDir);
                    // Make the enemy look at the target position
                    this.enemy.lookAt(targetPos);
                }

                // Force position update for these enemy types
                // This ensures the position change takes effect regardless of animation handlers
                this.enemy.position.x = newPosition.x;
                this.enemy.position.z = newPosition.z;
                // Don't modify Y position to avoid floor collision issues

                // Store the position in userData to prevent animation handlers from overriding it
                if (!this.enemy.userData.lastAIPosition) {
                    this.enemy.userData.lastAIPosition = new THREE.Vector3();
                }
                this.enemy.userData.lastAIPosition.copy(this.enemy.position);

                // Store the movement direction for animation handlers to use
                if (!this.enemy.userData.lastMoveDirection) {
                    this.enemy.userData.lastMoveDirection = new THREE.Vector3();
                }
                this.enemy.userData.lastMoveDirection.copy(directionToPlayer);
            } else {
                // Normal position update for other enemy types
                this.enemy.position.copy(newPosition);
            }
        }
    }

    /**
     * Execute melee attack
     * @private
     */
    _executeMeleeAttack() {
        if (!this.player) return;

        // Get attack data from enemy model if available
        const attackHitbox = this.enemy.userData.attackHitbox || {
            radius: this.attackRange,
            damage: this.enemyData.meleeDamage || 4,
            knockback: 5.0
        };

        // Emit attack event (to be handled by DungeonHandler)
        if (this.enemy.userData.onMeleeAttack) {
            this.enemy.userData.onMeleeAttack(attackHitbox);
        }
    }

    /**
     * Execute blocking behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeBlockingBehavior(deltaTime) {
        // Update block timer
        this.blockTimer -= deltaTime;

        // No movement while blocking, just face player
        // Could add slight backward movement if desired
    }

    /**
     * Execute circling behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeCirclingBehavior(deltaTime, directionToPlayer) {
        // CRITICAL FIX: Don't move if knocked back
        if (this.isKnockedBack) return;

        // Update circle timer
        this.circleTimer -= deltaTime;

        // Calculate perpendicular direction (for circling)
        const perpDirection = new THREE.Vector3(-directionToPlayer.z, 0, directionToPlayer.x);
        perpDirection.normalize().multiplyScalar(this.circleDirection);

        // Add a slight inward component to maintain distance
        const inwardDirection = directionToPlayer.clone().multiplyScalar(0.2);
        const circleDirection = perpDirection.clone().add(inwardDirection).normalize();

        // Calculate move amount
        const circleSpeed = this.enemyData.speed * 0.8; // Slightly slower when circling
        const moveAmount = circleSpeed * deltaTime;

        // Calculate new position
        const moveVector = circleDirection.multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Temporarily disable collision detection for regular movement
        let canMove = true;
        // Only check floor bounds, not collisions with objects

        // Apply movement if no collision
        if (canMove) {
            // Special handling for zombies and magma golems
            const enemyType = this.enemy.userData.type;
            if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                // IMPORTANT: Make sure the enemy faces the direction of movement BEFORE moving
                // This ensures the model is properly oriented
                if (circleDirection.lengthSq() > 0.001 && !this.enemy.userData.freezeRotation) {
                    // EMERGENCY FIX: Only update rotation if not frozen
                    // Create a normalized direction vector in the XZ plane
                    const moveDir = new THREE.Vector3(circleDirection.x, 0, circleDirection.z).normalize();
                    // Calculate the target position to look at
                    const targetPos = this.enemy.position.clone().add(moveDir);
                    // Make the enemy look at the target position
                    this.enemy.lookAt(targetPos);
                }

                // Force position update for these enemy types
                // This ensures the position change takes effect regardless of animation handlers
                this.enemy.position.x = newPosition.x;
                this.enemy.position.z = newPosition.z;
                // Don't modify Y position to avoid floor collision issues

                // Store the position in userData to prevent animation handlers from overriding it
                if (!this.enemy.userData.lastAIPosition) {
                    this.enemy.userData.lastAIPosition = new THREE.Vector3();
                }
                this.enemy.userData.lastAIPosition.copy(this.enemy.position);

                // Store the movement direction for animation handlers to use
                if (!this.enemy.userData.lastMoveDirection) {
                    this.enemy.userData.lastMoveDirection = new THREE.Vector3();
                }
                this.enemy.userData.lastMoveDirection.copy(circleDirection);
            } else {
                // Normal position update for other enemy types
                this.enemy.position.copy(newPosition);
            }
        }

        // Face slightly ahead of movement direction for more natural look
        const lookDirection = circleDirection.clone().add(directionToPlayer).normalize();
        this._faceTarget(this.enemy.position.clone().add(lookDirection));
    }

    /**
     * Predict player movement for attack timing
     * @returns {THREE.Vector3} - Predicted player position
     * @private
     */
    _predictPlayerMovement() {
        if (!this.player) return null;

        // Simple prediction based on current velocity
        const predictedPosition = this.player.position.clone();

        // If player has velocity, predict movement
        if (this.player.velocity) {
            const predictionTime = this._getScaledValue(0.3, 0.1, 0.5); // Shorter for melee
            predictedPosition.add(this.player.velocity.clone().multiplyScalar(predictionTime));
        }

        return predictedPosition;
    }
}
