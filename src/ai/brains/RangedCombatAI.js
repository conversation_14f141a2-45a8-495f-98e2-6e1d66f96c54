/**
 * Ranged Combat AI implementation
 * Completely rebuilt for reliability with skeleton archers
 */
import * as THREE from 'three';
import { AIBrain } from '../AIBrain.js';
import { AIStates } from '../AIStates.js';
import { groupCoordinator } from '../GroupCoordinator.js';

export class RangedCombatAI extends AIBrain {
    /**
     * Constructor for Ranged Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Ranged specific parameters
        this.preferredRange = enemyData.preferredRange || 8.0;
        this.minRange = enemyData.moveAwayRange || 2.0;
        this.maxRange = enemyData.maxRange || 30.0;

        // Log the actual ranges being used
        console.log(`[RangedCombatAI] Enemy ${enemy.name} ranges: preferred=${this.preferredRange.toFixed(1)}, min=${this.minRange.toFixed(1)}, max=${this.maxRange.toFixed(1)}`);

        // Movement parameters
        this.moveSpeed = enemyData.baseSpeed || 2.5;
        this.moveTimer = 0;
        this.moveDuration = 1.0 + Math.random() * 1.0; // 1-2 seconds of movement
        this.moveDirection = new THREE.Vector3();

        // Shooting parameters
        this.shootCooldown = enemyData.attackCooldown || 0.3;
        this.shootTimer = 0;
        this.aimDuration = 0.1; // Very short aiming time
        this.aimTimer = 0;
        this.isAiming = false;

        // Register with group coordinator
        groupCoordinator.registerEnemy(enemy, this);

        // Initialize state
        this.setState(AIStates.IDLE);
    }

    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Update timers
        if (this.moveTimer > 0) this.moveTimer -= deltaTime;
        if (this.shootTimer > 0) this.shootTimer -= deltaTime;
        if (this.isAiming) this.aimTimer += deltaTime;

        // Log state occasionally
        if (Math.random() < 0.05) {
            console.log(`[RangedCombatAI] ${this.enemy.name} - State: ${this.currentState}, Distance: ${distanceToPlayer.toFixed(1)}, ShootTimer: ${this.shootTimer.toFixed(1)}/${this.shootCooldown.toFixed(1)}`);
        }

        // SIMPLIFIED STATE MACHINE
        switch (this.currentState) {
            case AIStates.IDLE:
                // If player is in range, decide what to do
                if (distanceToPlayer <= this.maxRange) {
                    // If we can shoot, start aiming
                    if (this.shootTimer <= 0 && distanceToPlayer >= this.minRange) {
                        console.log(`[RangedCombatAI] ${this.enemy.name} - Starting to aim at player at distance ${distanceToPlayer.toFixed(1)}`);
                        this.setState(AIStates.AIMING);
                    }
                    // If player is too close, move away
                    else if (distanceToPlayer < this.minRange) {
                        console.log(`[RangedCombatAI] ${this.enemy.name} - Player too close, moving away`);
                        this.setState(AIStates.FLEEING);
                    }
                    // Otherwise, move to get a better position
                    else {
                        console.log(`[RangedCombatAI] ${this.enemy.name} - Moving to better position`);
                        this.setState(AIStates.MOVING);
                    }
                }
                // If player is out of range but visible, move closer
                else if (this._canSeePlayer()) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Player out of range, moving closer`);
                    this.setState(AIStates.MOVING);
                }
                break;

            case AIStates.MOVING:
                // If we can shoot, start aiming
                if (this.shootTimer <= 0 && distanceToPlayer >= this.minRange && distanceToPlayer <= this.maxRange) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Can shoot while moving, starting to aim`);
                    this.setState(AIStates.AIMING);
                }
                // If player is too close, move away
                else if (distanceToPlayer < this.minRange) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Player too close while moving, fleeing`);
                    this.setState(AIStates.FLEEING);
                }
                // If move timer expired, go back to idle
                else if (this.moveTimer <= 0) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Move complete, going idle`);
                    this.setState(AIStates.IDLE);
                }
                break;

            case AIStates.AIMING:
                // If aiming is complete, shoot
                if (this.aimTimer >= this.aimDuration) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Aim complete, shooting`);
                    this._executeShoot();
                    this.setState(AIStates.IDLE);
                }
                break;

            case AIStates.FLEEING:
                // If we've moved far enough away, go back to idle
                if (distanceToPlayer >= this.preferredRange) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Reached safe distance, going idle`);
                    this.setState(AIStates.IDLE);
                }
                // If we can shoot while fleeing, do it
                else if (this.shootTimer <= 0 && distanceToPlayer >= this.minRange) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Can shoot while fleeing, starting to aim`);
                    this.setState(AIStates.AIMING);
                }
                break;
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Always face the player unless fleeing
        if (this.currentState !== AIStates.FLEEING && this.player) {
            this._faceTarget(this.player.position);
        }

        // Execute behavior based on state
        switch (this.currentState) {
            case AIStates.IDLE:
                // Just stand still
                break;

            case AIStates.MOVING:
                this._executeMoveToPreferredRange(deltaTime, distanceToPlayer, directionToPlayer);
                break;

            case AIStates.AIMING:
                // Just stand still while aiming
                break;

            case AIStates.FLEEING:
                this._executeFleeingBehavior(deltaTime, directionToPlayer);
                break;
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        console.log(`[RangedCombatAI] ${this.enemy.name} - Entering state: ${state}`);

        switch (state) {
            case AIStates.IDLE:
                // No special initialization
                break;

            case AIStates.MOVING:
                // Set move timer
                this.moveTimer = this.moveDuration;
                // Choose random move direction
                this._chooseRandomMoveDirection();
                break;

            case AIStates.AIMING:
                // Reset aim timer
                this.aimTimer = 0;
                this.isAiming = true;
                break;

            case AIStates.FLEEING:
                // No special initialization
                break;
        }
    }

    /**
     * Choose a random movement direction
     * @private
     */
    _chooseRandomMoveDirection() {
        if (!this.player) return;

        // Get direction to player
        const dirToPlayer = this.player.position.clone().sub(this.enemy.position).normalize();

        // Calculate distance to player
        const distanceToPlayer = this.enemy.position.distanceTo(this.player.position);

        // Determine move direction based on distance
        if (distanceToPlayer < this.preferredRange - 1.0) {
            // Too close, move away slightly
            this.moveDirection = dirToPlayer.clone().multiplyScalar(-1);
            // Add slight randomness
            this.moveDirection.x += (Math.random() - 0.5) * 0.3;
            this.moveDirection.z += (Math.random() - 0.5) * 0.3;
        } else if (distanceToPlayer > this.preferredRange + 1.0) {
            // Too far, move closer
            this.moveDirection = dirToPlayer.clone();
            // Add slight randomness
            this.moveDirection.x += (Math.random() - 0.5) * 0.3;
            this.moveDirection.z += (Math.random() - 0.5) * 0.3;
        } else {
            // At good distance, strafe perpendicular to player
            const perpDirection = new THREE.Vector3(-dirToPlayer.z, 0, dirToPlayer.x);
            this.moveDirection = perpDirection.normalize();
            // Randomly choose direction
            if (Math.random() < 0.5) {
                this.moveDirection.multiplyScalar(-1);
            }
        }

        // Normalize the direction
        this.moveDirection.normalize();
    }

    /**
     * Execute movement to preferred range
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeMoveToPreferredRange(deltaTime, distanceToPlayer, directionToPlayer) {
        if (!this.player) return;

        // Calculate move amount
        const moveAmount = this.moveSpeed * deltaTime;

        // Calculate new position
        const moveVector = this.moveDirection.clone().multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check for collisions with walls and objects
        let canMove = true;
        if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a sphere representing the enemy's collision volume
            const enemyRadius = this.enemyData.size || 0.5;
            const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

            // Create a box3 for more accurate collision detection
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with all objects
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                // CRITICAL FIX: Skip floor objects
                // Check if this is a floor object by name or position
                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );

                // Also check if object is flat and horizontal (likely a floor)
                const objBox = new THREE.Box3().setFromObject(obj);
                const objHeight = objBox.max.y - objBox.min.y;

                // Check if object is at or below the enemy's feet
                const isAtFeetLevel = objBox.max.y <= (this.enemy.position.y + 0.1);

                // Check if object is very thin in Y direction (horizontal plane)
                const isHorizontalPlane = objHeight < 0.5;

                // Combined check for floor-like objects
                const isProbablyFloor = isFloor || (isHorizontalPlane && isAtFeetLevel);

                // Add more detailed logging to understand what's happening
                if (Math.random() < 0.01) { // Only log occasionally to avoid spam
                    console.log(`[RangedCombatAI] Collision object: ${obj.name || 'unnamed'}, height: ${objHeight.toFixed(2)}, maxY: ${objBox.max.y.toFixed(2)}, enemyY: ${this.enemy.position.y.toFixed(2)}, isFloor: ${isFloor}, isHorizontalPlane: ${isHorizontalPlane}, isAtFeetLevel: ${isAtFeetLevel}, isProbablyFloor: ${isProbablyFloor}`);
                }

                // Skip floor objects
                if (isProbablyFloor) {
                    continue;
                }

                try {
                    // More accurate collision check using box intersection
                    if (enemyBox.intersectsBox(objBox)) {
                        console.log(`[RangedCombatAI] ${this.enemy.name} - Collision detected with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }

                    // Additional point-based check for thin walls
                    if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                        console.log(`[RangedCombatAI] ${this.enemy.name} - Point collision detected with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    console.error(`[RangedCombatAI] Error checking collision with object: ${obj.name || 'unnamed'}`, error);
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
        } else {
            // If collision, choose a new random direction
            this._chooseRandomMoveDirection();
        }
    }

    /**
     * Execute fleeing behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeFleeingBehavior(deltaTime, directionToPlayer) {
        if (!this.player) return;

        // Move away from player
        const fleeDirection = directionToPlayer.clone().multiplyScalar(-1);

        // Add some randomness to fleeing direction
        const randomAngle = (Math.random() - 0.5) * Math.PI / 6; // +/- 30 degrees
        const randomizedDirection = new THREE.Vector3(
            fleeDirection.x * Math.cos(randomAngle) - fleeDirection.z * Math.sin(randomAngle),
            0,
            fleeDirection.x * Math.sin(randomAngle) + fleeDirection.z * Math.cos(randomAngle)
        );

        // Calculate move amount
        const fleeSpeed = this.moveSpeed * 1.2; // Slightly faster when fleeing
        const moveAmount = fleeSpeed * deltaTime;

        // Calculate new position
        const moveVector = randomizedDirection.normalize().multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check for collisions with walls and objects
        let canMove = true;
        if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a sphere representing the enemy's collision volume
            const enemyRadius = this.enemyData.size || 0.5;
            const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

            // Create a box3 for more accurate collision detection
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with all objects
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                // CRITICAL FIX: Skip floor objects
                // Check if this is a floor object by name or position
                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );

                // Also check if object is flat and horizontal (likely a floor)
                const objBox = new THREE.Box3().setFromObject(obj);
                const objHeight = objBox.max.y - objBox.min.y;

                // Check if object is at or below the enemy's feet
                const isAtFeetLevel = objBox.max.y <= (this.enemy.position.y + 0.1);

                // Check if object is very thin in Y direction (horizontal plane)
                const isHorizontalPlane = objHeight < 0.5;

                // Combined check for floor-like objects
                const isProbablyFloor = isFloor || (isHorizontalPlane && isAtFeetLevel);

                // Add more detailed logging to understand what's happening
                if (Math.random() < 0.01) { // Only log occasionally to avoid spam
                    console.log(`[RangedCombatAI] Fleeing collision object: ${obj.name || 'unnamed'}, height: ${objHeight.toFixed(2)}, maxY: ${objBox.max.y.toFixed(2)}, enemyY: ${this.enemy.position.y.toFixed(2)}, isProbablyFloor: ${isProbablyFloor}`);
                }

                // Skip floor objects
                if (isProbablyFloor) {
                    continue;
                }

                try {
                    // More accurate collision check using box intersection
                    if (enemyBox.intersectsBox(objBox)) {
                        console.log(`[RangedCombatAI] ${this.enemy.name} - Collision detected while fleeing with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }

                    // Additional point-based check for thin walls
                    if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                        console.log(`[RangedCombatAI] ${this.enemy.name} - Point collision detected while fleeing with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    console.error(`[RangedCombatAI] Error checking collision while fleeing with object: ${obj.name || 'unnamed'}`, error);
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
            // Face away from player when fleeing
            this._faceTarget(this.enemy.position.clone().add(randomizedDirection));
        } else {
            // If collision, try a different direction
            // Choose a perpendicular direction to avoid getting stuck
            const perpendicularDir = new THREE.Vector3(-randomizedDirection.z, 0, randomizedDirection.x);

            // Try moving in this new direction
            const altMoveVector = perpendicularDir.normalize().multiplyScalar(moveAmount * 0.5);
            const altPosition = this.enemy.position.clone().add(altMoveVector);

            // Apply this alternative movement without further collision checks
            // This helps prevent getting stuck against walls
            this.enemy.position.copy(altPosition);
            this._faceTarget(this.enemy.position.clone().add(perpendicularDir));
        }
    }

    /**
     * Execute shooting behavior
     * @private
     */
    _executeShoot() {
        console.log(`[RangedCombatAI] ${this.enemy.name} - Executing shoot`);

        // Reset shooting cooldown
        this.shootTimer = this.shootCooldown;
        this.isAiming = false;

        if (!this.player) {
            console.log(`[RangedCombatAI] ${this.enemy.name} - Cannot shoot: player is null`);
            return;
        }

        // Calculate direction to player
        const shootDirection = this.player.position.clone().sub(this.enemy.position).normalize();

        // Emit shoot event (to be handled by DungeonHandler)
        if (this.enemy.userData.onShoot) {
            console.log(`[RangedCombatAI] ${this.enemy.name} - Calling onShoot with direction: ${shootDirection.x.toFixed(2)},${shootDirection.y.toFixed(2)},${shootDirection.z.toFixed(2)}`);
            this.enemy.userData.onShoot(shootDirection);
        } else {
            console.log(`[RangedCombatAI] ${this.enemy.name} - No onShoot handler found`);
        }

        // Force a priority update
        if (this.enemy.userData) {
            this.enemy.userData.priorityUpdate = true;
        }
    }
}
