/**
 * BossControllerBufferedIntegration.js
 * 
 * This file integrates the BufferedMusicController with the BossController
 * to provide precise timing for projectile patterns and screen shakes.
 */

import { BufferedMusicController } from '../../audio/BufferedMusicController.js';

/**
 * Integrate buffered music controller with the BossController
 * 
 * @param {BossController} bossController - Your existing BossController instance
 * @param {String} dataPath - Path to the comprehensive data JSON file
 * @param {Object} options - Additional options
 * @returns {Promise} - Resolves when integration is complete
 */
export async function integrateBufferedController(bossController, dataPath, options = {}) {
  try {
    console.log(`[BossControllerBufferedIntegration] Loading data from: ${dataPath}`);
    
    // Default options
    const controllerOptions = {
      debug: bossController.options.debugMode,
      bufferTimeMs: options.bufferTimeMs || 200, // Default 200ms buffer
      speedMultiplier: options.speedMultiplier || 1.2, // Default 1.2x speed
      ...options
    };
    
    console.log(`[BossControllerBufferedIntegration] Using buffer time: ${controllerOptions.bufferTimeMs}ms, speed multiplier: ${controllerOptions.speedMultiplier}x`);
    
    // Create the buffered controller
    const bufferedController = new BufferedMusicController(controllerOptions);
    
    // Load the data
    const loaded = await bufferedController.loadData(dataPath);
    if (!loaded) {
      throw new Error("Failed to load data");
    }
    
    // Set up the pattern change callback
    bufferedController.setPatternChangeCallback((patternInfo) => {
      // Call the original timeline pattern trigger callback
      if (bossController._onTimelinePatternTrigger) {
        bossController._onTimelinePatternTrigger(patternInfo);
        console.log(`[BossControllerBufferedIntegration] Triggered pattern: ${patternInfo.pattern}, projectile: ${patternInfo.projectileType}, speed: ${patternInfo.speedMultiplier.toFixed(2)}x`);
      }
    });
    
    // Set up the screen shake callback
    bufferedController.setScreenShakeCallback((intensity, duration) => {
      // Trigger camera shake directly
      if (bossController.dungeonHandler && bossController.dungeonHandler.cameraShake) {
        bossController.dungeonHandler.cameraShake(intensity, duration);
        console.log(`[BossControllerBufferedIntegration] Triggered screen shake with intensity ${intensity}`);
      } else if (bossController.enemy && bossController.enemy.userData && 
                 bossController.enemy.userData.dungeonHandler && 
                 bossController.enemy.userData.dungeonHandler.cameraShake) {
        bossController.enemy.userData.dungeonHandler.cameraShake(intensity, duration);
        console.log(`[BossControllerBufferedIntegration] Triggered screen shake via enemy with intensity ${intensity}`);
      } else {
        console.warn("[BossControllerBufferedIntegration] Cannot trigger screen shake - no dungeonHandler available");
      }
    });
    
    // Store the controller in the boss controller
    bossController.bufferedController = bufferedController;
    
    // Override the update method to use the buffered controller
    const originalUpdate = bossController.update;
    bossController.update = function(deltaTime) {
      // Call the original update method
      originalUpdate.call(bossController, deltaTime);
      
      // Update the buffered controller
      if (bossController.active && bossController.bufferedController) {
        // Get current music time from the audio manager
        const currentTime = bossController.musicAnalyzer.getCurrentTime ? 
          bossController.musicAnalyzer.getCurrentTime() : 0;
        
        // Update the buffered controller
        bossController.bufferedController.update(currentTime);
      }
    };
    
    // Override the start method to start the buffered controller
    const originalStart = bossController.start;
    bossController.start = async function() {
      // Call the original start method
      const result = await originalStart.call(bossController);
      
      // Start the buffered controller
      if (result && bossController.bufferedController) {
        bossController.bufferedController.start();
      }
      
      return result;
    };
    
    // Override the stop method to stop the buffered controller
    const originalStop = bossController.stop;
    bossController.stop = function() {
      // Call the original stop method
      originalStop.call(bossController);
      
      // Stop the buffered controller
      if (bossController.bufferedController) {
        bossController.bufferedController.stop();
      }
    };
    
    // Disable automatic screen shakes in the BossController
    // This is done by overriding the _onTimelineEntryChange method
    bossController._onTimelineEntryChange = function(entry) {
      if (bossController.options.debugMode) {
        console.log(`[BossController] Timeline entry changed: ${entry.description || 'Unnamed'} (${entry.startTime}s - ${entry.endTime}s)`);
      }
      
      // We're not triggering camera shake here anymore
      // Instead, the BufferedMusicController handles shakes at specific timestamps
    };
    
    console.log("[BossControllerBufferedIntegration] Buffered integration complete");
    return true;
    
  } catch (error) {
    console.error("[BossControllerBufferedIntegration] Integration failed:", error);
    return false;
  }
}
