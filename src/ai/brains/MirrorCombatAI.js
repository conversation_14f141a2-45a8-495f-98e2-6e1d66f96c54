/**
 * Mirror Combat AI implementation
 * Mirrors the player's movements and attack patterns
 */
import * as THREE from 'three';
import { AIBrain } from '../AIBrain.js';
import { AIStates, TransitionConditions, checkCondition } from '../AIStates.js';

export class MirrorCombatAI extends AIBrain {
    /**
     * Constructor for Mirror Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Mirror specific parameters
        this.preferredRange = this._getScaledValue(enemyData.preferredRange || 4.0);
        this.mirrorDistance = this._getScaledValue(enemyData.mirrorDistance || 5.0);
        this.mirrorAccuracy = this._getScaledValue(0.7, 0.5, 0.9); // Higher with difficulty

        // Player action tracking
        this.playerActions = [];
        this.maxActionMemory = this._getScaledValue(5, 3, 10); // More memory with higher difficulty
        this.lastPlayerPosition = player ? player.position.clone() : new THREE.Vector3();
        this.lastPlayerVelocity = new THREE.Vector3();
        this.lastPlayerFacing = new THREE.Vector3(0, 0, -1); // Default forward

        // Mirroring behavior
        this.isMirroring = false;
        this.mirrorTimer = 0;
        this.mirrorDuration = this._getScaledValue(3.0, 2.0, 5.0);
        this.mirrorOffset = new THREE.Vector3(); // Offset from player's position

        // Action delay (lower with higher difficulty)
        this.actionDelay = this._getScaledValue(0.5, 0.8, 0.2);
        this.actionQueue = []; // Queue of actions to perform

        // Initialize state
        this.setState(AIStates.IDLE);
    }

    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Track player actions
        this._trackPlayerActions(deltaTime);

        // Process action queue
        this._processActionQueue(deltaTime);

        // Handle state transitions
        switch (this.currentState) {
            case AIStates.IDLE:
                // Transition to MIRRORING if player is visible
                if (this._canSeePlayer()) {
                    this.setState(AIStates.MIRRORING);
                }
                break;

            case AIStates.MIRRORING:
                // If player is not visible, go back to idle
                if (!this._canSeePlayer()) {
                    this.setState(AIStates.IDLE);
                }
                // If mirroring timer expired, reset it
                else if (this.mirrorTimer <= 0) {
                    this.mirrorTimer = this.mirrorDuration;
                    // Recalculate mirror offset
                    this._calculateMirrorOffset();
                }
                break;

            case AIStates.ATTACKING:
                // After attacking, go back to mirroring
                this.setState(AIStates.MIRRORING);
                break;

            case AIStates.SHOOTING:
                // After shooting, go back to mirroring
                this.setState(AIStates.MIRRORING);
                break;

            case AIStates.DODGING:
                // After dodging, go back to mirroring
                if (this.dodgeTimer <= 0) {
                    this.setState(AIStates.MIRRORING);
                }
                break;
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Execute behavior based on state
        switch (this.currentState) {
            case AIStates.IDLE:
                // Idle behavior (slight random movement)
                this._executeIdleBehavior(deltaTime);
                break;

            case AIStates.MIRRORING:
                // Mirror player's movements
                this._executeMirroringBehavior(deltaTime);
                break;

            case AIStates.ATTACKING:
                // Execute melee attack
                this._executeMeleeAttack();
                break;

            case AIStates.SHOOTING:
                // Execute ranged attack
                this._executeShoot();
                break;

            case AIStates.DODGING:
                // Execute dodge
                this._executeDodgeBehavior(deltaTime);
                break;
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        switch (state) {
            case AIStates.MIRRORING:
                // Initialize mirroring
                if (!this.isMirroring) {
                    this.isMirroring = true;
                    this.mirrorTimer = this.mirrorDuration;
                    this._calculateMirrorOffset();
                }
                break;

            case AIStates.ATTACKING:
                // Reset attack timer
                this.timeSinceLastAttack = 0;
                break;

            case AIStates.SHOOTING:
                // Reset attack timer
                this.timeSinceLastAttack = 0;
                break;

            case AIStates.DODGING:
                // Initialize dodging
                this.isDodging = true;
                this.dodgeTimer = this.dodgeDuration;
                break;
        }
    }

    /**
     * Track player actions
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _trackPlayerActions(deltaTime) {
        if (!this.player) return;

        // Calculate player velocity
        const currentPosition = this.player.position.clone();
        const displacement = currentPosition.clone().sub(this.lastPlayerPosition);
        const velocity = displacement.clone().divideScalar(deltaTime);

        // Check if player has moved significantly
        const hasMoved = displacement.lengthSq() > 0.01;

        // Check if player has changed facing direction
        let hasTurned = false;
        let currentFacing = new THREE.Vector3(0, 0, -1);
        if (this.player.quaternion) {
            currentFacing.applyQuaternion(this.player.quaternion);
            currentFacing.y = 0;
            currentFacing.normalize();

            const lastFacing = this.lastPlayerFacing.clone();
            hasTurned = currentFacing.dot(lastFacing) < 0.9; // Significant direction change
        }

        // Check if player has attacked
        const hasAttacked = this.player.userData && this.player.userData.isAttacking;

        // Record action if significant
        if (hasMoved || hasTurned || hasAttacked) {
            const action = {
                type: hasAttacked ? 'attack' : (hasMoved ? 'move' : 'turn'),
                position: currentPosition.clone(),
                velocity: velocity.clone(),
                facing: currentFacing.clone(),
                timestamp: Date.now()
            };

            // Add to action queue with delay
            this.actionQueue.push({
                action: action,
                delay: this.actionDelay,
                executed: false
            });

            // Add to player actions history
            this.playerActions.push(action);
            if (this.playerActions.length > this.maxActionMemory) {
                this.playerActions.shift();
            }
        }

        // Update last position and facing
        this.lastPlayerPosition.copy(currentPosition);
        this.lastPlayerVelocity.copy(velocity);
        this.lastPlayerFacing.copy(currentFacing);
    }

    /**
     * Process action queue
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _processActionQueue(deltaTime) {
        // Process each action in queue
        for (let i = 0; i < this.actionQueue.length; i++) {
            const queuedAction = this.actionQueue[i];

            // Reduce delay
            queuedAction.delay -= deltaTime;

            // Execute action if delay expired and not already executed
            if (queuedAction.delay <= 0 && !queuedAction.executed) {
                this._executePlayerAction(queuedAction.action);
                queuedAction.executed = true;
            }
        }

        // Remove executed actions
        this.actionQueue = this.actionQueue.filter(qa => !qa.executed);
    }

    /**
     * Execute a mirrored player action
     * @param {Object} action - The player action to mirror
     * @private
     */
    _executePlayerAction(action) {
        switch (action.type) {
            case 'attack':
                // Mirror attack
                if (this.timeSinceLastAttack >= this.attackCooldown) {
                    // Determine if melee or ranged based on player's attack
                    const isMeleeAttack = true; // Placeholder, determine based on player attack type

                    if (isMeleeAttack) {
                        this.setState(AIStates.ATTACKING);
                    } else {
                        this.setState(AIStates.SHOOTING);
                    }
                }
                break;

            case 'move':
                // Mirror movement is handled in _executeMirroringBehavior
                break;

            case 'turn':
                // Mirror turning
                if (this.player) {
                    // Calculate mirrored facing direction
                    const mirroredFacing = action.facing.clone();
                    mirroredFacing.x *= -1; // Mirror horizontally

                    // Apply mirrored facing
                    this._faceTarget(this.enemy.position.clone().add(mirroredFacing));
                }
                break;
        }
    }

    /**
     * Calculate mirror offset
     * @private
     */
    _calculateMirrorOffset() {
        if (!this.player) return;

        // Calculate base offset (opposite side of player)
        const baseOffset = new THREE.Vector3(0, 0, this.mirrorDistance * 2);

        // Rotate offset based on player's facing direction
        if (this.player.quaternion) {
            baseOffset.applyQuaternion(this.player.quaternion);
        }

        // Add some randomness based on difficulty (lower difficulty = more randomness)
        const randomFactor = 1 - this.mirrorAccuracy;
        const randomOffset = new THREE.Vector3(
            (Math.random() - 0.5) * randomFactor * this.mirrorDistance,
            0,
            (Math.random() - 0.5) * randomFactor * this.mirrorDistance
        );

        // Combine offsets
        this.mirrorOffset.copy(baseOffset).add(randomOffset);
    }

    /**
     * Execute mirroring behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeMirroringBehavior(deltaTime) {
        if (!this.player) return;

        // Update mirror timer
        this.mirrorTimer -= deltaTime;

        // Calculate target position (mirrored from player)
        const targetPosition = this.player.position.clone().add(this.mirrorOffset);

        // Calculate direction to target
        const directionToTarget = targetPosition.clone().sub(this.enemy.position);
        const distanceToTarget = directionToTarget.length();
        directionToTarget.normalize();

        // Move towards target position
        if (distanceToTarget > 0.5) {
            const moveSpeed = this.enemyData.speed;
            const moveAmount = moveSpeed * deltaTime;
            const moveVector = directionToTarget.multiplyScalar(Math.min(moveAmount, distanceToTarget));

            // Calculate new position
            const newPosition = this.enemy.position.clone().add(moveVector);

            // Check if new position is within floor bounds
            if (this.floorBounds) {
                const minX = this.floorBounds.min.x + 1.0; // Add buffer
                const maxX = this.floorBounds.max.x - 1.0;
                const minZ = this.floorBounds.min.z + 1.0;
                const maxZ = this.floorBounds.max.z - 1.0;

                // Clamp position to floor bounds
                newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
                newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
            }

            // Check for collisions with walls
            let canMove = true;
            if (this.collisionObjects) {
                const enemyRadius = this.enemyData.size || 0.5;
                for (const obj of this.collisionObjects) {
                    const objBox = new THREE.Box3().setFromObject(obj);
                    const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

                    // Simple collision check: if enemy's bounding sphere intersects with object's bounding box
                    if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                        canMove = false;
                        break;
                    }
                }
            }

            // Apply movement if no collision
            if (canMove) {
                this.enemy.position.copy(newPosition);
            }
        }

        // Mirror player's facing direction
        if (this.player.quaternion) {
            // Get player's forward direction
            const playerForward = new THREE.Vector3(0, 0, -1).applyQuaternion(this.player.quaternion);

            // Mirror the direction
            const mirroredForward = new THREE.Vector3(-playerForward.x, playerForward.y, -playerForward.z);

            // Face in mirrored direction
            this._faceTarget(this.enemy.position.clone().add(mirroredForward));
        }
    }

    /**
     * Execute melee attack
     * @private
     */
    _executeMeleeAttack() {
        if (!this.player) return;

        // Emit attack event (to be handled by DungeonHandler)
        if (this.enemy.userData.onMeleeAttack) {
            this.enemy.userData.onMeleeAttack();
        }
    }

    /**
     * Execute ranged attack
     * @private
     */
    _executeShoot() {
        if (!this.player) return;

        // Calculate shoot direction (mirrored from player's facing)
        let shootDirection;
        if (this.player.quaternion) {
            // Get player's forward direction
            const playerForward = new THREE.Vector3(0, 0, -1).applyQuaternion(this.player.quaternion);

            // Mirror the direction
            shootDirection = new THREE.Vector3(-playerForward.x, playerForward.y, -playerForward.z);
        } else {
            // Fallback: shoot towards player
            shootDirection = this.player.position.clone().sub(this.enemy.position).normalize();
        }

        // Emit shoot event (to be handled by DungeonHandler)
        if (this.enemy.userData.onShoot) {
            this.enemy.userData.onShoot(shootDirection);
        }
    }

    /**
     * Execute dodge behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeDodgeBehavior(deltaTime) {
        // Update dodge timer
        this.dodgeTimer -= deltaTime;

        // Calculate dodge direction (mirrored from player's movement)
        let dodgeDirection;
        if (this.lastPlayerVelocity.lengthSq() > 0.01) {
            // Mirror player's movement direction
            dodgeDirection = new THREE.Vector3(
                -this.lastPlayerVelocity.x,
                0,
                -this.lastPlayerVelocity.z
            ).normalize();
        } else {
            // Fallback: dodge perpendicular to player direction
            const directionToPlayer = this.player.position.clone().sub(this.enemy.position).normalize();
            dodgeDirection = new THREE.Vector3(-directionToPlayer.z, 0, directionToPlayer.x);

            // Randomly choose left or right
            if (Math.random() < 0.5) {
                dodgeDirection.multiplyScalar(-1);
            }
        }

        // Calculate move amount
        const dodgeSpeed = this.enemyData.speed * 1.5;
        const moveAmount = dodgeSpeed * deltaTime;

        // Apply movement
        const moveVector = dodgeDirection.multiplyScalar(moveAmount);
        this.enemy.position.add(moveVector);

        // Face in movement direction
        this._faceTarget(this.enemy.position.clone().add(dodgeDirection));

        // End dodge if timer expired
        if (this.dodgeTimer <= 0) {
            this.isDodging = false;
        }
    }
}
