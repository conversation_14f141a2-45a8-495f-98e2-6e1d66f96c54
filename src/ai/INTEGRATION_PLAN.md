# AI System Integration Plan

This document outlines the steps needed to integrate the new AI system with the existing codebase.

## Overview

The integration will involve:
1. Updating DungeonHandler.js to use the new AI system
2. Moving projectile handling to the new projectile system
3. Adding knockback functionality
4. Implementing idle walking and natural movement
5. Ensuring animations work correctly

## Integration Steps

### 1. Update DungeonHandler.js

#### 1.1. Import New AI System

Add imports at the top of DungeonHandler.js:

```javascript
import { createAIBrain, updateAIBrain, applyKnockback } from '../ai/AIFactory.js';
import { AIStates } from '../ai/AIStates.js';
import { getProjectileType, createProjectileMesh } from '../projectiles/ProjectileTypes.js';
import { Projectile } from '../projectiles/Projectile.js';
```

#### 1.2. Modify Enemy Spawning

Update the `_spawnEnemy` method to create and assign an AI brain:

```javascript
// After creating the enemy mesh and setting userData
// Create AI brain
enemy.userData.aiBrain = createAIBrain(enemy, enemy.userData, this.scene, this.player);

// Set up event handlers for AI actions
enemy.userData.onShoot = (direction, projectileCount = 1) => {
    this._handleEnemyShoot(enemy, direction, projectileCount);
};

enemy.userData.onMeleeAttack = (damageMultiplier = 1.0) => {
    this._handleEnemyMeleeAttack(enemy, damageMultiplier);
};

enemy.userData.onPhaseChange = (phase) => {
    this._handleEnemyPhaseChange(enemy, phase);
};

enemy.userData.takeDamage = (damage) => {
    this._handleEnemyTakeDamage(enemy, damage);
};
```

#### 1.3. Replace AI Logic in Update Loop

Replace the existing AI logic in the update loop:

```javascript
// Replace the existing AI logic block
// --- AI Logic --- 
for (let i = 0; i < this.activeEnemies.length; i++) {
    const enemy = this.activeEnemies[i];
    const enemyData = enemy.userData;
    
    // Skip if no AI brain
    if (!enemyData.aiBrain) continue;
    
    // Update AI brain
    const stateData = updateAIBrain(
        enemyData.aiBrain,
        deltaTime,
        this.collisionObjects,
        this.floorBounds
    );
    
    // Store state data
    if (stateData) {
        this.enemyStateTimers.set(enemyData.id, stateData);
    }
    
    // Handle animations based on state
    this._updateEnemyAnimations(enemy, stateData);
}
```

#### 1.4. Add Helper Methods

Add new helper methods for handling AI actions:

```javascript
/**
 * Handle enemy shooting
 * @param {Object} enemy - The enemy object
 * @param {THREE.Vector3} direction - Direction to shoot
 * @param {Number} projectileCount - Number of projectiles to shoot
 * @private
 */
_handleEnemyShoot(enemy, direction, projectileCount = 1) {
    const enemyData = enemy.userData;
    const enemyDefinition = getEnemyData(enemyData.aiType);
    
    // Calculate start position
    let startPos = new THREE.Vector3();
    enemy.updateMatrixWorld(true);
    
    // Try to find bow or weapon
    const weapon = enemy.getObjectByName('SkeletonBow') || enemy.getObjectByName('Weapon');
    if (weapon) {
        // Get position from weapon
        startPos.setFromMatrixPosition(weapon.matrixWorld);
    } else {
        // Fallback to enemy position
        startPos.copy(enemy.position);
        startPos.y += (enemyDefinition?.size || 0.6) * 0.7;
    }
    
    // Spawn projectiles
    for (let i = 0; i < projectileCount; i++) {
        // Add slight variation for multiple projectiles
        let shootDir = direction.clone();
        if (projectileCount > 1) {
            const spreadAngle = (Math.random() - 0.5) * Math.PI / 8; // +/- 22.5 degrees
            const spreadMatrix = new THREE.Matrix4().makeRotationY(spreadAngle);
            shootDir.applyMatrix4(spreadMatrix);
        }
        
        // Spawn projectile
        this.spawnProjectile(startPos, shootDir, enemyDefinition);
    }
}

/**
 * Handle enemy melee attack
 * @param {Object} enemy - The enemy object
 * @param {Number} damageMultiplier - Damage multiplier
 * @private
 */
_handleEnemyMeleeAttack(enemy, damageMultiplier = 1.0) {
    const enemyData = enemy.userData;
    const enemyDefinition = getEnemyData(enemyData.aiType);
    
    // Check if player is in range
    if (!this.player) return;
    
    const distanceToPlayer = enemy.position.distanceTo(this.player.position);
    const attackRange = enemyDefinition.attackRange || 2.5;
    
    if (distanceToPlayer <= attackRange) {
        // Deal damage to player
        const damage = (enemyDefinition.meleeDamage || 5) * damageMultiplier;
        if (this.player.userData && this.player.userData.takeDamage) {
            this.player.userData.takeDamage(damage);
        }
    }
}

/**
 * Handle enemy phase change (for bosses)
 * @param {Object} enemy - The enemy object
 * @param {Number} phase - New phase
 * @private
 */
_handleEnemyPhaseChange(enemy, phase) {
    // Handle phase change effects
    console.log(`Enemy ${enemy.userData.id} entering phase ${phase}`);
    
    // TODO: Add visual effects, sound, etc.
}

/**
 * Handle enemy taking damage
 * @param {Object} enemy - The enemy object
 * @param {Number} damage - Damage amount
 * @private
 */
_handleEnemyTakeDamage(enemy, damage) {
    const enemyData = enemy.userData;
    
    // Apply damage
    enemyData.health -= damage;
    
    // Apply knockback from player
    if (this.player) {
        const knockbackDirection = enemy.position.clone().sub(this.player.position).normalize();
        const knockbackStrength = 5.0;
        
        if (enemyData.aiBrain) {
            applyKnockback(enemyData.aiBrain, knockbackDirection, knockbackStrength);
        }
    }
    
    // Update health bar if it exists
    if (enemyData.updateHealthBar) {
        enemyData.updateHealthBar();
    }
}

/**
 * Update enemy animations based on state
 * @param {Object} enemy - The enemy object
 * @param {Object} stateData - State data from AI brain
 * @private
 */
_updateEnemyAnimations(enemy, stateData) {
    if (!stateData) return;
    
    const state = stateData.state;
    const enemyData = enemy.userData;
    
    // Get limbs
    const leftLeg = enemy.getObjectByName('leftLeg');
    const rightLeg = enemy.getObjectByName('rightLeg');
    const leftArm = enemy.getObjectByName('leftArm');
    const rightArm = enemy.getObjectByName('rightArm');
    
    // Apply animations based on state
    switch (state) {
        case AIStates.IDLE:
            // Idle animation
            this._applyIdleAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
            break;
            
        case AIStates.MOVING:
        case AIStates.FLEEING:
        case AIStates.STRAFING:
            // Walking animation
            this._applyWalkAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
            break;
            
        case AIStates.AIMING:
            // Aiming animation
            this._applyAimAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
            break;
            
        case AIStates.ATTACKING:
            // Attack animation
            this._applyAttackAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
            break;
            
        case AIStates.SHOOTING:
            // Shooting animation
            this._applyShootAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
            break;
            
        case AIStates.DODGING:
            // Dodge animation
            this._applyDodgeAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
            break;
            
        case AIStates.HOVERING:
        case AIStates.SWOOPING:
        case AIStates.ASCENDING:
            // Flying animation
            this._applyFlyAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm);
            break;
    }
}

/**
 * Apply idle animation
 * @private
 */
_applyIdleAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm) {
    if (!leftLeg || !rightLeg) return;
    
    const time = this.sceneManager.clock.getElapsedTime();
    const idleSpeed = 1.5;
    const idleBobAmplitude = 0.05;
    const idleSwayAmplitude = Math.PI / 32;
    
    // Subtle idle movement
    const bobOffset = Math.sin(time * idleSpeed) * idleBobAmplitude;
    const swayOffset = Math.sin(time * idleSpeed * 0.7) * idleSwayAmplitude;
    
    // Apply to limbs
    if (leftLeg) leftLeg.rotation.x = swayOffset;
    if (rightLeg) rightLeg.rotation.x = -swayOffset;
    if (leftArm) leftArm.rotation.x = swayOffset * 1.5;
    if (rightArm) rightArm.rotation.x = -swayOffset * 1.5;
    
    // Add slight body movement
    enemy.position.y += bobOffset * 0.1;
}

/**
 * Apply walk animation
 * @private
 */
_applyWalkAnimation(enemy, leftLeg, rightLeg, leftArm, rightArm) {
    if (!leftLeg || !rightLeg) return;
    
    const enemyData = enemy.userData;
    const walkSpeed = enemyData.speed * 2.0;
    const walkAmplitude = Math.PI / 10;
    const time = this.sceneManager.clock.getElapsedTime();
    
    // Walk cycle
    if (leftLeg) leftLeg.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude;
    if (rightLeg) rightLeg.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude;
    
    // Arm swing
    if (leftArm) leftArm.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude * 0.5;
    if (rightArm) rightArm.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude * 0.5;
    
    // Add slight random offset to avoid robotic movement
    const randomOffset = (Math.random() - 0.5) * 0.01;
    enemy.position.y += randomOffset;
}

// Add other animation methods similarly
```

### 2. Update Projectile System

#### 2.1. Modify spawnProjectile Method

Update the `spawnProjectile` method to use the new projectile system:

```javascript
/**
 * Spawn a projectile
 * @param {THREE.Vector3} startPos - Start position
 * @param {THREE.Vector3} direction - Direction
 * @param {Object} ownerData - Owner data
 */
spawnProjectile(startPos, direction, ownerData) {
    // Determine if player or enemy projectile
    const isPlayerProjectile = !ownerData?.aiType;
    
    // Get projectile type
    let projectileType = isPlayerProjectile ? 'default_soul_orb' : (ownerData.projectileType || 'arrow');
    
    // Get projectile data
    const projectileData = getProjectileType(projectileType);
    
    // Create projectile mesh
    const projectileMesh = createProjectileMesh(projectileType, startPos);
    
    // Calculate velocity
    const speed = isPlayerProjectile ? projectileData.speed : (ownerData.projectileSpeed || projectileData.speed);
    const velocity = direction.clone().multiplyScalar(speed);
    
    // Create projectile instance
    const damage = isPlayerProjectile ? projectileData.damage : (ownerData.projectileDamage || projectileData.damage);
    const range = isPlayerProjectile ? projectileData.range : (ownerData.projectileRange || projectileData.range);
    const size = isPlayerProjectile ? projectileData.size : (ownerData.projectileSize || projectileData.size);
    
    // Create projectile
    const projectile = new Projectile(
        this.scene,
        startPos,
        velocity,
        damage,
        range,
        size,
        !isPlayerProjectile,
        projectileType
    );
    
    // Store projectile in mesh userData
    projectileMesh.userData = projectile;
    
    // Add impact handler
    projectileMesh.userData.onImpact = (position, effectType, targetType) => {
        this._handleProjectileImpact(position, effectType, targetType);
    };
    
    // Add to scene and active projectiles
    this.scene.add(projectileMesh);
    this.activeProjectiles.push(projectileMesh);
}

/**
 * Handle projectile impact
 * @param {THREE.Vector3} position - Impact position
 * @param {String} effectType - Effect type
 * @param {String} targetType - Target type
 * @private
 */
_handleProjectileImpact(position, effectType, targetType) {
    // TODO: Add impact effects (particles, sound, etc.)
    console.log(`Projectile impact: ${effectType} on ${targetType} at ${position.x}, ${position.y}, ${position.z}`);
}
```

### 3. Test and Debug

After implementing these changes, test the system with different enemy types and difficulty levels. Look for:

- Proper AI behavior based on type
- Correct animations
- Projectile spawning and impacts
- Knockback effects
- Performance with multiple enemies

## Future Enhancements

After the basic integration is complete, consider these enhancements:

1. Add more sophisticated projectile impact effects
2. Implement more complex boss behaviors
3. Add sound effects for different AI actions
4. Create more enemy types with unique behaviors
5. Implement difficulty scaling based on player progress
