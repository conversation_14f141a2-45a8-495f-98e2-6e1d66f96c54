/**
 * ZombieAnimationHandlerFix.js
 *
 * This module provides a function to fix zombie animation handler issues
 * by ensuring the animation handler is properly initialized and attached.
 */

import { SimpleZombieAnimationHandler } from './SimpleZombieAnimationHandler.js';

/**
 * Fix zombie animation handler issues
 * @param {Object} enemy - The zombie enemy object
 * @returns {Boolean} - True if fixed, false otherwise
 */
export function fixZombieAnimationHandler(enemy) {
    // Check if this is a zombie enemy
    const isZombie = enemy.name.includes('zombie') ||
                    enemy.name.includes('Zombie') ||
                    (enemy.userData && enemy.userData.type === 'zombie');

    if (!isZombie) {
        console.log(`[ZombieAnimationHandlerFix] Not a zombie enemy: ${enemy.name}`);
        return false;
    }

    // Check if animation handler already exists
    const hasAnimHandler = enemy.userData && enemy.userData.animationHandler;

    if (hasAnimHandler) {
        console.log(`[ZombieAnimationHandlerFix] Animation handler already exists for ${enemy.name}`);
        return false;
    }

    console.log(`[ZombieAnimationHandlerFix] Fixing animation handler for ${enemy.name}`);

    // Verify the zombie model structure
    const hasBody = !!enemy.getObjectByName('body');
    const hasHead = !!enemy.getObjectByName('head');
    const hasLeftArm = !!enemy.getObjectByName('leftArm');
    const hasRightArm = !!enemy.getObjectByName('rightArm');
    const hasLeftLeg = !!enemy.getObjectByName('leftLeg');
    const hasRightLeg = !!enemy.getObjectByName('rightLeg');

    console.log(`[ZombieAnimationHandlerFix] Zombie model structure:`, {
        hasBody,
        hasHead,
        hasLeftArm,
        hasRightArm,
        hasLeftLeg,
        hasRightLeg
    });

    // Create and attach the animation handler
    try {
        // Use the SimpleZombieAnimationHandler instead of the complex one
        enemy.userData.animationHandler = new SimpleZombieAnimationHandler(enemy);
        console.log(`[ZombieAnimationHandlerFix] Successfully created SimpleZombieAnimationHandler for ${enemy.name}`);
        return true;
    } catch (error) {
        console.error(`[ZombieAnimationHandlerFix] Error creating animation handler:`, error);
        return false;
    }
}
