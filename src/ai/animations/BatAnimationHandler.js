/**
 * Bat Animation Handler
 * Handles animations for bat enemies
 */
import * as THREE from 'three';
import { AIStates } from '../AIStates.js';

export class BatAnimationHandler {
    /**
     * Create a bat animation handler
     * @param {THREE.Group} batModel - The bat model to animate
     */
    constructor(batModel) {
        this.batModel = batModel;
        this.bodyGroup = batModel.getObjectByName('body');
        this.leftWingGroup = batModel.getObjectByName('leftWing');
        this.rightWingGroup = batModel.getObjectByName('rightWing');

        // Get animation data from model
        this.animationData = batModel.userData.animationData || {
            wingFlapSpeed: 6.0,         // Increased from 5.0 for faster flapping
            wingFlapAmplitude: Math.PI / 3, // Increased from PI/4 for wider flaps
            bodyBobAmplitude: 0.15,     // Increased from 0.1 for more movement
            bodyBobSpeed: 2.5,          // Increased from 2.0 for faster bobbing
            attackAnimationDuration: 0.5
        };

        // Animation state
        this.currentState = AIStates.HOVERING;
        this.attackAnimationProgress = 0;
        this.isAttacking = false;

        // Store original positions
        this.originalBodyPosition = new THREE.Vector3();
        if (this.bodyGroup) {
            this.originalBodyPosition.copy(this.bodyGroup.position);
        }

        // Initialize wing positions
        this.resetWingPositions();
    }

    /**
     * Reset wing positions to default
     */
    resetWingPositions() {
        if (this.leftWingGroup) {
            this.leftWingGroup.rotation.z = Math.PI / 6;
        }
        if (this.rightWingGroup) {
            this.rightWingGroup.rotation.z = -Math.PI / 6;
        }
    }

    /**
     * Update animations based on state and time
     * @param {string} state - Current AI state
     * @param {number} deltaTime - Time since last update
     * @param {number} globalTime - Global time for continuous animations
     */
    update(state, deltaTime, globalTime) {
        if (!this.batModel || !this.bodyGroup) return;

        // Update current state
        this.currentState = state;

        // Debug output to help diagnose animation issues
        console.log(`Bat animation update - State: ${state}, Time: ${globalTime.toFixed(2)}`);

        // Handle attack animation progress
        if (this.isAttacking) {
            this.attackAnimationProgress += deltaTime / this.animationData.attackAnimationDuration;
            if (this.attackAnimationProgress >= 1.0) {
                this.isAttacking = false;
                this.attackAnimationProgress = 0;
            }
        }

        // Apply animations based on state
        switch (state) {
            case AIStates.HOVERING:
            case AIStates.STRAFING:
            case AIStates.IDLE: // Add IDLE state to ensure wings flap when idle
            case AIStates.MOVING: // Add MOVING state to ensure wings flap when moving
                this._applyFlyingAnimation(globalTime, 1.0);
                break;

            case AIStates.SWOOPING:
                this._applySwoopingAnimation(globalTime);
                break;

            case AIStates.ATTACKING:
                if (!this.isAttacking) {
                    this.isAttacking = true;
                    this.attackAnimationProgress = 0;
                }
                this._applyAttackingAnimation(globalTime);
                break;

            case AIStates.ASCENDING:
                this._applyFlyingAnimation(globalTime, 1.5); // Faster wing flaps when ascending
                break;

            default:
                // Always apply flying animation as fallback to ensure wings are always flapping
                console.log(`Bat using default animation for state: ${state}`);
                this._applyFlyingAnimation(globalTime, 1.0);
                break;
        }
    }

    /**
     * Apply flying animation
     * @param {number} time - Global time
     * @param {number} speedMultiplier - Speed multiplier for wing flaps
     * @private
     */
    _applyFlyingAnimation(time, speedMultiplier = 1.0) {
        // Wing flapping
        if (this.leftWingGroup && this.rightWingGroup) {
            const wingFlapSpeed = this.animationData.wingFlapSpeed * speedMultiplier;
            const wingFlapAmplitude = this.animationData.wingFlapAmplitude;

            // Sinusoidal wing flapping with improved curve
            const wingAngle = Math.sin(time * wingFlapSpeed) * wingFlapAmplitude;

            // Add secondary motion for more natural movement
            const secondaryMotion = Math.sin(time * wingFlapSpeed * 1.5) * (wingFlapAmplitude * 0.2);

            // Combine primary and secondary motion
            const combinedAngle = wingAngle + secondaryMotion;

            // Apply rotation with more dramatic base angle
            this.leftWingGroup.rotation.z = Math.PI / 8 + combinedAngle;  // Reduced from PI/6 for wider spread
            this.rightWingGroup.rotation.z = -Math.PI / 8 - combinedAngle;

            // Add slight x and y rotation for more natural movement
            const xRotation = Math.sin(time * wingFlapSpeed * 0.7) * 0.1;
            this.leftWingGroup.rotation.x = xRotation;
            this.rightWingGroup.rotation.x = xRotation;

            // Add slight y rotation
            const yRotation = Math.cos(time * wingFlapSpeed * 0.5) * 0.05;
            this.leftWingGroup.rotation.y = yRotation;
            this.rightWingGroup.rotation.y = -yRotation;
        }

        // Body bobbing with improved motion
        if (this.bodyGroup) {
            const bodyBobSpeed = this.animationData.bodyBobSpeed;
            const bodyBobAmplitude = this.animationData.bodyBobAmplitude;

            // Sinusoidal body bobbing
            const bodyOffset = Math.sin(time * bodyBobSpeed) * bodyBobAmplitude;

            // Add secondary motion for more natural movement
            const secondaryBodyMotion = Math.sin(time * bodyBobSpeed * 2.3) * (bodyBobAmplitude * 0.3);

            // Apply combined motion
            this.bodyGroup.position.y = this.originalBodyPosition.y + bodyOffset + secondaryBodyMotion;

            // Add slight rotation to body for more natural movement
            this.bodyGroup.rotation.x = Math.sin(time * bodyBobSpeed * 0.7) * 0.1;
            this.bodyGroup.rotation.z = Math.cos(time * bodyBobSpeed * 0.5) * 0.05;
        }
    }

    /**
     * Apply swooping animation
     * @param {number} time - Global time
     * @private
     */
    _applySwoopingAnimation(time) {
        // Faster wing flapping during swooping
        if (this.leftWingGroup && this.rightWingGroup) {
            const wingFlapSpeed = this.animationData.wingFlapSpeed * 2.0; // Increased from 1.8
            const wingFlapAmplitude = this.animationData.wingFlapAmplitude * 1.5; // Increased from 1.2

            // Sinusoidal wing flapping with improved curve
            const wingAngle = Math.sin(time * wingFlapSpeed) * wingFlapAmplitude;

            // Add secondary motion for more natural movement
            const secondaryMotion = Math.sin(time * wingFlapSpeed * 1.7) * (wingFlapAmplitude * 0.25);

            // Combine primary and secondary motion
            const combinedAngle = wingAngle + secondaryMotion;

            // More horizontal wings during swooping
            this.leftWingGroup.rotation.z = Math.PI / 12 + combinedAngle; // Even more horizontal (reduced from PI/8)
            this.rightWingGroup.rotation.z = -Math.PI / 12 - combinedAngle;

            // Add more dramatic x rotation for diving effect
            const xRotation = Math.sin(time * wingFlapSpeed * 0.8) * 0.15;
            this.leftWingGroup.rotation.x = xRotation + Math.PI / 12; // Add constant tilt
            this.rightWingGroup.rotation.x = xRotation + Math.PI / 12;

            // Add slight y rotation for more natural movement
            const yRotation = Math.cos(time * wingFlapSpeed * 0.6) * 0.08;
            this.leftWingGroup.rotation.y = yRotation;
            this.rightWingGroup.rotation.y = -yRotation;
        }

        // Body tilts forward during swooping with improved motion
        if (this.bodyGroup) {
            // More dramatic forward tilt
            this.bodyGroup.rotation.x = Math.PI / 5; // Increased from PI/6

            // Add dynamic side-to-side motion
            const swayAmount = Math.sin(time * 6) * 0.15;
            this.bodyGroup.rotation.z = swayAmount;

            // Add slight up and down motion
            const bobAmount = Math.sin(time * 8) * 0.08;
            this.bodyGroup.position.y = this.originalBodyPosition.y + bobAmount;
        }
    }

    /**
     * Apply attacking animation
     * @param {number} time - Global time
     * @private
     */
    _applyAttackingAnimation(time) {
        // Continue flying animation in background with faster speed
        this._applyFlyingAnimation(time, 1.5); // Increased from 1.2

        if (this.isAttacking) {
            // Attack animation progress (0 to 1)
            const progress = this.attackAnimationProgress;

            // Attack consists of:
            // 1. Quick forward lunge (0-0.3)
            // 2. Bite (0.3-0.7)
            // 3. Return to position (0.7-1.0)

            if (progress < 0.3) {
                // Forward lunge
                const lungeProgress = progress / 0.3;
                const easeIn = lungeProgress * lungeProgress; // Ease in

                // Move body forward with more dramatic motion
                if (this.bodyGroup) {
                    this.bodyGroup.position.z = this.originalBodyPosition.z + easeIn * 0.8; // Increased from 0.5
                    this.bodyGroup.rotation.x = easeIn * Math.PI / 3; // More aggressive tilt (increased from PI/4)

                    // Add slight side tilt for more dynamic movement
                    const sideTilt = Math.sin(time * 10) * 0.1 * easeIn;
                    this.bodyGroup.rotation.z = sideTilt;
                }

                // Wings back for lunge with more dramatic movement
                if (this.leftWingGroup && this.rightWingGroup) {
                    // More dramatic wing positions during lunge
                    this.leftWingGroup.rotation.z = Math.PI / 8 - easeIn * Math.PI / 6; // More extreme angles
                    this.rightWingGroup.rotation.z = -Math.PI / 8 + easeIn * Math.PI / 6;

                    // Add x rotation for more dynamic movement
                    const xRotation = Math.sin(time * 12) * 0.15 * easeIn;
                    this.leftWingGroup.rotation.x = xRotation + easeIn * Math.PI / 8;
                    this.rightWingGroup.rotation.x = xRotation + easeIn * Math.PI / 8;
                }
            }
            else if (progress < 0.7) {
                // Bite
                const biteProgress = (progress - 0.3) / 0.4;
                const biteAngle = Math.sin(biteProgress * Math.PI) * Math.PI / 8; // Increased from PI/12 for more dramatic bite

                // Jaw movement (simulated by body rotation) with more dramatic motion
                if (this.bodyGroup) {
                    this.bodyGroup.position.z = this.originalBodyPosition.z + 0.8; // Increased from 0.5
                    this.bodyGroup.rotation.x = Math.PI / 3 + biteAngle; // Increased from PI/4

                    // Add slight side-to-side motion during bite
                    const sideTilt = Math.sin(time * 15) * 0.12 * Math.sin(biteProgress * Math.PI);
                    this.bodyGroup.rotation.z = sideTilt;

                    // Add slight up-down motion during bite
                    const bobAmount = Math.sin(time * 18) * 0.05 * Math.sin(biteProgress * Math.PI);
                    this.bodyGroup.position.y = this.originalBodyPosition.y + bobAmount;
                }

                // Wings flutter rapidly during bite with more dramatic movement
                if (this.leftWingGroup && this.rightWingGroup) {
                    const flutterSpeed = 20.0; // Increased from 15.0
                    const flutterAmplitude = Math.PI / 12; // Increased from PI/16
                    const flutterAngle = Math.sin(time * flutterSpeed) * flutterAmplitude;

                    // Add secondary flutter motion
                    const secondaryFlutter = Math.sin(time * flutterSpeed * 1.5) * (flutterAmplitude * 0.5);
                    const combinedFlutter = flutterAngle + secondaryFlutter;

                    this.leftWingGroup.rotation.z = Math.PI / 10 + combinedFlutter; // Changed from PI/12
                    this.rightWingGroup.rotation.z = -Math.PI / 10 - combinedFlutter;

                    // Add x and y rotation for more dynamic movement
                    const xRotation = Math.sin(time * 18) * 0.1;
                    this.leftWingGroup.rotation.x = xRotation;
                    this.rightWingGroup.rotation.x = xRotation;

                    const yRotation = Math.cos(time * 15) * 0.08;
                    this.leftWingGroup.rotation.y = yRotation;
                    this.rightWingGroup.rotation.y = -yRotation;
                }
            }
            else {
                // Return to position with improved easing
                const returnProgress = (progress - 0.7) / 0.3;
                const easeOut = 1 - Math.pow(1 - returnProgress, 3); // Improved ease out curve

                // Move body back with more dynamic motion
                if (this.bodyGroup) {
                    this.bodyGroup.position.z = this.originalBodyPosition.z + 0.8 * (1 - easeOut); // Increased from 0.5
                    this.bodyGroup.rotation.x = Math.PI / 3 * (1 - easeOut); // Increased from PI/4

                    // Add slight side-to-side motion during return
                    const sideTilt = Math.sin(time * 12) * 0.1 * (1 - easeOut);
                    this.bodyGroup.rotation.z = sideTilt;

                    // Add slight up-down motion during return
                    const bobAmount = Math.sin(time * 15) * 0.06 * (1 - easeOut);
                    this.bodyGroup.position.y = this.originalBodyPosition.y + bobAmount;
                }

                // Wings return to normal with more dynamic motion
                if (this.leftWingGroup && this.rightWingGroup) {
                    // More dramatic wing movement during return
                    this.leftWingGroup.rotation.z = Math.PI / 10 + easeOut * (Math.PI / 5 - Math.PI / 10);
                    this.rightWingGroup.rotation.z = -Math.PI / 10 - easeOut * (Math.PI / 5 - Math.PI / 10);

                    // Add x and y rotation for more natural movement
                    const xRotation = Math.sin(time * 14) * 0.12 * (1 - easeOut);
                    this.leftWingGroup.rotation.x = xRotation;
                    this.rightWingGroup.rotation.x = xRotation;

                    const yRotation = Math.cos(time * 12) * 0.08 * (1 - easeOut);
                    this.leftWingGroup.rotation.y = yRotation;
                    this.rightWingGroup.rotation.y = -yRotation;
                }
            }
        }
    }

    /**
     * Trigger a bite attack animation
     */
    triggerBiteAttack() {
        this.isAttacking = true;
        this.attackAnimationProgress = 0;
    }
}
