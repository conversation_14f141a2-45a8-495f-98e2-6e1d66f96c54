/**
 * Animation handler for the Catacomb Overlord boss
 * Handles animations for the custom boss model
 */

import * as THREE from 'three';
import { AIStates } from '../AIStates.js';

export class CatacombOverlordAnimationHandler {
    /**
     * Create a new animation handler for the Catacomb Overlord boss
     * @param {THREE.Group} bossModel - The boss model to animate
     */
    constructor(bossModel) {
        this.bossModel = bossModel;
        
        // Get references to model parts
        this.headGroup = bossModel.getObjectByName('head');
        this.leftArmGroup = bossModel.getObjectByName('leftArm');
        this.rightArmGroup = bossModel.getObjectByName('rightArm');
        this.leftLegGroup = bossModel.getObjectByName('leftLeg');
        this.rightLegGroup = bossModel.getObjectByName('rightLeg');
        this.weaponGroup = this.leftArmGroup ? this.leftArmGroup.getObjectByName('weapon') : null;
        
        // Store original positions and rotations
        this.originalPositions = {
            head: this.headGroup ? this.headGroup.position.clone() : null,
            leftArm: this.leftArmGroup ? this.leftArmGroup.position.clone() : null,
            rightArm: this.rightArmGroup ? this.rightArmGroup.position.clone() : null,
            leftLeg: this.leftLegGroup ? this.leftLegGroup.position.clone() : null,
            rightLeg: this.rightLegGroup ? this.rightLegGroup.position.clone() : null
        };
        
        this.originalRotations = {
            head: this.headGroup ? this.headGroup.rotation.clone() : null,
            leftArm: this.leftArmGroup ? this.leftArmGroup.rotation.clone() : null,
            rightArm: this.rightArmGroup ? this.rightArmGroup.rotation.clone() : null,
            leftLeg: this.leftLegGroup ? this.leftLegGroup.rotation.clone() : null,
            rightLeg: this.rightLegGroup ? this.rightLegGroup.rotation.clone() : null
        };
        
        // Animation state
        this.currentState = AIStates.IDLE;
        this.attackAnimationProgress = 0;
        this.isAttacking = false;
        
        // Animation parameters
        this.animationData = {
            // Idle animation
            idleSpeed: 0.8,
            idleAmplitude: 0.05,
            
            // Walking animation
            walkSpeed: 1.5,
            walkAmplitude: 0.15,
            
            // Attack animation
            attackAnimationDuration: 0.8,
            attackWindupDuration: 0.4,
            
            // Head bobbing
            headBobSpeed: 1.2,
            headBobAmplitude: 0.1,
            
            // Weapon swing
            weaponSwingAmplitude: Math.PI / 3
        };
        
        console.log("[CatacombOverlordAnimationHandler] Initialized");
    }
    
    /**
     * Update animations based on state and time
     * @param {string} state - Current AI state
     * @param {number} deltaTime - Time since last update
     * @param {number} globalTime - Global time for continuous animations
     */
    update(state, deltaTime, globalTime) {
        if (!this.bossModel) return;
        
        // Update current state
        this.currentState = state;
        
        // Handle attack animation progress
        if (state === AIStates.ATTACKING) {
            // Always increment progress while in attack state
            this.attackAnimationProgress += deltaTime / this.animationData.attackAnimationDuration;
            this.isAttacking = true;
            
            // Loop the animation
            if (this.attackAnimationProgress >= 1.0) {
                this.attackAnimationProgress = 0;
            }
        } else {
            // Reset attack progress when not attacking
            this.isAttacking = false;
            this.attackAnimationProgress = 0;
        }
        
        // Apply animations based on state
        switch (state) {
            case AIStates.IDLE:
                this._applyIdleAnimation(globalTime);
                break;
                
            case AIStates.MOVING:
            case AIStates.STRAFING:
                this._applyWalkingAnimation(globalTime);
                break;
                
            case AIStates.ATTACKING:
                this._applyAttackingAnimation(globalTime, deltaTime);
                break;
                
            case AIStates.HIT_REACTING:
                this._applyHitReactionAnimation(globalTime);
                break;
                
            default:
                this._applyIdleAnimation(globalTime);
                break;
        }
        
        // Apply glowing eye effect
        this._applyGlowingEyeEffect(globalTime);
    }
    
    /**
     * Apply idle animation
     * @param {number} time - Global time for continuous animation
     * @private
     */
    _applyIdleAnimation(time) {
        const swaySpeed = this.animationData.idleSpeed;
        const swayAmplitude = this.animationData.idleAmplitude;
        
        // Subtle body sway
        if (this.bossModel) {
            // Store base Y position if not already set
            if (this.bossModel.userData.basePositionY === undefined) {
                this.bossModel.userData.basePositionY = this.bossModel.position.y;
            }
            
            // Vertical bobbing
            const bodyBob = Math.sin(time * swaySpeed) * swayAmplitude;
            this.bossModel.position.y = this.bossModel.userData.basePositionY + bodyBob;
        }
        
        // Head movement
        if (this.headGroup) {
            const headBobSpeed = this.animationData.headBobSpeed * 0.5;
            const headBobAmplitude = this.animationData.headBobAmplitude * 0.3;
            
            const headBob = Math.sin(time * headBobSpeed) * headBobAmplitude;
            this.headGroup.rotation.z = headBob * 0.2; // Slight head tilt
            this.headGroup.rotation.x = Math.sin(time * headBobSpeed * 1.2) * headBobAmplitude * 0.2; // Slight nod
        }
        
        // Arms hanging with subtle movement
        if (this.leftArmGroup) {
            const armSway = Math.sin(time * swaySpeed * 0.8) * (swayAmplitude * 0.5);
            this.leftArmGroup.rotation.z = armSway;
        }
        
        if (this.rightArmGroup) {
            const armSway = Math.sin(time * swaySpeed * 0.8 + Math.PI) * (swayAmplitude * 0.5);
            this.rightArmGroup.rotation.z = armSway;
        }
        
        // Subtle leg movement
        if (this.leftLegGroup) {
            const legSway = Math.sin(time * swaySpeed * 0.5) * (swayAmplitude * 0.3);
            this.leftLegGroup.rotation.x = legSway;
        }
        
        if (this.rightLegGroup) {
            const legSway = Math.sin(time * swaySpeed * 0.5 + Math.PI) * (swayAmplitude * 0.3);
            this.rightLegGroup.rotation.x = legSway;
        }
        
        // Weapon subtle movement
        if (this.weaponGroup) {
            const weaponSway = Math.sin(time * swaySpeed * 0.6) * (swayAmplitude * 0.2);
            this.weaponGroup.rotation.z = weaponSway;
        }
    }
    
    /**
     * Apply walking animation
     * @param {number} time - Global time for continuous animation
     * @private
     */
    _applyWalkingAnimation(time) {
        const walkSpeed = this.animationData.walkSpeed;
        const walkAmplitude = this.animationData.walkAmplitude;
        
        // Body movement
        if (this.bossModel) {
            // Store base Y position if not already set
            if (this.bossModel.userData.basePositionY === undefined) {
                this.bossModel.userData.basePositionY = this.bossModel.position.y;
            }
            
            // Vertical bobbing with a more pronounced up-down motion
            const bodyBob = Math.abs(Math.sin(time * walkSpeed)) * walkAmplitude;
            this.bossModel.position.y = this.bossModel.userData.basePositionY + bodyBob;
        }
        
        // Head movement - more pronounced while walking
        if (this.headGroup) {
            const headBob = Math.sin(time * walkSpeed * 1.2) * (walkAmplitude * 0.5);
            this.headGroup.rotation.z = headBob * 0.3; // Head tilt
            this.headGroup.rotation.x = Math.sin(time * walkSpeed * 0.8) * (walkAmplitude * 0.4); // Head nod
        }
        
        // Arms swinging
        if (this.leftArmGroup) {
            const armSwing = Math.sin(time * walkSpeed) * (walkAmplitude * 2);
            this.leftArmGroup.rotation.x = armSwing;
        }
        
        if (this.rightArmGroup) {
            const armSwing = Math.sin(time * walkSpeed + Math.PI) * (walkAmplitude * 2);
            this.rightArmGroup.rotation.x = armSwing;
        }
        
        // Legs walking
        if (this.leftLegGroup) {
            const legSwing = Math.sin(time * walkSpeed) * (walkAmplitude * 3);
            this.leftLegGroup.rotation.x = legSwing;
        }
        
        if (this.rightLegGroup) {
            const legSwing = Math.sin(time * walkSpeed + Math.PI) * (walkAmplitude * 3);
            this.rightLegGroup.rotation.x = legSwing;
        }
        
        // Weapon movement while walking
        if (this.weaponGroup) {
            const weaponSway = Math.sin(time * walkSpeed * 0.8) * (walkAmplitude * 0.5);
            this.weaponGroup.rotation.z = weaponSway;
        }
    }
    
    /**
     * Apply attacking animation
     * @param {number} time - Global time for continuous animation
     * @param {number} deltaTime - Time since last update
     * @private
     */
    _applyAttackingAnimation(time, deltaTime) {
        // Base idle animation for parts not involved in attack
        this._applyIdleAnimation(time);
        
        // Determine attack phase (windup or swing)
        const isWindup = this.attackAnimationProgress < this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration;
        
        // Head movement during attack
        if (this.headGroup) {
            if (isWindup) {
                // Look down during windup
                const windupProgress = this.attackAnimationProgress / (this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration);
                this.headGroup.rotation.x = windupProgress * 0.3; // Look down
            } else {
                // Look forward during swing
                const swingProgress = (this.attackAnimationProgress - (this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration)) / 
                                     (1 - (this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration));
                this.headGroup.rotation.x = 0.3 - (swingProgress * 0.3); // Return to normal
            }
        }
        
        // Left arm (weapon arm) attack animation
        if (this.leftArmGroup) {
            if (isWindup) {
                // Pull back for windup
                const windupProgress = this.attackAnimationProgress / (this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration);
                this.leftArmGroup.rotation.x = -windupProgress * Math.PI / 3; // Pull back
                this.leftArmGroup.rotation.z = windupProgress * Math.PI / 6; // Raise slightly
            } else {
                // Swing forward
                const swingProgress = (this.attackAnimationProgress - (this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration)) / 
                                     (1 - (this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration));
                
                // Fast swing forward and down
                this.leftArmGroup.rotation.x = (-Math.PI / 3) + (swingProgress * Math.PI / 2); // Swing forward
                this.leftArmGroup.rotation.z = (Math.PI / 6) - (swingProgress * Math.PI / 4); // Swing down
            }
        }
        
        // Right arm braces during attack
        if (this.rightArmGroup) {
            if (isWindup) {
                // Brace for attack
                const windupProgress = this.attackAnimationProgress / (this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration);
                this.rightArmGroup.rotation.x = windupProgress * Math.PI / 6; // Move forward slightly
            } else {
                // Hold position during swing
                this.rightArmGroup.rotation.x = Math.PI / 6;
            }
        }
        
        // Body leans into attack
        if (this.bossModel) {
            if (isWindup) {
                // Lean back slightly during windup
                const windupProgress = this.attackAnimationProgress / (this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration);
                this.bossModel.rotation.x = -windupProgress * 0.1; // Lean back
            } else {
                // Lean forward during swing
                const swingProgress = (this.attackAnimationProgress - (this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration)) / 
                                     (1 - (this.animationData.attackWindupDuration / this.animationData.attackAnimationDuration));
                this.bossModel.rotation.x = (-0.1) + (swingProgress * 0.2); // Lean forward
                
                // Return to normal at end of swing
                if (swingProgress > 0.8) {
                    const returnProgress = (swingProgress - 0.8) / 0.2;
                    this.bossModel.rotation.x = 0.1 - (returnProgress * 0.1); // Return to normal
                }
            }
        }
    }
    
    /**
     * Apply hit reaction animation
     * @param {number} time - Global time for continuous animation
     * @private
     */
    _applyHitReactionAnimation(time) {
        // Base idle animation
        this._applyIdleAnimation(time);
        
        // Add hit reaction effects
        if (this.bossModel) {
            // Slight backward lean
            this.bossModel.rotation.x = -0.1;
        }
        
        if (this.headGroup) {
            // Head jerks back
            this.headGroup.rotation.x = -0.2;
        }
        
        // Arms raise slightly in defensive posture
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = -0.3;
            this.leftArmGroup.rotation.z = 0.2;
        }
        
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = -0.3;
            this.rightArmGroup.rotation.z = -0.2;
        }
    }
    
    /**
     * Apply glowing eye effect
     * @param {number} time - Global time for continuous animation
     * @private
     */
    _applyGlowingEyeEffect(time) {
        // Find eye materials in the model
        this.bossModel.traverse(child => {
            if (child.isMesh) {
                const materials = Array.isArray(child.material) ? child.material : [child.material];
                
                materials.forEach(material => {
                    // Check if this is an eye material (has emissive property and is red)
                    if (material.emissive && material.emissive.r > 0.5 && material.emissive.g < 0.3 && material.emissive.b < 0.3) {
                        // Pulse the emissive intensity
                        const pulseValue = 1.0 + 0.5 * Math.sin(time * 2.0);
                        material.emissiveIntensity = pulseValue;
                    }
                });
            }
        });
    }
    
    /**
     * Reset all animations to default state
     */
    reset() {
        // Reset all rotations to original values
        if (this.headGroup && this.originalRotations.head) {
            this.headGroup.rotation.copy(this.originalRotations.head);
        }
        
        if (this.leftArmGroup && this.originalRotations.leftArm) {
            this.leftArmGroup.rotation.copy(this.originalRotations.leftArm);
        }
        
        if (this.rightArmGroup && this.originalRotations.rightArm) {
            this.rightArmGroup.rotation.copy(this.originalRotations.rightArm);
        }
        
        if (this.leftLegGroup && this.originalRotations.leftLeg) {
            this.leftLegGroup.rotation.copy(this.originalRotations.leftLeg);
        }
        
        if (this.rightLegGroup && this.originalRotations.rightLeg) {
            this.rightLegGroup.rotation.copy(this.originalRotations.rightLeg);
        }
        
        // Reset boss model rotation
        if (this.bossModel) {
            this.bossModel.rotation.set(0, this.bossModel.rotation.y, 0);
            
            // Reset Y position
            if (this.bossModel.userData.basePositionY !== undefined) {
                this.bossModel.position.y = this.bossModel.userData.basePositionY;
            }
        }
        
        // Reset animation state
        this.currentState = AIStates.IDLE;
        this.attackAnimationProgress = 0;
        this.isAttacking = false;
    }
}
