import { AIStates } from '../AIStates.js';
import * as THREE from 'three';

/**
 * Handles animations for the Magma Golem enemy type
 */
export class MagmaGolemAnimationHandler {
    /**
     * Create a magma golem animation handler
     * @param {THREE.Group} golemModel - The magma golem model to animate
     */
    constructor(golemModel) {
        this.golemModel = golemModel;
        this.bodyGroup = golemModel.getObjectByName('body');
        this.headGroup = golemModel.getObjectByName('head');
        this.leftArmGroup = golemModel.getObjectByName('leftArm');
        this.rightArmGroup = golemModel.getObjectByName('rightArm');
        this.leftLegGroup = golemModel.getObjectByName('leftLeg');
        this.rightLegGroup = golemModel.getObjectByName('rightLeg');

        // Get animation data from model or use defaults
        this.animationData = golemModel.userData.animationData || {
            walkSpeed: 1.2,              // Slower walk speed for heavy golem
            walkAmplitude: Math.PI / 8,  // Smaller amplitude for heavy movement
            armSwingAmplitude: Math.PI / 6, // Arm swing amount
            bodySwayAmplitude: 0.05,     // Body sway amount
            bodySwaySpeed: 0.8,          // Body sway speed
            attackAnimationDuration: 2.4, // Increased to match other enemies
            headBobAmplitude: 0.03,      // Head bobbing amount
            headBobSpeed: 1.2            // Head bobbing speed
        };

        // Animation state
        this.currentState = AIStates.IDLE;
        this.attackAnimationProgress = 0;
        this.isAttacking = false;
        this.attackStartTime = 0;
        this.lastGlobalTime = 0;
        this.attackLoopCount = 0;  // Add counter for attack loops
        this.hitReactionProgress = 0;
        this.isHitReacting = false;

        // Store original positions and rotations
        this.originalPositions = {};
        this.originalRotations = {};

        this.storeOriginalTransforms();
        
        // Magma effect parameters
        this.magmaEffectIntensity = 0.8;
        this.magmaEffectSpeed = 1.5;
        this.lastMagmaUpdateTime = 0;
        this.magmaUpdateInterval = 0.2; // Update magma effect every 0.2 seconds

        // Add transition blending
        this.previousState = null;
        this.transitionProgress = 0;
        this.transitionDuration = 0.15; // 150ms transition
        this.previousAnimationPose = null;
        this.currentAnimationPose = null;
    }

    /**
     * Store original positions and rotations of all body parts
     */
    storeOriginalTransforms() {
        const groups = [
            'golemModel', 'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            if (group) {
                this.originalPositions[groupName] = group.position.clone();
                this.originalRotations[groupName] = group.rotation.clone();
            }
        }
    }

    /**
     * Reset all body parts to their original positions and rotations
     */
    resetTransforms() {
        const groups = [
            'golemModel', 'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            const originalPosition = this.originalPositions[groupName];
            const originalRotation = this.originalRotations[groupName];

            if (group && originalPosition && originalRotation) {
                group.position.copy(originalPosition);
                group.rotation.copy(originalRotation);
            }
        }
    }

    /**
     * Store the current pose for blending
     */
    _storePose() {
        if (!this.previousAnimationPose) {
            this.previousAnimationPose = {
                body: this.bodyGroup ? {
                    rotation: this.bodyGroup.rotation.clone(),
                    position: this.bodyGroup.position.clone()
                } : null,
                head: this.headGroup ? {
                    rotation: this.headGroup.rotation.clone(),
                    position: this.headGroup.position.clone()
                } : null,
                leftArm: this.leftArmGroup ? {
                    rotation: this.leftArmGroup.rotation.clone(),
                    position: this.leftArmGroup.position.clone()
                } : null,
                rightArm: this.rightArmGroup ? {
                    rotation: this.rightArmGroup.rotation.clone(),
                    position: this.rightArmGroup.position.clone()
                } : null,
                leftLeg: this.leftLegGroup ? {
                    rotation: this.leftLegGroup.rotation.clone(),
                    position: this.leftLegGroup.position.clone()
                } : null,
                rightLeg: this.rightLegGroup ? {
                    rotation: this.rightLegGroup.rotation.clone(),
                    position: this.rightLegGroup.position.clone()
                } : null
            };
        }
    }

    /**
     * Blend between previous and current pose
     * @param {number} blendFactor - 0 to 1 blend factor
     */
    _blendPoses(blendFactor) {
        if (!this.previousAnimationPose || !this.currentAnimationPose) return;

        const blend = (current, previous, factor) => {
            if (!current || !previous) return;
            current.rotation.lerp(previous.rotation, 1 - factor);
            current.position.lerp(previous.position, 1 - factor);
        };

        // Blend each body part
        if (this.bodyGroup) blend(this.bodyGroup, this.previousAnimationPose.body, blendFactor);
        if (this.headGroup) blend(this.headGroup, this.previousAnimationPose.head, blendFactor);
        if (this.leftArmGroup) blend(this.leftArmGroup, this.previousAnimationPose.leftArm, blendFactor);
        if (this.rightArmGroup) blend(this.rightArmGroup, this.previousAnimationPose.rightArm, blendFactor);
        if (this.leftLegGroup) blend(this.leftLegGroup, this.previousAnimationPose.leftLeg, blendFactor);
        if (this.rightLegGroup) blend(this.rightLegGroup, this.previousAnimationPose.rightLeg, blendFactor);
    }

    /**
     * Update animations based on state and time
     * @param {string} state - Current AI state
     * @param {number} deltaTime - Time since last update
     * @param {number} globalTime - Global time for continuous animations
     */
    update(state, deltaTime, globalTime) {
        if (!this.golemModel) return;

        // Handle state transition
        if (this.currentState !== state) {
            this._storePose();
            this.previousState = this.currentState;
            this.currentState = state;
            this.transitionProgress = 0;

            // Handle attack state changes
            if (state === AIStates.ATTACKING) {
                this.isAttacking = true;
                // Only reset progress when starting a new attack
                if (this.previousState !== AIStates.ATTACKING) {
                    this.attackStartTime = globalTime;
                    this.attackAnimationProgress = 0;
                }
            } else if (this.isAttacking) {
                this.isAttacking = false;
            }
        }

        // Update transition progress
        if (this.transitionProgress < this.transitionDuration) {
            this.transitionProgress += deltaTime;
        }

        // Handle attack animation progress
        if (this.isAttacking) {
            // Update progress based on deltaTime
            this.attackAnimationProgress += deltaTime / this.animationData.attackAnimationDuration;
            
            // Loop the animation
            if (this.attackAnimationProgress >= 1.0) {
                this.attackStartTime = globalTime;
                this.attackAnimationProgress = 0;
            }
        }
        
        // Handle hit reaction progress
        if (this.isHitReacting) {
            this.hitReactionProgress += deltaTime / 0.6;
            if (this.hitReactionProgress >= 1.0) {
                this.isHitReacting = false;
                this.hitReactionProgress = 0;
            }
        }

        // Apply animations based on state
        switch (state) {
            case AIStates.IDLE:
                this._applyIdleAnimation(globalTime);
                break;

            case AIStates.MOVING:
            case AIStates.STRAFING:
            case AIStates.FLEEING:
                this._applyWalkingAnimation(globalTime);
                break;

            case AIStates.ATTACKING:
                this._applyAttackingAnimation(this.attackAnimationProgress, globalTime);
                break;

            case AIStates.HIT_REACTING:
            case AIStates.KNOCKBACK:
                if (!this.isHitReacting) {
                    this.isHitReacting = true;
                    this.hitReactionProgress = 0;
                }
                this._applyHitReactionAnimation(globalTime);
                break;

            default:
                this._applyIdleAnimation(globalTime);
                break;
        }

        // Store current pose for next frame's blending
        this.currentAnimationPose = {
            body: this.bodyGroup ? {
                rotation: this.bodyGroup.rotation.clone(),
                position: this.bodyGroup.position.clone()
            } : null,
            head: this.headGroup ? {
                rotation: this.headGroup.rotation.clone(),
                position: this.headGroup.position.clone()
            } : null,
            leftArm: this.leftArmGroup ? {
                rotation: this.leftArmGroup.rotation.clone(),
                position: this.leftArmGroup.position.clone()
            } : null,
            rightArm: this.rightArmGroup ? {
                rotation: this.rightArmGroup.rotation.clone(),
                position: this.rightArmGroup.position.clone()
            } : null,
            leftLeg: this.leftLegGroup ? {
                rotation: this.leftLegGroup.rotation.clone(),
                position: this.leftLegGroup.position.clone()
            } : null,
            rightLeg: this.rightLegGroup ? {
                rotation: this.rightLegGroup.rotation.clone(),
                position: this.rightLegGroup.position.clone()
            } : null
        };

        // Apply transition blending if in transition
        if (this.transitionProgress < this.transitionDuration) {
            const blendFactor = Math.min(this.transitionProgress / this.transitionDuration, 1);
            this._blendPoses(blendFactor);
        }
        
        // Apply magma effect regardless of state
        this._applyMagmaEffect(globalTime, deltaTime);
    }

    /**
     * Apply idle animation
     * @param {number} time - Global time for continuous animation
     * @private
     */
    _applyIdleAnimation(time) {
        // Reset transforms first
        this.resetTransforms();

        // Subtle body sway
        const swaySpeed = this.animationData.bodySwaySpeed;
        const swayAmplitude = this.animationData.bodySwayAmplitude;

        if (this.bodyGroup) {
            this.bodyGroup.rotation.z = Math.sin(time * swaySpeed) * swayAmplitude;
            this.bodyGroup.rotation.x = Math.sin(time * swaySpeed * 0.7) * (swayAmplitude * 0.5);
        }

        // Head bobbing
        if (this.headGroup) {
            const headBobSpeed = this.animationData.headBobSpeed * 0.8;
            const headBobAmplitude = this.animationData.headBobAmplitude * 0.7;

            const headBob = Math.sin(time * headBobSpeed) * headBobAmplitude;
            this.headGroup.rotation.z = headBob * 0.5; // Slight head tilt
            this.headGroup.rotation.x = Math.sin(time * headBobSpeed * 1.2) * headBobAmplitude * 0.3; // Slight nod
        }

        // Arms hanging with subtle movement
        if (this.leftArmGroup) {
            const armSway = Math.sin(time * swaySpeed * 1.1) * (swayAmplitude * 0.8);
            this.leftArmGroup.rotation.z = armSway + 0.1; // Slightly out from body
            this.leftArmGroup.rotation.x = Math.sin(time * swaySpeed * 0.9) * (swayAmplitude * 0.5);
        }

        if (this.rightArmGroup) {
            const armSway = Math.sin(time * swaySpeed * 1.1 + Math.PI) * (swayAmplitude * 0.8);
            this.rightArmGroup.rotation.z = armSway - 0.1; // Slightly out from body
            this.rightArmGroup.rotation.x = Math.sin(time * swaySpeed * 0.9 + Math.PI) * (swayAmplitude * 0.5);
        }
    }

    /**
     * Apply walking animation
     * @param {number} time - Global time for continuous animation
     * @private
     */
    _applyWalkingAnimation(time) {
        // Reset transforms first
        this.resetTransforms();

        const walkSpeed = this.animationData.walkSpeed;
        const walkAmplitude = this.animationData.walkAmplitude;

        // Leg movement
        if (this.leftLegGroup) {
            this.leftLegGroup.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude;
        }
        if (this.rightLegGroup) {
            this.rightLegGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude;
        }

        // Arm swing
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * (walkAmplitude * 0.5);
        }
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = Math.sin(time * walkSpeed) * (walkAmplitude * 0.5);
        }

        // Body bob - only apply Y offset RELATIVE to base position
        if (this.golemModel) {
            if (this.golemModel.userData.basePositionY === undefined) {
                this.golemModel.userData.basePositionY = this.golemModel.position.y;
            }
            const bodyBob = Math.abs(Math.sin(time * walkSpeed)) * 0.1;
            this.golemModel.position.y = this.golemModel.userData.basePositionY + bodyBob;
        }
    }

    /**
     * Apply attacking animation
     * @param {number} progress - Attack animation progress (0 to 1)
     * @param {number} time - Global time for continuous animation
     * @private
     */
    _applyAttackingAnimation(progress, time) {
        this.resetTransforms();
        
        // Three-phase attack animation
        // 1. Wind-up (0-0.3): Pull back for punch
        // 2. Strike (0.3-0.6): Fast forward punch
        // 3. Recovery (0.6-1.0): Return to ready position
        
        const windUpPhase = progress < 0.3;
        const strikePhase = progress >= 0.3 && progress < 0.6;
        const recoveryPhase = progress >= 0.6;
        
        // Calculate phase-specific progress
        const windUpProgress = windUpPhase ? (progress / 0.3) : 1.0;
        const strikeProgress = strikePhase ? ((progress - 0.3) / 0.3) : (recoveryPhase ? 1.0 : 0.0);
        const recoveryProgress = recoveryPhase ? ((progress - 0.6) / 0.4) : 0.0;
        
        // Easing functions for smoother animation
        const easeInWindUp = Math.pow(windUpProgress, 2);
        const easeOutStrike = strikePhase ? (1 - Math.pow(1 - strikeProgress, 2)) : 0;
        const easeInOutRecovery = recoveryPhase ? (1 - Math.pow(1 - recoveryProgress, 2)) : 0;
        
        // Body movement
        if (this.bodyGroup) {
            // Wind-up: Rotate back and to the side
            // Strike: Rotate forward and twist
            // Recovery: Return to neutral
            
            if (windUpPhase) {
                // Pull back and twist for power
                this.bodyGroup.rotation.y = 0.4 * easeInWindUp;
                this.bodyGroup.rotation.z = 0.2 * easeInWindUp;
                this.bodyGroup.rotation.x = 0.15 * easeInWindUp;
            } else if (strikePhase) {
                // Explosive forward rotation
                this.bodyGroup.rotation.y = (0.4 - 0.8 * easeOutStrike);
                this.bodyGroup.rotation.z = (0.2 - 0.4 * easeOutStrike);
                this.bodyGroup.rotation.x = (0.15 - 0.3 * easeOutStrike);
                
                // Forward lunge
                this.bodyGroup.position.z = -0.5 * easeOutStrike;
            } else {
                // Smooth return to neutral
                this.bodyGroup.rotation.y = -0.4 * (1 - easeInOutRecovery);
                this.bodyGroup.rotation.z = -0.2 * (1 - easeInOutRecovery);
                this.bodyGroup.rotation.x = -0.15 * (1 - easeInOutRecovery);
                this.bodyGroup.position.z = -0.5 * (1 - easeInOutRecovery);
            }
        }
        
        // Determine which arm is punching (alternate between left and right)
        const isSecondPunch = Math.floor(time) % 2 === 0;
        const punchingArm = isSecondPunch ? this.rightArmGroup : this.leftArmGroup;
        const guardingArm = isSecondPunch ? this.leftArmGroup : this.rightArmGroup;
        
        if (punchingArm && guardingArm) {
            if (windUpPhase) {
                // Wind up the punching arm
                punchingArm.rotation.x = -Math.PI/3 * easeInWindUp;
                punchingArm.rotation.z = (isSecondPunch ? -0.5 : 0.5) * easeInWindUp;
                
                // Guard position with other arm
                guardingArm.rotation.x = Math.PI/6 * easeInWindUp;
                guardingArm.rotation.z = (isSecondPunch ? 0.3 : -0.3) * easeInWindUp;
            } else if (strikePhase) {
                // Explosive punch forward
                punchingArm.rotation.x = (-Math.PI/3 + Math.PI * easeOutStrike);
                punchingArm.rotation.z = (isSecondPunch ? -0.5 : 0.5) * (1 - easeOutStrike);
                
                // Supporting arm movement
                guardingArm.rotation.x = Math.PI/6 + Math.PI/6 * easeOutStrike;
                guardingArm.rotation.z = (isSecondPunch ? 0.3 : -0.3) * (1 - easeOutStrike);
            } else {
                // Return to neutral
                punchingArm.rotation.x = (Math.PI * 0.7) * (1 - easeInOutRecovery);
                punchingArm.rotation.z = (isSecondPunch ? -0.5 : 0.5) * (1 - easeInOutRecovery) * 0.5;
                
                guardingArm.rotation.x = (Math.PI/3) * (1 - easeInOutRecovery);
                guardingArm.rotation.z = (isSecondPunch ? 0.3 : -0.3) * (1 - easeInOutRecovery) * 0.5;
            }
        }
        
        // Head follows the punch motion
        if (this.headGroup) {
            if (windUpPhase) {
                // Look in direction of wind-up
                this.headGroup.rotation.y = 0.3 * easeInWindUp;
                this.headGroup.rotation.x = -0.1 * easeInWindUp;
            } else if (strikePhase) {
                // Follow through with punch
                this.headGroup.rotation.y = (0.3 - 0.6 * easeOutStrike);
                this.headGroup.rotation.x = (-0.1 + 0.3 * easeOutStrike);
            } else {
                // Return to neutral
                this.headGroup.rotation.y = -0.3 * (1 - easeInOutRecovery);
                this.headGroup.rotation.x = 0.2 * (1 - easeInOutRecovery);
            }
        }
        
        // Legs provide stable base with weight shift
        if (this.leftLegGroup && this.rightLegGroup) {
            const stanceWidth = 0.3;
            
            if (windUpPhase) {
                // Widen stance and shift weight back
                this.leftLegGroup.rotation.z = -stanceWidth * easeInWindUp;
                this.rightLegGroup.rotation.z = stanceWidth * easeInWindUp;
                
                const backLean = 0.2 * easeInWindUp;
                this.leftLegGroup.rotation.x = backLean;
                this.rightLegGroup.rotation.x = backLean;
            } else if (strikePhase) {
                // Shift weight forward
                this.leftLegGroup.rotation.z = -stanceWidth;
                this.rightLegGroup.rotation.z = stanceWidth;
                
                const forwardLean = -0.3 * easeOutStrike;
                this.leftLegGroup.rotation.x = forwardLean;
                this.rightLegGroup.rotation.x = forwardLean;
            } else {
                // Return to neutral
                this.leftLegGroup.rotation.z = -stanceWidth * (1 - easeInOutRecovery);
                this.rightLegGroup.rotation.z = stanceWidth * (1 - easeInOutRecovery);
                
                const returnLean = -0.3 * (1 - easeInOutRecovery);
                this.leftLegGroup.rotation.x = returnLean;
                this.rightLegGroup.rotation.x = returnLean;
            }
        }
        
        // Enhanced magma effect during attack
        const attackIntensity = strikePhase ? (1.0 + easeOutStrike) : (windUpPhase ? (0.8 + 0.2 * easeInWindUp) : 1.0);
        this.magmaEffectIntensity = attackIntensity;
        this.magmaEffectSpeed = 3.0 + attackIntensity * 2;
    }

    /**
     * Apply hit reaction animation
     * @param {number} time - Global time for continuous animation
     * @private
     */
    _applyHitReactionAnimation(time) {
        // Reset transforms first
        this.resetTransforms();
        
        const progress = this.hitReactionProgress;
        
        // Hit reaction is divided into two phases:
        // 1. Impact (0-0.3): Immediate reaction to hit
        // 2. Recovery (0.3-1.0): Return to neutral position
        
        if (progress < 0.3) {
            // Impact phase
            const impactProgress = progress / 0.3;
            
            // Body recoils
            if (this.bodyGroup) {
                this.bodyGroup.rotation.z = 0.2 * impactProgress;
                this.bodyGroup.rotation.x = -0.15 * impactProgress;
                this.bodyGroup.position.z = -0.2 * impactProgress;
            }
            
            // Head reacts
            if (this.headGroup) {
                this.headGroup.rotation.z = 0.3 * impactProgress;
                this.headGroup.rotation.x = 0.2 * impactProgress;
            }
            
            // Arms flail
            if (this.leftArmGroup && this.rightArmGroup) {
                this.leftArmGroup.rotation.x = 0.3 * impactProgress;
                this.leftArmGroup.rotation.z = 0.4 * impactProgress;
                
                this.rightArmGroup.rotation.x = 0.3 * impactProgress;
                this.rightArmGroup.rotation.z = -0.4 * impactProgress;
            }
            
            // Legs buckle
            if (this.leftLegGroup && this.rightLegGroup) {
                this.leftLegGroup.rotation.x = 0.2 * impactProgress;
                this.rightLegGroup.rotation.x = 0.2 * impactProgress;
            }
        } 
        else {
            // Recovery phase
            const recoveryProgress = (progress - 0.3) / 0.7;
            
            // Body returns to neutral
            if (this.bodyGroup) {
                this.bodyGroup.rotation.z = 0.2 * (1 - recoveryProgress);
                this.bodyGroup.rotation.x = -0.15 * (1 - recoveryProgress);
                this.bodyGroup.position.z = -0.2 * (1 - recoveryProgress);
            }
            
            // Head returns to neutral
            if (this.headGroup) {
                this.headGroup.rotation.z = 0.3 * (1 - recoveryProgress);
                this.headGroup.rotation.x = 0.2 * (1 - recoveryProgress);
            }
            
            // Arms return to neutral
            if (this.leftArmGroup && this.rightArmGroup) {
                this.leftArmGroup.rotation.x = 0.3 * (1 - recoveryProgress);
                this.leftArmGroup.rotation.z = 0.4 * (1 - recoveryProgress);
                
                this.rightArmGroup.rotation.x = 0.3 * (1 - recoveryProgress);
                this.rightArmGroup.rotation.z = -0.4 * (1 - recoveryProgress);
            }
            
            // Legs return to neutral
            if (this.leftLegGroup && this.rightLegGroup) {
                this.leftLegGroup.rotation.x = 0.2 * (1 - recoveryProgress);
                this.rightLegGroup.rotation.x = 0.2 * (1 - recoveryProgress);
            }
        }
        
        // Increase magma effect during hit reaction
        this.magmaEffectIntensity = 1.2;
        this.magmaEffectSpeed = 2.5;
    }
    
    /**
     * Apply magma effect to the golem
     * @param {number} time - Global time for continuous animation
     * @param {number} deltaTime - Time since last update
     * @private
     */
    _applyMagmaEffect(time, deltaTime) {
        // Update magma effect periodically to avoid performance issues
        this.lastMagmaUpdateTime += deltaTime;
        if (this.lastMagmaUpdateTime < this.magmaUpdateInterval) {
            return;
        }
        
        this.lastMagmaUpdateTime = 0;
        
        // Create a raycaster for wall checks
        const raycaster = new THREE.Raycaster();
        const golemPosition = this.golemModel.position.clone();
        
        // Find all magma materials in the model
        this.golemModel.traverse(child => {
            if (child.isMesh) {
                const materials = Array.isArray(child.material) ? child.material : [child.material];
                
                materials.forEach(material => {
                    // Check if this is a magma material (has emissive property)
                    if (material.emissive && material.emissiveIntensity > 0) {
                        // Get world position of this mesh part
                        const meshWorldPos = new THREE.Vector3();
                        child.getWorldPosition(meshWorldPos);
                        
                        // Cast rays in multiple directions to check for walls
                        const directions = [
                            new THREE.Vector3(1, 0, 0),
                            new THREE.Vector3(-1, 0, 0),
                            new THREE.Vector3(0, 0, 1),
                            new THREE.Vector3(0, 0, -1)
                        ];
                        
                        let isVisible = false;
                        for (const dir of directions) {
                            raycaster.set(meshWorldPos, dir);
                            // Check if there are any intersections with walls within 1 unit
                            const intersects = raycaster.intersectObjects(this.golemModel.parent.parent.children, true);
                            if (!intersects.length || intersects[0].distance > 1) {
                                isVisible = true;
                                break;
                            }
                        }
                        
                        if (isVisible) {
                            // Pulse the emissive intensity
                            const pulseValue = 0.5 + 0.5 * Math.sin(time * this.magmaEffectSpeed);
                            material.emissiveIntensity = this.magmaEffectIntensity * pulseValue;
                        } else {
                            // Hide the effect if behind walls
                            material.emissiveIntensity = 0;
                        }
                    }
                });
            }
        });
    }
}
