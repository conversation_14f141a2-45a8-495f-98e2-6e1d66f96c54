/**
 * MagmaGolemAnimationHandlerFix.js
 * 
 * This module provides a function to fix magma golem animation handler issues
 * by ensuring the animation handler is properly initialized and attached.
 */

import { SimpleMagmaGolemAnimationHandler } from './SimpleMagmaGolemAnimationHandler.js';

/**
 * Fix magma golem animation handler issues
 * @param {Object} enemy - The magma golem enemy object
 * @returns {Boolean} - True if fixed, false otherwise
 */
export function fixMagmaGolemAnimationHandler(enemy) {
    // Check if this is a magma golem enemy
    const isMagmaGolem = enemy.name.includes('magma_golem') || 
                    enemy.name.includes('MagmaGolem') || 
                    (enemy.userData && enemy.userData.type === 'magma_golem');
    
    if (!isMagmaGolem) {
        console.log(`[MagmaGolemAnimationHandlerFix] Not a magma golem enemy: ${enemy.name}`);
        return false;
    }
    
    // Check if animation handler already exists
    const hasAnimHandler = enemy.userData && enemy.userData.animationHandler;
    
    if (hasAnimHandler) {
        console.log(`[MagmaGolemAnimationHandlerFix] Animation handler already exists for ${enemy.name}`);
        return false;
    }
    
    console.log(`[MagmaGolemAnimationHandlerFix] Fixing animation handler for ${enemy.name}`);
    
    // Verify the magma golem model structure
    const hasBody = !!enemy.getObjectByName('body');
    const hasHead = !!enemy.getObjectByName('head');
    const hasLeftArm = !!enemy.getObjectByName('leftArm');
    const hasRightArm = !!enemy.getObjectByName('rightArm');
    const hasLeftLeg = !!enemy.getObjectByName('leftLeg');
    const hasRightLeg = !!enemy.getObjectByName('rightLeg');
    
    console.log(`[MagmaGolemAnimationHandlerFix] Magma golem model structure:`, {
        hasBody,
        hasHead,
        hasLeftArm,
        hasRightArm,
        hasLeftLeg,
        hasRightLeg
    });
    
    // Create and attach the animation handler
    try {
        // Use the SimpleMagmaGolemAnimationHandler
        enemy.userData.animationHandler = new SimpleMagmaGolemAnimationHandler(enemy);
        console.log(`[MagmaGolemAnimationHandlerFix] Successfully created SimpleMagmaGolemAnimationHandler for ${enemy.name}`);
        return true;
    } catch (error) {
        console.error(`[MagmaGolemAnimationHandlerFix] Error creating animation handler:`, error);
        return false;
    }
}
