/**
 * AI Factory
 * Creates AI brain instances based on enemy type and difficulty
 */
import { AI_BRAIN_TYPES } from '../entities/EnemyTypes.js';
import { RangedCombatAI } from './brains/RangedCombatAI.js';
import { MeleeCombatAI } from './brains/MeleeCombatAI.js';
import { MirrorCombatAI } from './brains/MirrorCombatAI.js';
import { AssassinCombatAI } from './brains/AssassinCombatAI.js';
import { FleeingCombatAI } from './brains/FleeingCombatAI.js';
import { FlyingCombatAI } from './brains/FlyingCombatAI.js';
import { BossCombatAI } from './brains/BossCombatAI.js';
import { EnhancedMeleeCombatAI } from './brains/EnhancedMeleeCombatAI.js';

/**
 * Create an AI brain for an enemy
 * @param {Object} enemy - The enemy object
 * @param {Object} enemyData - The enemy data (from userData)
 * @param {Object} scene - The scene object
 * @param {Object} player - The player object
 * @returns {Object} - The AI brain instance
 */
export function createAIBrain(enemy, enemyData, scene, player) {
    // Get AI type and difficulty
    const aiType = enemyData.aiType || AI_BRAIN_TYPES.RANGED; // Default to ranged
    const difficulty = enemyData.difficulty || 1; // Default to difficulty 1
    const enemyType = enemyData.type || 'unknown'; // Get specific enemy type

    // Define which enemy types use enhanced AI (fixed per enemy type)
    const enhancedEnemyTypes = new Set([
        'zombie',           // Enhanced melee AI
        'magma_golem',      // Enhanced melee AI
        'skeleton_archer',  // Enhanced ranged AI (when implemented)
        'bat',             // Enhanced flying AI (when implemented)
        'catacombs_overlord' // Enhanced boss AI (when implemented)
    ]);

    // Determine if enhanced AI should be used based on enemy type
    const useEnhancedAI = enhancedEnemyTypes.has(enemyType);

    console.log(`[AIFactory] Creating AI for ${enemyType} (${aiType}): Enhanced=${useEnhancedAI}`);

    // Create brain based on AI type
    switch (aiType) {
        case AI_BRAIN_TYPES.RANGED:
            // TODO: Create EnhancedRangedCombatAI for skeleton_archer
            return new RangedCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.MELEE:
            // Use enhanced melee AI for specific enemy types
            if (useEnhancedAI) {
                return new EnhancedMeleeCombatAI(enemy, enemyData, scene, player, difficulty);
            } else {
                return new MeleeCombatAI(enemy, enemyData, scene, player, difficulty);
            }

        case AI_BRAIN_TYPES.MIRROR:
            return new MirrorCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.ASSASSIN:
            return new AssassinCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.FLEEING:
            return new FleeingCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.FLYING:
            // TODO: Create EnhancedFlyingCombatAI for bat
            return new FlyingCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.BOSS:
            // TODO: Create EnhancedBossCombatAI for catacombs_overlord
            return new BossCombatAI(enemy, enemyData, scene, player, difficulty);

        default:
            console.warn(`Unknown AI type: ${aiType}, falling back to RangedCombatAI`);
            return new RangedCombatAI(enemy, enemyData, scene, player, difficulty);
    }
}

/**
 * Update AI brain
 * @param {Object} aiBrain - The AI brain instance
 * @param {Number} deltaTime - Time since last update
 * @param {Array} collisionObjects - Objects to check for collision
 * @param {Object} floorBounds - Bounds of the floor
 * @returns {Object} - Updated state data
 */
export function updateAIBrain(aiBrain, deltaTime, collisionObjects, floorBounds) {
    if (!aiBrain) return null;

    // Update brain
    return aiBrain.update(deltaTime, collisionObjects, floorBounds);
}

/**
 * Apply knockback to an enemy
 * @param {Object} aiBrain - The AI brain instance
 * @param {THREE.Vector3} direction - Direction of knockback
 * @param {Number} strength - Strength of knockback
 */
export function applyKnockback(aiBrain, direction, strength) {
    if (!aiBrain) return;

    // Apply knockback
    aiBrain.applyKnockback(direction, strength);
}
