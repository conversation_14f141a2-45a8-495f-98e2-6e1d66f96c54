/**
 * Group Coordinator
 * Manages coordination between groups of enemies
 */
import * as THREE from 'three';

// Singleton instance
let instance = null;

export class GroupCoordinator {
    /**
     * Constructor for the Group Coordinator
     * @returns {GroupCoordinator} - The singleton instance
     */
    constructor() {
        // Singleton pattern
        if (instance) {
            return instance;
        }
        
        instance = this;
        
        // Group data by enemy type
        this.groups = new Map();
        
        // Coordination timers
        this.updateInterval = 1.0; // Update group coordination every second
        this.timeSinceLastUpdate = 0;
    }
    
    /**
     * Register an enemy with the coordinator
     * @param {Object} enemy - The enemy object
     * @param {Object} aiBrain - The AI brain
     */
    registerEnemy(enemy, aiBrain) {
        const enemyType = enemy.userData.type || 'unknown';
        
        // Create group if it doesn't exist
        if (!this.groups.has(enemyType)) {
            this.groups.set(enemyType, {
                members: [],
                attackingMembers: [],
                flankingPositions: [],
                lastAttackTime: 0
            });
        }
        
        // Add enemy to group
        const group = this.groups.get(enemyType);
        group.members.push({
            enemy,
            aiBrain,
            id: enemy.id || Math.random().toString(36).substr(2, 9),
            isAttacking: false,
            flankingPosition: null
        });
        
        // Calculate flanking positions for the group
        this._updateFlankingPositions(enemyType);
    }
    
    /**
     * Unregister an enemy from the coordinator
     * @param {Object} enemy - The enemy object
     */
    unregisterEnemy(enemy) {
        const enemyType = enemy.userData.type || 'unknown';
        
        // Check if group exists
        if (!this.groups.has(enemyType)) {
            return;
        }
        
        // Get group
        const group = this.groups.get(enemyType);
        
        // Find enemy in group
        const index = group.members.findIndex(member => 
            member.enemy === enemy || member.id === enemy.id
        );
        
        // Remove enemy from group
        if (index !== -1) {
            group.members.splice(index, 1);
            
            // Remove from attacking members if present
            const attackIndex = group.attackingMembers.indexOf(group.members[index]);
            if (attackIndex !== -1) {
                group.attackingMembers.splice(attackIndex, 1);
            }
            
            // Update flanking positions
            this._updateFlankingPositions(enemyType);
            
            // Remove group if empty
            if (group.members.length === 0) {
                this.groups.delete(enemyType);
            }
        }
    }
    
    /**
     * Update the coordinator
     * @param {Number} deltaTime - Time since last update
     */
    update(deltaTime) {
        this.timeSinceLastUpdate += deltaTime;
        
        // Update group coordination periodically
        if (this.timeSinceLastUpdate >= this.updateInterval) {
            this.timeSinceLastUpdate = 0;
            
            // Update each group
            for (const [enemyType, group] of this.groups.entries()) {
                this._updateGroup(enemyType, group);
            }
        }
    }
    
    /**
     * Update a specific group
     * @param {String} enemyType - The enemy type
     * @param {Object} group - The group data
     * @private
     */
    _updateGroup(enemyType, group) {
        // Skip if group is empty
        if (group.members.length === 0) {
            return;
        }
        
        // Update flanking positions
        this._updateFlankingPositions(enemyType);
        
        // Assign flanking positions to members
        this._assignFlankingPositions(group);
        
        // Coordinate attacks based on enemy type
        switch (enemyType) {
            case 'skeleton_archer':
                this._coordinateRangedAttacks(group);
                break;
                
            case 'skeleton_warrior':
                this._coordinateMeleeAttacks(group);
                break;
                
            // Add more enemy types as needed
                
            default:
                // No special coordination for unknown types
                break;
        }
    }
    
    /**
     * Update flanking positions for a group
     * @param {String} enemyType - The enemy type
     * @private
     */
    _updateFlankingPositions(enemyType) {
        const group = this.groups.get(enemyType);
        
        // Skip if group is empty
        if (!group || group.members.length === 0) {
            return;
        }
        
        // Get player position from first member (all should have same player reference)
        const firstMember = group.members[0];
        if (!firstMember.aiBrain || !firstMember.aiBrain.player) {
            return;
        }
        
        const player = firstMember.aiBrain.player;
        const playerPosition = player.position.clone();
        
        // Calculate flanking positions around player
        const numPositions = Math.max(group.members.length, 4); // At least 4 positions
        const radius = 8.0; // Distance from player
        
        const positions = [];
        for (let i = 0; i < numPositions; i++) {
            const angle = (i / numPositions) * Math.PI * 2;
            const x = playerPosition.x + Math.cos(angle) * radius;
            const z = playerPosition.z + Math.sin(angle) * radius;
            
            positions.push(new THREE.Vector3(x, playerPosition.y, z));
        }
        
        group.flankingPositions = positions;
    }
    
    /**
     * Assign flanking positions to group members
     * @param {Object} group - The group data
     * @private
     */
    _assignFlankingPositions(group) {
        // Skip if no positions
        if (group.flankingPositions.length === 0) {
            return;
        }
        
        // Assign each member to the closest available position
        const assignedPositions = new Set();
        
        for (const member of group.members) {
            if (!member.enemy || !member.aiBrain) continue;
            
            // Find closest unassigned position
            let closestPosition = null;
            let closestDistance = Infinity;
            
            for (let i = 0; i < group.flankingPositions.length; i++) {
                // Skip if position already assigned
                if (assignedPositions.has(i)) continue;
                
                const position = group.flankingPositions[i];
                const distance = member.enemy.position.distanceTo(position);
                
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestPosition = i;
                }
            }
            
            // Assign position
            if (closestPosition !== null) {
                assignedPositions.add(closestPosition);
                member.flankingPosition = group.flankingPositions[closestPosition];
                
                // Update AI brain
                if (member.aiBrain.flankingTarget) {
                    member.aiBrain.flankingTarget.copy(member.flankingPosition);
                } else {
                    member.aiBrain.flankingTarget = member.flankingPosition.clone();
                }
            }
        }
    }
    
    /**
     * Coordinate ranged attacks for a group
     * @param {Object} group - The group data
     * @private
     */
    _coordinateRangedAttacks(group) {
        // Skip if group is empty
        if (group.members.length === 0) {
            return;
        }
        
        // Get current time
        const now = Date.now();
        
        // Minimum time between group attacks (ms)
        const minAttackInterval = 1000; // 1 second
        
        // Check if enough time has passed since last attack
        if (now - group.lastAttackTime < minAttackInterval) {
            return;
        }
        
        // Determine how many archers should attack at once
        // More archers = more simultaneous attackers
        const maxSimultaneousAttackers = Math.ceil(group.members.length / 2);
        
        // Count current attackers
        let currentAttackers = 0;
        for (const member of group.members) {
            if (member.isAttacking) {
                currentAttackers++;
            }
        }
        
        // If we already have enough attackers, don't add more
        if (currentAttackers >= maxSimultaneousAttackers) {
            return;
        }
        
        // Find members that can attack (not already attacking and ready)
        const readyMembers = group.members.filter(member => 
            !member.isAttacking && 
            member.aiBrain && 
            member.aiBrain.timeSinceLastAttack >= member.aiBrain.attackCooldown
        );
        
        // Randomly select members to attack
        const numNewAttackers = Math.min(
            maxSimultaneousAttackers - currentAttackers,
            readyMembers.length
        );
        
        // Shuffle ready members
        const shuffled = [...readyMembers].sort(() => 0.5 - Math.random());
        
        // Select attackers
        for (let i = 0; i < numNewAttackers; i++) {
            const member = shuffled[i];
            
            // Signal to AI brain to attack
            if (member.aiBrain.initiateAttack) {
                member.aiBrain.initiateAttack();
                member.isAttacking = true;
                
                // Add to attacking members
                group.attackingMembers.push(member);
                
                // Update last attack time
                group.lastAttackTime = now;
            }
        }
    }
    
    /**
     * Coordinate melee attacks for a group
     * @param {Object} group - The group data
     * @private
     */
    _coordinateMeleeAttacks(group) {
        // Skip if group is empty
        if (group.members.length === 0) {
            return;
        }
        
        // Get current time
        const now = Date.now();
        
        // Minimum time between group attacks (ms)
        const minAttackInterval = 800; // 0.8 seconds
        
        // Check if enough time has passed since last attack
        if (now - group.lastAttackTime < minAttackInterval) {
            return;
        }
        
        // For melee, we want to attack one at a time to avoid crowding
        const maxSimultaneousAttackers = Math.min(2, Math.ceil(group.members.length / 3));
        
        // Count current attackers
        let currentAttackers = 0;
        for (const member of group.members) {
            if (member.isAttacking) {
                currentAttackers++;
            }
        }
        
        // If we already have enough attackers, don't add more
        if (currentAttackers >= maxSimultaneousAttackers) {
            return;
        }
        
        // Find members that can attack (not already attacking, ready, and close enough)
        const readyMembers = group.members.filter(member => {
            if (!member.isAttacking && 
                member.aiBrain && 
                member.aiBrain.timeSinceLastAttack >= member.aiBrain.attackCooldown) {
                
                // Check if close enough to player
                if (member.aiBrain.player && member.enemy) {
                    const distanceToPlayer = member.enemy.position.distanceTo(member.aiBrain.player.position);
                    return distanceToPlayer <= (member.aiBrain.attackRange || 3.0);
                }
            }
            return false;
        });
        
        // Randomly select members to attack
        const numNewAttackers = Math.min(
            maxSimultaneousAttackers - currentAttackers,
            readyMembers.length
        );
        
        // Shuffle ready members
        const shuffled = [...readyMembers].sort(() => 0.5 - Math.random());
        
        // Select attackers
        for (let i = 0; i < numNewAttackers; i++) {
            const member = shuffled[i];
            
            // Signal to AI brain to attack
            if (member.aiBrain.initiateAttack) {
                member.aiBrain.initiateAttack();
                member.isAttacking = true;
                
                // Add to attacking members
                group.attackingMembers.push(member);
                
                // Update last attack time
                group.lastAttackTime = now;
            }
        }
    }
    
    /**
     * Notify that an enemy has finished attacking
     * @param {Object} enemy - The enemy object
     */
    notifyAttackComplete(enemy) {
        const enemyType = enemy.userData.type || 'unknown';
        
        // Check if group exists
        if (!this.groups.has(enemyType)) {
            return;
        }
        
        // Get group
        const group = this.groups.get(enemyType);
        
        // Find enemy in group
        const member = group.members.find(m => 
            m.enemy === enemy || m.id === enemy.id
        );
        
        if (member) {
            member.isAttacking = false;
            
            // Remove from attacking members
            const index = group.attackingMembers.indexOf(member);
            if (index !== -1) {
                group.attackingMembers.splice(index, 1);
            }
        }
    }
}

// Export singleton instance
export const groupCoordinator = new GroupCoordinator();
