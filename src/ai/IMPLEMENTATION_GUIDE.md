# Combat AI System Implementation Guide

This guide explains how to use and extend the modular combat AI system.

## Overview

The combat AI system is designed to be modular, scalable, and easy to extend. It consists of:

1. **AI Brains**: Different AI behaviors (Ranged, <PERSON>ee, <PERSON>, etc.)
2. **AI States**: Shared states and transitions
3. **AI Memory**: System for tracking player behavior
4. **Projectile System**: Modular projectile types

## Using the AI System

### Creating a New Enemy Type

1. Add a new entry to `ENEMY_TYPES_DATA` in `src/entities/EnemyTypes.js`:

```javascript
my_new_enemy: {
    // Visuals
    modelPrefab: createMyEnemyModel, // Function to create the model
    size: 0.8,
    // Base Stats
    health: 40,
    baseSpeed: 1.7,
    speedVariation: 0.2,
    mass: 1.1, // For knockback calculations
    // AI Parameters
    aiType: AI_BRAIN_TYPES.MELEE, // Choose an AI brain type
    difficulty: 2, // Difficulty level (1-5)
    attackCooldown: 1.8,
    preferredRange: 2.5,
    attackRange: 3.0,
    // Other parameters specific to the AI type
    ...
}
```

2. Add the enemy type to the `ENEMY_TYPES` enum:

```javascript
export const ENEMY_TYPES = {
    // Existing types...
    MY_NEW_ENEMY: 'my_new_enemy'
};
```

### Spawning an Enemy

Use the existing `_spawnEnemy` method in `DungeonHandler.js`:

```javascript
this._spawnEnemy(ENEMY_TYPES.MY_NEW_ENEMY);
```

### Setting Difficulty

Set the `difficulty` parameter in the enemy type definition (1-5):

```javascript
difficulty: 3, // Higher difficulty
```

Or dynamically when spawning:

```javascript
const enemy = this._spawnEnemy(ENEMY_TYPES.SKELETON_ARCHER);
enemy.userData.difficulty = 4; // Increase difficulty
```

## Extending the AI System

### Creating a New AI Brain Type

1. Create a new file in `src/ai/brains/` (e.g., `MyNewAI.js`)
2. Extend the `AIBrain` base class:

```javascript
import { AIBrain } from '../AIBrain.js';
import { AIStates } from '../AIStates.js';

export class MyNewAI extends AIBrain {
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Initialize custom parameters
        this.myCustomParam = this._getScaledValue(10, 5, 15); // Scales with difficulty
    }

    // Override methods
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Custom state transitions
    }

    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Custom behavior
    }

    // Add custom methods
    _myCustomMethod() {
        // Custom functionality
    }
}
```

3. Add the new AI type to `AI_BRAIN_TYPES` in `src/entities/EnemyTypes.js`:

```javascript
export const AI_BRAIN_TYPES = {
    // Existing types...
    MY_NEW_TYPE: 'my_new_type'
};
```

4. Update the `createAIBrain` function in `src/ai/AIFactory.js`:

```javascript
import { MyNewAI } from './brains/MyNewAI.js';

// In the switch statement:
case AI_BRAIN_TYPES.MY_NEW_TYPE:
    return new MyNewAI(enemy, enemyData, scene, player, difficulty);
```

### Creating a New Projectile Type

Add a new entry to `ProjectileTypes` in `src/projectiles/ProjectileTypes.js`:

```javascript
my_new_projectile: {
    name: 'My Projectile',
    damage: 7,
    speed: 8.0,
    range: 14.0,
    size: 0.3,
    gravity: -3.0,
    color: 0xFF0000, // Red
    trailEffect: true,
    trailColor: 0xFF8800,
    trailLength: 10,
    impactEffect: 'my_impact',
    createMesh: (position) => {
        // Custom mesh creation
        const geometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
        const material = new THREE.MeshBasicMaterial({ color: 0xFF0000 });
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(position);
        return mesh;
    }
}
```

## AI Behavior Parameters

### Ranged Combat AI

- `preferredRange`: Optimal distance from player
- `moveAwayRange`: Distance at which to start moving away
- `maxRange`: Maximum effective range
- `strafeChance`: Probability of strafing
- `predictionAccuracy`: Accuracy of prediction shooting

### Melee Combat AI

- `preferredRange`: Optimal distance from player
- `attackRange`: Range at which to attack
- `chargeRange`: Range at which to start charging
- `blockChance`: Probability of blocking
- `circleChance`: Probability of circling

### Assassin Combat AI

- `preferredRange`: Optimal distance from player
- `attackRange`: Range at which to attack
- `stalkRange`: Range at which to start stalking
- `backstabMultiplier`: Damage multiplier for backstab
- `stealthLevel`: How hard to detect (0-1)

### Flying Combat AI

- `preferredRange`: Optimal distance from player
- `attackRange`: Range at which to attack
- `minHoverHeight`: Minimum hover height
- `maxHoverHeight`: Maximum hover height
- `swoopChance`: Probability of swooping

### Fleeing Combat AI

- `fleeThreshold`: Distance at which to start fleeing
- `panicThreshold`: Distance at which to start panicking
- `safeDistance`: Distance considered safe

### Mirror Combat AI

- `preferredRange`: Optimal distance from player
- `mirrorDistance`: Distance to maintain from player
- `mirrorAccuracy`: Accuracy of mirroring (0-1)
- `actionDelay`: Delay before mirroring action

### Boss Combat AI

- `phaseThresholds`: Health percentages for phase transitions
- `specialAttackCooldown`: Cooldown for special attacks
- `teleportCooldown`: Cooldown for teleporting

## Difficulty Scaling

All AI brains scale their parameters based on difficulty level (1-5):

- **Level 1**: Easiest, slow reactions, low accuracy
- **Level 2**: Easy, slightly better reactions and accuracy
- **Level 3**: Medium, balanced reactions and accuracy
- **Level 4**: Hard, good reactions and accuracy
- **Level 5**: Hardest, fast reactions, high accuracy

Parameters that scale with difficulty include:
- Reaction time
- Accuracy
- Aggressiveness
- Dodge chance
- Attack patterns
- Special ability usage

## Knockback System

Apply knockback to enemies when hit:

```javascript
// In your attack code:
if (hit) {
    const knockbackDirection = enemy.position.clone().sub(player.position).normalize();
    const knockbackStrength = 20.0; // Optimized for impactful 3-4 unit knockback distance

    // Apply knockback through the AI brain
    if (enemy.userData.aiBrain) {
        applyKnockback(enemy.userData.aiBrain, knockbackDirection, knockbackStrength);
    }
}
```

## Memory System

The AI memory system allows enemies to adapt to player behavior:

```javascript
// Record player attack
aiBrain.memory.recordPlayerAttack({
    type: 'melee',
    damage: 10,
    hit: true
});

// Get adaptive response
const adaptiveResponse = aiBrain.memory.getAdaptiveResponse();
if (adaptiveResponse.shouldKeepDistance) {
    // Adjust AI behavior
}
```

## Troubleshooting

- **AI not moving**: Check if the enemy has a valid AI brain and the correct AI type
- **AI stuck in walls**: Ensure collision detection is working properly
- **AI not attacking**: Check attack cooldown and range parameters
- **Projectiles not appearing**: Verify projectile type exists and is correctly assigned

## Performance Considerations

- Use appropriate difficulty levels based on game performance
- Consider reducing AI complexity for large numbers of enemies
- Use simpler projectile effects for better performance
- Disable memory system for distant enemies
