import * as THREE from 'three';

/**
 * Utility class for frustum culling to optimize rendering and updates
 */
class FrustumCulling {
    constructor() {
        this.frustum = new THREE.Frustum();
        this.projScreenMatrix = new THREE.Matrix4();
        this.tempBox = new THREE.Box3();
        this.tempSphere = new THREE.Sphere();
        this.tempVector = new THREE.Vector3();
        
        // Performance tracking
        this.stats = {
            totalObjects: 0,
            culledObjects: 0,
            lastUpdateTime: 0
        };
    }

    /**
     * Update the frustum based on the current camera
     * @param {THREE.Camera} camera - The camera to use for frustum calculation
     */
    updateFrustum(camera) {
        this.projScreenMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
        this.frustum.setFromProjectionMatrix(this.projScreenMatrix);
        this.stats.lastUpdateTime = performance.now();
    }

    /**
     * Check if an object is visible in the frustum
     * @param {THREE.Object3D} object - The object to check
     * @param {number} [boundingVolumeExpansion=0] - Optional expansion for the bounding volume
     * @returns {boolean} - True if the object is visible
     */
    isVisible(object, boundingVolumeExpansion = 0) {
        if (!object) return false;

        // For objects with a custom bounding sphere in userData
        if (object.userData && object.userData.boundingSphere) {
            this.tempSphere.copy(object.userData.boundingSphere);
            this.tempSphere.radius += boundingVolumeExpansion;
            return this.frustum.intersectsSphere(this.tempSphere);
        }

        // For objects with geometry
        if (object.geometry) {
            if (object.geometry.boundingSphere === null) {
                object.geometry.computeBoundingSphere();
            }
            
            if (object.geometry.boundingSphere) {
                // Use geometry's bounding sphere, transformed to world space
                this.tempSphere.copy(object.geometry.boundingSphere);
                this.tempSphere.radius += boundingVolumeExpansion;
                this.tempSphere.applyMatrix4(object.matrixWorld);
                return this.frustum.intersectsSphere(this.tempSphere);
            }
        }

        // Fallback to position check with a small radius
        this.tempVector.setFromMatrixPosition(object.matrixWorld);
        return this.frustum.containsPoint(this.tempVector);
    }

    /**
     * Check if a group of objects is visible in the frustum
     * @param {Array<THREE.Object3D>} objects - Array of objects to check
     * @param {number} [boundingVolumeExpansion=0] - Optional expansion for the bounding volume
     * @returns {Array<boolean>} - Array of visibility flags
     */
    getVisibilityFlags(objects, boundingVolumeExpansion = 0) {
        this.stats.totalObjects = objects.length;
        this.stats.culledObjects = 0;
        
        const visibilityFlags = objects.map(object => {
            const isVisible = this.isVisible(object, boundingVolumeExpansion);
            if (!isVisible) this.stats.culledObjects++;
            return isVisible;
        });
        
        return visibilityFlags;
    }

    /**
     * Get performance statistics
     * @returns {Object} - Statistics object
     */
    getStats() {
        return {
            ...this.stats,
            cullingRatio: this.stats.totalObjects > 0 ? 
                this.stats.culledObjects / this.stats.totalObjects : 0
        };
    }
}

export default FrustumCulling;
