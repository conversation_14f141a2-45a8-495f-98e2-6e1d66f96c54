import { MusicConductor } from '../audio/MusicConductor.js';

class AudioManager {
    constructor() {
        this.sounds = {};
        this.isMuted = false;
        this.audioContext = null;
        this.masterGain = null;

        // Initialize the music conductor
        this.musicConductor = new MusicConductor();

        this._loadSounds();
        this._initAudioContext();
    }

    _initAudioContext() {
        try {
            window.AudioContext = window.AudioContext || window.webkitAudioContext;
            this.audioContext = new AudioContext();
            this.masterGain = this.audioContext.createGain();
            this.masterGain.connect(this.audioContext.destination);
            console.log("AudioContext initialized.");
        } catch (e) {
            console.error("Web Audio API is not supported in this browser.");
            // Fallback or disable audio features
        }
    }

    _loadSounds() {
        // Map keys to the corresponding HTMLAudioElement IDs
        const soundMap = {
            'chat': 'chat-sound',
            'footstep': 'footstep-sound',
            'bg_ambient_surround': 'bg-music',
            'bg_ambient_music': 'ambient-music-track',
            'reveal': 'reveal-sound',
            'select': 'select-sound',
            'start_button': 'start-button-sound',
            'creepy_noise': 'creepy-noise-sound',
            'flicker_laugh': 'flicker_laugh_sound'
        };

        for (const key in soundMap) {
            const element = document.getElementById(soundMap[key]);
            if (element) {
                this.sounds[key] = element;
                console.log(`[AudioManager] Loaded sound '${key}'`);
            } else {
                console.warn(`[AudioManager] Audio element with ID '${soundMap[key]}' not found for key '${key}'.`);
            }
        }
    }

    _resumeContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            console.log("Attempting to resume suspended AudioContext...");
            this.audioContext.resume().then(() => {
                console.log("AudioContext resumed successfully.");
            }).catch(e => console.error("Error resuming AudioContext:", e));
        }
    }

    playSound(key, loop = false, volume = 1.0) {
        this._resumeContext(); // Attempt to resume context on interaction
        const sound = this.sounds[key];
        if (sound && !this.isMuted) {
            try {
                sound.loop = loop;
                sound.volume = volume;
                sound.currentTime = 0;
                const playPromise = sound.play();
                if (playPromise !== undefined) {
                    playPromise.catch(error => {
                        console.error(`Error playing sound '${key}':`, error);
                        // Autoplay might have been blocked
                    });
                }
            } catch (error) {
                console.error(`Error trying to play sound '${key}':`, error);
            }
        } else if (!sound) {
            console.warn(`[AudioManager] Sound key '${key}' not found.`);
        }
    }

    stopSound(key) {
        const sound = this.sounds[key];
        if (sound) {
            sound.pause();
            sound.currentTime = 0;
        }
    }

    stopAllSounds() {
        console.log("[AudioManager] Stopping all sounds.");
        for (const key in this.sounds) {
             this.stopSound(key);
        }
    }

    // Example fade function (more robust fading needed)
    fadeVolume(key, targetVolume, duration = 1000) {
        const sound = this.sounds[key];
        if (!sound) return;

        const startVolume = sound.volume;
        const startTime = performance.now();

        const fade = (currentTime) => {
            const elapsedTime = currentTime - startTime;
            const progress = Math.min(elapsedTime / duration, 1);
            sound.volume = startVolume + (targetVolume - startVolume) * progress;

            if (progress < 1) {
                requestAnimationFrame(fade);
            } else {
                 if (targetVolume === 0) {
                      this.stopSound(key);
                 }
                 console.log(`[AudioManager] Fade complete for '${key}' to volume ${targetVolume}`);
            }
        };
        requestAnimationFrame(fade);
    }

    toggleMute(buttonElement) {
        this.isMuted = !this.isMuted;
        console.log(`[AudioManager] Mute toggled: ${this.isMuted}`);
        if (this.masterGain) {
             this.masterGain.gain.setValueAtTime(this.isMuted ? 0 : 1, this.audioContext.currentTime);
        }
        // Also pause/resume looping sounds
        for (const key in this.sounds) {
            if (this.sounds[key].loop) {
                 if (this.isMuted) {
                     this.sounds[key].pause();
                 } else {
                     this.sounds[key].play().catch(e => console.warn(`Could not resume loop for ${key}:`, e));
                 }
            }
        }
        if (buttonElement) {
             buttonElement.textContent = this.isMuted ? 'Unmute' : 'Mute';
        }

        // Also mute/unmute the music conductor
        if (this.musicConductor && this.musicConductor.initialized) {
            if (this.isMuted) {
                // Mute music conductor
                if (this.musicConductor.effects && this.musicConductor.effects.mainVolume) {
                    this.musicConductor.effects.mainVolume.volume.value = -Infinity;
                }
            } else {
                // Unmute music conductor
                if (this.musicConductor.effects && this.musicConductor.effects.mainVolume) {
                    this.musicConductor.effects.mainVolume.volume.value = 0;
                }
            }
        }
    }

    // Basic Mute Button UI
     _createMuteButton() {
         const button = document.createElement('button');
         button.id = 'mute-button'; // Use ID from HTML if exists, or style new one
         button.textContent = 'Mute';
         button.style.position = 'absolute';
         button.style.top = '10px';
         button.style.right = '10px';
         button.style.padding = '5px 10px';
         button.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
         button.style.border = '1px solid #fff';
         button.style.borderRadius = '5px';
         button.style.color = '#fff';
         button.style.pointerEvents = 'auto';
         button.style.cursor = 'pointer';
         button.style.zIndex = '100';
         button.onclick = () => this.toggleMute(button);
         document.body.appendChild(button);
     }
    // --- Music System Methods ---

    /**
     * Initialize the music system
     * Must be called after user interaction
     */
    async initMusicSystem() {
        if (this.musicConductor) {
            return await this.musicConductor.init();
        }
        return false;
    }

    /**
     * Start playing music for an area
     * @param {string} areaName - The area ID to start music for
     */
    async startAreaMusic(areaName) {
        if (this.musicConductor && this.musicConductor.initialized) {
            return await this.musicConductor.start(areaName);
        } else if (this.musicConductor) {
            // Try to initialize first
            await this.musicConductor.init();
            return await this.musicConductor.start(areaName);
        }
        return false;
    }

    /**
     * Transition to a new area with proper musical transition
     * @param {string} areaName - The area ID to transition to
     */
    async transitionAreaMusic(areaName) {
        if (this.musicConductor && this.musicConductor.initialized) {
            return await this.musicConductor.transitionTo(areaName);
        }
        return false;
    }

    /**
     * Enter a special room within the current area
     * @param {string} area - The area ID
     * @param {string} room - The room type (e.g., "shop", "altar")
     */
    async enterSpecialRoomMusic(area, room) {
        if (this.musicConductor && this.musicConductor.initialized) {
            return await this.musicConductor.enterSpecialRoom(area, room);
        }
        return false;
    }

    /**
     * Exit a special room and return to the main area music
     * @param {string} area - The area ID
     * @param {string} room - The room type (e.g., "shop", "altar")
     */
    async exitSpecialRoomMusic(area, room) {
        if (this.musicConductor && this.musicConductor.initialized) {
            return await this.musicConductor.exitSpecialRoom(area, room);
        }
        return false;
    }

    /**
     * Update combat state based on enemy count and player health
     * @param {number} enemyCount - Number of active enemies
     * @param {number} currentHealth - Current player health
     * @param {number} maxHealth - Maximum player health
     * @param {boolean} enemyKilled - Whether an enemy was just killed
     * @param {boolean} enteredRoom - Whether the player just entered a room
     */
    updateCombatState(enemyCount, currentHealth, maxHealth, enemyKilled = false, enteredRoom = false) {
        if (this.musicConductor && this.musicConductor.initialized) {
            return this.musicConductor.updateCombatState(enemyCount, currentHealth, maxHealth, enemyKilled, enteredRoom);
        }
        return false;
    }

    /**
     * Handle player being hit
     * Plays hit stinger and applies temporary effects
     */
    onPlayerHit() {
        if (this.musicConductor && this.musicConductor.initialized) {
            return this.musicConductor.onPlayerHit();
        }
        return false;
    }

    /**
     * Toggle debug mode for the music system
     */
    toggleMusicDebug() {
        if (this.musicConductor) {
            return this.musicConductor.toggleDebug();
        }
        return false;
    }

    /**
     * Clean up music system resources
     */
    disposeMusicSystem() {
        if (this.musicConductor) {
            this.musicConductor.dispose();
        }
    }
}

export default AudioManager;