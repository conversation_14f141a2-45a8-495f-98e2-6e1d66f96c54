import * as THREE from 'three';

/**
 * Animation Analyzer
 *
 * A utility for analyzing and evaluating the quality of procedural animations.
 * This tool helps identify issues with animations such as:
 * - Unnatural movements
 * - Jittering or stuttering
 * - Unrealistic joint rotations
 * - Inconsistent timing
 * - Asymmetrical movement
 * - Unrealistic weight distribution
 */

class AnimationAnalyzer {
    constructor() {
        // Analysis configuration
        this.config = {
            // Thresholds for acceptable motion
            maxRotationSpeed: Math.PI * 2, // radians per second
            maxAcceleration: Math.PI * 4,  // radians per second^2
            naturalJointLimits: {
                // Natural joint rotation limits in radians
                arm: {
                    x: { min: -Math.PI/2, max: Math.PI/2 },
                    y: { min: -Math.PI/4, max: Math.PI/4 },
                    z: { min: -Math.PI/3, max: Math.PI/3 }
                },
                leg: {
                    x: { min: -Math.PI/2, max: Math.PI/2 },
                    y: { min: -Math.PI/8, max: Math.PI/8 },
                    z: { min: -Math.PI/8, max: Math.PI/8 }
                }
            },
            // Timing consistency thresholds
            cycleTolerance: 0.1, // 10% variation allowed in cycle timing
            // Smoothness metrics
            jitterThreshold: 0.05, // radians of unexpected movement

            // Advanced metrics
            symmetryTolerance: 0.15, // 15% asymmetry allowed between left/right
            weightShiftFactor: 0.7, // How much weight shift is expected during walk
            walkCyclePhaseOffset: Math.PI, // Expected phase offset between legs
            armLegCoordinationFactor: 0.8, // Expected coordination between arms and legs

            // Animation-specific expectations
            animationExpectations: {
                IDLE: {
                    movementMagnitude: 0.1, // Small movements
                    symmetryImportance: 0.7, // Fairly symmetrical
                    rhythmImportance: 0.5, // Moderate rhythm importance
                    weightShiftImportance: 0.3 // Minor weight shifts
                },
                MOVING: {
                    movementMagnitude: 0.5, // Larger movements
                    symmetryImportance: 0.9, // Highly symmetrical (opposite sides)
                    rhythmImportance: 0.9, // High rhythm importance
                    weightShiftImportance: 0.8 // Significant weight shifts
                },
                AIMING: {
                    movementMagnitude: 0.2, // Small movements
                    symmetryImportance: 0.3, // Asymmetrical (aiming)
                    rhythmImportance: 0.3, // Low rhythm importance
                    weightShiftImportance: 0.5 // Moderate weight shifts
                },
                SHOOTING: {
                    movementMagnitude: 0.3, // Moderate movements
                    symmetryImportance: 0.2, // Very asymmetrical (shooting)
                    rhythmImportance: 0.4, // Moderate rhythm importance
                    weightShiftImportance: 0.6 // Moderate weight shifts
                },
                KNOCKBACK: {
                    movementMagnitude: 0.6, // Large movements
                    symmetryImportance: 0.4, // Somewhat asymmetrical
                    rhythmImportance: 0.2, // Low rhythm importance
                    weightShiftImportance: 0.9 // Major weight shifts
                },
                DODGING: {
                    movementMagnitude: 0.7, // Large movements
                    symmetryImportance: 0.3, // Asymmetrical
                    rhythmImportance: 0.6, // Moderate rhythm importance
                    weightShiftImportance: 0.9 // Major weight shifts
                }
            }
        };

        // Analysis state
        this.state = {
            previousRotations: new Map(), // Map of limb name to previous rotation
            rotationVelocities: new Map(), // Map of limb name to rotation velocity
            rotationAccelerations: new Map(), // Map of limb name to rotation acceleration
            cycleTimings: new Map(), // Map of animation type to cycle timings
            lastUpdateTime: 0, // Last time the analysis was updated
            issues: [], // List of detected issues
            currentAnimState: null, // Current animation state
            limbPositions: {}, // Current positions of limbs
            scores: {
                smoothness: 1.0,
                naturalness: 1.0,
                consistency: 1.0,
                symmetry: 1.0,
                weightDistribution: 1.0,
                overall: 1.0
            }
        };
    }

    /**
     * Reset the analyzer state
     */
    reset() {
        this.state.previousRotations.clear();
        this.state.rotationVelocities.clear();
        this.state.rotationAccelerations.clear();
        this.state.cycleTimings.clear();
        this.state.issues = [];
        this.state.currentAnimState = null;
        this.state.limbPositions = {};
        this.state.scores = {
            smoothness: 1.0,
            naturalness: 1.0,
            consistency: 1.0,
            symmetry: 1.0,
            weightDistribution: 1.0,
            overall: 1.0
        };
        this.state.lastUpdateTime = performance.now() / 1000;
    }

    /**
     * Update the analyzer with the current state of the skeleton
     * @param {Object} skeleton - The skeleton object
     * @param {String} animationState - The current animation state
     * @param {Number} animationTime - The current animation time
     */
    update(skeleton, animationState, animationTime) {
        const currentTime = performance.now() / 1000;
        const deltaTime = currentTime - this.state.lastUpdateTime;
        this.state.lastUpdateTime = currentTime;

        if (deltaTime <= 0) return; // Skip if no time has passed

        // Store current animation state
        this.state.currentAnimState = animationState;

        // Clear previous issues
        this.state.issues = [];

        // Get limbs
        const limbs = this._getLimbs(skeleton);
        if (!limbs) return;

        // Store limb positions for weight distribution analysis
        this.state.limbPositions = {};
        for (const [name, limb] of Object.entries(limbs)) {
            if (!limb) continue;
            this.state.limbPositions[name] = {
                position: limb.position.clone(),
                worldPosition: new THREE.Vector3().setFromMatrixPosition(limb.matrixWorld)
            };
        }

        // Analyze each limb
        for (const [name, limb] of Object.entries(limbs)) {
            if (!limb) continue;

            // Get current rotation
            const rotation = {
                x: limb.rotation.x,
                y: limb.rotation.y,
                z: limb.rotation.z
            };

            // Check joint limits
            this._checkJointLimits(name, rotation);

            // Calculate velocities and accelerations
            if (this.state.previousRotations.has(name)) {
                const prevRotation = this.state.previousRotations.get(name);
                const velocity = {
                    x: (rotation.x - prevRotation.x) / deltaTime,
                    y: (rotation.y - prevRotation.y) / deltaTime,
                    z: (rotation.z - prevRotation.z) / deltaTime
                };

                // Check for jitter/stuttering
                this._checkForJitter(name, velocity, deltaTime);

                // Store velocity
                this.state.rotationVelocities.set(name, velocity);

                // Calculate acceleration if we have previous velocity
                if (this.state.rotationVelocities.has(name)) {
                    const prevVelocity = this.state.rotationVelocities.get(name);
                    const acceleration = {
                        x: (velocity.x - prevVelocity.x) / deltaTime,
                        y: (velocity.y - prevVelocity.y) / deltaTime,
                        z: (velocity.z - prevVelocity.z) / deltaTime
                    };

                    // Check for unnatural acceleration
                    this._checkAcceleration(name, acceleration);

                    // Store acceleration
                    this.state.rotationAccelerations.set(name, acceleration);
                }
            }

            // Store current rotation for next update
            this.state.previousRotations.set(name, rotation);
        }

        // Analyze animation cycle consistency
        this._analyzeAnimationCycle(animationState, animationTime);

        // Analyze symmetry between left and right limbs
        this._analyzeSymmetry(limbs);

        // Analyze weight distribution
        this._analyzeWeightDistribution(skeleton, animationState);

        // Calculate overall scores
        this._calculateScores();
    }

    /**
     * Get the limbs from the skeleton
     * @param {Object} skeleton - The skeleton object
     * @returns {Object} Object containing limbs
     * @private
     */
    _getLimbs(skeleton) {
        if (!skeleton) return null;

        return {
            leftLeg: skeleton.getObjectByName('leftLeg'),
            rightLeg: skeleton.getObjectByName('rightLeg'),
            leftArm: skeleton.getObjectByName('leftArm'),
            rightArm: skeleton.getObjectByName('rightArm')
        };
    }

    /**
     * Check if joint rotations are within natural limits
     * @param {String} limbName - Name of the limb
     * @param {Object} rotation - Current rotation
     * @private
     */
    _checkJointLimits(limbName, rotation) {
        const limbType = limbName.includes('Arm') ? 'arm' : 'leg';
        const limits = this.config.naturalJointLimits[limbType];

        // Check each axis
        for (const axis of ['x', 'y', 'z']) {
            if (rotation[axis] < limits[axis].min || rotation[axis] > limits[axis].max) {
                this.state.issues.push({
                    type: 'joint_limit',
                    limb: limbName,
                    axis: axis,
                    value: rotation[axis],
                    min: limits[axis].min,
                    max: limits[axis].max,
                    severity: 'medium'
                });
            }
        }
    }

    /**
     * Check for jitter or stuttering in movement
     * @param {String} limbName - Name of the limb
     * @param {Object} velocity - Current velocity
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _checkForJitter(limbName, velocity, deltaTime) {
        // Calculate magnitude of velocity
        const velocityMagnitude = Math.sqrt(
            velocity.x * velocity.x +
            velocity.y * velocity.y +
            velocity.z * velocity.z
        );

        // Check if velocity is above threshold and changes rapidly
        if (velocityMagnitude > this.config.jitterThreshold / deltaTime) {
            // If we have previous acceleration
            if (this.state.rotationAccelerations.has(limbName)) {
                const accel = this.state.rotationAccelerations.get(limbName);
                const accelMagnitude = Math.sqrt(
                    accel.x * accel.x +
                    accel.y * accel.y +
                    accel.z * accel.z
                );

                // If acceleration is high, might be jitter
                if (accelMagnitude > this.config.maxAcceleration) {
                    this.state.issues.push({
                        type: 'jitter',
                        limb: limbName,
                        velocity: velocityMagnitude,
                        acceleration: accelMagnitude,
                        severity: 'high'
                    });
                }
            }
        }
    }

    /**
     * Check for unnatural acceleration
     * @param {String} limbName - Name of the limb
     * @param {Object} acceleration - Current acceleration
     * @private
     */
    _checkAcceleration(limbName, acceleration) {
        // Calculate magnitude of acceleration
        const accelMagnitude = Math.sqrt(
            acceleration.x * acceleration.x +
            acceleration.y * acceleration.y +
            acceleration.z * acceleration.z
        );

        // Check if acceleration is above threshold
        if (accelMagnitude > this.config.maxAcceleration) {
            this.state.issues.push({
                type: 'acceleration',
                limb: limbName,
                value: accelMagnitude,
                threshold: this.config.maxAcceleration,
                severity: accelMagnitude > this.config.maxAcceleration * 2 ? 'high' : 'medium'
            });
        }
    }

    /**
     * Analyze animation cycle consistency
     * @param {String} animationState - Current animation state
     * @param {Number} animationTime - Current animation time
     * @private
     */
    _analyzeAnimationCycle(animationState, animationTime) {
        // For cyclical animations like walking, check consistency of timing
        if (['MOVING', 'IDLE'].includes(animationState)) {
            // Store cycle data if not already tracking this animation
            if (!this.state.cycleTimings.has(animationState)) {
                this.state.cycleTimings.set(animationState, {
                    lastPeakTime: animationTime,
                    intervals: [],
                    lastValue: 0
                });
            }

            // Get cycle data
            const cycleData = this.state.cycleTimings.get(animationState);

            // For simplicity, we'll use a sine wave approximation to detect cycles
            // In a real implementation, you'd analyze the actual limb movements
            const cycleValue = Math.sin(animationTime * 2);

            // Detect zero crossings (approximation of completing half a cycle)
            if (cycleData.lastValue <= 0 && cycleValue > 0) {
                const interval = animationTime - cycleData.lastPeakTime;
                cycleData.intervals.push(interval);
                cycleData.lastPeakTime = animationTime;

                // Keep only the last 5 intervals
                if (cycleData.intervals.length > 5) {
                    cycleData.intervals.shift();
                }

                // Check consistency if we have enough intervals
                if (cycleData.intervals.length >= 3) {
                    const avgInterval = cycleData.intervals.reduce((sum, val) => sum + val, 0) / cycleData.intervals.length;

                    // Check each interval against the average
                    for (const interval of cycleData.intervals) {
                        const deviation = Math.abs(interval - avgInterval) / avgInterval;

                        if (deviation > this.config.cycleTolerance) {
                            this.state.issues.push({
                                type: 'cycle_inconsistency',
                                animation: animationState,
                                deviation: deviation,
                                threshold: this.config.cycleTolerance,
                                severity: deviation > this.config.cycleTolerance * 2 ? 'high' : 'medium'
                            });
                            break;
                        }
                    }
                }
            }

            // Store current value for next comparison
            cycleData.lastValue = cycleValue;
        }
    }

    /**
     * Analyze symmetry between left and right limbs
     * @param {Object} limbs - Object containing limbs
     * @private
     */
    _analyzeSymmetry(limbs) {
        // Skip if missing limbs
        if (!limbs.leftLeg || !limbs.rightLeg || !limbs.leftArm || !limbs.rightArm) {
            return;
        }

        // Get animation expectations
        const animState = this.state.currentAnimState || 'IDLE';
        const expectations = this.config.animationExpectations[animState] || this.config.animationExpectations.IDLE;

        // Check if this animation should be symmetrical
        const symmetryImportance = expectations.symmetryImportance;

        // If symmetry isn't important for this animation, give a high score
        if (symmetryImportance < 0.3) {
            this.state.scores.symmetry = 1.0;
            return;
        }

        // Compare left and right leg rotations
        const leftLegRot = limbs.leftLeg.rotation;
        const rightLegRot = limbs.rightLeg.rotation;

        // For walking, legs should be in opposite phases (180 degrees out of phase)
        // For idle, legs should be in similar positions
        let legSymmetryScore = 1.0;

        if (animState === 'MOVING') {
            // For walking, check if legs are properly out of phase
            const expectedPhaseOffset = this.config.walkCyclePhaseOffset;
            const actualXDiff = Math.abs(leftLegRot.x + rightLegRot.x); // Should be close to 0 if perfectly out of phase

            if (actualXDiff > this.config.symmetryTolerance) {
                legSymmetryScore -= Math.min(0.5, actualXDiff / Math.PI);
                this.state.issues.push({
                    type: 'symmetry',
                    limb: 'legs',
                    message: 'Legs not properly out of phase during walk cycle',
                    severity: actualXDiff > this.config.symmetryTolerance * 2 ? 'high' : 'medium'
                });
            }
        } else {
            // For other animations, check if legs have appropriate symmetry
            const xDiff = Math.abs(leftLegRot.x - rightLegRot.x);
            const yDiff = Math.abs(leftLegRot.y - rightLegRot.y);
            const zDiff = Math.abs(leftLegRot.z - rightLegRot.z);

            const totalDiff = (xDiff + yDiff + zDiff) / 3;

            if (totalDiff > this.config.symmetryTolerance && symmetryImportance > 0.5) {
                legSymmetryScore -= Math.min(0.5, totalDiff / Math.PI);
                this.state.issues.push({
                    type: 'symmetry',
                    limb: 'legs',
                    message: 'Asymmetrical leg positions',
                    severity: totalDiff > this.config.symmetryTolerance * 2 ? 'high' : 'medium'
                });
            }
        }

        // Compare arm symmetry similarly
        const leftArmRot = limbs.leftArm.rotation;
        const rightArmRot = limbs.rightArm.rotation;

        let armSymmetryScore = 1.0;

        // For aiming and shooting, asymmetry is expected
        if (animState === 'AIMING' || animState === 'SHOOTING') {
            // No penalty for asymmetry in aiming/shooting
        } else if (animState === 'MOVING') {
            // For walking, arms should be opposite to legs
            const expectedPhaseOffset = this.config.walkCyclePhaseOffset;
            const actualXDiff = Math.abs(leftArmRot.x + rightArmRot.x); // Should be close to 0 if perfectly out of phase

            if (actualXDiff > this.config.symmetryTolerance) {
                armSymmetryScore -= Math.min(0.5, actualXDiff / Math.PI);
                this.state.issues.push({
                    type: 'symmetry',
                    limb: 'arms',
                    message: 'Arms not properly out of phase during walk cycle',
                    severity: actualXDiff > this.config.symmetryTolerance * 2 ? 'high' : 'medium'
                });
            }
        } else {
            // For other animations, check appropriate symmetry
            const xDiff = Math.abs(leftArmRot.x - rightArmRot.x);
            const yDiff = Math.abs(leftArmRot.y - rightArmRot.y);
            const zDiff = Math.abs(leftArmRot.z - rightArmRot.z);

            const totalDiff = (xDiff + yDiff + zDiff) / 3;

            if (totalDiff > this.config.symmetryTolerance && symmetryImportance > 0.5) {
                armSymmetryScore -= Math.min(0.5, totalDiff / Math.PI);
                this.state.issues.push({
                    type: 'symmetry',
                    limb: 'arms',
                    message: 'Asymmetrical arm positions',
                    severity: totalDiff > this.config.symmetryTolerance * 2 ? 'high' : 'medium'
                });
            }
        }

        // Calculate overall symmetry score
        this.state.scores.symmetry = Math.max(0, (legSymmetryScore * 0.5 + armSymmetryScore * 0.5));
    }

    /**
     * Analyze weight distribution
     * @param {Object} skeleton - The skeleton object
     * @param {String} animationState - Current animation state
     * @private
     */
    _analyzeWeightDistribution(skeleton, animationState) {
        if (!skeleton) return;

        // Get animation expectations
        const animState = animationState || 'IDLE';
        const expectations = this.config.animationExpectations[animState] || this.config.animationExpectations.IDLE;

        // If weight shift isn't important for this animation, give a high score
        if (expectations.weightShiftImportance < 0.3) {
            this.state.scores.weightDistribution = 1.0;
            return;
        }

        // Get positions of feet
        const leftLegPos = this.state.limbPositions.leftLeg?.worldPosition;
        const rightLegPos = this.state.limbPositions.rightLeg?.worldPosition;

        if (!leftLegPos || !rightLegPos) {
            this.state.scores.weightDistribution = 1.0;
            return;
        }

        // Check vertical position of skeleton (should be appropriate for animation)
        const skeletonY = skeleton.position.y;
        let weightScore = 1.0;

        // For walking, check if there's appropriate weight shift
        if (animState === 'MOVING') {
            // In a walk cycle, the body should shift slightly side to side
            // This is approximated by checking if the skeleton's Y position varies
            // In a real implementation, you'd track this over time

            // For now, we'll just check if the skeleton is at an appropriate height
            const expectedHeight = (leftLegPos.y + rightLegPos.y) / 2 + 0.5; // Approximate expected height
            const heightDiff = Math.abs(skeletonY - expectedHeight);

            if (heightDiff > 0.2) {
                weightScore -= Math.min(0.5, heightDiff);
                this.state.issues.push({
                    type: 'weight_distribution',
                    message: 'Inappropriate body height during walking',
                    severity: heightDiff > 0.4 ? 'high' : 'medium'
                });
            }
        } else if (animState === 'DODGING') {
            // For dodging, check if there's appropriate weight shift to one side
            const lateralShift = skeleton.position.x;

            if (Math.abs(lateralShift) < 0.2) {
                weightScore -= 0.3;
                this.state.issues.push({
                    type: 'weight_distribution',
                    message: 'Insufficient lateral weight shift during dodge',
                    severity: 'medium'
                });
            }
        }

        // Update weight distribution score
        this.state.scores.weightDistribution = Math.max(0, weightScore);
    }

    /**
     * Calculate overall animation quality scores
     * @private
     */
    _calculateScores() {
        // Initialize scores
        let smoothnessScore = 1.0;
        let naturalnessScore = 1.0;
        let consistencyScore = 1.0;
        let symmetryScore = this.state.scores.symmetry;
        let weightDistributionScore = this.state.scores.weightDistribution;

        // Count issues by type
        const jitterIssues = this.state.issues.filter(issue => issue.type === 'jitter');
        const jointIssues = this.state.issues.filter(issue => issue.type === 'joint_limit');
        const accelIssues = this.state.issues.filter(issue => issue.type === 'acceleration');
        const cycleIssues = this.state.issues.filter(issue => issue.type === 'cycle_inconsistency');

        // Calculate smoothness score (affected by jitter and acceleration issues)
        if (jitterIssues.length > 0 || accelIssues.length > 0) {
            const jitterPenalty = jitterIssues.reduce((sum, issue) => {
                return sum + (issue.severity === 'high' ? 0.2 : 0.1);
            }, 0);

            const accelPenalty = accelIssues.reduce((sum, issue) => {
                return sum + (issue.severity === 'high' ? 0.15 : 0.05);
            }, 0);

            smoothnessScore = Math.max(0, 1.0 - jitterPenalty - accelPenalty);
        }

        // Calculate naturalness score (affected by joint limit issues)
        if (jointIssues.length > 0) {
            const jointPenalty = jointIssues.reduce((sum, issue) => {
                return sum + (issue.severity === 'high' ? 0.15 : 0.05);
            }, 0);

            naturalnessScore = Math.max(0, 1.0 - jointPenalty);
        }

        // Calculate consistency score (affected by cycle consistency issues)
        if (cycleIssues.length > 0) {
            const cyclePenalty = cycleIssues.reduce((sum, issue) => {
                return sum + (issue.severity === 'high' ? 0.2 : 0.1);
            }, 0);

            consistencyScore = Math.max(0, 1.0 - cyclePenalty);
        }

        // Update scores
        this.state.scores.smoothness = smoothnessScore;
        this.state.scores.naturalness = naturalnessScore;
        this.state.scores.consistency = consistencyScore;

        // Calculate overall score (weighted average)
        this.state.scores.overall = (
            smoothnessScore * 0.25 +
            naturalnessScore * 0.25 +
            consistencyScore * 0.15 +
            symmetryScore * 0.2 +
            weightDistributionScore * 0.15
        );
    }

    /**
     * Get the current analysis results
     * @returns {Object} Analysis results
     */
    getResults() {
        return {
            scores: this.state.scores,
            issues: this.state.issues,
            recommendations: this._generateRecommendations()
        };
    }

    /**
     * Generate recommendations based on detected issues
     * @returns {Array} List of recommendations
     * @private
     */
    _generateRecommendations() {
        const recommendations = [];

        // Group issues by type
        const issuesByType = {};
        for (const issue of this.state.issues) {
            if (!issuesByType[issue.type]) {
                issuesByType[issue.type] = [];
            }
            issuesByType[issue.type].push(issue);
        }

        // Generate recommendations for joint limit issues
        if (issuesByType['joint_limit']) {
            const limbsWithIssues = new Set(issuesByType['joint_limit'].map(issue => issue.limb));

            for (const limb of limbsWithIssues) {
                recommendations.push({
                    type: 'joint_limit',
                    message: `Reduce rotation range for ${limb} to stay within natural limits`,
                    priority: 'medium'
                });
            }
        }

        // Generate recommendations for jitter issues
        if (issuesByType['jitter']) {
            recommendations.push({
                type: 'jitter',
                message: 'Smooth out rapid changes in movement by adding interpolation or damping',
                priority: 'high'
            });
        }

        // Generate recommendations for acceleration issues
        if (issuesByType['acceleration']) {
            recommendations.push({
                type: 'acceleration',
                message: 'Reduce acceleration by adding easing to animation transitions',
                priority: 'medium'
            });
        }

        // Generate recommendations for cycle inconsistency issues
        if (issuesByType['cycle_inconsistency']) {
            recommendations.push({
                type: 'cycle_inconsistency',
                message: 'Ensure consistent timing in cyclical animations like walking',
                priority: 'medium'
            });
        }

        // Generate recommendations for symmetry issues
        if (issuesByType['symmetry']) {
            const legIssues = issuesByType['symmetry'].filter(issue => issue.limb === 'legs');
            const armIssues = issuesByType['symmetry'].filter(issue => issue.limb === 'arms');

            if (legIssues.length > 0) {
                if (this.state.currentAnimState === 'MOVING') {
                    recommendations.push({
                        type: 'symmetry',
                        message: 'Ensure legs are properly out of phase during walk cycle (180° offset)',
                        priority: 'high'
                    });
                } else {
                    recommendations.push({
                        type: 'symmetry',
                        message: 'Improve leg symmetry for more natural stance',
                        priority: 'medium'
                    });
                }
            }

            if (armIssues.length > 0) {
                if (this.state.currentAnimState === 'MOVING') {
                    recommendations.push({
                        type: 'symmetry',
                        message: 'Ensure arms swing in opposition to legs during walk cycle',
                        priority: 'high'
                    });
                } else if (!['AIMING', 'SHOOTING'].includes(this.state.currentAnimState)) {
                    recommendations.push({
                        type: 'symmetry',
                        message: 'Improve arm symmetry for more natural posture',
                        priority: 'medium'
                    });
                }
            }
        }

        // Generate recommendations for weight distribution issues
        if (issuesByType['weight_distribution']) {
            if (this.state.currentAnimState === 'MOVING') {
                recommendations.push({
                    type: 'weight_distribution',
                    message: 'Adjust vertical body position during walk cycle to simulate weight shift',
                    priority: 'medium'
                });
            } else if (this.state.currentAnimState === 'DODGING') {
                recommendations.push({
                    type: 'weight_distribution',
                    message: 'Increase lateral weight shift during dodge animation',
                    priority: 'high'
                });
            } else {
                recommendations.push({
                    type: 'weight_distribution',
                    message: 'Improve weight distribution for more realistic posture',
                    priority: 'medium'
                });
            }
        }

        return recommendations;
    }
}

export default AnimationAnalyzer;
