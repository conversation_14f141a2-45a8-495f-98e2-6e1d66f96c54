import * as THREE from 'three';
import { ROOM_WORLD_SIZE } from '../scenes/roomGenerator.js';

// Debug colors
const DEBUG_COLORS = {
    WALL: 0x00ff00,         // Green for walls
    OUTER_WALL: 0x00aa00,   // Darker green for outer walls
    INNER_WALL: 0x88ff88,   // Lighter green for inner walls
    DOOR: 0xffff00,         // Yellow for doors
    VALID_DOOR: 0x00ffff,   // Cyan for valid doors
    INVALID_DOOR: 0xff0000, // Red for invalid doors
    FLOOR: 0x0000ff,        // Blue for floor segments
    ROOM_BOUNDARY: 0xff00ff // Magenta for room boundaries
};

// Debug flags
let DEBUG_ENABLED = false;
let DEBUG_WALLS = true;
let DEBUG_DOORS = true;
let DEBUG_CONNECTIONS = true;
let DEBUG_FLOOR = true;

// Debug container
let debugContainer = null;
let debugScene = null;
let debugCamera = null;
let debugRenderer = null;
let debugCanvas = null;

/**
 * Initialize the debug system
 */
export function initDebugSystem() {
    DEBUG_ENABLED = true;
    console.log('[DEBUG] Initializing debug system...');
    
    // Create debug container
    debugContainer = document.createElement('div');
    debugContainer.id = 'debug-container';
    debugContainer.style.position = 'fixed';
    debugContainer.style.top = '10px';
    debugContainer.style.right = '10px';
    debugContainer.style.width = '300px';
    debugContainer.style.height = '300px';
    debugContainer.style.backgroundColor = '#000';
    debugContainer.style.border = '2px solid #fff';
    debugContainer.style.zIndex = '1000';
    document.body.appendChild(debugContainer);
    
    // Create debug scene
    debugScene = new THREE.Scene();
    debugScene.background = new THREE.Color(0x111111);
    
    // Create debug camera (orthographic top-down view)
    const aspect = 1;
    const viewSize = ROOM_WORLD_SIZE * 3;
    debugCamera = new THREE.OrthographicCamera(
        -viewSize * aspect / 2, viewSize * aspect / 2,
        viewSize / 2, -viewSize / 2,
        0.1, 1000
    );
    debugCamera.position.set(0, 50, 0);
    debugCamera.lookAt(0, 0, 0);
    debugCamera.rotation.z = Math.PI; // Flip to match game coordinates
    
    // Create debug renderer
    debugRenderer = new THREE.WebGLRenderer({ antialias: true });
    debugRenderer.setSize(300, 300);
    debugCanvas = debugRenderer.domElement;
    debugContainer.appendChild(debugCanvas);
    
    // Add debug controls
    addDebugControls();
    
    console.log('[DEBUG] Debug system initialized');
    
    // Start debug render loop
    renderDebugView();
}

/**
 * Add debug controls to the debug container
 */
function addDebugControls() {
    const controlsContainer = document.createElement('div');
    controlsContainer.style.position = 'absolute';
    controlsContainer.style.bottom = '5px';
    controlsContainer.style.left = '5px';
    controlsContainer.style.right = '5px';
    controlsContainer.style.display = 'flex';
    controlsContainer.style.justifyContent = 'space-between';
    debugContainer.appendChild(controlsContainer);
    
    // Toggle buttons
    const createToggle = (label, property, initialState) => {
        const button = document.createElement('button');
        button.textContent = label;
        button.style.backgroundColor = initialState ? '#4CAF50' : '#f44336';
        button.style.color = 'white';
        button.style.border = 'none';
        button.style.padding = '5px';
        button.style.cursor = 'pointer';
        button.style.fontSize = '10px';
        
        button.addEventListener('click', () => {
            window[property] = !window[property];
            button.style.backgroundColor = window[property] ? '#4CAF50' : '#f44336';
        });
        
        controlsContainer.appendChild(button);
        return button;
    };
    
    createToggle('Walls', 'DEBUG_WALLS', DEBUG_WALLS);
    createToggle('Doors', 'DEBUG_DOORS', DEBUG_DOORS);
    createToggle('Conn', 'DEBUG_CONNECTIONS', DEBUG_CONNECTIONS);
    createToggle('Floor', 'DEBUG_FLOOR', DEBUG_FLOOR);
    
    // Close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'X';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '5px';
    closeButton.style.right = '5px';
    closeButton.style.backgroundColor = '#f44336';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.width = '20px';
    closeButton.style.height = '20px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.fontSize = '12px';
    closeButton.style.padding = '0';
    
    closeButton.addEventListener('click', () => {
        disableDebugSystem();
    });
    
    debugContainer.appendChild(closeButton);
}

/**
 * Disable the debug system
 */
export function disableDebugSystem() {
    if (!DEBUG_ENABLED) return;
    
    console.log('[DEBUG] Disabling debug system...');
    
    // Remove debug container
    if (debugContainer && debugContainer.parentNode) {
        debugContainer.parentNode.removeChild(debugContainer);
    }
    
    // Clean up resources
    debugScene = null;
    debugCamera = null;
    debugRenderer = null;
    debugCanvas = null;
    debugContainer = null;
    
    DEBUG_ENABLED = false;
    console.log('[DEBUG] Debug system disabled');
}

/**
 * Render the debug view
 */
function renderDebugView() {
    if (!DEBUG_ENABLED) return;
    
    debugRenderer.render(debugScene, debugCamera);
    
    requestAnimationFrame(renderDebugView);
}

/**
 * Clear the debug scene
 */
export function clearDebugScene() {
    if (!DEBUG_ENABLED || !debugScene) return;
    
    // Remove all objects from the scene
    while (debugScene.children.length > 0) {
        const object = debugScene.children[0];
        debugScene.remove(object);
    }
}

/**
 * Visualize a room in the debug view
 * @param {Object} roomData - Room data
 * @param {Array} walls - Wall segments
 * @param {Array} doors - Door objects
 * @param {Array} floorSegments - Floor segments
 */
export function visualizeRoom(roomData, walls, doors, floorSegments) {
    if (!DEBUG_ENABLED || !debugScene) return;
    
    clearDebugScene();
    
    // Add room ID text
    addRoomIdText(roomData.id);
    
    // Visualize floor segments
    if (DEBUG_FLOOR && floorSegments) {
        floorSegments.forEach(segment => {
            const { position, width, depth } = segment;
            const geometry = new THREE.PlaneGeometry(width, depth);
            const material = new THREE.MeshBasicMaterial({ 
                color: DEBUG_COLORS.FLOOR,
                transparent: true,
                opacity: 0.3,
                side: THREE.DoubleSide
            });
            const plane = new THREE.Mesh(geometry, material);
            plane.position.set(position.x, 0.1, position.z);
            plane.rotation.x = -Math.PI / 2;
            debugScene.add(plane);
            
            // Add outline
            const edges = new THREE.EdgesGeometry(geometry);
            const line = new THREE.LineSegments(
                edges,
                new THREE.LineBasicMaterial({ color: DEBUG_COLORS.FLOOR })
            );
            line.position.set(position.x, 0.2, position.z);
            line.rotation.x = -Math.PI / 2;
            debugScene.add(line);
        });
    }
    
    // Visualize walls
    if (DEBUG_WALLS && walls) {
        walls.forEach(wall => {
            const { position, rotation, length, isOuterBoundary, direction } = wall;
            const geometry = new THREE.BoxGeometry(length, 1, 0.5);
            const color = isOuterBoundary ? DEBUG_COLORS.OUTER_WALL : DEBUG_COLORS.INNER_WALL;
            const material = new THREE.MeshBasicMaterial({ color });
            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(position.x, 0.3, position.z);
            mesh.rotation.y = rotation;
            debugScene.add(mesh);
            
            // Add direction indicator
            const dirIndicator = new THREE.ArrowHelper(
                new THREE.Vector3(Math.sin(rotation), 0, Math.cos(rotation)),
                new THREE.Vector3(position.x, 0.5, position.z),
                1,
                0xffffff,
                0.5,
                0.3
            );
            debugScene.add(dirIndicator);
            
            // Add direction text
            addDirectionText(position.x, position.z, direction);
        });
    }
    
    // Visualize doors
    if (DEBUG_DOORS && doors) {
        doors.forEach(door => {
            if (!door || !door.position) return;
            
            const { position, userData } = door;
            const connectionDir = userData.connectionDirection;
            const targetRoomId = roomData.connections[connectionDir.toLowerCase()];
            
            // Check if the door is valid (has a valid connection)
            const isValid = targetRoomId !== null && targetRoomId !== undefined && typeof targetRoomId === 'number';
            const color = isValid ? DEBUG_COLORS.VALID_DOOR : DEBUG_COLORS.INVALID_DOOR;
            
            // Create door visualization
            const geometry = new THREE.BoxGeometry(2, 1, 2);
            const material = new THREE.MeshBasicMaterial({ 
                color,
                transparent: true,
                opacity: 0.7
            });
            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(position.x, 0.4, position.z);
            mesh.rotation.y = door.rotation.y;
            debugScene.add(mesh);
            
            // Add connection info
            if (isValid) {
                addConnectionText(position.x, position.z, `${connectionDir}→${targetRoomId}`);
            } else {
                addConnectionText(position.x, position.z, `${connectionDir}→INVALID`, 0xff0000);
            }
        });
    }
}

/**
 * Add room ID text to the debug view
 * @param {number} roomId - Room ID
 */
function addRoomIdText(roomId) {
    if (!DEBUG_ENABLED || !debugScene) return;
    
    // Create canvas for text
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 128;
    canvas.height = 64;
    
    // Draw text
    context.fillStyle = '#ffffff';
    context.font = 'Bold 24px Arial';
    context.fillText(`Room ${roomId}`, 10, 30);
    
    // Create texture
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    
    // Create sprite
    const material = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(material);
    sprite.position.set(0, 1, 0);
    sprite.scale.set(5, 2.5, 1);
    
    debugScene.add(sprite);
}

/**
 * Add direction text to the debug view
 * @param {number} x - X position
 * @param {number} z - Z position
 * @param {string} direction - Direction (n, s, e, w)
 */
function addDirectionText(x, z, direction) {
    if (!DEBUG_ENABLED || !debugScene || !DEBUG_CONNECTIONS) return;
    
    // Create canvas for text
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 64;
    canvas.height = 32;
    
    // Draw text
    context.fillStyle = '#ffffff';
    context.font = 'Bold 16px Arial';
    context.fillText(direction || '?', 10, 20);
    
    // Create texture
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    
    // Create sprite
    const material = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(material);
    sprite.position.set(x, 0.6, z);
    sprite.scale.set(2, 1, 1);
    
    debugScene.add(sprite);
}

/**
 * Add connection text to the debug view
 * @param {number} x - X position
 * @param {number} z - Z position
 * @param {string} text - Connection text
 * @param {number} color - Text color
 */
function addConnectionText(x, z, text, color = 0xffffff) {
    if (!DEBUG_ENABLED || !debugScene || !DEBUG_CONNECTIONS) return;
    
    // Create canvas for text
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 128;
    canvas.height = 32;
    
    // Draw text
    context.fillStyle = `#${color.toString(16).padStart(6, '0')}`;
    context.font = 'Bold 14px Arial';
    context.fillText(text, 10, 20);
    
    // Create texture
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    
    // Create sprite
    const material = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(material);
    sprite.position.set(x, 0.8, z);
    sprite.scale.set(4, 1, 1);
    
    debugScene.add(sprite);
}

/**
 * Validate room connections
 * @param {Object} roomData - Room data
 * @param {Map} floorLayout - Floor layout
 * @returns {Object} Validation results
 */
export function validateRoomConnections(roomData, floorLayout) {
    if (!roomData || !floorLayout) {
        console.error('[DEBUG] Cannot validate connections: missing roomData or floorLayout');
        return { valid: false, errors: ['Missing roomData or floorLayout'] };
    }
    
    const errors = [];
    const warnings = [];
    
    // Check each connection
    for (const [direction, targetRoomId] of Object.entries(roomData.connections || {})) {
        if (targetRoomId === null || targetRoomId === undefined) continue;
        
        // Check if target room exists
        if (!floorLayout.has(targetRoomId)) {
            errors.push(`Connection ${direction} → Room ${targetRoomId} does not exist in floor layout`);
            continue;
        }
        
        // Check if target room has a connection back to this room
        const targetRoom = floorLayout.get(targetRoomId);
        const oppositeDir = getOppositeDirection(direction);
        
        if (!targetRoom.connections || targetRoom.connections[oppositeDir] !== roomData.id) {
            errors.push(`Connection ${direction} → Room ${targetRoomId} is not bidirectional (${oppositeDir} → ${targetRoom.connections ? targetRoom.connections[oppositeDir] : 'none'})`);
        }
    }
    
    // Check for missing walls
    if (roomData.shape) {
        const expectedWallCount = getExpectedWallCount(roomData.shape);
        const actualWallCount = countWalls(roomData);
        
        if (actualWallCount < expectedWallCount) {
            warnings.push(`Room ${roomData.id} (${roomData.shape}) has ${actualWallCount} walls, expected at least ${expectedWallCount}`);
        }
    }
    
    return {
        valid: errors.length === 0,
        errors,
        warnings
    };
}

/**
 * Get the opposite direction
 * @param {string} dir - Direction (n, s, e, w)
 * @returns {string} Opposite direction
 */
function getOppositeDirection(dir) {
    if (!dir) return null;
    const dirLower = dir.toLowerCase();
    switch (dirLower) {
        case 'n': return 's';
        case 's': return 'n';
        case 'e': return 'w';
        case 'w': return 'e';
        default: return null;
    }
}

/**
 * Get the expected wall count for a room shape
 * @param {string} shape - Room shape
 * @returns {number} Expected wall count
 */
function getExpectedWallCount(shape) {
    switch (shape) {
        case 'SQUARE': return 4;
        case 'RECTANGLE': return 4;
        case 'L_SHAPE': return 6;
        case 'T_SHAPE': return 10; // Including inner walls
        case 'U_SHAPE_UP':
        case 'U_SHAPE_DOWN': return 10; // Including inner walls
        default: return 4;
    }
}

/**
 * Count walls in a room
 * @param {Object} roomData - Room data
 * @returns {number} Wall count
 */
function countWalls(roomData) {
    // This is a placeholder - in a real implementation, you would count the actual walls
    // For now, we'll just return a default value
    return roomData.wallCount || 0;
}

/**
 * Log debug information about a room
 * @param {Object} roomData - Room data
 * @param {Array} walls - Wall segments
 * @param {Array} doors - Door objects
 * @param {Map} floorLayout - Floor layout
 */
export function logRoomDebugInfo(roomData, walls, doors, floorLayout) {
    console.group(`[DEBUG] Room ${roomData.id} (${roomData.shape || 'unknown shape'})`);
    
    // Log room data
    console.log('Room Data:', roomData);
    
    // Log walls
    console.log(`Walls (${walls ? walls.length : 0}):`);
    if (walls) {
        walls.forEach((wall, index) => {
            console.log(`  Wall ${index}: ${wall.direction || '?'} - Position: (${wall.position.x.toFixed(1)}, ${wall.position.z.toFixed(1)}), Length: ${wall.length.toFixed(1)}, Outer: ${wall.isOuterBoundary}`);
        });
    }
    
    // Log doors
    console.log(`Doors (${doors ? doors.length : 0}):`);
    if (doors) {
        doors.forEach((door, index) => {
            if (!door || !door.position) {
                console.log(`  Door ${index}: INVALID (no position)`);
                return;
            }
            
            const connectionDir = door.userData ? door.userData.connectionDirection : '?';
            const targetRoomId = roomData.connections ? roomData.connections[connectionDir.toLowerCase()] : null;
            console.log(`  Door ${index}: ${connectionDir} → Room ${targetRoomId}, Position: (${door.position.x.toFixed(1)}, ${door.position.z.toFixed(1)})`);
        });
    }
    
    // Validate connections
    const validationResult = validateRoomConnections(roomData, floorLayout);
    console.log('Connection Validation:', validationResult);
    
    console.groupEnd();
}

/**
 * Toggle the debug system
 */
export function toggleDebugSystem() {
    if (DEBUG_ENABLED) {
        disableDebugSystem();
    } else {
        initDebugSystem();
    }
}

// Export debug colors for use in other modules
export { DEBUG_COLORS };
