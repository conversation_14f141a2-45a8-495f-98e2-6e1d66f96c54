import * as THREE from 'three';
import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

// --- Canvas Text Texture Helper ---
function createCanvasTexture(text, options = {}) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    const font = options.fontStyle || 'bold 40px sans-serif'; // Use fontStyle from options
    const color = options.fontColor || '#FFFFFF'; // Use fontColor from options
    const padding = options.padding || 10;
    const bgColor = options.backgroundColor;

    context.font = font;
    const textMetrics = context.measureText(text);
    let width = textMetrics.width + padding * 2;
    const fontSizeMatch = font.match(/(\d+)px/);
    let estimatedHeight = fontSizeMatch ? parseInt(fontSizeMatch[1], 10) * 1.4 : 60;
    let height = estimatedHeight + padding * 2;

    canvas.width = width;
    canvas.height = height;

    if (bgColor) {
        context.fillStyle = bgColor;
        context.fillRect(0, 0, canvas.width, canvas.height);
    }

    context.font = font;
    context.fillStyle = color;
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(text, canvas.width / 2, canvas.height / 2);

    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    // texture.colorSpace = THREE.SRGBColorSpace; // Consider enabling for color accuracy

    return { texture: texture, width: width, height: height };
}

// --- Text Mesh Creator (Using Canvas Texture) ---
export function createTextMesh(text, options = {}, scaleFactor = 0.01) {
    // Note: font object is passed but not used if using canvas texture
    // options: { fontStyle, fontColor, backgroundColor, padding, emissiveColor, emissiveIntensity, isButton, action }

    const canvasData = createCanvasTexture(text, {
        fontStyle: options.fontStyle || 'bold 40px sans-serif',
        fontColor: options.fontColor || '#FFFFFF',
        backgroundColor: options.backgroundColor, // Optional
        padding: options.padding || 10
    });

    const planeWidth = canvasData.width * scaleFactor;
    const planeHeight = canvasData.height * scaleFactor;

    const geometry = new THREE.PlaneGeometry(planeWidth, planeHeight);
    let material;

    if (options.emissiveColor) {
        // Use MeshStandardMaterial for emissive property
        material = new THREE.MeshStandardMaterial({
            map: canvasData.texture,
            transparent: true,
            side: THREE.DoubleSide,
            color: 0xffffff, // Base color (can be white if map dominates)
            emissive: new THREE.Color(options.emissiveColor),
            emissiveIntensity: options.emissiveIntensity || 0.5,
            roughness: 0.8, // Adjust as needed
            metalness: 0.1  // Adjust as needed
        });
    } else {
        material = new THREE.MeshBasicMaterial({
            map: canvasData.texture,
            transparent: !options.backgroundColor, // Only transparent if no background
            side: THREE.DoubleSide // Render both sides
        });
    }

    const mesh = new THREE.Mesh(geometry, material);
    mesh.userData = {
        type: 'canvasText',
        originalScale: mesh.scale.clone(),
        isButton: options.isButton || false
    };
    if (options.action) mesh.userData.action = options.action;

    return mesh;
}

// --- Alternative: 3D Text Geometry Creator (Requires Font Object) ---
export function createTextGeometryMesh(text, font, options = {}, scaleFactor = 1.0) {
    // options: { size, height, color, emissiveColor, emissiveIntensity, isButton, action }
    if (!font) {
        console.error("Cannot create 3D text geometry: Font not loaded.");
        return new THREE.Mesh(); // Return empty mesh
    }

    const size = options.size || 0.5;
    const height = options.height || 0.1;
    const bevelEnabled = options.bevelEnabled !== undefined ? options.bevelEnabled : true;
    const bevelThickness = options.bevelThickness !== undefined ? options.bevelThickness : 0.01;
    const bevelSize = options.bevelSize !== undefined ? options.bevelSize : 0.005;
    const bevelSegments = options.bevelSegments !== undefined ? options.bevelSegments : 2;
    const curveSegments = options.curveSegments !== undefined ? options.curveSegments : 12;

    const geometry = new TextGeometry(text, {
        font: font,
        size: size,
        height: height,
        curveSegments: curveSegments,
        bevelEnabled: bevelEnabled,
        bevelThickness: bevelThickness,
        bevelSize: bevelSize,
        bevelOffset: 0,
        bevelSegments: bevelSegments
    });

    // Center the geometry
    geometry.computeBoundingBox();
    const textWidth = geometry.boundingBox.max.x - geometry.boundingBox.min.x;
    geometry.translate(-textWidth / 2, 0, 0);

    let material;
    const matOptions = {
        color: new THREE.Color(options.color || 0xffffff)
    };
    if (options.emissiveColor) {
        matOptions.emissive = new THREE.Color(options.emissiveColor);
        matOptions.emissiveIntensity = options.emissiveIntensity || 0.5;
        matOptions.roughness = 0.8;
        matOptions.metalness = 0.1;
        material = new THREE.MeshStandardMaterial(matOptions);
    } else {
        material = new THREE.MeshBasicMaterial(matOptions);
    }

    const mesh = new THREE.Mesh(geometry, material);
    mesh.scale.setScalar(scaleFactor);

    mesh.userData = {
        type: '3dText',
        originalScale: mesh.scale.clone(),
        isButton: options.isButton || false
    };
    if (options.action) mesh.userData.action = options.action;

    return mesh;
} 