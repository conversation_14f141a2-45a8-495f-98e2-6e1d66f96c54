import * as THREE from 'three';

/**
 * Animation Reference
 * 
 * A utility for capturing and displaying animation reference frames
 * to help understand and debug animation issues.
 */
class AnimationReference {
    constructor() {
        // Configuration
        this.config = {
            maxFrames: 4,           // Maximum number of frames to capture
            captureInterval: 0.33,  // Time between captures in seconds
            viewAngles: [0, 90, 180], // View angles to capture (degrees around Y axis)
            gridSize: 3,            // Grid size for display (3x3 grid)
        };

        // State
        this.state = {
            capturedFrames: [],     // Array of captured frames
            isCapturing: false,     // Whether we're currently capturing
            captureTimer: 0,        // Timer for capturing frames
            lastCaptureTime: 0,     // Last time a frame was captured
            currentAnimState: null, // Current animation state
            renderer: null,         // Renderer for capturing frames
            camera: null,           // Camera for capturing frames
        };

        // Create an offscreen renderer for capturing frames
        this.setupOffscreenRenderer();
    }

    /**
     * Setup offscreen renderer for capturing frames
     */
    setupOffscreenRenderer() {
        // Create a small renderer for capturing frames
        this.state.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            preserveDrawingBuffer: true,
            alpha: true
        });
        this.state.renderer.setSize(256, 256);
        this.state.renderer.setClearColor(0x333333);

        // Create a camera for capturing frames
        this.state.camera = new THREE.PerspectiveCamera(50, 1, 0.1, 1000);
        this.state.camera.position.set(0, 1.5, 3);
        this.state.camera.lookAt(0, 1, 0);
    }

    /**
     * Start capturing animation frames
     * @param {String} animationState - The animation state to capture
     */
    startCapturing(animationState) {
        this.state.capturedFrames = [];
        this.state.isCapturing = true;
        this.state.captureTimer = 0;
        this.state.lastCaptureTime = 0;
        this.state.currentAnimState = animationState;
        
        console.log(`Started capturing frames for ${animationState} animation`);
    }

    /**
     * Stop capturing animation frames
     */
    stopCapturing() {
        this.state.isCapturing = false;
        console.log(`Stopped capturing frames. Captured ${this.state.capturedFrames.length} frames.`);
    }

    /**
     * Update the reference with the current state of the skeleton
     * @param {Object} skeleton - The skeleton object
     * @param {String} animationState - The current animation state
     * @param {Number} animationTime - The current animation time
     * @param {Object} scene - The scene object
     */
    update(skeleton, animationState, animationTime, scene) {
        if (!this.state.isCapturing) return;
        if (animationState !== this.state.currentAnimState) return;

        this.state.captureTimer += 0.016; // Approximately 60fps

        // Check if it's time to capture a frame
        if (this.state.captureTimer - this.state.lastCaptureTime >= this.config.captureInterval) {
            this.captureFrame(skeleton, animationState, animationTime, scene);
            this.state.lastCaptureTime = this.state.captureTimer;

            // Stop capturing if we've reached the maximum number of frames
            if (this.state.capturedFrames.length >= this.config.maxFrames) {
                this.stopCapturing();
            }
        }
    }

    /**
     * Capture a frame of the animation
     * @param {Object} skeleton - The skeleton object
     * @param {String} animationState - The current animation state
     * @param {Number} animationTime - The current animation time
     * @param {Object} scene - The scene object
     */
    captureFrame(skeleton, animationState, animationTime, scene) {
        if (!skeleton) return;

        const frameData = {
            animationState,
            animationTime,
            frameNumber: this.state.capturedFrames.length,
            timestamp: animationTime.toFixed(2),
            views: []
        };

        // Create a clone of the skeleton for capturing
        const skeletonClone = skeleton.clone();
        
        // Create a simple scene for rendering
        const captureScene = new THREE.Scene();
        captureScene.background = new THREE.Color(0x333333);
        
        // Add lighting
        const ambientLight = new THREE.AmbientLight(0x404040);
        captureScene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 10, 5);
        captureScene.add(directionalLight);
        
        // Add the skeleton clone
        captureScene.add(skeletonClone);
        
        // Capture from different angles
        for (const angle of this.config.viewAngles) {
            // Position camera based on angle
            const radius = 3;
            const x = radius * Math.sin(angle * Math.PI / 180);
            const z = radius * Math.cos(angle * Math.PI / 180);
            this.state.camera.position.set(x, 1.5, z);
            this.state.camera.lookAt(0, 1, 0);
            
            // Render the scene
            this.state.renderer.render(captureScene, this.state.camera);
            
            // Get the image data
            const imageData = this.state.renderer.domElement.toDataURL('image/png');
            
            // Add to views
            frameData.views.push({
                angle,
                imageData
            });
        }
        
        // Add to captured frames
        this.state.capturedFrames.push(frameData);
        
        console.log(`Captured frame ${frameData.frameNumber} at time ${frameData.timestamp}s`);
    }

    /**
     * Get the captured frames
     * @returns {Array} Array of captured frames
     */
    getCapturedFrames() {
        return this.state.capturedFrames;
    }

    /**
     * Generate HTML for the animation reference display
     * @returns {String} HTML for the animation reference display
     */
    generateReferenceHTML() {
        if (this.state.capturedFrames.length === 0) {
            return '<div class="empty-reference">No frames captured yet. Click "Capture Animation" to start.</div>';
        }

        let html = '<div class="animation-reference">';
        html += '<h3>Animation Reference</h3>';
        
        // Create the grid header
        html += '<div class="reference-grid">';
        
        // Add column headers (Pose A, Pose B, etc.)
        html += '<div class="reference-header-row">';
        for (let i = 0; i < this.state.capturedFrames.length; i++) {
            html += `<div class="reference-header">Pose ${String.fromCharCode(65 + i)}</div>`;
        }
        html += '<div class="reference-header">Timing Info</div>';
        html += '</div>';
        
        // Add rows for each view angle
        for (let viewIndex = 0; viewIndex < this.config.viewAngles.length; viewIndex++) {
            html += '<div class="reference-row">';
            
            // Add cells for each frame
            for (let frameIndex = 0; frameIndex < this.state.capturedFrames.length; frameIndex++) {
                const frame = this.state.capturedFrames[frameIndex];
                const view = frame.views[viewIndex];
                
                if (view) {
                    html += `<div class="reference-cell">
                        <img src="${view.imageData}" alt="Frame ${frameIndex} View ${viewIndex}" />
                    </div>`;
                } else {
                    html += '<div class="reference-cell empty"></div>';
                }
            }
            
            // Add timing info for this row
            if (viewIndex === 0) {
                html += '<div class="reference-timing">';
                for (let frameIndex = 0; frameIndex < this.state.capturedFrames.length; frameIndex++) {
                    const frame = this.state.capturedFrames[frameIndex];
                    html += `<div>Pose ${String.fromCharCode(65 + frameIndex)}: Frame ${frame.frameNumber} (${frame.timestamp}s)</div>`;
                }
                html += '</div>';
            } else {
                html += '<div class="reference-cell empty"></div>';
            }
            
            html += '</div>';
        }
        
        html += '</div>'; // Close reference-grid
        html += '</div>'; // Close animation-reference
        
        return html;
    }

    /**
     * Clear all captured frames
     */
    clearFrames() {
        this.state.capturedFrames = [];
        console.log('Cleared all captured frames');
    }

    /**
     * Check if currently capturing
     * @returns {Boolean} Whether currently capturing
     */
    isCapturing() {
        return this.state.isCapturing;
    }

    /**
     * Get the current animation state being captured
     * @returns {String} Current animation state
     */
    getCurrentAnimState() {
        return this.state.currentAnimState;
    }
}

export default AnimationReference;
