/**
 * Debug commands for teleporting in the game
 * These can be called from the browser console
 */

/**
 * Teleport the player to the boss room
 * @returns {boolean} True if successful, false otherwise
 */
export function teleportToBoss() {
    console.log("=== DEBUG: Teleport to Boss ===");
    
    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager || !sceneManager.currentHandler) {
        console.error("Scene manager or current handler not found");
        return false;
    }
    
    const dungeonHandler = sceneManager.currentHandler;
    console.log("DungeonHandler found:", !!dungeonHandler);
    
    // Get the player controller
    const playerController = dungeonHandler.playerController;
    if (!playerController) {
        console.error("Player controller not found");
        return false;
    }
    console.log("PlayerController found:", !!playerController);
    
    // Call the teleport method
    try {
        console.log("Calling teleportToBoss method...");
        playerController.teleportToBoss();
        console.log("Teleport command executed");
        return true;
    } catch (error) {
        console.error("Error teleporting to boss:", error);
        return false;
    }
}

/**
 * Teleport directly to a specific room by ID
 * @param {number} roomId - The ID of the room to teleport to
 * @returns {boolean} True if successful, false otherwise
 */
export function teleportToRoom(roomId) {
    console.log(`=== DEBUG: Teleport to Room ${roomId} ===`);
    
    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager || !sceneManager.currentHandler) {
        console.error("Scene manager or current handler not found");
        return false;
    }
    
    const dungeonHandler = sceneManager.currentHandler;
    
    // Call the transition method directly
    try {
        console.log(`Transitioning to room ${roomId}...`);
        dungeonHandler._transitionToRoom(roomId, 'north');
        console.log("Teleport command executed");
        return true;
    } catch (error) {
        console.error("Error teleporting to room:", error);
        return false;
    }
}

/**
 * List all available rooms in the dungeon
 * @returns {Array} Array of room IDs and types
 */
export function listRooms() {
    console.log("=== DEBUG: List Rooms ===");
    
    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager || !sceneManager.currentHandler) {
        console.error("Scene manager or current handler not found");
        return [];
    }
    
    const dungeonHandler = sceneManager.currentHandler;
    const rooms = [];
    
    // List all rooms in the floor layout
    if (dungeonHandler.floorLayout) {
        for (const [id, roomData] of dungeonHandler.floorLayout.entries()) {
            rooms.push({
                id,
                type: roomData.type || 'normal',
                visited: roomData.visited || false,
                isCurrent: id === dungeonHandler.currentRoomId
            });
        }
    }
    
    console.table(rooms);
    return rooms;
}

// Make commands available globally
window.teleportToBoss = teleportToBoss;
window.teleportToRoom = teleportToRoom;
window.listRooms = listRooms;

console.log("Teleport debug commands loaded. Available commands:");
console.log("- teleportToBoss() - Teleport to the boss room");
console.log("- teleportToRoom(roomId) - Teleport to a specific room by ID");
console.log("- listRooms() - List all available rooms");
