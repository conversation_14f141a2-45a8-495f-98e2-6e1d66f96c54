import * as THREE from 'three';

/**
 * Shader that makes the game look like code/matrix style
 * Can be configured for 8-bit or 32-bit visualization
 */
const CodeVisualShader = {
    uniforms: {
        'tDiffuse': { value: null },
        'time': { value: 0.0 },
        'pixelSize': { value: 4.0 },
        'colorReduction': { value: 8.0 },
        'scanlineIntensity': { value: 0.3 },
        'colorTint': { value: new THREE.Vector3(0.0, 0.8, 0.0) }, // RGB tint (default: green)
        'characterDensity': { value: 0.5 },
        'glitchIntensity': { value: 0.1 },
        'glitchSpeed': { value: 5.0 },
        'is32Bit': { value: 0 },                // 0 = 8-bit mode, 1 = 32-bit mode
        'characterDetail': { value: 1.0 },      // Higher values = more detailed characters
        'colorfulText': { value: 0.0 },         // Higher values = more colorful text
        'bloomStrength': { value: 0.5 },        // Bloom/glow effect strength
        'matrixEffect': { value: 0.0 }          // Matrix-style falling code effect strength
    },

    vertexShader: `
        varying vec2 vUv;
        void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
    `,

    fragmentShader: `
        uniform sampler2D tDiffuse;
        uniform float time;
        uniform float pixelSize;
        uniform float colorReduction;
        uniform float scanlineIntensity;
        uniform vec3 colorTint;
        uniform float characterDensity;
        uniform float glitchIntensity;
        uniform float glitchSpeed;
        uniform float is32Bit;
        uniform float characterDetail;
        uniform float colorfulText;
        uniform float bloomStrength;
        uniform float matrixEffect;
        varying vec2 vUv;

        // Random function
        float random(vec2 st) {
            return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
        }

        // Improved character lookup function with detail parameter
        float character(float n, vec2 p, float detail) {
            // Higher detail for 32-bit mode
            float detailFactor = mix(4.0, 8.0, detail);
            p = floor(p * vec2(detailFactor, detailFactor) + 2.5);

            if (clamp(p.x, 0.0, detailFactor) == p.x && clamp(p.y, 0.0, detailFactor) == p.y) {
                if (int(mod(n / exp2(p.x + detailFactor * p.y), 2.0)) == 1) return 1.0;
            }
            return 0.0;
        }

        // Matrix falling code effect
        float matrixCode(vec2 uv, float time, float speed) {
            // Create columns of falling code
            float columnWidth = 0.02;
            float column = floor(uv.x / columnWidth);

            // Each column has its own speed and starting position
            float columnSpeed = 0.2 + 0.3 * random(vec2(column, 0.0));
            float startPos = random(vec2(column, 0.0)) * 10.0;

            // Calculate vertical position with time
            float yPos = fract(uv.y + time * columnSpeed + startPos);

            // Create a trail effect - brighter at the head, fading at the tail
            float trail = smoothstep(0.0, 0.4, yPos) * smoothstep(1.0, 0.6, yPos);

            // Add some random blinking
            float blink = step(0.95, random(vec2(column, floor(time * 20.0))));

            return trail * (1.0 - blink * 0.5);
        }

        // Simple bloom/glow effect
        vec3 applyBloom(vec3 color, float strength) {
            // Threshold and create a glow from bright areas
            vec3 brightPass = max(color - vec3(0.7), 0.0);
            return color + brightPass * strength * 2.0;
        }

        void main() {
            // Determine if we're in 32-bit mode
            bool highDetail = is32Bit > 0.5;

            // Apply pixelation - less pixelation in 32-bit mode
            float pixelFactor = mix(pixelSize, pixelSize * 0.5, float(highDetail));
            vec2 pixelatedUV = floor(vUv * vec2(pixelFactor * 16.0)) / vec2(pixelFactor * 16.0);

            // Add slight movement to simulate screen refresh
            pixelatedUV.y += sin(time * 0.5 + pixelatedUV.x * 10.0) * 0.002;

            // Apply glitch effect - more subtle in 32-bit mode
            float glitchFactor = mix(glitchIntensity, glitchIntensity * 0.7, float(highDetail));
            float glitchOffset = 0.0;
            if (random(vec2(floor(time * glitchSpeed), 0.0)) < glitchFactor) {
                float lineNoise = step(0.8, random(vec2(floor(time * 20.0), floor(vUv.y * 20.0))));
                glitchOffset = lineNoise * 0.01 * random(vec2(floor(time * 20.0), floor(vUv.y * 20.0)));
            }
            pixelatedUV.x += glitchOffset;

            // Get pixelated color
            vec4 texel = texture2D(tDiffuse, pixelatedUV);

            // Apply color reduction - higher bit depth in 32-bit mode
            float bitDepth = mix(colorReduction, min(colorReduction * 3.0, 32.0), float(highDetail));
            float colorLevels = pow(2.0, bitDepth);
            texel.rgb = floor(texel.rgb * colorLevels) / colorLevels;

            // Convert to tinted monochrome with color preservation in 32-bit mode
            float luminance = dot(texel.rgb, vec3(0.299, 0.587, 0.114));
            vec3 tintedColor;

            if (highDetail) {
                // In 32-bit mode, preserve some of the original color
                float colorPreservation = 0.3 + colorfulText * 0.5;
                tintedColor = mix(
                    vec3(luminance) * (vec3(1.0) - colorTint) + luminance * colorTint,
                    texel.rgb,
                    colorPreservation
                );
            } else {
                // In 8-bit mode, use simple tinting
                tintedColor = vec3(luminance) * (vec3(1.0) - colorTint) + luminance * colorTint;
            }

            // Apply scanlines - more subtle in 32-bit mode
            float scanlineFactor = mix(scanlineIntensity, scanlineIntensity * 0.6, float(highDetail));
            float scanlineFreq = mix(100.0, 200.0, float(highDetail));
            float scanline = sin(vUv.y * scanlineFreq) * 0.5 + 0.5;
            tintedColor *= 1.0 - (scanline * scanlineFactor);

            // Apply character effect with appropriate detail level
            float detail = mix(1.0, characterDetail, float(highDetail));
            float charFreq = mix(40.0, 80.0, float(highDetail));
            float charVal = character(
                random(floor(pixelatedUV * charFreq) / charFreq) * 10.0,
                mod(vUv * charFreq, mix(4.0, 8.0, detail)),
                detail
            );

            // Mix character effect with tinted color based on density
            vec3 codeColor = mix(
                tintedColor,
                tintedColor * (charVal + 0.5),
                characterDensity
            );

            // Apply matrix falling code effect if enabled
            if (matrixEffect > 0.0) {
                float codeIntensity = matrixCode(vUv, time, 1.0);
                // Brighten the code where the matrix effect is stronger
                codeColor = mix(codeColor, codeColor * 1.5, codeIntensity * matrixEffect);
            }

            // Add subtle pulsing effect - more complex in 32-bit mode
            if (highDetail) {
                // Multiple frequency pulsing
                codeColor *= 0.85 + 0.15 * sin(time * 2.0) + 0.1 * sin(time * 5.0 + vUv.x * 10.0);
            } else {
                // Simple pulsing
                codeColor *= 0.8 + 0.2 * sin(time * 2.0);
            }

            // Apply bloom/glow effect in 32-bit mode
            if (highDetail) {
                codeColor = applyBloom(codeColor, bloomStrength);
            }

            gl_FragColor = vec4(codeColor, texel.a);
        }
    `
};

export { CodeVisualShader };
