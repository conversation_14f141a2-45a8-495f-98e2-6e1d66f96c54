import * as THREE from 'three';
import { FontLoader } from 'three/addons/loaders/FontLoader.js'; // Needed if SceneManager loads font
import { STATE } from '../constants.js';
import HeroPageHandler from '../scenes/HeroPageHandler.js';
import CharacterCreationHandler from '../scenes/CharacterCreationHandler.js';
import DungeonHandler from '../scenes/DungeonHandler.js'; // Import DungeonHandler
import AudioManager from '../utils/audioManager.js'; // Import AudioManager
import FrustumCulling from '../utils/FrustumCulling.js'; // Import FrustumCulling
import { initCRTEffect } from '../effects/CRTIntegration.js'; // Import CRT effect
import { initHDRFramerateEffect } from '../effects/HDRFramerateIntegration.js'; // Import HDR and Framerate effect
import { initConsolePresetEffect } from '../effects/ConsolePresetIntegration.js'; // Import Console Preset effect

// --- NippleJS Constants ---
const JOYSTICK_THRESHOLD = 0.3;
const JOYSTICK_TAP_THRESHOLD = 0.1;
const JOYSTICK_VERTICAL_THRESHOLD = 0.5;
// --------------------------

class SceneManager {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.font = null; // Font can be loaded once here or by the first handler that needs it
        this.clock = new THREE.Clock();
        this.currentState = STATE.LOADING;
        this.fadeOverlay = document.getElementById('fade-overlay');
        this.activeSceneHandler = null;
        this.isFading = false;
        this.onFadeCompleteCallback = null;

        this.audioManager = new AudioManager(); // Instantiate AudioManager
        this.frustumCulling = new FrustumCulling(); // Instantiate FrustumCulling
        this.enableFrustumCulling = true; // Flag to enable/disable frustum culling

        // Camera fix tracking
        this._lastFrameFixedCamera = false;
        this._cameraState = {
            position: null,
            quaternion: null,
            up: null,
            zoom: null,
            isTopDown: false
        };

        // CRT effect will be initialized after renderer is created

        // --- NEW: Joystick State (Moved from PlayerController) ---
        this.joystickManagerLeft = null;
        this.joystickManagerRight = null;
        this.leftStickData = { active: false, vector: { x: 0, y: 0 } };
        this.rightStickData = { active: false, vector: { x: 0, y: 0 } };
        this.lastLeftStickY = 0;
        this.lastTapTimeRight = 0;
        this.tapDebounce = 200; // ms
        this.isMobileLandscape = false;
        this.boundOrientationChange = this._handleOrientationChange.bind(this);
        this.lastProcessedStickY = 0; // Added for dialogue navigation processing
        // -----------------------------------------------------

        this._initEngine();
        this._setupEventlisteners();
    }

    _initEngine() {
        console.log("Initializing 3D engine...");
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000000);

        // Default camera, will be adjusted by handlers
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.z = 5;

        const canvasElement = document.getElementById('webgl-canvas');
        if (!canvasElement) {
            console.error("CRITICAL: #webgl-canvas element not found!");
            // Maybe display an error message in the HTML
            const errorDiv = document.createElement('div');
            errorDiv.style.color = 'red';
            errorDiv.style.position = 'absolute';
            errorDiv.style.top = '50%';
            errorDiv.style.left = '50%';
            errorDiv.style.transform = 'translate(-50%, -50%)';
            errorDiv.textContent = 'Error: Cannot initialize 3D canvas.';
            document.body.appendChild(errorDiv);
            return; // Stop initialization
        }
        this.renderer = new THREE.WebGLRenderer({ antialias: true, canvas: canvasElement });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5));
        this.renderer.shadowMap.enabled = true; // Enable shadows by default
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Set up tone mapping for better brightness
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 2.5; // Higher exposure for brighter overall lighting
        this.renderer.outputEncoding = THREE.sRGBEncoding;

        // Enhanced lighting for better brightness (can be adjusted by handlers)
        const ambientLight = new THREE.AmbientLight(0xffffff, 1.0); // Doubled ambient light intensity
        ambientLight.name = "globalAmbientLight";
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8); // Increased directional light intensity
        directionalLight.position.set(5, 10, 7.5);
        directionalLight.name = "globalDirectionalLight";
        this.scene.add(directionalLight);

        console.log("Engine initialized.");

        // Initialize CRT effect
        this.crtEffect = initCRTEffect(this.renderer, this.scene, this.camera);
        console.log("CRT effect initialized.");

        // Initialize HDR and Framerate effect
        this.hdrFramerateEffect = initHDRFramerateEffect(this.renderer, this.scene, this.camera);
        console.log("HDR and Framerate effect initialized.");

        // Initialize Console Preset effect (with references to other effects for integration)
        this.consolePresetEffect = initConsolePresetEffect(
            this.renderer,
            this.scene,
            this.camera,
            this.hdrFramerateEffect,
            this.crtEffect
        );
        console.log("Console Preset effect initialized.");

        // --- Initialize Mobile Controls Check ---
        this._handleOrientationChange(); // Check initial orientation
        window.addEventListener('orientationchange', this.boundOrientationChange);
        // ------------------------------------
    }

    _setupEventlisteners() {
        window.addEventListener('resize', this._onWindowResize.bind(this));
        // Global listeners? If most are scene-specific, add/remove them in handlers.
    }

    _onWindowResize() {
        if (!this.camera) return;
        const aspect = window.innerWidth / window.innerHeight;

        // Check if we're in DungeonHandler
        const isDungeonScene = this.activeSceneHandler instanceof DungeonHandler;

        // Save full camera state before resize (only for non-DungeonHandler scenes)
        if (!isDungeonScene) {
            this._saveCameraState();
        }

        if (this.camera.isPerspectiveCamera) {
            this.camera.aspect = aspect;
        } else if (this.camera.isOrthographicCamera) {
            // For DungeonHandler, let it handle its own camera resizing
            if (isDungeonScene) {
                // Don't modify the camera here, let DungeonHandler's onResize handle it
            } else {
                // For other scenes, use default frustum size
                const frustumSize = 15; // Default size
                this.camera.left = frustumSize * aspect / -2;
                this.camera.right = frustumSize * aspect / 2;
                this.camera.top = frustumSize / 2;
                this.camera.bottom = frustumSize / -2;
            }
        }

        // Restore zoom value from saved state (only for non-DungeonHandler scenes)
        if (!isDungeonScene && this._cameraState.zoom !== null) {
            this.camera.zoom = this._cameraState.zoom;
        }

        // Update projection matrix (DungeonHandler will do this in its onResize)
        if (!isDungeonScene) {
            this.camera.updateProjectionMatrix();
        }

        // Always resize the renderer
        this.renderer.setSize(window.innerWidth, window.innerHeight);

        // Update CRT effect resolution
        if (this.crtEffect && this.crtEffect.manager) {
            this.crtEffect.manager.updateResolution();
            if (!isDungeonScene && this.crtEffect.manager.crtPass && this._cameraState.zoom !== null) {
                this.crtEffect.manager.crtPass.uniforms.cameraZoom.value = this._cameraState.zoom;
            }
        }

        // Handlers might need to adjust layouts on resize too
        // This is especially important for DungeonHandler to handle its own camera
        if (this.activeSceneHandler && typeof this.activeSceneHandler.onResize === 'function') {
            this.activeSceneHandler.onResize();
        }
    }

    // --- Fade Transition ---
    startFade(onComplete) {
        if (this.isFading) return; // Prevent overlapping fades
        console.log("Starting fade out...");
        this.isFading = true;
        this.onFadeCompleteCallback = onComplete;

        if (!this.fadeOverlay) {
            console.error("Fade overlay element not found! Cannot fade.");
            this.isFading = false;
            if (this.onFadeCompleteCallback) this.onFadeCompleteCallback();
            return;
        }

        this.fadeOverlay.style.display = 'block';
        this.fadeOverlay.offsetHeight; // Trigger reflow
        this.fadeOverlay.classList.add('active');

        // Wait for fade-out CSS transition (defined in index.html)
        // Assumes 1s transition now - adjust if CSS changes
        setTimeout(() => {
            console.log("Fade out complete.");
            if (this.onFadeCompleteCallback) {
                this.onFadeCompleteCallback(); // Execute the state change / scene setup
                this.onFadeCompleteCallback = null; // Clear callback
            }
             // Start fade-in *after* the next scene is potentially set up
             this.endFade(); // <<< RESTORED: endFade is called by startFade again
        }, 1000); // Match CSS transition duration + buffer (Set to 1s)
    }

    endFade() {
         if (!this.isFading || !this.fadeOverlay) return;
         console.log("Starting fade in...");

        // Wait a tiny bit for scene setup before fading in
        setTimeout(() => {
             this.fadeOverlay.classList.remove('active');
             // Wait for fade-in CSS transition before hiding overlay
             setTimeout(() => {
                 console.log("Fade in complete.");
                 this.fadeOverlay.style.display = 'none';
                 this.isFading = false;
             }, 1000); // Match CSS transition duration + buffer (Set to 1s)
         }, 50); // Short delay before fade-in
    }

    // --- State Management ---
    async changeState(newState, params = {}) {
        console.log(`Changing state from ${this.currentState} to ${newState}`, params);

        // Store current camera state if it exists
        const prevCameraState = this.camera ? {
            zoom: this.camera.zoom,
            position: this.camera.position.clone(),
            quaternion: this.camera.quaternion.clone(),
            up: this.camera.up.clone(),
            isOrthographic: this.camera.isOrthographicCamera
        } : null;

        // Reset scene and camera
        if (this.activeSceneHandler) {
            await this.activeSceneHandler.cleanup();
        }

        this.currentState = newState;
        this.activeSceneHandler = null;

        // Reset camera state tracking
        this._lastFrameFixedCamera = false;
        this._cameraState.position = null;

        try {
            switch (newState) {
                case STATE.HERO_PAGE:
                    this.activeSceneHandler = new HeroPageHandler(this);
                    await this.activeSceneHandler.init(this.scene);
                    break;
                case STATE.CHARACTER_CREATION:
                    this.activeSceneHandler = new CharacterCreationHandler(this);
                    await this.activeSceneHandler.init(this.scene);
                    break;
                case STATE.DUNGEON:
                    // For dungeon, position the camera farther from the floor
                    this.activeSceneHandler = new DungeonHandler(this, params); // Pass params

                    // Always modify DungeonHandler camera settings before init to match user preferences
                    // Position the camera farther from the floor and point more downward
                    // This matches the camera style in the Flicker scene that the user prefers
                    this.activeSceneHandler.originalCameraOffset = new THREE.Vector3(0, 20, 25); // Increased Y and Z
                    this.activeSceneHandler.topDownCameraOffset = new THREE.Vector3(0, 35, 0); // Increased Y

                    await this.activeSceneHandler.init(this.scene);
                    break;
                case STATE.LOADING:
                default:
                    console.log(`Entering ${newState} state.`);
                    // Optionally show a loading indicator in the DOM
                    break;
            }

            // If changing TO dungeon, ensure player controller exists
            if (newState === STATE.DUNGEON && !(this.activeSceneHandler instanceof DungeonHandler)) {
                 console.error("State changed to Dungeon, but handler is not DungeonHandler!");
                 // Handle error appropriately
            }

            console.log(`Successfully initialized state: ${newState}`);
        } catch (error) {
            console.error(`Failed to initialize state ${newState}:`, error);
            // Handle error, maybe revert to a safe state or show an error message
            this.changeState(STATE.LOADING); // Revert to loading on error
        }

        // Restore camera state if it existed and if the new handler didn't create its own camera
        if (prevCameraState && this.camera) {
            // Only restore zoom and position if the camera type hasn't changed
            // (e.g., don't restore orthographic settings to a perspective camera)
            if (prevCameraState.isOrthographic === this.camera.isOrthographicCamera) {
                this.camera.zoom = prevCameraState.zoom;
                this.camera.updateProjectionMatrix();

                // Update CRT effect with restored zoom
                try {
                    if (this.crtEffect && this.crtEffect.manager && this.crtEffect.manager.crtPass &&
                        this.crtEffect.manager.crtPass.uniforms &&
                        this.crtEffect.manager.crtPass.uniforms.cameraZoom) {
                        this.crtEffect.manager.crtPass.uniforms.cameraZoom.value = prevCameraState.zoom;
                    }
                } catch (error) {
                    console.warn('Error updating CRT effect zoom:', error);
                }

                // Only restore position if we're not in a scene that manages its own camera position
                if (!(this.activeSceneHandler instanceof DungeonHandler)) {
                    this.camera.position.copy(prevCameraState.position);
                    this.camera.quaternion.copy(prevCameraState.quaternion);
                    this.camera.up.copy(prevCameraState.up);
                }
            }
        }
    }

    // --- Main Loop ---
    animate() {
        // Use arrow function for binding `this` correctly
        requestAnimationFrame(() => this.animate());

        const deltaTime = this.clock.getDelta();

        // --- Process Mobile Input FIRST ---
        this._processMobileInput(deltaTime);
        // ---------------------------------

        // Update frustum for culling before scene updates
        if (this.enableFrustumCulling && this.camera) {
            this.frustumCulling.updateFrustum(this.camera);
        }

        // Update logic for the currently active scene handler
        if (this.activeSceneHandler && typeof this.activeSceneHandler.update === 'function') {
            this.activeSceneHandler.update(deltaTime, this.scene, this.camera);
        }

        // Global animations or updates can go here (e.g., stats display)

        if (this.renderer && this.scene && this.camera) {
             try {
                 // Store camera properties before effects
                 const isTopDownView = this.activeSceneHandler?.isTopDownView;
                 const isDungeonScene = this.activeSceneHandler instanceof DungeonHandler;

                 // Only save camera state if we're not in DungeonHandler
                 // Let DungeonHandler manage its own camera
                 if (!isDungeonScene) {
                     this._saveCameraState();
                 }

                 // Get current time for effects
                 const currentTime = performance.now();

                 // First check if we should render this frame based on game FPS limiting
                 let shouldRenderGameFrame = true;
                 if (this.consolePresetEffect) {
                     shouldRenderGameFrame = this.consolePresetEffect.shouldRenderGameFrame(currentTime);
                 }

                 // Only proceed if we should render this frame based on game FPS
                 if (shouldRenderGameFrame) {
                     // Check if 8-bit code effect is enabled
                     const eightBitCodeEnabled = this.consolePresetEffect && this.consolePresetEffect.eightBitCodeEnabled;

                     // Coordinate HDR and CRT effects
                     if (this.hdrFramerateEffect && this.crtEffect) {
                         // Both effects are available - integrate them

                         // First check if we should render this frame (framerate limiting)
                         let shouldRender = this.hdrFramerateEffect.shouldRenderFrame(currentTime);

                         if (shouldRender) {
                             // Apply HDR settings first (tone mapping, etc.)
                             this.hdrFramerateEffect.applyHDRSettings();

                             // Get current render target
                             const currentRenderTarget = this.renderer.getRenderTarget();

                             // Render scene to a temporary target using CRT effect
                             // This will apply CRT distortion, scanlines, etc.
                             this.crtEffect.update();

                             // Apply bit depth post-processing after CRT
                             // This will simulate lower bit depths and apply dithering
                             this.hdrFramerateEffect.applyBitDepthPostProcessing();

                             // Apply 8-bit code effect if enabled
                             if (eightBitCodeEnabled) {
                                 this.consolePresetEffect.update(currentTime);
                             }

                             // Restore original render target
                             this.renderer.setRenderTarget(currentRenderTarget);
                         }
                     }
                     // HDR effect and 8-bit code effect available
                     else if (this.hdrFramerateEffect && eightBitCodeEnabled) {
                         // First apply HDR effect
                         let shouldRender = this.hdrFramerateEffect.shouldRenderFrame(currentTime);

                         if (shouldRender) {
                             this.hdrFramerateEffect.update(currentTime);

                             // Then apply 8-bit code effect
                             this.consolePresetEffect.update(currentTime);
                         }
                     }
                     // Only HDR effect available
                     else if (this.hdrFramerateEffect) {
                         // HDR effect handles framerate limiting and rendering
                         this.hdrFramerateEffect.update(currentTime);
                     }
                     // Only CRT effect and 8-bit code effect available
                     else if (this.crtEffect && eightBitCodeEnabled) {
                         // First apply CRT effect
                         this.crtEffect.update();

                         // Then apply 8-bit code effect
                         this.consolePresetEffect.update(currentTime);
                     }
                     // Only CRT effect available
                     else if (this.crtEffect) {
                         // Update CRT effect (this handles rendering internally)
                         this.crtEffect.update();
                     }
                     // Only 8-bit code effect available
                     else if (eightBitCodeEnabled) {
                         // Render scene normally first
                         this.renderer.render(this.scene, this.camera);

                         // Then apply 8-bit code effect
                         this.consolePresetEffect.update(currentTime);
                     }
                     // No effects available
                     else {
                         // Fallback to normal rendering
                         this.renderer.render(this.scene, this.camera);
                     }
                 }

                 // Only restore camera state if we're not in DungeonHandler
                 // Let DungeonHandler manage its own camera
                 if (!isDungeonScene) {
                     this._restoreCameraState();
                 }

                 // Track if we fixed the camera this frame
                 this._lastFrameFixedCamera = isTopDownView && !isDungeonScene;
             } catch (error) {
                 console.error("Error during rendering:", error);
                 // Potentially stop the loop or try to recover
                 // For now, just log it to avoid crashing the tab entirely
             }
        } else {
             console.warn("Skipping render: Renderer, Scene, or Camera not ready.");
        }
    }

    async start() {
        console.log("Starting SceneManager...");
        // Optionally load global assets here (e.g., font) if not handled by specific scenes
        // try {
        //     const loader = new FontLoader();
        //     this.font = await loader.loadAsync('assets/fonts/helvetiker_bold.typeface.json');
        //     console.log("Global font loaded by SceneManager.");
        // } catch (error) {
        //     console.error("Failed to load global font:", error);
        //     // Handle font loading failure
        //     return; // Don't start if essential assets fail
        // }

        // Start the animation loop
        this.animate();

        // Initial state transition (after engine init and loop start)
        await this.changeState(STATE.HERO_PAGE); // Start with the hero page
    }

    // --- NEW: Joystick Management Methods (Moved from PlayerController) ---
     _initJoysticks() {
        this._destroyJoysticks(); // Ensure clean state

        // Check if nipplejs is loaded
        if (typeof nipplejs === 'undefined') {
             console.error("nipplejs library not loaded! Cannot initialize joysticks.");
             return;
        }

        const commonOptions = {
            mode: 'static',
            size: 150,
            threshold: 0.1, // Lower threshold for sensitivity
            color: 'grey',
            fadeTime: 150,
            dynamicPage: true,
            restJoystick: true,
            restOpacity: 0.5,
            lockX: false,
            lockY: false,
        };

        try {
            const zoneLeft = document.getElementById('joystick-left');
            if (zoneLeft) {
                this.joystickManagerLeft = nipplejs.create({ ...commonOptions, zone: zoneLeft, position: { left: '12.5%', bottom: '15%' } });
                this._bindJoystickEvents(this.joystickManagerLeft, 'left');
                console.log("[SceneManager] Left joystick initialized.");
            } else {
                console.warn("[SceneManager] Left joystick zone not found!");
            }

            const zoneRight = document.getElementById('joystick-right');
            if (zoneRight) {
                this.joystickManagerRight = nipplejs.create({ ...commonOptions, zone: zoneRight, position: { right: '12.5%', bottom: '15%' } });
                this._bindJoystickEvents(this.joystickManagerRight, 'right');
                console.log("[SceneManager] Right joystick initialized.");
            } else {
                console.warn("[SceneManager] Right joystick zone not found!");
            }
        } catch (error) {
            console.error("[SceneManager] Error initializing nipplejs:", error);
            this._destroyJoysticks();
        }
    }

    _destroyJoysticks() {
        if (this.joystickManagerLeft) {
            this.joystickManagerLeft.destroy();
            this.joystickManagerLeft = null;
            console.log("[SceneManager] Left joystick destroyed.");
        }
        if (this.joystickManagerRight) {
            this.joystickManagerRight.destroy();
            this.joystickManagerRight = null;
            console.log("[SceneManager] Right joystick destroyed.");
        }
        this.leftStickData = { active: false, vector: { x: 0, y: 0 } };
        this.rightStickData = { active: false, vector: { x: 0, y: 0 } };
    }

    _bindJoystickEvents(manager, side) {
        manager.on('start', (evt, nipple) => {
            if (side === 'left') this.leftStickData.active = true;
            if (side === 'right') this.rightStickData.active = true;
            this.lastLeftStickY = 0;
        });

        manager.on('move', (evt, nipple) => {
            const vector = nipple.vector || { x: 0, y: 0 }; // Ensure vector exists
            if (side === 'left') {
                this.leftStickData.vector = vector;
                this.lastLeftStickY = vector.y; // Store Y for processing in update
            } else if (side === 'right') {
                this.rightStickData.vector = vector;
            }
        });

        manager.on('end', (evt, nipple) => {
             const distance = nipple.distance || 0; // Get distance before resetting
             // Reset stick data immediately
             if (side === 'left') {
                 this.leftStickData.active = false;
                 this.leftStickData.vector = { x: 0, y: 0 };
                 this.lastLeftStickY = 0;
             } else if (side === 'right') {
                 this.rightStickData.active = false;
                 this.rightStickData.vector = { x: 0, y: 0 };

                 // Process Tap based on the distance *before* reset
                 if (distance <= JOYSTICK_TAP_THRESHOLD) {
                     const now = performance.now();
                     if (now - this.lastTapTimeRight > this.tapDebounce) {
                         console.log(`[SceneManager] Right joystick tap registered (dist: ${distance.toFixed(2)})`);
                         this.lastTapTimeRight = now;
                         // Set a flag or directly call logic in _processMobileInput
                         // Let's set a flag for clarity
                         this.rightStickTapFlag = true;
                     } else {
                          console.log("[SceneManager] Right joystick tap ignored (debounce).");
                     }
                 }
             }
        });
    }

    _handleOrientationChange() {
        const likelyMobile = ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);
        const isLandscape = window.matchMedia("(orientation: landscape)").matches;

        this.isMobileLandscape = likelyMobile && isLandscape;
        console.log(`[SceneManager] Orientation changed. Is Mobile Landscape: ${this.isMobileLandscape}`);

        if (this.isMobileLandscape) {
            this._initJoysticks();
        } else {
            this._destroyJoysticks();
        }
    }

     // --- NEW: Process Mobile Input ---
     _processMobileInput(deltaTime) {
         if (!this.isMobileLandscape) return; // Only process if joysticks are active

         // --- Handle Right Stick Tap Actions ---
         if (this.rightStickTapFlag) {
             this.rightStickTapFlag = false; // Consume the flag

             if (this.currentState === STATE.HERO_PAGE && this.activeSceneHandler instanceof HeroPageHandler) {
                 console.log("[SceneManager] Processing Right Tap -> HeroPage Start");
                 this.activeSceneHandler.triggerStartGame();
             } else if (this.currentState === STATE.CHARACTER_CREATION && this.activeSceneHandler instanceof CharacterCreationHandler) {
                 console.log("[SceneManager] Processing Right Tap -> Dialogue Confirmation");
                 this.activeSceneHandler.confirmDialogue();
             } else {
                  console.log("[SceneManager] Right Tap ignored (state not Hero/Creation).");
             }
         }
         // --- End Right Stick Tap ---

         // --- Handle Left Stick Dialogue Navigation ---
         if (this.currentState === STATE.CHARACTER_CREATION && this.activeSceneHandler instanceof CharacterCreationHandler) {
            // Check vertical movement threshold based on lastLeftStickY updated in 'move' event
            if (this.leftStickData.active) { // Check only if stick is active
                const currentY = this.leftStickData.vector.y;
                // Check if crossed threshold going up
                if (currentY > JOYSTICK_VERTICAL_THRESHOLD && this.lastProcessedStickY <= JOYSTICK_VERTICAL_THRESHOLD) {
                    console.log("[SceneManager] Left Stick UP detected -> Navigating Dialogue");
                    this.activeSceneHandler.navigateDialogue('up');
                    this.lastProcessedStickY = currentY; // Mark as processed
                }
                // Check if crossed threshold going down
                else if (currentY < -JOYSTICK_VERTICAL_THRESHOLD && this.lastProcessedStickY >= -JOYSTICK_VERTICAL_THRESHOLD) {
                    console.log("[SceneManager] Left Stick DOWN detected -> Navigating Dialogue");
                    this.activeSceneHandler.navigateDialogue('down');
                    this.lastProcessedStickY = currentY; // Mark as processed
                }
                // Update lastProcessedStickY if stick returns towards center below threshold
                else if (Math.abs(currentY) < JOYSTICK_VERTICAL_THRESHOLD) {
                     this.lastProcessedStickY = currentY;
                }
            } else {
                 this.lastProcessedStickY = 0; // Reset if stick is not active
            }
         } else {
             // Reset lastProcessedStickY if not in character creation state
             this.lastProcessedStickY = 0;
         }
         // --- End Left Stick Dialogue Nav ---

         // --- Pass Input to PlayerController if in Dungeon State ---
         if (this.currentState === STATE.DUNGEON && this.activeSceneHandler instanceof DungeonHandler) {
             const playerController = this.activeSceneHandler.playerController;
             if (playerController && typeof playerController.handleMobileInput === 'function') {
                 playerController.handleMobileInput(this.leftStickData, this.rightStickData);
             } else {
                 // console.warn("PlayerController or handleMobileInput not found in DungeonHandler!");
             }
         }
         // --- End Pass Input to Player ---
     }
     // --- END NEW METHODS ---

     // --- Camera State Management Methods ---
     _saveCameraState() {
         if (!this.camera) return;

         // Save camera state
         this._cameraState.position = this.camera.position.clone();
         this._cameraState.quaternion = this.camera.quaternion.clone();
         this._cameraState.up = this.camera.up.clone();
         this._cameraState.zoom = this.camera.zoom;
         this._cameraState.isTopDown = this.activeSceneHandler?.isTopDownView || false;
     }

     _restoreCameraState() {
         if (!this.camera || !this._cameraState.position) return;

         // Don't restore camera state if we're in DungeonHandler
         // Let the DungeonHandler manage its own camera
         if (this.activeSceneHandler instanceof DungeonHandler) {
             return;
         }

         // For other scenes, restore the camera state
         this.camera.position.copy(this._cameraState.position);
         this.camera.quaternion.copy(this._cameraState.quaternion);
         this.camera.up.copy(this._cameraState.up);
         this.camera.zoom = this._cameraState.zoom;
     }
}

export default SceneManager;
