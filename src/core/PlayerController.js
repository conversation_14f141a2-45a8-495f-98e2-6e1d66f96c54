import * as THREE from 'three';
import { VOXEL_SIZE } from '../generators/prefabs/shared.js'; // Import VOXEL_SIZE
import { STATE } from '../constants.js'; // <<< Import STATE
import SoulOrbHealthBar from '../ui/SoulOrbHealthBar.js';

// Re-import player constants if needed (or define them here)
const PLAYER_PIXEL_SCALE_XY = 3;
const PLAYER_PIXEL_SCALE_Z = 3;
const PLAYER_VOXEL_WIDTH = VOXEL_SIZE * PLAYER_PIXEL_SCALE_XY;
const PLAYER_VOXEL_HEIGHT = VOXEL_SIZE * PLAYER_PIXEL_SCALE_XY;
const PLAYER_VOXEL_DEPTH = VOXEL_SIZE * PLAYER_PIXEL_SCALE_Z;

// Remove TRAIL_LENGTH constant - will use currentHealth instead
// const TRAIL_LENGTH = 8;
const BASE_MAX_TRAIL_LENGTH = 5;    // Trail length when player is at 100% health
const TRAIL_SPAWN_INTERVAL = 0.12;
const BASE_TRAIL_FADE_DURATION = 0.6; // Renamed: Fade duration at 100% health
const MIN_TRAIL_FADE_DURATION = 0.1; // Minimum fade duration
const TRAIL_LIGHT_INTENSITY = 0.4;
const TRAIL_LIGHT_RANGE = 1.0;
const TRAIL_LIGHT_COLOR = 0x88ddff;

// --- Axes Helper Constants ---
const AXES_HELPER_SIZE = 2.0; // <<< INCREASED SIZE SIGNIFICANTLY for debugging (larger than enemy)
// ---------------------------

// --- NippleJS Constants (Keep for handleMobileInput) ---
const JOYSTICK_THRESHOLD = 0.3; // How far joystick needs to move to register as input
// const JOYSTICK_TAP_THRESHOLD = 0.1; // Tap detection moved to SceneManager
// const JOYSTICK_VERTICAL_THRESHOLD = 0.5; // Dialogue nav moved to SceneManager

const PROJECTILE_GRAVITY = -4.9; // <<< Reduced gravity further

class PlayerController {
    constructor(playerMesh, camera, scene, clock, dungeonHandler, collisionObjects = [], floorBounds = { minX: -Infinity, maxX: Infinity, minZ: -Infinity, maxZ: Infinity }) {
        this.playerMesh = playerMesh;
        this.camera = camera; // Keep for potential future use (e.g., camera relative movement)
        this.scene = scene; // Store scene reference
        this.clock = clock; // Store clock reference
        this.dungeonHandler = dungeonHandler; // <<< Store DungeonHandler reference
        this.collisionObjects = collisionObjects;
        this.floorBounds = floorBounds;

        // Find limb subgroups
        this.body = this.playerMesh.getObjectByName('Body'); // Assuming a main 'Body' group exists
        this.head = this.playerMesh.getObjectByName('Head'); // Assuming 'Head' exists
        this.leftLeg = this.playerMesh.getObjectByName('leftLeg');
        this.rightLeg = this.playerMesh.getObjectByName('rightLeg');
        this.leftArm = this.playerMesh.getObjectByName('leftArm');
        this.rightArm = this.playerMesh.getObjectByName('rightArm');
        this.leftFoot = this.playerMesh.getObjectByName('leftFoot'); // <<< Add feet if they exist
        this.rightFoot = this.playerMesh.getObjectByName('rightFoot'); // <<< Add feet if they exist

        this.moveSpeed = 8.0; // Increased from 5.0 to make player faster
        this.keys = {
            forward: false, backward: false, left: false, right: false,
            shootUp: false, shootDown: false, shootLeft: false, shootRight: false,
            toggleCamera: false, // <<< Add key state for camera toggle
            zoomIn: false,      // <<< Add key state for zoom in
            zoomOut: false,      // <<< Add key state for zoom out
            toggleDebugLight: false // <<< Add key state for debug light toggle
        };
        this.inputEnabled = false;

        // Collision detection helpers - Updated counts for VOXEL_SIZE = 0.05
        // const legHeightVoxels = 14; // << Remove old dimension constants
        // const bodyHeightVoxels = 14;
        // const headWidthVoxels = 14;
        // const headHeightVoxels = 10;
        // const hairWidthVoxels = 14;
        // const bodyDepthVoxels = 11;
        // const armWidthVoxels = 5;
        // const armHeightVoxels = 13;
        // const armDepthVoxels = 5;
        // const neckWidthVoxels = 5;
        // const neckHeightVoxels = 3;
        // const neckDepthVoxels = 5;
        // const shoeWidthVoxels = 8;
        // const shoeHeightVoxels = 3;
        // const shoeDepthVoxels = 8;
        // const headDepthVoxels = 10;

        const playerScale = 0.7; // Match the scale applied in DungeonHandler

        // Approximate bounding box based on voxel counts and VOXEL_SIZE
        // --- New Dimensions for Elemental Model (v2 - Macro Voxels) ---
        // const elementalWidthVoxels = 11; // Old base voxel counts
        // const elementalHeightVoxels = 25;
        // const elementalDepthVoxels = 7;

        const elementalMacroWidth = 7;   // Approx max width in macro voxels
        const elementalMacroHeight = 22; // Updated total height (was 21)
        const elementalMacroDepth = PLAYER_PIXEL_SCALE_Z;   // Depth now matches the scale (Should be 3)

        let playerWidth = elementalMacroWidth * PLAYER_VOXEL_WIDTH; // Use macro dimensions
        let playerHeight = elementalMacroHeight * PLAYER_VOXEL_HEIGHT;
        let playerDepth = elementalMacroDepth * PLAYER_VOXEL_DEPTH; // Use macro depth
        // -------------------------------------------------------------

        // Apply scaling to the calculated collision dimensions
        playerWidth *= playerScale;
        playerHeight *= playerScale;
        playerDepth *= playerScale;

        this.playerSize = new THREE.Vector3(playerWidth, playerHeight, playerDepth);
        this.playerBox = new THREE.Box3();
        // --- Add Box Helpers ---
        this.playerBoxHelper = new THREE.Box3Helper(this.playerBox, 0x00ff00); // Green for player
        this.scene.add(this.playerBoxHelper);
        this.wallBoxHelpers = {}; // Store wall helpers temporarily
        // -----------------------
        this._updatePlayerBounds();

        // Bind event listeners
        this.boundKeyDown = this._handleKeyDown.bind(this);
        this.boundKeyUp = this._handleKeyUp.bind(this);

        this.animationTime = 0;
        this.walkCycleSpeed = 15; // Adjust speed of swing
        this.walkCycleMagnitude = Math.PI / 8; // Adjust amount of swing
        // Idle Animation Parameters
        this.idleCycleSpeed = 1.5;
        this.idleCycleMagnitude = Math.PI / 48; // Small magnitude for subtle sway
        this.idleTime = 0; // Timer for idle duration

        // --- Player Health ---
        this.maxHealth = 200; // Set to 200 for UI purposes
        this.actualHealth = 8; // Track actual health separately
        this.currentHealth = 8; // Start with 8 health for UI display
        this.healthBar = new SoulOrbHealthBar(camera, this.maxHealth);
        this.healthBar.updateHealth(this.currentHealth); // Explicitly set the health bar to show 8
        // ---------------------

        // --- Player Souls ---
        this.souls = 0; // Start with 0 souls
        this.onSoulsChanged = null; // Callback for when souls change
        // -------------------

        // --- Foot Light Trail ---
        this.footTrailLights = []; // Array to hold trail lights
        this.trailSpawnTimer = 0;  // Timer to control spawn rate
        // --- End Foot Light Trail ---

        // --- ESP Debug State ---
        this.isEspEnabled = false;
        // -----------------------

        // --- Rotation Helpers State ---
        this.isEspRotationViewActive = false; // <<< Separate state for rotation view
        this.rotationHelpersGroup = new THREE.Group(); // <<< Group for helpers
        this.scene.add(this.rotationHelpersGroup);
        this.rotationHelpersGroup.visible = false; // Start hidden
        this.boneHelpers = {}; // Map to store helpers by bone name
        this._createRotationHelpers(); // Create helpers on initialization
        // -----------------------------

        // --- Shooting State ---
        this.shootDirection = new THREE.Vector3();
        this.isShooting = false;
        this.shootCooldown = 0.25; // Cooldown in seconds (4 shots per second)
        this.timeSinceLastShot = this.shootCooldown; // Allow shooting immediately
        // ---------------------

        // --- Projectile Stats ---
        this.baseProjectileDamage = 10;
        this.baseProjectileRange = 20.0; // <<< Increased default range further
        this.baseProjectileSize = 0.15;

        this.projectileDamage = this.baseProjectileDamage;
        this.projectileRange = this.baseProjectileRange;
        this.projectileSize = this.baseProjectileSize;
        // ------------------------

        // Store a reference to the DungeonHandler in the player's userData
        // This will help other systems (like CRTEffectManager) find the DungeonHandler
        if (this.playerMesh && this.dungeonHandler) {
            this.playerMesh.userData.dungeonHandler = this.dungeonHandler;
        }

        this._updateRotationHelpersVisibility(); // Ensure helpers are hidden initially
    }

    enable() {
        console.log("PlayerController enabled");
        window.addEventListener('keydown', this.boundKeyDown);
        window.addEventListener('keyup', this.boundKeyUp);
        this.inputEnabled = true;

        // Ensure health bar exists and is properly initialized
        if (!this.healthBar) {
            this.healthBar = new SoulOrbHealthBar(this.camera, this.maxHealth);
            this.healthBar.updateHealth(this.currentHealth);
        }
    }

    disable() {
        console.log("PlayerController disabled");
        window.removeEventListener('keydown', this.boundKeyDown);
        window.removeEventListener('keyup', this.boundKeyUp);
        this.inputEnabled = false;
        this.keys = { forward: false, backward: false, left: false, right: false,
            shootUp: false, shootDown: false, shootLeft: false, shootRight: false };

        // Cleanup foot trail lights
        this.footTrailLights.forEach(light => {
            if (light && this.scene) {
                this.scene.remove(light);
            }
        });
        this.footTrailLights = [];

        // Do NOT dispose health bar during room transitions
        // if (this.healthBar) {
        //     this.healthBar.dispose();
        // }

        // Cleanup ALL box helpers (player + collision)
        if (this.playerBoxHelper && this.scene) {
            this.scene.remove(this.playerBoxHelper);
        }
        Object.values(this.wallBoxHelpers).forEach(helper => {
            if (helper && this.scene) this.scene.remove(helper);
        });
        this.wallBoxHelpers = {};
    }

    _handleKeyDown(event) {
        let isEspToggle = false;
        // Check for Tab key
        if (event.code === 'Tab') {
             isEspToggle = true;

             // Display room shape name when Tab is pressed
             if (this.dungeonHandler && this.inputEnabled) {
                 this.dungeonHandler.displayRoomShape();
             }
        }

        if (isEspToggle) {
            // Only toggle if input is generally enabled
            if (!this.inputEnabled) return;

            // Toggle both ESP states
            this.isEspEnabled = !this.isEspEnabled;
            this.isEspRotationViewActive = this.isEspEnabled; // Link rotation view to general ESP for now
            console.log(`ESP Toggled: ${this.isEspEnabled}, Rotation View: ${this.isEspRotationViewActive}`);

            // Update visibility of different helpers
            if (this.playerBoxHelper) this.playerBoxHelper.visible = this.isEspEnabled;
            this._updateEspHelpersVisibility(); // Updates collision helpers
            this._updateRotationHelpersVisibility(); // Updates bone rotation helpers

            // --- Call DungeonHandler to update display ---
            if (this.dungeonHandler) {
                 this.dungeonHandler._updateTotalRoomCountDisplay(this.isEspEnabled);
                 this.dungeonHandler.setEspRotationViewActive(this.isEspRotationViewActive); // <<< Notify DungeonHandler
            }
            // -------------------------------------------

            event.preventDefault(); // Prevent default Tab behavior (like changing focus)
            return; // Don't process other keys if ESP was toggled
        }

        if (!this.inputEnabled) return; // Ignore other keys if disabled

        switch (event.code) {
            // --- Movement (Arrow Keys) ---
            case 'ArrowUp':    this.keys.forward = true; break;
            case 'ArrowDown':  this.keys.backward = true; break;
            case 'ArrowLeft':  this.keys.left = true; break;
            case 'ArrowRight': this.keys.right = true; break;

            // --- Shooting (WASD) ---
            case 'KeyW': this.keys.shootUp = true; break;
            case 'KeyS': this.keys.shootDown = true; break;
            case 'KeyA': this.keys.shootLeft = true; break;
            case 'KeyD': this.keys.shootRight = true; break;

            // --- Camera Toggle ---
            case 'KeyC': // <<< Add case for 'C' key
                if (!this.keys.toggleCamera) { // Prevent repeated calls while key is held
                    this.keys.toggleCamera = true;
                    this.dungeonHandler?.toggleCameraView(); // Call method on DungeonHandler
                }
                break;

            // --- DEBUG: Damage Player ---
            case 'KeyU':
                this.takeDamage(1);
                break;
            // --- DEBUG: Increase Max Health ---
            case 'KeyY':
                this.increaseMaxHealth(1);
                break;
            // --- DEBUG: Increase Projectile Stats ---
            case 'Digit1': this.increaseProjectileSize(0.02); break;
            case 'Digit2': this.increaseProjectileRange(1.0); break;
            case 'Digit3': this.increaseProjectileDamage(5); break;
            // --- DEBUG: Toggle Music Debug HUD ---
            case 'KeyM':
                if (this.dungeonHandler && this.dungeonHandler.audioManager) {
                    this.dungeonHandler.audioManager.toggleMusicDebug();
                }
                break;

            // --- Camera Zoom ---
            case 'Slash': this.keys.zoomOut = true; break; // Updated code for '-'
            case 'BracketRight': this.keys.zoomIn = true; break; // Updated code for '+' (often '=' key)

            // --- Debug Light Toggle ---
            case 'KeyO':
                if (!this.keys.toggleDebugLight) { // Prevent rapid toggles
                    this.keys.toggleDebugLight = true;
                    this.dungeonHandler?.toggleDebugLight(); // Call method on DungeonHandler
                }
                break;

            // --- Teleport to Boss (F10) ---
            case 'F10':
                console.log("[PlayerController] F10 key pressed - Teleporting to boss");
                console.log("[PlayerController] dungeonHandler exists:", !!this.dungeonHandler);
                if (this.dungeonHandler) {
                    console.log("[PlayerController] Calling teleportToBoss method");
                    this.teleportToBoss();
                    // Prevent default F10 behavior
                    event.preventDefault();
                    console.log("[PlayerController] After teleportToBoss call");
                } else {
                    console.error("[PlayerController] Cannot teleport: dungeonHandler is null");
                }
                break;


            // --- Alternative Teleport to Boss (F11 key) ---
            case 'F11':
                console.log("[PlayerController] F11 key pressed - Direct teleport to boss");
                if (this.dungeonHandler) {
                    // Direct teleport to boss room using room ID 1 (usually the boss room)
                    this.dungeonHandler._transitionToRoom(1, 'north');
                    event.preventDefault(); // Prevent default F11 behavior
                } else {
                    console.error("[PlayerController] Cannot teleport: dungeonHandler is null");
                }
                break;

            // --- Alternative Teleport to Boss (T key) ---
            case 'KeyT':
                console.log("[PlayerController] T key pressed - Teleporting to boss");
                if (this.dungeonHandler) {
                    this.teleportToBoss();
                } else {
                    console.error("[PlayerController] Cannot teleport: dungeonHandler is null");
                }
                break;
        }
    }

    _handleKeyUp(event) {
        switch (event.code) {
            // --- Movement (Arrow Keys) ---
            case 'ArrowUp':    this.keys.forward = false; break;
            case 'ArrowDown':  this.keys.backward = false; break;
            case 'ArrowLeft':  this.keys.left = false; break;
            case 'ArrowRight': this.keys.right = false; break;

            // --- Shooting (WASD) ---
            case 'KeyW': this.keys.shootUp = false; break;
            case 'KeyS': this.keys.shootDown = false; break;
            case 'KeyA': this.keys.shootLeft = false; break;
            case 'KeyD': this.keys.shootRight = false; break;

            // --- Camera Toggle ---
            case 'KeyC': this.keys.toggleCamera = false; break; // <<< Reset toggle key state on key up

            // --- Camera Zoom ---
            case 'Slash': this.keys.zoomOut = false; break; // Updated code for '-'
            case 'BracketRight': this.keys.zoomIn = false; break; // Updated code for '+'

            // --- Debug Light Toggle ---
            case 'KeyO': this.keys.toggleDebugLight = false; break; // Reset flag on key up
        }
    }

    _updatePlayerBounds() {
        const boxCenter = this.playerMesh.position.clone();
        // --- Restore standard vertical centering ---
        // boxCenter.y = this.playerSize.y * 0.45;   // Previous attempt (slightly lower center)
        boxCenter.y += this.playerSize.y * 0.5;    // Standard center alignment
        // -------------------------------------------
        this.playerBox.setFromCenterAndSize(
            boxCenter,
            this.playerSize
        );
        // Update the helper
        if (this.playerBoxHelper) {
            this.playerBoxHelper.box.copy(this.playerBox); // Use copy to update helper's box
        }
    }

    // Check collision between player's potential bounding box and collision objects
    _checkCollision(potentialPosition) {
        const potentialPlayerBox = this.playerBox.clone().translate(
             potentialPosition.clone().sub(this.playerMesh.position)
        );

        for (const obj of this.collisionObjects) {
             // Ensure collision object has geometry to create a bounding box
            if (!obj.geometry) continue;

            // Use setFromObject for more reliable world bounding box
            const objectBox = new THREE.Box3().setFromObject(obj);

            if (potentialPlayerBox.intersectsBox(objectBox)) {
                // console.log("Collision detected with:", obj.name || obj.uuid);
                return true; // Collision detected
            }
        }
        return false; // No collision
    }

    _isWithinFloorBounds(position) {
         // Use minX/maxX etc. from constructor
         return position.x >= this.floorBounds.minX && position.x <= this.floorBounds.maxX &&
                position.z >= this.floorBounds.minZ && position.z <= this.floorBounds.maxZ;
     }

    // New method to resolve ground collision
    _resolveGroundCollision() {
        // We abandon the bounding box approach for ground resolution for now,
        // as the bounds calculation itself seems tied to the mesh origin incorrectly.
        // Instead, we enforce a minimum mesh Y position.

        // Estimate the required offset to lift the mesh origin so the feet are visually on the ground.
        // This value might need tuning based on the model geometry.
        const requiredMeshYOffset = 0.3; // Increased offset from 0.25

        if (this.playerMesh.position.y < requiredMeshYOffset) {
            const correction = requiredMeshYOffset - this.playerMesh.position.y;
            // console.log(`[Ground Resolve] Mesh Y too low (${this.playerMesh.position.y.toFixed(3)}). Target: ${requiredMeshYOffset}. Applying Correction: ${correction.toFixed(3)}`);
            this.playerMesh.position.y += correction; // Apply correction directly to mesh Y

            // Update bounds AFTER correction as the mesh position changed.
            this._updatePlayerBounds();
        }
     }

    // --- NEW: Helper to check collision at a specific position ---
    _checkCollisionAtPosition(potentialPosition) {
        // --- Check Numerical Bounds FIRST ---
        if (potentialPosition.x < this.floorBounds.minX || potentialPosition.x > this.floorBounds.maxX ||
            potentialPosition.z < this.floorBounds.minZ || potentialPosition.z > this.floorBounds.maxZ) {
            return true;
        }
        // -------------------------------

        const tempBoxCenter = potentialPosition.clone();
        // Align temp box the same way as playerBox
        // tempBoxCenter.y = this.playerSize.y * 0.45; // Using slightly lowered center
        tempBoxCenter.y += this.playerSize.y * 0.5; // Standard center alignment
        const tempPlayerBox = new THREE.Box3();
        tempPlayerBox.setFromCenterAndSize(tempBoxCenter, this.playerSize);

        // -- Collision helpers are now managed in update() --

        let collisionDetected = false; // Flag to track if any collision happened

        // Check against dynamic collision objects
        for (const collisionObject of this.collisionObjects) {
            let objectBox = new THREE.Box3();
            let collidedWithObject = false;

            // --- Use Standard 3D Check for ALL Collision Objects ---
            // Includes visible walls AND invisible boundary walls
            if (collisionObject.geometry || (collisionObject.children && collisionObject.children.length > 0)) {
                 objectBox.setFromObject(collisionObject);
                 if (tempPlayerBox.intersectsBox(objectBox)) {
                     collidedWithObject = true;
                 }
             }
            // -----------------------------------------------------

            if (collidedWithObject) {
                 // console.log(`[Collision Check] Collision detected with object:`, collisionObject.name || collisionObject.uuid);
                collisionDetected = true; // Set flag
                break;
            }
        }

        return collisionDetected; // Return true if any collision occurred
    }
    // --- END NEW HELPER ---

    // --- NEW: Helper to manage ESP helper visibility ---
    _updateEspHelpersVisibility() {
        // Update Player Helper
        if (this.playerBoxHelper) {
            this.playerBoxHelper.visible = this.isEspEnabled;
        }

        // Update Collision Object Helpers
        const currentCollisionObjectUUIDs = new Set(this.collisionObjects.map(obj => obj.uuid));

        // Remove helpers for objects that are no longer in collisionObjects
        for (const uuid in this.wallBoxHelpers) {
            if (!currentCollisionObjectUUIDs.has(uuid)) {
                const helper = this.wallBoxHelpers[uuid];
                if (helper && this.scene) {
                    this.scene.remove(helper);
                }
                delete this.wallBoxHelpers[uuid];
            }
        }

        // Add/Update helpers for current collisionObjects
        for (const collisionObject of this.collisionObjects) {
            let helper = this.wallBoxHelpers[collisionObject.uuid];
            const objectBox = new THREE.Box3().setFromObject(collisionObject);
            const objectUUID = collisionObject.uuid;

            // Determine color based on name (visible walls vs invisible boundaries)
            let helperColor;
            if (collisionObject.name.startsWith('invisibleBoundary')) {
                helperColor = 0xffff00; // Yellow for invisible walls
            } else if (collisionObject.name === 'StonebrickWallSegment') {
                helperColor = 0xff0000; // Red for visible walls
            } else {
                helperColor = 0x808080; // Grey for anything else (shouldn't happen often)
            }

            if (!helper) {
                // Create new helper if it doesn't exist
                helper = new THREE.Box3Helper(objectBox, helperColor);
                this.scene.add(helper);
                this.wallBoxHelpers[objectUUID] = helper;
            } else {
                // Update existing helper's box and color (in case object changed)
                helper.box = objectBox;
                helper.material.color.setHex(helperColor);
            }
            // Set visibility based on ESP state
            helper.visible = this.isEspEnabled;

            // --- Remove Floor Child Helper Logic ---
            // We are no longer checking floor children directly for collision
            // or visualizing them separately in ESP mode.
            // Ensure any old child helpers are removed
            for (const uuidKey in this.wallBoxHelpers) {
                if (uuidKey.startsWith(`${objectUUID}_child_`)) {
                    const staleChildHelper = this.wallBoxHelpers[uuidKey];
                    if (staleChildHelper && this.scene) {
                        this.scene.remove(staleChildHelper);
                    }
                    delete this.wallBoxHelpers[uuidKey]; // Remove from dictionary
                }
            }
            // --- End Floor Child Helper Logic ---
        }
    }
    // --- End ESP Helper Update ---

    // --- NEW: Handle Mobile Input (Called by SceneManager) ---
    handleMobileInput(leftStickData, rightStickData) {
        if (!this.inputEnabled) return; // Don't process if controller disabled

        // Reset joystick contributions first
        this.keys.forward = this.keys.forward && !this.isUsingKeyboard('forward'); // Keep if keyboard pressed
        this.keys.backward = this.keys.backward && !this.isUsingKeyboard('backward');
        this.keys.left = this.keys.left && !this.isUsingKeyboard('left');
        this.keys.right = this.keys.right && !this.isUsingKeyboard('right');
        this.keys.shootUp = this.keys.shootUp && !this.isUsingKeyboard('shootUp');
        this.keys.shootDown = this.keys.shootDown && !this.isUsingKeyboard('shootDown');
        this.keys.shootLeft = this.keys.shootLeft && !this.isUsingKeyboard('shootLeft');
        this.keys.shootRight = this.keys.shootRight && !this.isUsingKeyboard('shootRight');

        // Apply Left Stick (Movement)
        if (leftStickData && leftStickData.active) {
            if (leftStickData.vector.y > JOYSTICK_THRESHOLD) this.keys.forward = true;
            if (leftStickData.vector.y < -JOYSTICK_THRESHOLD) this.keys.backward = true;
            if (leftStickData.vector.x < -JOYSTICK_THRESHOLD) this.keys.left = true;
            if (leftStickData.vector.x > JOYSTICK_THRESHOLD) this.keys.right = true;
        }

        // Apply Right Stick (Shooting)
        if (rightStickData && rightStickData.active) {
            if (rightStickData.vector.y > JOYSTICK_THRESHOLD) this.keys.shootUp = true;
            if (rightStickData.vector.y < -JOYSTICK_THRESHOLD) this.keys.shootDown = true;
            if (rightStickData.vector.x < -JOYSTICK_THRESHOLD) this.keys.shootLeft = true;
            if (rightStickData.vector.x > JOYSTICK_THRESHOLD) this.keys.shootRight = true;
        }
    }

    // Helper to check if a specific key direction is from keyboard (implementation depends on how _handleKeyDown/_handleKeyUp set state)
    // For now, we assume if the key is true, it might be keyboard, so we reset based on joystick status
    // A more robust solution might involve separate state for keyboard vs joystick input.
    isUsingKeyboard(direction) {
        // Simple check: if the corresponding keyboard event flag is true, assume keyboard
        // This needs refinement if mixing inputs is desired differently.
        switch (direction) {
            // Link key flags to actual keyboard event keys if necessary for more complex checks
            // This is a basic implementation assuming direct mapping
            default: return false; // Placeholder - refine if needed
        }
    }
    // --- END NEW METHOD ---

    update(deltaTime) {
        // --- Update ESP Helpers ---
        // Do this early in the update, using current collisionObjects
        this._updateEspHelpersVisibility();
        // ------------------------

        if (!this.inputEnabled) return;

        const time = this.clock.getElapsedTime();
        this.trailSpawnTimer += deltaTime;
        this.timeSinceLastShot += deltaTime;

        // --- Movement Logic (Driven by this.keys, which is now updated by keyboard AND handleMobileInput) ---
        const moveDirection = new THREE.Vector3();
        if (this.keys.forward) moveDirection.z -= 1;
        if (this.keys.backward) moveDirection.z += 1;
        if (this.keys.left) moveDirection.x -= 1;
        if (this.keys.right) moveDirection.x += 1;

        let attemptedMove = false;
        let movementBasedQuaternion = this.playerMesh.quaternion.clone(); // Store original quaternion

        if (moveDirection.lengthSq() > 0) {
            // console.log("Processing Movement (Keys/Stick detected)"); // Updated Log
            attemptedMove = true;
            moveDirection.normalize();
            const moveDistance = this.moveSpeed * deltaTime;
            const moveVector = moveDirection.multiplyScalar(moveDistance);

            const originalPosition = this.playerMesh.position.clone();
            const finalMove = new THREE.Vector3(0, 0, 0);

            // --- Independent Axis Collision Check ---
            // Check X movement
            const potentialPositionX = originalPosition.clone();
            potentialPositionX.x += moveVector.x;
            if (!this._checkCollisionAtPosition(potentialPositionX)) {
                finalMove.x = moveVector.x;
            }

            // Check Z movement (start from position *after potential X move* for smoother cornering)
            // Alternatively, check from originalPosition if strict axis separation is desired.
            // Let's check from originalPosition for simplicity and robustness against wall sticking.
            const potentialPositionZ = originalPosition.clone();
            potentialPositionZ.z += moveVector.z;
            if (!this._checkCollisionAtPosition(potentialPositionZ)) {
                finalMove.z = moveVector.z;
            }
            // --- End Independent Axis Check ---

            // Apply the allowed movement
            this.playerMesh.position.add(finalMove);
            this._updatePlayerBounds();

            // Get final effective movement after collision checks
            const actualMove = this.playerMesh.position.clone().sub(originalPosition);

            // --- Player Orientation (Based ONLY on Movement) ---
            // Calculate movement rotation but store it temporarily
            const movementMade = actualMove.clone().setY(0);
            if (movementMade.lengthSq() > 0.0001) {
                let angle = Math.atan2(-movementMade.x, -movementMade.z);
                const fortyFiveDegrees = Math.PI / 4; // Changed from 90 to 45 degrees
                let snappedAngle = Math.round(angle / fortyFiveDegrees) * fortyFiveDegrees;
                snappedAngle += Math.PI;
                movementBasedQuaternion.setFromAxisAngle(new THREE.Vector3(0, 1, 0), snappedAngle);
            }

            // --- Walk Animation ---
            this.animationTime += deltaTime;
            this.idleTime = 0; // Reset idle timer when moving
            const swingAngle = Math.sin(this.animationTime * this.walkCycleSpeed) * this.walkCycleMagnitude;
            if (this.leftLeg) this.leftLeg.rotation.x = swingAngle;
            if (this.rightLeg) this.rightLeg.rotation.x = -swingAngle;
            if (this.leftArm) this.leftArm.rotation.x = -swingAngle;
            if (this.rightArm) this.rightArm.rotation.x = swingAngle;

        } else { // Not moving
            attemptedMove = false;
            // --- Idle/Reset Logic ---
            this.idleTime += deltaTime;
            if (this.idleTime <= 0.1) {
                this.animationTime += deltaTime;
                const idleAngle = Math.sin(this.animationTime * this.idleCycleSpeed) * this.idleCycleMagnitude;
                if (this.leftLeg) this.leftLeg.rotation.x = idleAngle;
                if (this.rightLeg) this.rightLeg.rotation.x = idleAngle;
                if (this.leftArm) this.leftArm.rotation.x = -idleAngle;
                if (this.rightArm) this.rightArm.rotation.x = -idleAngle;
            } else {
                if (this.leftLeg) this.leftLeg.rotation.x = 0;
                if (this.rightLeg) this.rightLeg.rotation.x = 0;
                if (this.leftArm) this.leftArm.rotation.x = 0;
                if (this.rightArm) this.rightArm.rotation.x = 0;
            }
        }
        // --- END of movement logic ---

        // --- Shooting Logic (Driven by this.keys) ---
        this.shootDirection.set(0, 0, 0);
        if (this.keys.shootUp)    this.shootDirection.z = -1;
        if (this.keys.shootDown)  this.shootDirection.z = 1;
        if (this.keys.shootLeft)  this.shootDirection.x = -1;
        if (this.keys.shootRight) this.shootDirection.x = 1;

        this.isShooting = this.shootDirection.lengthSq() > 0;

        // --- Apply Rotation FIRST based on priority ---
        if (this.isShooting) {
            // Calculate and apply shooting rotation if WASD is pressed
            this.shootDirection.normalize(); // Normalize here for angle calculation
            let shootAngle = Math.atan2(-this.shootDirection.x, -this.shootDirection.z);
            const fortyFiveDegrees = Math.PI / 4; // Changed from 90 to 45 degrees
            let snappedShootAngle = Math.round(shootAngle / fortyFiveDegrees) * fortyFiveDegrees;
            snappedShootAngle += Math.PI;
            this.playerMesh.quaternion.setFromAxisAngle(new THREE.Vector3(0, 1, 0), snappedShootAngle);
        } else if (attemptedMove) {
            // Apply movement rotation only if NOT shooting but moving
            const movementMade = moveDirection.clone().setY(0);
            if (movementMade.lengthSq() > 0.0001) {
                let angle = Math.atan2(-movementMade.x, -movementMade.z);
                const fortyFiveDegrees = Math.PI / 4; // Changed from 90 to 45 degrees
                let snappedAngle = Math.round(angle / fortyFiveDegrees) * fortyFiveDegrees;
                snappedAngle += Math.PI;
                this.playerMesh.quaternion.setFromAxisAngle(new THREE.Vector3(0, 1, 0), snappedAngle);
            }
        }
        // If neither shooting nor moving, rotation remains untouched
        // --- End Rotation Logic ---

        // --- Fire Shot based on Cooldown ---
        if (this.isShooting && this.timeSinceLastShot >= this.shootCooldown) {
            // console.log("Processing Shoot Action (Keys/Stick detected, Cooldown Ready)"); // Updated Log
            this.timeSinceLastShot = 0;
            // shootDirection is already normalized if isShooting is true

            // --- Spawn Projectile ---
            if (this.dungeonHandler) {
                const startPos = this.playerMesh.position.clone();
                startPos.y += this.playerSize.y * 0.5;
                // --- Pass current projectile stats ---
                // Create a player projectile data object
                const playerProjectileData = {
                    damage: this.projectileDamage,
                    range: this.projectileRange,
                    size: this.projectileSize
                };

                this.dungeonHandler.spawnProjectile(
                    startPos,
                    this.shootDirection,
                    playerProjectileData
                );
            } else {
                console.warn("DungeonHandler reference not found in PlayerController!");
            }
            // -------------------------
        }
        // --- END Shooting Logic ---

        // Resolve ground collision AFTER movement and animation update
        this._resolveGroundCollision(); // Ensure Y pos is correct

        // Calculate allowed trail length based on health percentage
        const allowedTrailLength = Math.round((this.currentHealth / this.maxHealth) * BASE_MAX_TRAIL_LENGTH);
        // Calculate current fade duration based on health percentage
        const currentFadeDuration = MIN_TRAIL_FADE_DURATION + (BASE_TRAIL_FADE_DURATION - MIN_TRAIL_FADE_DURATION) * (this.currentHealth / this.maxHealth);

        // --- Update Foot Light Trail ---
        // Spawn new light if attemptedMove and interval passed (use attemptedMove)
        if (attemptedMove && this.trailSpawnTimer >= TRAIL_SPAWN_INTERVAL) {
            this.trailSpawnTimer = 0; // Reset timer

            let trailLight = null;
            // Check against calculated allowedTrailLength
            if (this.footTrailLights.length >= allowedTrailLength) {
                // Reuse the oldest light (only if allowed length > 0)
                if (allowedTrailLength > 0) {
                     trailLight = this.footTrailLights.shift();
                } else {
                    // If allowed length is 0, don't reuse or spawn
                    trailLight = null;
                }
            } else {
                // Create a new light (if allowed length > 0 and we haven't reached it)
                 if (allowedTrailLength > 0) { // Check allowed length, not current health
                    trailLight = new THREE.PointLight(TRAIL_LIGHT_COLOR, 0, TRAIL_LIGHT_RANGE);
                    this.scene.add(trailLight);
                 } else {
                     trailLight = null;
                 }
            }

            // If we have a light to position (either new or reused)
            if (trailLight) {
                // Position and activate the light
                trailLight.position.set(
                    this.playerMesh.position.x,
                    VOXEL_SIZE * 2, // Keep light near ground
                    this.playerMesh.position.z
                );
                trailLight.intensity = TRAIL_LIGHT_INTENSITY; // Set initial intensity
                trailLight.visible = true;
                trailLight.userData.spawnTime = time; // Store spawn time

                this.footTrailLights.push(trailLight); // Add to the end of the array
            }
        }

        // Update existing trail lights (fade)
        this.footTrailLights.forEach(light => {
            const age = time - light.userData.spawnTime;
            // Use currentFadeDuration for check
            if (age >= currentFadeDuration || currentFadeDuration <= MIN_TRAIL_FADE_DURATION) {
                light.intensity = 0;
                light.visible = false;
            } else {
                // Use currentFadeDuration for ratio calculation
                const fadeRatio = Math.max(0, 1.0 - (age / currentFadeDuration));
                light.intensity = TRAIL_LIGHT_INTENSITY * fadeRatio;
                light.visible = true; // Ensure visible while fading
            }
        });
        // --- End Update Foot Light Trail ---

        // --- Update Rotation Helpers ---
        this._updateRotationHelpers();
        // -----------------------------

        // Update health bar animation
        if (this.healthBar) {
            this.healthBar.update(deltaTime);
        }
    }

    // --- Health Methods ---
    takeDamage(amount, sourcePosition) {
        // Ensure amount is a number
        if (typeof amount !== 'number' || isNaN(amount)) {
            console.error(`Invalid damage amount: ${amount}, using default of 1`);
            amount = 1;
        }

        // Reduce actual health
        this.actualHealth = Math.max(0, this.actualHealth - amount);
        // Update displayed health
        this.currentHealth = Math.min(this.maxHealth, this.actualHealth);

        // Update health bar UI
        if (this.healthBar) {
            this.healthBar.updateHealth(this.currentHealth);
        }

        console.log(`Player took ${amount} damage. Actual health: ${this.actualHealth} (UI shows: ${this.currentHealth}/${this.maxHealth})`);

        // Trigger music effect for player hit
        if (this.dungeonHandler && this.dungeonHandler.audioManager) {
            this.dungeonHandler.audioManager.onPlayerHit();
        }

        return this.actualHealth <= 0;
    }

    heal(amount) {
        // Increase actual health without limit
        this.actualHealth += amount;
        // Show maxHealth in the UI if we're above it
        this.currentHealth = Math.min(this.maxHealth, this.actualHealth);
        if (this.healthBar) {
            this.healthBar.updateHealth(this.currentHealth);
        }
        console.log(`Player healed ${amount}. Actual health: ${this.actualHealth} (UI shows: ${this.currentHealth}/${this.maxHealth})`);
    }

    // Example: Method to increase max health (like finding a heart container)
    increaseMaxHealth(amount) {
        this.maxHealth += amount;
        if (this.healthBar) {
            this.healthBar.dispose();
            this.healthBar = new SoulOrbHealthBar(this.camera, this.maxHealth);
            this.healthBar.updateHealth(this.currentHealth);
        }
        this.currentHealth += amount; // Usually also heal by the amount increased
        console.log(`Player max health increased by ${amount}. Current health: ${this.currentHealth}/${this.maxHealth}`);
        // Trail length will now scale based on the new max health
    }
    // ----------------------

    // --- Projectile Stat Upgrade Methods (Example) ---
    increaseProjectileDamage(amount) {
        this.projectileDamage += amount;
        console.log(`Projectile Damage increased to ${this.projectileDamage}`);
        // Add potential caps or diminishing returns later
    }

    increaseProjectileRange(amount) {
        this.projectileRange += amount;
        console.log(`Projectile Range increased to ${this.projectileRange.toFixed(1)}`);
    }

    increaseProjectileSize(amount) {
        // Add a max size maybe?
        const maxSize = 0.8;
        this.projectileSize = Math.min(maxSize, this.projectileSize + amount);
        console.log(`Projectile Size increased to ${this.projectileSize.toFixed(2)}`);
    }

    /**
     * Teleport the player to the boss
     * Creates a visual effect and moves the player to the boss's position
     */
    teleportToBoss() {
        console.log("[PlayerController] teleportToBoss method called");
        if (!this.dungeonHandler) {
            console.error("[PlayerController] Cannot teleport: dungeonHandler is null");
            return;
        }

        console.log("[PlayerController] DungeonHandler methods:", Object.keys(this.dungeonHandler));
        console.log("[PlayerController] findBossRoom exists:", typeof this.dungeonHandler.findBossRoom === 'function');
        console.log("[PlayerController] teleportToRoom exists:", typeof this.dungeonHandler.teleportToRoom === 'function');
        console.log("[PlayerController] currentRoom exists:", !!this.dungeonHandler.currentRoom);

        // Find the current room and check if it has a boss
        const currentRoom = this.dungeonHandler.currentRoom;
        console.log("[PlayerController] Current room:", currentRoom ? 'exists' : 'null');

        if (!currentRoom || !currentRoom.boss) {
            console.log("[PlayerController] Current room has no boss, searching for boss room");
            // Try to find a room with a boss
            const bossRoom = this.dungeonHandler.findBossRoom();
            console.log("[PlayerController] findBossRoom result:", bossRoom ? 'found boss room' : 'no boss room found');

            if (bossRoom) {
                // If we found a boss room but we're not in it, teleport to that room first
                if (bossRoom !== currentRoom) {
                    console.log("[PlayerController] Teleporting to boss room...");
                    try {
                        this.dungeonHandler.teleportToRoom(bossRoom);
                        console.log("[PlayerController] teleportToRoom completed");

                        // Wait a short time for the room to load before teleporting to the boss
                        console.log("[PlayerController] Setting timeout for _teleportToBossPosition");
                        setTimeout(() => {
                            console.log("[PlayerController] Timeout triggered, calling _teleportToBossPosition");
                            this._teleportToBossPosition();
                        }, 100);
                        return;
                    } catch (error) {
                        console.error("[PlayerController] Error in teleportToRoom:", error);
                    }
                } else {
                    console.log("[PlayerController] Already in boss room");
                }
            } else {
                console.log("[PlayerController] No boss found in the dungeon");
                return;
            }
        } else {
            console.log("[PlayerController] Current room has a boss");
        }

        // If we're already in the boss room, teleport directly to the boss
        console.log("[PlayerController] Calling _teleportToBossPosition directly");
        this._teleportToBossPosition();
    }

    /**
     * Internal method to handle the actual teleportation to the boss position
     * @private
     */
    _teleportToBossPosition() {
        console.log("[PlayerController] _teleportToBossPosition method called");

        // Find active bosses in the current room
        console.log("[PlayerController] DungeonHandler exists:", !!this.dungeonHandler);
        if (this.dungeonHandler) {
            console.log("[PlayerController] activeBosses exists:", !!this.dungeonHandler.activeBosses);
            if (this.dungeonHandler.activeBosses) {
                console.log("[PlayerController] activeBosses length:", this.dungeonHandler.activeBosses.length);
            }
        }

        if (!this.dungeonHandler || !this.dungeonHandler.activeBosses || this.dungeonHandler.activeBosses.length === 0) {
            console.log("[PlayerController] No active bosses found in the dungeon");

            // Try to find any enemies in the room that might be bosses
            if (this.dungeonHandler && this.dungeonHandler.activeEnemies && this.dungeonHandler.activeEnemies.length > 0) {
                console.log("[PlayerController] Found active enemies:", this.dungeonHandler.activeEnemies.length);

                // Look for enemies that might be bosses based on their userData
                const possibleBoss = this.dungeonHandler.activeEnemies.find(enemy => {
                    if (!enemy || !enemy.userData) return false;
                    return enemy.userData.aiType === 'boss' ||
                           enemy.userData.aiType === 'catacombs_overlord' ||
                           (enemy.userData.aiType && enemy.userData.aiType.includes('boss'));
                });

                if (possibleBoss) {
                    console.log("[PlayerController] Found a possible boss enemy");

                    // Get the boss position
                    const bossPosition = possibleBoss.position.clone();

                    // Add a small offset so we don't teleport directly on top of the boss
                    bossPosition.x -= 2; // Move slightly to the left of the boss

                    console.log(`[PlayerController] Boss found at position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);

                    // Create a teleport effect at the current position
                    this._createTeleportEffect(this.playerMesh.position.clone());

                    // Move the player to the boss position
                    this.playerMesh.position.copy(bossPosition);

                    // Create a teleport effect at the new position
                    this._createTeleportEffect(bossPosition);

                    console.log(`[PlayerController] Teleported to boss at position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);
                    return;
                }
            }

            // If we still can't find a boss, just teleport to the center of the room
            if (this.dungeonHandler && this.dungeonHandler.currentRoom) {
                console.log("[PlayerController] Teleporting to center of room instead");
                const roomCenter = new THREE.Vector3(0, 0, 0);

                // Create a teleport effect at the current position
                this._createTeleportEffect(this.playerMesh.position.clone());

                // Move the player to the room center
                this.playerMesh.position.copy(roomCenter);

                // Create a teleport effect at the new position
                this._createTeleportEffect(roomCenter);

                console.log(`[PlayerController] Teleported to room center at position: ${roomCenter.x.toFixed(2)}, ${roomCenter.y.toFixed(2)}, ${roomCenter.z.toFixed(2)}`);
                return;
            }

            return;
        }

        // Get the first active boss position
        const boss = this.dungeonHandler.activeBosses[0];
        if (!boss) {
            console.log("[PlayerController] Boss reference is invalid");
            return;
        }

        console.log("[PlayerController] Boss object:", boss);
        console.log("[PlayerController] Boss position exists:", !!boss.position);

        // Get the boss position
        const bossPosition = boss.position.clone();

        // Add a small offset so we don't teleport directly on top of the boss
        bossPosition.x -= 2; // Move slightly to the left of the boss

        console.log(`[PlayerController] Boss found at position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);

        // Create a teleport effect at the current position
        this._createTeleportEffect(this.playerMesh.position.clone());

        // Move the player to the boss position
        this.playerMesh.position.copy(bossPosition);

        // Create a teleport effect at the new position
        this._createTeleportEffect(bossPosition);

        console.log(`[PlayerController] Teleported to boss at position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);
    }

    /**
     * Create a visual effect for teleportation
     * @param {THREE.Vector3} position - Position to create the effect
     * @private
     */
    _createTeleportEffect(position) {
        // Use the dungeonHandler's particle effect system if available
        if (this.dungeonHandler && this.dungeonHandler.createParticleEffect) {
            // Create a teleport effect using the particle system
            this.dungeonHandler.createParticleEffect(
                'teleport_flash', // Effect type
                position,          // Position
                1.0,               // Scale
                30                 // Particle count
            );
            return;
        }

        // Fallback if particle system is not available
        console.log(`[PlayerController] Created teleport effect at ${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)}`);

        // Create a simple light flash if we can't create particles
        if (this.scene) {
            const flashLight = new THREE.PointLight(0x00ffff, 2.0, 5.0);
            flashLight.position.copy(position);
            this.scene.add(flashLight);

            // Remove the light after a short time
            setTimeout(() => {
                if (flashLight && this.scene) {
                    this.scene.remove(flashLight);
                }
            }, 300);
        }
    }
    // -------------------------------------------------

    updateCollisionObjects(newCollisionObjects) {
        this.collisionObjects = newCollisionObjects;
    }

    // --- Internal: Create Rotation Helpers ---
    _createRotationHelpers() {
        console.log("[PlayerController] _createRotationHelpers called.");
        this.clearRotationHelpers(); // Clear existing before creating new
        this.rotationHelpersGroup = new THREE.Group();
        this.rotationHelpersGroup.name = "PlayerRotationHelpers";

        // <<< Target GROUP names, not bone names >>>
        const groupNamesToMatch = ["Body", "leftArm", "rightArm", "leftLeg", "rightLeg"]; // Added Body
        const foundGroupNames = []; // To log names we actually find
        const allObjectInfo = [];

        console.log("[PlayerController] Traversing player mesh for TARGET GROUPS...");
        this.playerMesh.traverse((object) => {
             const info = `Name: '${object.name}', Type: ${object.constructor.name}`;
             allObjectInfo.push(info);

            if (object.isGroup && groupNamesToMatch.includes(object.name)) {
                 console.log(`    -> Found Target Group: '${object.name}'`);

                 // <<< FIND FIRST MESH INSIDE THE GROUP >>>
                 let targetMesh = null;
                 object.traverse((child) => {
                     if (!targetMesh && child.isMesh) { // Find the first Mesh
                         targetMesh = child;
                     }
                 });

                 if (targetMesh) {
                     console.log(`        -> Attaching helper to first Mesh inside Group: '${targetMesh.name || '(unnamed)'}'`);
                     // <<< Create helper attached to the MESH, not the group >>>
                     const helper = new THREE.BoxHelper(targetMesh, 0x00ffff); // Cyan
                     helper.material.linewidth = 2;
                     helper.matrixAutoUpdate = false;
                     this.rotationHelpersGroup.add(helper);
                     // <<< Store helper keyed by the PARENT GROUP's name for the update loop >>>
                     this.boneHelpers[object.name] = helper;
                     foundGroupNames.push(object.name); // Log the group name we matched
                 } else {
                      console.warn(`        -> No Mesh found inside Group: '${object.name}'. Cannot create helper.`);
                  }
                 // <<< END FIND FIRST MESH >>>
            }
        });

        console.log("[PlayerController] Finished traversing. All Objects found count:", allObjectInfo.length);
        console.log("[PlayerController] Target Groups found by name:", foundGroupNames);
        console.log("[PlayerController] Created bone helpers for matched groups:", Object.keys(this.boneHelpers));
        this.scene.add(this.rotationHelpersGroup);
        this._updateRotationHelpersVisibility(); // Set initial visibility
    }

    // --- Internal: Update Rotation Helper Transforms ---
    _updateRotationHelpers() {
        // No update needed if group isn't visible
        if (!this.rotationHelpersGroup || !this.rotationHelpersGroup.visible) return;

        // <<< RE-ADD Temporary variables for position/quaternion copy >>>
        const worldPosition = new THREE.Vector3();
        const worldQuaternion = new THREE.Quaternion();

        // Iterate through helpers keyed by GROUP name now
        for (const groupName in this.boneHelpers) {
            const helper = this.boneHelpers[groupName];
            // <<< Get the GROUP first to find the target MESH >>>
            const group = this.playerMesh.getObjectByName(groupName);
            let targetMesh = null;
            if (group) {
                 group.traverse((child) => {
                     if (!targetMesh && child.isMesh) { targetMesh = child; }
                 });
            }

            // <<< Check if target MESH and helper exist >>>
            if (targetMesh && helper && helper.isBoxHelper) {
                 // <<< Apply position/quaternion from the MESH >>>
                 targetMesh.updateWorldMatrix(true, false); // Update mesh's world matrix

                 targetMesh.getWorldPosition(worldPosition);
                 targetMesh.getWorldQuaternion(worldQuaternion);

                 helper.position.copy(worldPosition);
                 helper.quaternion.copy(worldQuaternion);
            } else {
                 // console.warn(`[PlayerController] Target Mesh for group '${groupName}' not found or helper invalid.`);
            }
        }
    }

    _updateAnimation(deltaTime) {
    }

    // <<< ADD Missing clearRotationHelpers Function >>>
    clearRotationHelpers() {
        console.log("[PlayerController] Clearing existing rotation helpers...");
        if (this.rotationHelpersGroup) {
            // Dispose and remove each helper individually
            Object.values(this.boneHelpers).forEach(helper => {
                if (helper) {
                    if (helper.geometry) helper.geometry.dispose();
                    if (helper.material) helper.material.dispose();
                    this.rotationHelpersGroup.remove(helper);
                }
            });
            // Remove the main group from the scene
            this.scene.remove(this.rotationHelpersGroup);
            console.log("[PlayerController] Removed rotation helpers group from scene.");
        } else {
            console.log("[PlayerController] No rotation helpers group found to clear.");
        }
        // Reset the state variables
        this.rotationHelpersGroup = null;
        this.boneHelpers = {};
    }
    // <<< END Added Function >>>

    // <<< RE-ADD Missing _updateRotationHelpersVisibility Function >>>
    _updateRotationHelpersVisibility() {
        if (this.rotationHelpersGroup) {
            this.rotationHelpersGroup.visible = this.isEspRotationViewActive;
             console.log(`[PlayerController] Setting rotation helpers group visibility to: ${this.rotationHelpersGroup.visible}`);
        } else {
             console.log("[PlayerController] Rotation helpers group not found, cannot set visibility.");
        }
    }
    // <<< END Re-added Function >>>

    /**
     * Add souls to the player
     * @param {number} amount - The amount of souls to add
     */
    addSouls(amount) {
        this.souls += amount;
        console.log(`Player now has ${this.souls} souls`);

        // Update UI if needed
        if (this.onSoulsChanged) {
            this.onSoulsChanged(this.souls);
        }
    }

    /**
     * Set a callback for when souls change
     * @param {function} callback - Function to call when souls change
     */
    setSoulsChangedCallback(callback) {
        this.onSoulsChanged = callback;
    }
}

export default PlayerController;