/**
 * Defines the properties and content for different game areas (themes/biomes).
 * This structure will be used by the dungeon generator to determine:
 * - Wall/Floor prefabs
 * - Enemy types and density
 * - Interior objects (decorations, interactables)
 * - Item drops
 * - Overall visual theme (lighting, fog, etc. - potentially)
 * - Allowed room layout shapes
 */

export const Areas = {
    // --- Starting Area ---
    'catacombs': {
        name: 'The Catacombs',
        type: 'start', // Indicates this is the fixed starting area
        walls: ['stonebrick'], // Prefab generator key(s) for walls
        floors: ['cave_floor'], // Prefab generator key(s) for floors (Placeholder)
        doors: ['stone_archway'],
        allowedShapes: [
            'SQUARE_1X1',
            'RECTANGULAR',
            'L_SHAPE',
            'T_SHAPE',
            'CROSS_SHAPE',
            'RECT_2X1',
            'RECT_1X2',
            'RECT_3X1',
            'RECT_1X3',
            'U_SHAPE_DOWN'
        ],
        enemies: [
            { type: 'skeleton_archer', weight: 10 }, // Skeleton archer enemy
            { type: 'bat', weight: 5 },             // Bat enemy
            { type: 'zombie', weight: 8 },           // Zombie enemy
            { type: 'magma_golem', weight: 6 }      // Magma golem enemy (moderate weight)
        ],
        bossRoom: 'catacombs_boss_room', // Reference to the boss room configuration
        interiorObjects: [
            { type: 'vine', probability: 0.9, minQuantity: 3, maxQuantity: 5, placement: 'wall' },
            { type: 'torch', probability: 1.0, minQuantity: 5, maxQuantity: 15, placement: 'wall' },
            {
                type: 'stone_vase',
                probability: 0.20, // Changed from 1.0 (debug) to 0.20 (20%)
                minQuantity: 4,    // If they appear, place at least 4
                maxQuantity: 8,    // Up to 8
                placement: 'floor',
                placementDetail: ['random', 'corners'], // Specify allowed placement zones
                isDestructible: true, // Mark as destructible
                destructionEffect: 'collapse', // Like the skeleton
                health: 1              // Give it 1 health
            },
            {
                type: 'stone_pillar',
                probability: 0.25, // 25% chance for pillars to appear
                minQuantity: 4,    // Always spawn 4 if probability met
                maxQuantity: 4,
                placement: 'floor',
                placementDetail: ['corners'], // Specifically place in corners
                isDestructible: true,
                destructionEffect: 'collapse', // Same as vase
                health: 1
            },
            {
                type: 'stone_rubble',
                probability: 0.20, // 20% chance for rubble
                minQuantity: 2,
                maxQuantity: 6,
                placement: 'floor',
                placementDetail: ['random'], // Random placement
                isDestructible: true,
                destructionEffect: 'collapse', // Same as vase
                health: 1
            }
            // Add more catacomb-specific objects like rubble, bones, coffins etc. later
        ],
        items: [
            // Define potential item drops specific to this area later
        ],
        lighting: {
            // Define lighting properties if needed (e.g., ambient color, intensity)
        },
        // Add other area-specific properties like fog, background music key, etc.
    },

    // Add the boss room configuration before the random pool
    'catacombs_boss_room': {
        name: 'Catacombs Overlord Chamber',
        type: 'boss',
        walls: ['stonebrick'], // Match catacombs theme
        floors: ['cave_floor'],
        doors: ['stone_archway'],
        allowedShapes: ['BOSS_ARENA'], // Use our custom boss arena shape
        enemies: [
            { type: 'catacombs_overlord', weight: 1 } // The boss enemy type
        ],
        interiorObjects: [
            { type: 'torch', probability: 1.0, minQuantity: 8, maxQuantity: 12, placement: 'wall' }, // More torches for dramatic lighting
            { type: 'stone_pillar', probability: 1.0, minQuantity: 4, maxQuantity: 4, placement: 'floor', placementDetail: ['corners'] }, // Always have corner pillars
            { type: 'ritual_circle', probability: 1.0, minQuantity: 1, maxQuantity: 1, placement: 'floor', placementDetail: ['center'] } // Central ritual circle
        ],
        lighting: {
            ambient: 0.3,
            intensity: 1.2
        }
    },

    // --- Pool of Random Areas (Player cycles through 5 of these after Catacombs) ---
    'random_pool': [
        'fungal_caverns',
        'flooded_ruins',
        'crystal_caves',
        'ancient_library',
        'lava_tubes',
        // Add more distinct area IDs to this pool
    ],

    'fungal_caverns': {
        name: 'Fungal Caverns',
        type: 'random',
        walls: ['rough_stone', 'glowing_mushroom_wall'], // Example
        floors: ['mossy_earth', 'mushroom_patch'],     // Example
        doors: ['mossy_archway', 'stone_archway'],
        allowedShapes: ['SQUARE_1X1', 'L_SHAPE', 'T_SHAPE', 'U_SHAPE_DOWN'], // More organic/complex shapes?
        enemies: [/* Define fungal enemies */],
        interiorObjects: [
            { type: 'giant_mushroom', probability: 0.6, minQuantity: 2, maxQuantity: 4, placement: 'floor' },
            { type: 'spore_cloud', probability: 0.3, minQuantity: 1, maxQuantity: 2, placement: 'air' },
        ],
        items: [/* Fungal-themed items */],
        // ... other properties
    },
    'flooded_ruins': {
        name: 'Flooded Ruins',
        type: 'random',
        walls: ['cracked_stone', 'waterlogged_brick'],
        floors: ['shallow_water', 'stone_debris'],
        doors: ['stone_archway', 'cracked_archway'],
        allowedShapes: ['RECTANGULAR', 'SQUARE_2X2', 'L_SHAPE', 'RECT_3X1', 'RECT_1X3'], // Potentially larger, broken shapes
        enemies: [/* Amphibious enemies */],
        interiorObjects: [
            { type: 'broken_pillar', probability: 0.4, minQuantity: 1, maxQuantity: 3, placement: 'floor' },
            { type: 'underwater_chest', probability: 0.1, minQuantity: 1, maxQuantity: 1, placement: 'floor' },
        ],
        items: [],
        // ... other properties
    },
    // ... Define other areas in the random_pool ('crystal_caves', etc.) ...
    'crystal_caves': {
        name: 'Crystal Caves',
        type: 'random',
        walls: ['crystal_wall', 'rough_stone'],
        floors: ['crystal_floor', 'geode_floor'],
        doors: ['crystal_archway'],
        allowedShapes: ['CROSS_SHAPE', 'SQUARE_1X1', 'L_SHAPE', 'RECT_1X2', 'SQUARE_2X2'], // More complex, maybe jagged
        enemies: [/* Crystal elementals */],
        interiorObjects: [/* Large crystals, glowing fissures */],
        items: [],
    },
    'ancient_library': {
        name: 'Ancient Library',
        type: 'random',
        walls: ['bookshelf_wall', 'ornate_stone'],
        floors: ['wood_parquet', 'tiled_floor'],
        doors: ['ornate_archway', 'wooden_doorframe'],
        allowedShapes: ['RECTANGULAR', 'RECT_2X1', 'RECT_3X1', 'T_SHAPE', 'SQUARE_2X2'], // More structured shapes
        enemies: [/* Animated books, spectral librarians */],
        interiorObjects: [/* Lecterns, desks, scrolls */],
        items: [],
    },
     'lava_tubes': {
        name: 'Lava Tubes',
        type: 'random',
        walls: ['basalt_wall', 'cooling_lava'],
        floors: ['obsidian_floor', 'lava_river_edge'], // Careful with placement!
        doors: ['obsidian_archway'],
        allowedShapes: ['L_SHAPE', 'U_SHAPE_DOWN', 'RECT_1X3', 'RECT_3X1', 'T_SHAPE'], // Tunnel-like
        enemies: [/* Fire elementals, magma beasts */],
        interiorObjects: [/* Hardened lava formations, heat vents */],
        items: [],
    },


    // --- Hardcoded Ending Areas (Sequence after the 5 random areas) ---
    'end_sequence': [
        'obsidian_fortress',
        'astral_plane',
        'final_boss_arena'
    ],

    'obsidian_fortress': {
        name: 'Obsidian Fortress',
        type: 'end',
        walls: ['obsidian_wall', 'fortress_brick'],
        floors: ['polished_obsidian'],
        doors: ['obsidian_archway', 'fortress_gate'],
        allowedShapes: ['SQUARE_2X2', 'RECT_3X2', 'RECT_2X1', 'RECTANGULAR'], // Large, imposing rooms
        enemies: [/* Tougher guards */],
        interiorObjects: [/* Banners, weapon racks */],
        items: [],
        // ... other properties
    },
    'astral_plane': {
        name: 'Astral Plane',
        type: 'end',
        walls: ['void_wall', 'starfield_backdrop'],
        floors: ['crystal_platform'],
        doors: ['void_portal'],
        allowedShapes: ['CROSS_SHAPE', 'SQUARE_1X1', 'RECT_1X2'], // Fragmented, possibly non-rectangular feel?
        enemies: [/* Ethereal enemies */],
        interiorObjects: [/* Floating crystals */],
        items: [],
        // ... other properties
    },
    'final_boss_arena': {
        name: '???',
        type: 'end_boss',
        // Using our new custom boss arena shape
        walls: ['boss_arena_wall'],
        floors: ['boss_arena_floor'],
        doors: ['boss_arena_gate'],
        allowedShapes: ['BOSS_ARENA'], // Changed from SQUARE_2X2 to our new BOSS_ARENA shape
        enemies: [{ type: 'FINAL_BOSS', weight: 1 }],
        interiorObjects: [],
        items: [],
        // ... other properties
    },
};

// Helper function to get a random area ID from the pool
export function getRandomAreaId(rng = Math.random) {
    const pool = Areas['random_pool'];
    if (!pool || pool.length === 0) {
        console.error("Random area pool is empty!");
        return 'catacombs'; // Fallback
    }
    const index = Math.floor(rng() * pool.length);
    return pool[index];
}

// Helper function to get the data for a specific area ID
export function getAreaData(areaId) {
    return Areas[areaId] || null;
}

// Helper function to get the sequence of ending area IDs
export function getEndingSequence() {
    return Areas['end_sequence'] || [];
}