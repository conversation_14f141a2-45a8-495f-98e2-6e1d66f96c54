/**
 * MusicDebugHUD.js
 *
 * Creates a debug overlay for the adaptive music system.
 * Displays current music state, transitions, and effect values.
 */

/**
 * Create a debug HUD for the music system
 * @param {MusicConductor} conductor - The MusicConductor instance
 * @returns {Object} Debug HUD interface
 */
export function createMusicDebugHUD(conductor) {
    // Create container
    const container = document.createElement('div');
    container.id = 'music-debug-hud';
    container.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        background-color: rgba(0, 0, 0, 0.7);
        color: #fff;
        font-family: monospace;
        font-size: 12px;
        padding: 10px;
        border-radius: 5px;
        z-index: 9999;
        max-width: 300px;
        display: none;
    `;

    // Create content elements
    const title = document.createElement('div');
    title.textContent = 'MUSIC SYSTEM DEBUG';
    title.style.fontWeight = 'bold';
    title.style.marginBottom = '5px';
    title.style.borderBottom = '1px solid #555';
    container.appendChild(title);

    // Current area
    const areaInfo = document.createElement('div');
    areaInfo.id = 'music-debug-area';
    container.appendChild(areaInfo);

    // Current loop
    const loopInfo = document.createElement('div');
    loopInfo.id = 'music-debug-loop';
    loopInfo.style.color = '#00ff00'; // Highlight the loop info in green
    container.appendChild(loopInfo);

    // Current tempo
    const tempoInfo = document.createElement('div');
    tempoInfo.id = 'music-debug-tempo';
    container.appendChild(tempoInfo);

    // Buffer time
    const bufferInfo = document.createElement('div');
    bufferInfo.id = 'music-debug-buffer';
    bufferInfo.style.color = '#ffcc00'; // Highlight the buffer info in yellow
    container.appendChild(bufferInfo);

    // Active leitmotifs
    const leitmotifInfo = document.createElement('div');
    leitmotifInfo.id = 'music-debug-leitmotifs';
    container.appendChild(leitmotifInfo);

    // Combat state
    const combatInfo = document.createElement('div');
    combatInfo.id = 'music-debug-combat';
    container.appendChild(combatInfo);

    // Health state
    const healthInfo = document.createElement('div');
    healthInfo.id = 'music-debug-health';
    container.appendChild(healthInfo);

    // Transition state
    const transitionInfo = document.createElement('div');
    transitionInfo.id = 'music-debug-transition';
    container.appendChild(transitionInfo);

    // Waveform display
    const waveformContainer = document.createElement('div');
    waveformContainer.style.cssText = `
        width: 100%;
        height: 40px;
        background-color: #222;
        margin-top: 10px;
        position: relative;
        overflow: hidden;
    `;

    const waveform = document.createElement('canvas');
    waveform.width = 280;
    waveform.height = 40;
    waveform.style.cssText = `
        width: 100%;
        height: 100%;
    `;
    waveformContainer.appendChild(waveform);

    // Bar indicator
    const barIndicator = document.createElement('div');
    barIndicator.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 2px;
        height: 100%;
        background-color: #f00;
    `;
    waveformContainer.appendChild(barIndicator);

    container.appendChild(waveformContainer);

    // Add to document
    document.body.appendChild(container);

    // Animation frame for waveform
    let animationFrame = null;
    const ctx = waveform.getContext('2d');

    // Update functions
    function updateArea() {
        const area = conductor.currentArea || 'none';
        const room = conductor.currentSpecialRoom ? ` (${conductor.currentSpecialRoom})` : '';
        areaInfo.textContent = `Area: ${area}${room}`;
    }

    function updateTempo() {
        const bpm = conductor.currentTempo || 0;
        const stretch = conductor.currentTempoStretch || 1.0;
        tempoInfo.textContent = `Tempo: ${bpm} BPM (${stretch.toFixed(2)}x)`;
    }

    function updateLeitmotifs() {
        const active = Array.from(conductor.activeLeitmotifs || []).join(', ') || 'none';
        leitmotifInfo.textContent = `Leitmotifs: ${active}`;
    }

    function updateCombat(enemyCount, tempoStretch, inCombat) {
        const pressureLevel = conductor.pressureLevel || 0;
        combatInfo.textContent = `Combat: ${inCombat ? 'YES' : 'NO'} (${enemyCount} enemies, ${tempoStretch.toFixed(2)}x, pressure: ${pressureLevel.toFixed(1)})`;
    }

    function updateHealth(ratio, frequency) {
        healthInfo.textContent = `Health: ${(ratio * 100).toFixed(0)}% (Filter: ${Math.round(frequency)}Hz)`;
    }

    function updateLoop(index, loopPath, duration) {
        // Extract just the filename from the path
        const filename = loopPath ? loopPath.split('/').pop() : 'none';
        loopInfo.textContent = `Loop: ${index + 1}/${conductor.loopChain.length} - ${filename} (${duration.toFixed(2)}s)`;
    }

    function updateBuffer(bufferTime) {
        if (bufferTime > 0) {
            // Format buffer time with color coding based on duration
            const formattedTime = `${bufferTime}ms`;

            // Change color based on buffer size
            if (bufferTime > 2000) {
                bufferInfo.style.color = '#ff6600'; // Orange for large buffers
            } else if (bufferTime > 1000) {
                bufferInfo.style.color = '#ffcc00'; // Yellow for medium buffers
            } else {
                bufferInfo.style.color = '#00ccff'; // Light blue for small buffers
            }

            // Check if there's a scheduled loop with remaining time
            let additionalInfo = '';
            if (conductor.nextLoopScheduled && conductor.nextLoopScheduledTime > 0) {
                const now = Date.now();
                const remainingMs = Math.max(0, conductor.nextLoopScheduledTime - now);
                if (remainingMs > 0) {
                    // Format remaining time more compactly
                    const remainingSec = (remainingMs / 1000).toFixed(1);
                    additionalInfo = ` +${remainingSec}s`;
                }
            }

            bufferInfo.textContent = `Buffer: ${formattedTime}${additionalInfo}`;
        } else {
            bufferInfo.style.color = '#aaaaaa'; // Gray when no buffer

            // Check if there's a scheduled loop with remaining time
            let waitInfo = '';
            if (conductor.nextLoopScheduled && conductor.nextLoopScheduledTime > 0) {
                const now = Date.now();
                const remainingMs = Math.max(0, conductor.nextLoopScheduledTime - now);
                if (remainingMs > 0) {
                    // Format remaining time more compactly
                    const remainingSec = (remainingMs / 1000).toFixed(1);
                    waitInfo = ` (${remainingSec}s)`;
                }
            }

            bufferInfo.textContent = `Buffer: NONE${waitInfo}`;
        }
    }

    function updateTransition() {
        transitionInfo.textContent = `Transition: ${conductor.isTransitioning ? 'IN PROGRESS' : 'NONE'}`;
    }

    function updateWaveform() {
        if (!conductor.mainLoopPlayer || !conductor.mainLoopPlayer.buffer) {
            return;
        }

        // Clear canvas
        ctx.fillStyle = '#222';
        ctx.fillRect(0, 0, waveform.width, waveform.height);

        // Draw waveform (simplified)
        ctx.strokeStyle = '#0f0';
        ctx.lineWidth = 1;
        ctx.beginPath();

        // Fake waveform for now (would normally use actual audio data)
        const time = Date.now() / 1000;
        for (let x = 0; x < waveform.width; x++) {
            const t = x / waveform.width * 10 + time;
            const y = Math.sin(t * 5) * 10 + Math.sin(t * 3) * 5;
            const yPos = waveform.height / 2 + y;

            if (x === 0) {
                ctx.moveTo(x, yPos);
            } else {
                ctx.lineTo(x, yPos);
            }
        }

        ctx.stroke();

        // Update bar indicator position (based on current time)
        const barPos = (time % 2) / 2 * waveform.width;
        barIndicator.style.left = `${barPos}px`;
    }

    // Update all info
    function updateAll() {
        updateArea();
        updateTempo();
        updateLeitmotifs();
        updateCombat(conductor.enemyCount, conductor.currentTempoStretch, conductor.inCombat);
        updateHealth(conductor.playerHealth, conductor.effects?.lowpass?.frequency?.value || 20000);

        // Always update buffer time from the conductor
        const bufferTime = conductor.currentBufferTime || 0;
        updateBuffer(bufferTime);

        // Log buffer time for debugging
        if (bufferTime > 0) {
            console.log(`[MusicDebugHUD] Displaying buffer time: ${bufferTime}ms`);
        } else {
            // Check if tempo is changing but buffer isn't being set
            if (conductor.needsTransitionBuffer && bufferTime === 0) {
                console.warn(`[MusicDebugHUD] Warning: Transition buffer flag is set but buffer time is 0`);
            }
        }
        updateTransition();

        // Update current loop info
        if (conductor.currentLoopIndex >= 0 && conductor.loopChain && conductor.loopChain.length > 0) {
            const loopPath = conductor.loopChain[conductor.currentLoopIndex];
            const baseDuration = conductor.currentLoopPlayer?.buffer?.duration || 0;
            const adjustedDuration = baseDuration / conductor.currentTempoStretch;
            updateLoop(conductor.currentLoopIndex, loopPath, adjustedDuration);
        } else {
            updateLoop(-1, null, 0);
        }

        updateWaveform();

        // Schedule next update
        animationFrame = requestAnimationFrame(updateAll);
    }

    // Start animation
    function start() {
        if (animationFrame === null) {
            animationFrame = requestAnimationFrame(updateAll);
        }
    }

    // Stop animation
    function stop() {
        if (animationFrame !== null) {
            cancelAnimationFrame(animationFrame);
            animationFrame = null;
        }
    }

    // Toggle visibility
    function toggle(visible) {
        container.style.display = visible ? 'block' : 'none';

        if (visible) {
            start();
        } else {
            stop();
        }
    }

    // Clean up
    function dispose() {
        stop();
        if (container.parentNode) {
            container.parentNode.removeChild(container);
        }
    }

    // Return interface
    return {
        updateArea,
        updateTempo,
        updateLeitmotifs,
        updateCombat,
        updateHealth,
        updateBuffer,
        updateLoop,
        updateTransition,
        toggle,
        dispose
    };
}
