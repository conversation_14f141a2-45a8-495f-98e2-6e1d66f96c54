/**
 * Music Data Configuration
 *
 * This file defines all music assets and their metadata for the adaptive music system.
 * It follows a modular Loop Chain structure with Hit Interrupts, Temporal Insertions, and Emotive Cues.
 */

export const MusicData = {
    // --- Starting Area ---
    "catacombs": {
        // Loop Chain - 5 modular 4-bar loops that cycle in sequence
        loopChain: [
            "assets/music/catacombs/loop_a.mp3",
            "assets/music/catacombs/loop_b.mp3",
            "assets/music/catacombs/loop_c.mp3",
            "assets/music/catacombs/loop_d.mp3",
            "assets/music/catacombs/loop_e.mp3"
        ],
        tempo: 100, // BPM
        barLength: 4, // Number of beats per bar

        // Interrupts - Played when specific events occur
        interrupts: {
            // Hit interrupt - Played when player takes damage
            "hit": {
                path: "assets/music/catacombs/interrupts/hit_interrupt.mp3",
                priority: 2 // Higher priority interrupts can override lower ones
            },
            // Other interrupts
            "danger": {
                path: "assets/music/catacombs/interrupts/danger_interrupt.mp3",
                priority: 1
            }
        },

        // Temporal Insertions - One-off loops for narrative/emotional moments
        temporalInsertions: {
            // Narrative insertions
            "memory_recall": {
                path: "assets/music/catacombs/insertions/memory_recall.mp3",
                type: "narrative",
                priority: 3
            },
            "dialogue": {
                path: "assets/music/catacombs/insertions/dialogue.mp3",
                type: "narrative",
                priority: 3
            },
            // Emotive insertions
            "fear": {
                path: "assets/music/catacombs/insertions/fear.mp3",
                type: "emotive",
                priority: 2
            }
        },

        // Legacy support for leitmotifs
        leitmotifs: {
            "mother": "assets/music/catacombs/leitmotifs/mother_layer.mp3",
            "guilt": "assets/music/catacombs/leitmotifs/guilt_layer.mp3"
        },

        // Special rooms
        specialRooms: {
            "shop": {
                loopChain: [
                    "assets/music/catacombs/specialRooms/shop_a.mp3",
                    "assets/music/catacombs/specialRooms/shop_b.mp3"
                ],
                // No transitions available yet
                enterTransition: null,
                exitTransition: null
            },
            "event": {
                loopChain: [
                    "assets/music/catacombs/specialRooms/event_a.mp3",
                    "assets/music/catacombs/specialRooms/event_b.mp3"
                ],
                // No transitions available yet
                enterTransition: null,
                exitTransition: null
            }
        },

        // Area transitions
        transitions: {
            "lava_tubes": "assets/music/catacombs/placeholder.mp3"
        }
    },

    // --- Other Areas (to be implemented) ---
    "lava_tubes": {
        // For now, just use the catacombs loops as placeholders
        loopChain: [
            "assets/music/catacombs/loop_a.mp3",
            "assets/music/catacombs/loop_b.mp3",
            "assets/music/catacombs/loop_c.mp3",
            "assets/music/catacombs/loop_d.mp3",
            "assets/music/catacombs/loop_e.mp3"
        ],
        tempo: 110, // Faster tempo for more intense area
        barLength: 4,
        interrupts: {
            "hit": {
                path: "assets/music/catacombs/interrupts/hit_interrupt.mp3", // Use catacombs as placeholder
                priority: 2
            }
        },
        temporalInsertions: {},
        leitmotifs: {},
        specialRooms: {},
        transitions: {
            "catacombs": "assets/music/catacombs/placeholder.mp3"
        }
    },

    // --- Stingers & One-Shots ---
    "stingers": {
        // Use placeholder file for stingers
        "player_hit": "assets/music/catacombs/placeholder.mp3",
        "player_death": "assets/music/catacombs/placeholder.mp3",
        "enemy_death": "assets/music/catacombs/placeholder.mp3",
        "boss_appear": "assets/music/catacombs/placeholder.mp3"
    },

    // --- Default Fallbacks ---
    "defaults": {
        crossfadeDuration: 2, // seconds
        barTransitionWaitTime: 2, // seconds (maximum time to wait for a bar to complete)
        defaultTempo: 95, // BPM
        defaultBarLength: 4 // Number of beats per bar
    }
};

/**
 * Placeholder function to check if music assets exist
 * This helps prevent runtime errors when music files aren't available yet
 */
export function validateMusicAssets() {
    // This would normally check if files exist, but for now we'll just log
    console.log("[MusicSystem] Validating music assets (placeholder)");

    // In a real implementation, this would:
    // 1. Check if required files exist
    // 2. Create a list of missing files
    // 3. Return fallback paths for missing files

    return {
        valid: false,
        missingFiles: ["All music files are currently placeholders"],
        message: "Music system is using placeholder paths. Add actual music files to enable the full system."
    };
}

/**
 * Get special room data if it exists for the given area and room type
 */
export function getSpecialRoomData(areaId, roomType) {
    if (!MusicData[areaId] || !MusicData[areaId].specialRooms || !MusicData[areaId].specialRooms[roomType]) {
        return null;
    }
    return MusicData[areaId].specialRooms[roomType];
}

/**
 * Get transition music between two areas if it exists
 */
export function getTransitionMusic(fromAreaId, toAreaId) {
    if (!MusicData[fromAreaId] || !MusicData[fromAreaId].transitions || !MusicData[fromAreaId].transitions[toAreaId]) {
        return null;
    }
    return MusicData[fromAreaId].transitions[toAreaId];
}
