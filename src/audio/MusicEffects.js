/**
 * MusicEffects.js
 *
 * Creates and manages audio effects for the adaptive music system.
 * Provides effects chains for main music, leitmotifs, and stingers.
 */

// Note: Tone and SoundTouch are loaded globally from the CDN in index.html
// If SoundTouch is not available, we'll use fallbacks for tempo stretching

/**
 * Create all music effects and chains
 * @returns {Object} Object containing all effects and chains
 */
export function createMusicEffects() {
    let mainVolume, lowpass, distortion, reverb, tempoShifter;
    let mainChain, leitmotif<PERSON>hain, stingerChain;

    try {
        // Check if Tone is available
        if (typeof Tone === 'undefined') {
            throw new Error("Tone.js is not loaded");
        }

        // Create effects
        mainVolume = new Tone.Volume(0);
        lowpass = new Tone.Filter({
            type: "lowpass",
            frequency: 20000, // Start fully open
            rolloff: -24
        });
        distortion = new Tone.Distortion({
            distortion: 0.8,
            wet: 0 // Start with no distortion
        });
        reverb = new Tone.Reverb({
            decay: 1.5,
            wet: 0.2
        });
        tempoShifter = new Tone.PitchShift({
            pitch: 0, // No pitch shift
            windowSize: 0.1,
            delayTime: 0,
            feedback: 0
        });

        // Create effect chains
        mainChain = new Tone.Channel().chain(
            tempoShifter,
            lowpass,
            distortion,
            reverb,
            mainVolume,
            Tone.Destination
        );

        // Leitmotif chain (similar to main but with more reverb)
        leitmotifChain = new Tone.Channel().chain(
            lowpass,
            new Tone.Reverb({
                decay: 2,
                wet: 0.4
            }),
            mainVolume,
            Tone.Destination
        );

        // Stinger chain (bypasses some effects for more impact)
        stingerChain = new Tone.Channel().chain(
            new Tone.Volume(-5),
            Tone.Destination
        );

        console.log("[MusicEffects] Successfully created effects and chains");
    } catch (error) {
        console.error("[MusicEffects] Error creating effects:", error);

        // Create fallback effects
        mainVolume = { volume: { value: 0, rampTo: () => {} } };
        lowpass = { frequency: { value: 20000 } };
        distortion = { wet: { value: 0, rampTo: () => {} } };
        reverb = { wet: { value: 0.2, rampTo: () => {} } };
        tempoShifter = { playbackRate: { value: 1 } };

        // Create a dummy chain that does nothing
        const dummyChain = {
            chain: () => dummyChain,
            connect: () => {}
        };

        mainChain = dummyChain;
        leitmotifChain = dummyChain;
        stingerChain = dummyChain;

        console.log("[MusicEffects] Using fallback effects and chains");
    }

    // Return all effects and chains
    return {
        // Individual effects for direct manipulation
        mainVolume,
        lowpass,
        distortion,
        reverb,
        tempoShifter,

        // Effect chains for connecting players
        mainChain,
        leitmotifChain,
        stingerChain
    };
}

/**
 * Apply a preset to the effects chain
 * @param {Object} effects - Effects object from createMusicEffects()
 * @param {string} presetName - Name of the preset to apply
 */
export function applyEffectsPreset(effects, presetName) {
    try {
        const presets = {
            "default": {
                lowpassFreq: 20000,
                distortionWet: 0,
                reverbWet: 0.2
            },
            "underwater": {
                lowpassFreq: 1000,
                distortionWet: 0.1,
                reverbWet: 0.6
            },
            "nightmare": {
                lowpassFreq: 5000,
                distortionWet: 0.3,
                reverbWet: 0.7
            },
            "boss": {
                lowpassFreq: 20000,
                distortionWet: 0.1,
                reverbWet: 0.4
            }
        };

        const preset = presets[presetName] || presets.default;

        // Apply preset values safely
        if (effects.lowpass && effects.lowpass.frequency) {
            effects.lowpass.frequency.value = preset.lowpassFreq;
        }

        if (effects.distortion && effects.distortion.wet) {
            effects.distortion.wet.value = preset.distortionWet;
        }

        if (effects.reverb && effects.reverb.wet) {
            effects.reverb.wet.value = preset.reverbWet;
        }

        console.log(`[MusicEffects] Applied preset: ${presetName}`);
        return true;
    } catch (error) {
        console.error(`[MusicEffects] Error applying preset ${presetName}:`, error);
        return false;
    }
}

/**
 * Create a ducking effect (temporarily reduce volume when triggered)
 * @param {Tone.Volume} volumeNode - Volume node to duck
 * @param {number} amount - Amount to duck in dB
 * @param {number} attackTime - Attack time in seconds
 * @param {number} releaseTime - Release time in seconds
 * @returns {Function} Function to trigger ducking
 */
export function createDuckingEffect(volumeNode, amount = -10, attackTime = 0.1, releaseTime = 0.5) {
    try {
        // Check if volumeNode is valid
        if (!volumeNode || !volumeNode.volume || typeof volumeNode.volume.value === 'undefined') {
            console.warn("[MusicEffects] Invalid volume node for ducking effect");
            return function() {}; // Return empty function
        }

        const originalVolume = volumeNode.volume.value;

        return function trigger() {
            try {
                // Duck volume
                volumeNode.volume.rampTo(originalVolume + amount, attackTime);

                // Release after a short time
                setTimeout(() => {
                    try {
                        volumeNode.volume.rampTo(originalVolume, releaseTime);
                    } catch (error) {
                        console.error("[MusicEffects] Error in ducking release:", error);
                    }
                }, attackTime * 1000);
            } catch (error) {
                console.error("[MusicEffects] Error in ducking trigger:", error);
            }
        };
    } catch (error) {
        console.error("[MusicEffects] Error creating ducking effect:", error);
        return function() {}; // Return empty function
    }
}
