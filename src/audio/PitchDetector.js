/**
 * PitchDetector.js
 * 
 * Detects pitch changes in audio to identify melody notes.
 * Uses autocorrelation for pitch detection.
 */

export class PitchDetector {
    /**
     * Constructor for PitchDetector
     * @param {Object} options - Configuration options
     */
    constructor(options = {}) {
        // Configuration
        this.minFrequency = options.minFrequency || 80;  // Minimum detectable frequency (Hz)
        this.maxFrequency = options.maxFrequency || 1000; // Maximum detectable frequency (Hz)
        this.confidenceThreshold = options.confidenceThreshold || 0.8; // Minimum confidence to report a pitch
        this.bufferSize = options.bufferSize || 2048; // Buffer size for analysis
        
        // State
        this.sampleRate = 44100; // Default, will be updated when initialized
        this.noteHistory = []; // History of detected notes
        this.currentNote = null; // Currently detected note
        this.lastNoteChangeTime = 0; // Time of last note change
        this.noteChangeCount = 0; // Count of note changes in recent window
        
        // Note change detection
        this.noteChangeThreshold = 2; // Semitones difference to count as a change
        this.minNoteDuration = 100; // Minimum duration (ms) to consider a note valid
        
        // Melody pattern detection
        this.melodyPatterns = {
            ascending: false,
            descending: false,
            alternating: false,
            repeating: false,
            stepwise: false,
            leaping: false
        };
        
        // Debug
        this.debug = options.debug || false;
    }
    
    /**
     * Initialize the pitch detector with audio context
     * @param {AudioContext} audioContext - Web Audio API context
     */
    initialize(audioContext) {
        if (audioContext) {
            this.sampleRate = audioContext.sampleRate;
            console.log(`[PitchDetector] Initialized with sample rate: ${this.sampleRate}Hz`);
        }
    }
    
    /**
     * Analyze audio data to detect pitch
     * @param {Float32Array} audioData - Time domain audio data
     * @returns {Object} Pitch information
     */
    analyze(audioData) {
        if (!audioData || audioData.length === 0) {
            return { pitch: 0, note: null, confidence: 0 };
        }
        
        // Detect pitch using autocorrelation
        const result = this._detectPitchAutocorrelation(audioData);
        
        // Only process if we have a confident pitch detection
        if (result.confidence >= this.confidenceThreshold) {
            // Convert frequency to musical note
            const note = this._frequencyToNote(result.pitch);
            result.note = note;
            
            // Track note changes
            this._trackNoteChange(note, result.confidence);
            
            // Detect melody patterns
            this._detectMelodyPatterns();
            
            if (this.debug) {
                console.log(`[PitchDetector] Detected note: ${note.name}${note.octave} (${result.pitch.toFixed(1)}Hz, confidence: ${result.confidence.toFixed(2)})`);
            }
        }
        
        return result;
    }
    
    /**
     * Get information about recent note changes
     * @returns {Object} Note change information
     */
    getNoteChangeInfo() {
        const now = Date.now();
        
        // Calculate note change rate (changes per second)
        // Only count changes in the last 2 seconds
        const recentChanges = this.noteHistory.filter(n => now - n.timestamp < 2000);
        const noteChangeRate = recentChanges.length > 1 ? 
            recentChanges.length / 2 : 0; // Changes per second
        
        // Calculate average interval between changes
        let avgInterval = 0;
        if (recentChanges.length > 1) {
            let intervalSum = 0;
            for (let i = 1; i < recentChanges.length; i++) {
                intervalSum += recentChanges[i].timestamp - recentChanges[i-1].timestamp;
            }
            avgInterval = intervalSum / (recentChanges.length - 1);
        }
        
        // Determine if we have a melody (at least 3 different notes in sequence)
        const uniqueNotes = new Set(recentChanges.map(n => n.name + n.octave)).size;
        const hasMelody = uniqueNotes >= 3;
        
        return {
            rate: noteChangeRate,
            avgInterval,
            hasMelody,
            patterns: this.melodyPatterns,
            recentNotes: recentChanges.slice(-8) // Last 8 notes
        };
    }
    
    /**
     * Detect pitch using autocorrelation
     * @param {Float32Array} buffer - Audio buffer
     * @returns {Object} Pitch information
     * @private
     */
    _detectPitchAutocorrelation(buffer) {
        // Implementation of autocorrelation pitch detection algorithm
        const bufferSize = buffer.length;
        const correlations = new Float32Array(bufferSize);
        
        // Calculate autocorrelation
        for (let lag = 0; lag < bufferSize; lag++) {
            let sum = 0;
            for (let i = 0; i < bufferSize - lag; i++) {
                sum += buffer[i] * buffer[i + lag];
            }
            correlations[lag] = sum;
        }
        
        // Find peaks in autocorrelation
        const minPeriod = Math.floor(this.sampleRate / this.maxFrequency);
        const maxPeriod = Math.ceil(this.sampleRate / this.minFrequency);
        
        let bestPeriod = 0;
        let bestCorrelation = 0;
        
        for (let period = minPeriod; period <= maxPeriod; period++) {
            if (correlations[period] > bestCorrelation) {
                bestCorrelation = correlations[period];
                bestPeriod = period;
            }
        }
        
        // Normalize correlation for confidence
        const normalized = bestCorrelation / correlations[0];
        const confidence = Math.max(0, Math.min(1, normalized));
        
        // Calculate pitch from period
        const pitch = bestPeriod > 0 ? this.sampleRate / bestPeriod : 0;
        
        return { pitch, confidence };
    }
    
    /**
     * Convert frequency to musical note
     * @param {Number} frequency - Frequency in Hz
     * @returns {Object} Note information
     * @private
     */
    _frequencyToNote(frequency) {
        if (frequency <= 0) return { name: 'N/A', octave: 0, number: 0 };
        
        // A4 is 440Hz, which is note number 69
        const noteNumber = 12 * (Math.log(frequency / 440) / Math.log(2)) + 69;
        const roundedNoteNumber = Math.round(noteNumber);
        
        // Calculate cents deviation from equal temperament
        const cents = Math.round((noteNumber - roundedNoteNumber) * 100);
        
        // Note names in scientific pitch notation
        const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
        
        // Calculate note name and octave
        const octave = Math.floor((roundedNoteNumber - 12) / 12);
        const noteName = noteNames[roundedNoteNumber % 12];
        
        return {
            name: noteName,
            octave,
            number: roundedNoteNumber,
            cents,
            frequency
        };
    }
    
    /**
     * Track note changes
     * @param {Object} note - Note information
     * @param {Number} confidence - Detection confidence
     * @private
     */
    _trackNoteChange(note, confidence) {
        const now = Date.now();
        
        // Check if this is a new note
        let isNewNote = false;
        
        if (this.currentNote) {
            // Consider it a new note if the note number differs by at least the threshold
            const semitoneChange = Math.abs(note.number - this.currentNote.number);
            isNewNote = semitoneChange >= this.noteChangeThreshold;
            
            // Also check if enough time has passed since the last change
            const timeSinceLastChange = now - this.lastNoteChangeTime;
            if (isNewNote && timeSinceLastChange < this.minNoteDuration) {
                // Too soon after last change, might be noise
                isNewNote = false;
            }
        } else {
            // First note
            isNewNote = true;
        }
        
        if (isNewNote) {
            // Add to history with timestamp
            const noteWithTime = { ...note, timestamp: now, confidence };
            this.noteHistory.push(noteWithTime);
            
            // Limit history size
            if (this.noteHistory.length > 20) {
                this.noteHistory.shift();
            }
            
            // Update current note and change time
            this.currentNote = note;
            this.lastNoteChangeTime = now;
            this.noteChangeCount++;
            
            if (this.debug) {
                console.log(`[PitchDetector] Note change detected: ${note.name}${note.octave}`);
            }
        }
    }
    
    /**
     * Detect melody patterns in recent notes
     * @private
     */
    _detectMelodyPatterns() {
        // Need at least 4 notes to detect patterns
        if (this.noteHistory.length < 4) return;
        
        // Get recent notes
        const recentNotes = this.noteHistory.slice(-8);
        
        // Reset patterns
        Object.keys(this.melodyPatterns).forEach(key => {
            this.melodyPatterns[key] = false;
        });
        
        // Check for ascending pattern (at least 3 consecutive ascending notes)
        let ascendingCount = 0;
        for (let i = 1; i < recentNotes.length; i++) {
            if (recentNotes[i].number > recentNotes[i-1].number) {
                ascendingCount++;
            } else {
                ascendingCount = 0;
            }
            if (ascendingCount >= 2) { // 3 consecutive notes (2 intervals)
                this.melodyPatterns.ascending = true;
                break;
            }
        }
        
        // Check for descending pattern (at least 3 consecutive descending notes)
        let descendingCount = 0;
        for (let i = 1; i < recentNotes.length; i++) {
            if (recentNotes[i].number < recentNotes[i-1].number) {
                descendingCount++;
            } else {
                descendingCount = 0;
            }
            if (descendingCount >= 2) { // 3 consecutive notes (2 intervals)
                this.melodyPatterns.descending = true;
                break;
            }
        }
        
        // Check for alternating pattern (up-down-up or down-up-down)
        let alternatingCount = 0;
        let lastDirection = 0;
        for (let i = 1; i < recentNotes.length; i++) {
            const direction = Math.sign(recentNotes[i].number - recentNotes[i-1].number);
            if (direction !== 0) {
                if (lastDirection !== 0 && direction !== lastDirection) {
                    alternatingCount++;
                } else {
                    alternatingCount = 0;
                }
                lastDirection = direction;
            }
        }
        this.melodyPatterns.alternating = alternatingCount >= 2;
        
        // Check for repeating pattern (same note repeated)
        let repeatingCount = 0;
        for (let i = 1; i < recentNotes.length; i++) {
            if (recentNotes[i].number === recentNotes[i-1].number) {
                repeatingCount++;
            }
        }
        this.melodyPatterns.repeating = repeatingCount >= 2;
        
        // Check for stepwise motion (intervals of 1-2 semitones)
        let stepwiseCount = 0;
        for (let i = 1; i < recentNotes.length; i++) {
            const interval = Math.abs(recentNotes[i].number - recentNotes[i-1].number);
            if (interval >= 1 && interval <= 2) {
                stepwiseCount++;
            }
        }
        this.melodyPatterns.stepwise = stepwiseCount >= recentNotes.length / 2;
        
        // Check for leaping motion (intervals of 4+ semitones)
        let leapCount = 0;
        for (let i = 1; i < recentNotes.length; i++) {
            const interval = Math.abs(recentNotes[i].number - recentNotes[i-1].number);
            if (interval >= 4) {
                leapCount++;
            }
        }
        this.melodyPatterns.leaping = leapCount >= 2;
    }
    
    /**
     * Generate simulated pitch data for testing
     * @returns {Object} Simulated pitch data
     */
    generateSimulatedPitch() {
        const now = Date.now();
        const time = now / 1000;
        
        // Create a simple melody pattern that changes over time
        const patternType = Math.floor(time / 5) % 4; // Change pattern every 5 seconds
        
        let frequency;
        switch (patternType) {
            case 0: // Ascending scale
                frequency = 440 + (Math.floor(time * 2) % 8) * 50;
                break;
            case 1: // Descending scale
                frequency = 880 - (Math.floor(time * 2) % 8) * 50;
                break;
            case 2: // Arpeggio (C major chord)
                {
                    const notes = [261.63, 329.63, 392.00, 523.25]; // C4, E4, G4, C5
                    frequency = notes[Math.floor(time * 3) % notes.length];
                }
                break;
            case 3: // Alternating
                frequency = 440 + (Math.sin(time * 3) * 100);
                break;
        }
        
        // Add some vibrato
        frequency += Math.sin(time * 20) * 5;
        
        // Convert to note
        const note = this._frequencyToNote(frequency);
        
        // Simulate confidence
        const confidence = 0.85 + Math.random() * 0.15;
        
        // Track the note change
        this._trackNoteChange(note, confidence);
        
        // Detect patterns
        this._detectMelodyPatterns();
        
        return {
            pitch: frequency,
            note,
            confidence
        };
    }
}
