# Adaptive Music System

This directory contains the implementation of the game's adaptive music system, featuring a Loop Chain Playback Structure with Hit Interrupts, Temporal Insertions, and Emotive Cues.

## Key Components

- **MusicConductor.js** - Core class that manages the adaptive music system
- **musicData.js** - Configuration data for all music assets
- **MusicEffects.js** - Audio effects for the music system
- **MusicDebugHUD.js** - Debug overlay for the music system

## Loop Chain Playback Structure

The music system uses a modular approach where each area's theme consists of a chain of 5 four-bar loops that play in sequence:

```
loop_a → loop_b → loop_c → loop_d → loop_e → loop_a...
```

At the end of each loop, the system checks for pending events:
- Hit interrupts (when player takes damage)
- Temporal insertions (narrative/emotional moments)
- Area transitions

## Features

### 1. Loop Chain Playback
- Automatically cycles through loops in sequence
- Plays each loop in full, synced to the area's tempo
- Restarts from the first loop after reaching the end of the chain

### 2. Hit Interrupts
- When the player is hit, a special "hit interrupt" loop plays
- After the interrupt, the system continues to the next loop in the chain
- Example: `loop_b → hit_interrupt → loop_c`

### 3. Temporal Insertions
- One-off loops for narrative events, emotional moments, etc.
- After the insertion, the system continues to the next loop in the chain
- Example: `loop_d → memory_recall → loop_e`
- Categories: narrative, emotive, alignment

### 4. Area Transitions
- Smooth transitions between different areas
- Transitions occur at loop boundaries for musical coherence

### 5. Special Rooms
- Special rooms (shops, altars, etc.) have their own loop chains
- Smooth transitions when entering/exiting special rooms

### 6. Dynamic Audio Effects
- Low-pass filtering based on player health
- Tempo changes based on combat intensity
- Volume ducking and distortion effects for impacts

## Usage

To trigger music events from gameplay:

```javascript
// Start music for an area
MusicConductor.start('catacombs');

// Handle player being hit
MusicConductor.onPlayerHit();

// Queue a narrative insertion
MusicConductor.queueInsertion('memory_recall');

// Transition to a new area (at next loop boundary)
MusicConductor.queueTransition('lava_tubes');

// Enter a special room (at next loop boundary)
MusicConductor.enterSpecialRoom('catacombs', 'shop');
```

## File Structure

Music assets should be organized as follows:

```
assets/music/
├── catacombs/
│   ├── loop_a.mp3
│   ├── loop_b.mp3
│   ├── loop_c.mp3
│   ├── loop_d.mp3
│   ├── loop_e.mp3
│   ├── interrupts/
│   │   ├── hit_interrupt.mp3
│   │   └── danger_interrupt.mp3
│   ├── insertions/
│   │   ├── memory_recall.mp3
│   │   ├── dialogue.mp3
│   │   └── fear.mp3
│   └── specialRooms/
│       ├── shop_a.mp3
│       ├── shop_b.mp3
│       ├── event_a.mp3
│       └── event_b.mp3
└── other_areas/
    └── ...
```

## Technical Requirements

- All loops for an area should be at the same tempo (e.g., 100 BPM for catacombs)
- Each loop should be exactly 4 bars long for proper synchronization
- All files should be high-quality MP3 files (at least 192kbps)
- Loops should have clean loop points with no audible gaps or clicks
- Interrupts and insertions should be designed to flow smoothly back into the main loop chain
