/**
 * MusicConductor.js
 *
 * Core class for the adaptive music system with Loop Chain Playback Structure.
 * Handles music transitions, hit interrupts, temporal insertions, and emotive cues.
 */

import { MusicData, getSpecialRoomData, getTransitionMusic, validateMusicAssets } from './musicData.js';
import { createMusicEffects } from './MusicEffects.js';
import { createMusicDebugHUD } from './MusicDebugHUD.js';
import { isSoundTouchAvailable, createTempoShifter } from './SoundTouchWrapper.js';

export class MusicConductor {
    constructor() {
        // Initialize Tone.js
        this.initialized = false;
        this.ready = false;

        // Current state
        this.currentArea = null;
        this.currentSpecialRoom = null;
        this.currentTempo = MusicData.defaults.defaultTempo;
        this.currentBarLength = MusicData.defaults.defaultBarLength;
        this.currentTempoStretch = 1.0;
        this.activeLeitmotifs = new Set();

        // Loop Chain state
        this.loopChain = [];
        this.loopPlayers = [];
        this.currentLoopPlayer = null;
        this.currentLoopIndex = 0;
        this.nextLoopScheduled = false;
        this.nextLoopScheduledTime = 0;
        this.nextLoopTimerId = null;
        this.scheduledLoopIndex = 0;
        this.loopStartTime = 0;
        this.loopEndTime = 0;
        this.originalLoopDuration = 0; // Original duration of the current loop in seconds
        this.barDuration = 0; // Duration of one bar in seconds

        // Pending events
        this.pendingInterrupt = null;
        this.pendingInsertion = null;
        this.pendingTransition = null;
        this.pendingEvents = []; // Queue of events to process at next loop point

        // Audio players
        this.loopPlayers = []; // Array of players for the loop chain
        this.currentLoopPlayer = null; // Currently active loop player
        this.interruptPlayer = null; // Player for hit interrupts
        this.insertionPlayer = null; // Player for temporal insertions
        this.transitionPlayer = null; // Player for area transitions
        this.leitmotifPlayers = {};
        this.stingerPlayers = {};

        // Effects
        this.effects = null;

        // Debug HUD
        this.debugHUD = null;
        this.debugMode = false;

        // Transition state
        this.isTransitioning = false;
        this.needsTransitionBuffer = false;
        this.currentBufferTime = 0; // Current buffer time in ms
        this.isPlayingInterrupt = false;
        this.isPlayingInsertion = false;

        // Combat state
        this.inCombat = false;
        this.enemyCount = 0;
        this.playerHealth = 1.0; // Normalized 0-1

        // Pressure system
        this.pressureLevel = 0; // Pressure level from 0-100
        this.pressureDecayRate = 5; // How much pressure decays per second
        this.lastDamageTime = 0; // When player last took damage

        // Tempo fade timers
        this.tempoResetTimer = null;
        this.tempoFadeInterval = null;

        // Validate music assets (placeholder check)
        this.assetsValidation = validateMusicAssets();

        // Set up Tone.js Transport for scheduling
        if (typeof Tone !== 'undefined') {
            Tone.Transport.loop = false;
            Tone.Transport.loopEnd = '4m'; // Default 4-bar loop
        }

        console.log("[MusicConductor] Created, waiting for initialization");
    }

    /**
     * Initialize the music system
     * Must be called after user interaction due to AudioContext restrictions
     */
    async init() {
        if (this.initialized) return true;

        try {
            // Check if Tone is available
            if (typeof Tone === 'undefined') {
                console.error("[MusicConductor] Tone.js is not loaded");
                throw new Error("Tone.js is not loaded");
            }

            // Start AudioContext (requires user interaction)
            try {
                await Tone.start();
                console.log("[MusicConductor] Tone.js started");
            } catch (toneError) {
                console.warn("[MusicConductor] Could not start Tone.js:", toneError);
                console.warn("[MusicConductor] This may be due to no user interaction yet");
                // Continue anyway, we'll try again later
            }

            // Create effects chain
            this.effects = createMusicEffects();

            // Create debug HUD if needed
            if (this.debugMode) {
                this.debugHUD = createMusicDebugHUD(this);
            }

            // Initialize players
            this._initPlayers();

            this.initialized = true;
            this.ready = true;

            console.log("[MusicConductor] Initialized successfully");
            return true;
        } catch (error) {
            console.error("[MusicConductor] Initialization failed:", error);

            // Create minimal effects to prevent errors
            this.effects = createMusicEffects(); // This will create fallback effects

            // Set initialized but not ready
            this.initialized = true;
            this.ready = false;

            return false;
        }
    }

    /**
     * Initialize audio players
     */
    _initPlayers() {
        try {
            // Check if Tone is available
            if (typeof Tone === 'undefined') {
                throw new Error("Tone.js is not loaded");
            }

            // Reset tempo stretch to default
            this.currentTempoStretch = 1.0;

            // Check if SoundTouch is available
            this.soundTouchAvailable = isSoundTouchAvailable();
            if (this.soundTouchAvailable) {
                console.log("[MusicConductor] SoundTouch.js is available for high-quality tempo stretching");
            } else {
                console.log("[MusicConductor] SoundTouch.js is not available, using Tone.js for tempo stretching");
            }

            // Create loop chain players
            try {
                // We'll create players for each loop in the chain when we start an area
                // For now, just initialize empty arrays
                this.loopPlayers = [];
                this.currentLoopPlayer = null;
            } catch (error) {
                console.warn("[MusicConductor] Could not initialize loop players:", error);
                // Create dummy players
                this.loopPlayers = [];
                this.currentLoopPlayer = {
                    start: () => {},
                    stop: () => {},
                    load: () => Promise.resolve(),
                    buffer: { duration: 0 }
                };
            }

            // Create interrupt player
            try {
                this.interruptPlayer = new Tone.Player({
                    url: "", // Will be set when needed
                    loop: false,
                    autostart: false,
                    volume: -10, // dB
                    onload: () => console.log("[MusicConductor] Interrupt player loaded")
                }).connect(this.effects.mainChain);
            } catch (error) {
                console.warn("[MusicConductor] Could not create interrupt player:", error);
                // Create a dummy player
                this.interruptPlayer = {
                    start: () => {},
                    stop: () => {},
                    load: () => Promise.resolve(),
                    buffer: { duration: 0 }
                };
            }

            // Create insertion player
            try {
                this.insertionPlayer = new Tone.Player({
                    url: "", // Will be set when needed
                    loop: false,
                    autostart: false,
                    volume: -10, // dB
                    onload: () => console.log("[MusicConductor] Insertion player loaded")
                }).connect(this.effects.mainChain);
            } catch (error) {
                console.warn("[MusicConductor] Could not create insertion player:", error);
                // Create a dummy player
                this.insertionPlayer = {
                    start: () => {},
                    stop: () => {},
                    load: () => Promise.resolve(),
                    buffer: { duration: 0 }
                };
            }

            // Transition player
            try {
                this.transitionPlayer = new Tone.Player({
                    loop: false,
                    autostart: false,
                    volume: -10, // dB
                    onload: () => console.log("[MusicConductor] Transition player loaded")
                }).connect(this.effects.mainChain);
            } catch (error) {
                console.warn("[MusicConductor] Could not create transition player:", error);
                // Create a dummy player
                this.transitionPlayer = {
                    start: () => {},
                    stop: () => {},
                    load: () => Promise.resolve(),
                    buffer: { duration: 0 }
                };
            }

            // Create a single reusable stinger player instead of one per stinger
            try {
                // Create a single player for all stingers
                this.stingerPlayer = new Tone.Player({
                    url: "", // Will be set when needed
                    loop: false,
                    autostart: false,
                    volume: -8, // dB
                    onload: () => console.log(`[MusicConductor] Stinger player loaded`)
                }).connect(this.effects.stingerChain);

                // Create a map of stinger names to paths
                this.stingerPaths = {};
                for (const [key, path] of Object.entries(MusicData.stingers)) {
                    this.stingerPaths[key] = path;
                }

                console.log(`[MusicConductor] Initialized stinger player with ${Object.keys(this.stingerPaths).length} stingers`);
            } catch (stingersError) {
                console.warn("[MusicConductor] Could not create stinger player:", stingersError);

                // Create a dummy player
                this.stingerPlayer = {
                    start: () => {},
                    stop: () => {},
                    load: () => Promise.resolve(),
                    buffer: { duration: 0 }
                };

                this.stingerPaths = {};
            }

            console.log("[MusicConductor] Players initialized");
        } catch (error) {
            console.error("[MusicConductor] Failed to initialize players:", error);

            // Create dummy players
            this.mainLoopPlayer = {
                start: () => {},
                stop: () => {},
                load: () => Promise.resolve(),
                buffer: { duration: 0 }
            };

            this.transitionPlayer = {
                start: () => {},
                stop: () => {},
                load: () => Promise.resolve(),
                buffer: { duration: 0 }
            };
        }
    }

    /**
     * Start playing music for an area
     * @param {string} areaName - The area ID to start music for
     */
    async start(areaName) {
        if (!this.initialized) {
            await this.init();
        }

        if (!this.ready) {
            console.warn("[MusicConductor] Not ready to start music yet");
            return false;
        }

        console.log(`[MusicConductor] Starting music for area: ${areaName}`);

        // Check if area exists in music data
        if (!MusicData[areaName]) {
            console.warn(`[MusicConductor] No music data for area: ${areaName}, using default`);
            areaName = "catacombs"; // Fallback to default area
        }

        // Set current area and tempo
        this.currentArea = areaName;
        this.currentTempo = MusicData[areaName].tempo || MusicData.defaults.defaultTempo;
        this.currentBarLength = MusicData[areaName].barLength || MusicData.defaults.defaultBarLength;

        // Calculate bar duration in seconds
        this.barDuration = (60 / this.currentTempo) * this.currentBarLength;

        // Set Tone.js tempo
        try {
            if (typeof Tone !== 'undefined' && Tone.Transport) {
                Tone.Transport.bpm.value = this.currentTempo;
            }
        } catch (error) {
            console.warn("[MusicConductor] Could not set Tone.js tempo:", error);
        }

        // Reset loop chain state
        this.currentLoopIndex = 0;
        this.nextLoopScheduled = false;
        this.pendingEvents = [];
        this.isPlayingInterrupt = false;
        this.isPlayingInsertion = false;

        // Stop any currently playing music
        this._stopAllPlayers();

        // Get loop chain for this area
        this.loopChain = MusicData[areaName].loopChain || [];

        if (this.loopChain.length === 0) {
            console.warn(`[MusicConductor] No loop chain found for area: ${areaName}, falling back to single loop`);
            // Fallback to single loop if no chain is defined
            if (MusicData[areaName].loop) {
                this.loopChain = [MusicData[areaName].loop];
            } else {
                console.error(`[MusicConductor] No music found for area: ${areaName}`);
                return false;
            }
        }

        // Create players for each loop in the chain
        try {
            // Clean up existing players
            this._cleanupLoopPlayers();

            // Create new players for each loop in the chain
            for (let i = 0; i < this.loopChain.length; i++) {
                const loopPath = this.loopChain[i];

                try {
                    // Create a new player for this loop
                    const player = new Tone.Player({
                        url: loopPath,
                        loop: false, // We'll handle looping manually
                        autostart: false,
                        volume: -10, // dB
                        onload: () => console.log(`[MusicConductor] Loop player ${i} loaded`)
                    }).connect(this.effects.mainChain);

                    // Add to our array of players
                    this.loopPlayers.push(player);

                    console.log(`[MusicConductor] Created player for loop ${i}: ${loopPath}`);
                } catch (playerError) {
                    console.error(`[MusicConductor] Failed to create player for loop ${i}:`, playerError);

                    // Create a dummy player as a fallback
                    const dummyPlayer = {
                        start: () => {},
                        stop: () => {},
                        dispose: () => {},
                        buffer: { duration: this.barDuration * 4, loaded: true },
                        state: 'stopped',
                        playbackRate: { value: 1 }
                    };

                    this.loopPlayers.push(dummyPlayer);
                }
            }

            // Make sure we have at least one player
            if (this.loopPlayers.length === 0) {
                throw new Error("No loop players could be created");
            }

            // Start the first loop in the chain
            const result = await this._playNextLoopInChain();

            if (!result) {
                throw new Error("Failed to play first loop in chain");
            }

            console.log(`[MusicConductor] Started music for ${areaName}`);
            return true;
        } catch (error) {
            console.error(`[MusicConductor] Failed to start music for ${areaName}:`, error);

            // Clean up any players that might have been created
            this._cleanupLoopPlayers();

            return false;
        }
    }

    /**
     * Transition to a new area with proper musical transition
     * @param {string} areaName - The area ID to transition to
     * @param {boolean} immediate - Whether to transition immediately or at the next loop boundary
     */
    async transitionTo(areaName, immediate = false) {
        if (!this.initialized || !this.ready) {
            console.warn("[MusicConductor] Not ready for transition");
            return false;
        }

        // Don't transition to the same area
        if (areaName === this.currentArea && !this.currentSpecialRoom) {
            console.log(`[MusicConductor] Already in area ${areaName}, no transition needed`);
            return true;
        }

        console.log(`[MusicConductor] Transitioning from ${this.currentArea} to ${areaName}`);

        // If not immediate, queue the transition to happen at the next loop boundary
        if (!immediate) {
            return this.queueTransition(areaName);
        }

        // Otherwise, perform immediate transition
        // Check if we have a transition clip
        const transitionPath = getTransitionMusic(this.currentArea, areaName);

        if (transitionPath) {
            // We have a transition clip, use it
            try {
                // Set up transition sequence
                this.isTransitioning = true;

                // Fade out current music
                const fadeOutTime = 1; // seconds
                this.effects.mainVolume.volume.rampTo(-40, fadeOutTime); // Fade to -40dB

                // Wait for fade out
                await new Promise(resolve => setTimeout(resolve, fadeOutTime * 1000));

                // Stop all current players
                this._stopAllPlayers();

                // Load and play transition
                await this.transitionPlayer.load(transitionPath);
                this.transitionPlayer.start();

                // Reset volume
                this.effects.mainVolume.volume.value = 0;

                // Wait for transition to complete
                const transitionDuration = this.transitionPlayer.buffer.duration;
                await new Promise(resolve => setTimeout(resolve, transitionDuration * 1000));

                // Start new area music
                this.currentArea = areaName;
                this.currentTempo = MusicData[areaName].tempo || MusicData.defaults.defaultTempo;
                this.currentBarLength = MusicData[areaName].barLength || MusicData.defaults.defaultBarLength;

                // Calculate bar duration in seconds
                this.barDuration = (60 / this.currentTempo) * this.currentBarLength;

                // Set Tone.js tempo
                if (typeof Tone !== 'undefined' && Tone.Transport) {
                    Tone.Transport.bpm.value = this.currentTempo;
                }

                // Start the new area with its loop chain
                await this.start(areaName);

                this.isTransitioning = false;
                console.log(`[MusicConductor] Transition to ${areaName} complete`);
                return true;
            } catch (error) {
                console.error(`[MusicConductor] Transition to ${areaName} failed:`, error);
                this.isTransitioning = false;

                // Fallback to direct switch
                return this.forceTransition(areaName);
            }
        } else {
            // No transition clip, just start the new area at the next loop boundary
            console.log(`[MusicConductor] No transition clip for ${this.currentArea} to ${areaName}, using loop boundary transition`);

            // If immediate is requested, force transition now
            if (immediate) {
                return this.forceTransition(areaName);
            }

            // Otherwise queue the transition for the next loop boundary
            return this.queueTransition(areaName);
        }
    }

    /**
     * Force an immediate transition to a new area (no smooth transition)
     * @param {string} areaName - The area ID to transition to
     */
    async forceTransition(areaName) {
        if (!this.initialized || !this.ready) {
            console.warn("[MusicConductor] Not ready for forced transition");
            return false;
        }

        console.log(`[MusicConductor] Forcing transition to ${areaName}`);

        try {
            // Stop all current music
            this._stopAllPlayers();

            // Start the new area with its loop chain
            return await this.start(areaName);
        } catch (error) {
            console.error(`[MusicConductor] Forced transition to ${areaName} failed:`, error);
            return false;
        }
    }

    /**
     * Enter a special room within the current area
     * @param {string} area - The area ID
     * @param {string} room - The room type (e.g., "shop", "altar")
     * @param {boolean} immediate - Whether to transition immediately or at the next loop boundary
     */
    async enterSpecialRoom(area, room, immediate = false) {
        if (!this.initialized || !this.ready) {
            console.warn("[MusicConductor] Not ready to enter special room");
            return false;
        }

        // Check if special room exists
        const roomData = getSpecialRoomData(area, room);
        if (!roomData) {
            console.warn(`[MusicConductor] No music data for special room: ${room} in area: ${area}`);
            return false;
        }

        console.log(`[MusicConductor] Entering special room: ${room} in area: ${area}`);

        // If not immediate, queue the transition to happen at the next loop boundary
        if (!immediate) {
            // Add to pending events with high priority
            this.pendingEvents.push({
                type: 'special_room',
                area: area,
                room: room,
                priority: 8 // High priority but below area transitions
            });
            return true;
        }

        try {
            this.isTransitioning = true;

            // Play enter transition if available
            if (roomData.enterTransition) {
                // Fade out main loop
                const fadeOutTime = 0.5; // seconds
                this.effects.mainVolume.volume.rampTo(-20, fadeOutTime);

                // Wait for fade
                await new Promise(resolve => setTimeout(resolve, fadeOutTime * 1000));

                // Stop all current players
                this._stopAllPlayers();

                // Play transition
                await this.transitionPlayer.load(roomData.enterTransition);
                this.transitionPlayer.start();

                // Wait for transition to complete
                const transitionDuration = this.transitionPlayer.buffer.duration;
                await new Promise(resolve => setTimeout(resolve, transitionDuration * 1000));
            } else {
                // No transition, just stop current players
                this._stopAllPlayers();
            }

            // Set up loop chain for special room
            if (roomData.loopChain && roomData.loopChain.length > 0) {
                this.loopChain = roomData.loopChain;
            } else if (roomData.loop) {
                // Fallback to single loop if no chain is defined
                this.loopChain = [roomData.loop];
            } else {
                console.error(`[MusicConductor] No music found for special room: ${room}`);
                this.isTransitioning = false;
                return false;
            }

            // Reset loop chain state
            this.currentLoopIndex = 0;
            this.nextLoopScheduled = false;
            this.pendingEvents = [];
            this.isPlayingInterrupt = false;
            this.isPlayingInsertion = false;

            // Create players for each loop in the chain
            this._cleanupLoopPlayers();

            for (let i = 0; i < this.loopChain.length; i++) {
                const loopPath = this.loopChain[i];
                const player = new Tone.Player({
                    url: loopPath,
                    loop: false,
                    autostart: false,
                    volume: -10, // dB
                    onload: () => console.log(`[MusicConductor] Special room loop player ${i} loaded`)
                }).connect(this.effects.mainChain);

                this.loopPlayers.push(player);
            }

            // Start the first loop in the chain
            await this._playNextLoopInChain();

            // Reset volume
            this.effects.mainVolume.volume.value = 0;

            // Update state
            this.currentSpecialRoom = room;

            this.isTransitioning = false;
            console.log(`[MusicConductor] Entered special room: ${room}`);
            return true;
        } catch (error) {
            console.error(`[MusicConductor] Failed to enter special room: ${room}:`, error);
            this.isTransitioning = false;
            return false;
        }
    }

    /**
     * Exit a special room and return to the main area music
     * @param {string} area - The area ID
     * @param {string} room - The room type (e.g., "shop", "altar")
     * @param {boolean} immediate - Whether to transition immediately or at the next loop boundary
     */
    async exitSpecialRoom(area, room, immediate = false) {
        if (!this.initialized || !this.ready || !this.currentSpecialRoom) {
            console.warn("[MusicConductor] Not in a special room or not ready");
            return false;
        }

        // Check if special room exists
        const roomData = getSpecialRoomData(area, room);
        if (!roomData) {
            console.warn(`[MusicConductor] No music data for special room: ${room} in area: ${area}`);
            return this.forceTransition(area); // Fallback to main area
        }

        console.log(`[MusicConductor] Exiting special room: ${room} in area: ${area}`);

        // If not immediate, queue the transition to happen at the next loop boundary
        if (!immediate) {
            // Add to pending events with high priority
            this.pendingEvents.push({
                type: 'exit_special_room',
                area: area,
                room: room,
                priority: 8 // High priority but below area transitions
            });
            return true;
        }

        try {
            this.isTransitioning = true;

            // Play exit transition if available
            if (roomData.exitTransition) {
                // Fade out special room loop
                const fadeOutTime = 0.5; // seconds
                this.effects.mainVolume.volume.rampTo(-20, fadeOutTime);

                // Wait for fade
                await new Promise(resolve => setTimeout(resolve, fadeOutTime * 1000));

                // Stop all current players
                this._stopAllPlayers();

                // Play transition
                await this.transitionPlayer.load(roomData.exitTransition);
                this.transitionPlayer.start();

                // Wait for transition to complete
                const transitionDuration = this.transitionPlayer.buffer.duration;
                await new Promise(resolve => setTimeout(resolve, transitionDuration * 1000));
            } else {
                // No transition, just fade out
                const fadeOutTime = 0.5; // seconds
                this.effects.mainVolume.volume.rampTo(-20, fadeOutTime);

                // Wait for fade
                await new Promise(resolve => setTimeout(resolve, fadeOutTime * 1000));

                // Stop all current players
                this._stopAllPlayers();
            }

            // Update state
            this.currentSpecialRoom = null;

            // Start the main area music with its loop chain
            await this.start(area);

            this.isTransitioning = false;
            console.log(`[MusicConductor] Exited special room: ${room}`);
            return true;
        } catch (error) {
            console.error(`[MusicConductor] Failed to exit special room: ${room}:`, error);
            this.isTransitioning = false;
            return this.forceTransition(area); // Fallback to main area
        }
    }

    /**
     * Set the global tempo
     * @param {number} bpm - Beats per minute
     */
    setTempo(bpm) {
        if (!this.initialized) return false;

        console.log(`[MusicConductor] Setting tempo to ${bpm} BPM`);
        this.currentTempo = bpm;

        try {
            if (typeof Tone !== 'undefined' && Tone.Transport) {
                Tone.Transport.bpm.value = bpm;
            }
        } catch (error) {
            console.warn("[MusicConductor] Could not set Tone.js tempo:", error);
        }

        return true;
    }

    /**
     * Set tempo stretch factor (changes speed without affecting pitch)
     * @param {number} multiplier - Tempo multiplier (1.0 = normal, 1.25 = 25% faster)
     */
    setTempoStretch(multiplier) {
        if (!this.initialized) return false;

        // Store the old tempo stretch value before updating
        const oldTempoStretch = this.currentTempoStretch;

        console.log(`[MusicConductor] Setting tempo stretch to ${multiplier}x`);
        this.currentTempoStretch = multiplier;

        // Apply tempo stretch to all active loop players
        try {
            // Apply to current loop player if available
            if (this.currentLoopPlayer && typeof this.currentLoopPlayer.playbackRate !== 'undefined') {
                // Check if playbackRate is an object with a value property or just a number
                if (typeof this.currentLoopPlayer.playbackRate === 'object' && this.currentLoopPlayer.playbackRate !== null) {
                    this.currentLoopPlayer.playbackRate.value = multiplier;
                } else if (typeof this.currentLoopPlayer.playbackRate === 'number') {
                    this.currentLoopPlayer.playbackRate = multiplier;
                }
            }

            // Apply to all loop players in the chain
            for (const player of this.loopPlayers) {
                if (player && typeof player.playbackRate !== 'undefined') {
                    // Check if playbackRate is an object with a value property or just a number
                    if (typeof player.playbackRate === 'object' && player.playbackRate !== null) {
                        player.playbackRate.value = multiplier;
                    } else if (typeof player.playbackRate === 'number') {
                        player.playbackRate = multiplier;
                    }
                }
            }

            // Apply to special players if they exist
            if (this.interruptPlayer && typeof this.interruptPlayer.playbackRate !== 'undefined') {
                // Check if playbackRate is an object with a value property or just a number
                if (typeof this.interruptPlayer.playbackRate === 'object' && this.interruptPlayer.playbackRate !== null) {
                    this.interruptPlayer.playbackRate.value = multiplier;
                } else if (typeof this.interruptPlayer.playbackRate === 'number') {
                    this.interruptPlayer.playbackRate = multiplier;
                }
            }

            if (this.insertionPlayer && typeof this.insertionPlayer.playbackRate !== 'undefined') {
                // Check if playbackRate is an object with a value property or just a number
                if (typeof this.insertionPlayer.playbackRate === 'object' && this.insertionPlayer.playbackRate !== null) {
                    this.insertionPlayer.playbackRate.value = multiplier;
                } else if (typeof this.insertionPlayer.playbackRate === 'number') {
                    this.insertionPlayer.playbackRate = multiplier;
                }
            }

            if (this.transitionPlayer && typeof this.transitionPlayer.playbackRate !== 'undefined') {
                // Check if playbackRate is an object with a value property or just a number
                if (typeof this.transitionPlayer.playbackRate === 'object' && this.transitionPlayer.playbackRate !== null) {
                    this.transitionPlayer.playbackRate.value = multiplier;
                } else if (typeof this.transitionPlayer.playbackRate === 'number') {
                    this.transitionPlayer.playbackRate = multiplier;
                }
            }

            // Apply to Tone.js Transport tempo if needed
            if (typeof Tone !== 'undefined' && Tone.Transport) {
                // Calculate new BPM based on original tempo and multiplier
                const newBPM = this.currentTempo * multiplier;
                // Ramp to new BPM over 0.5 seconds for smooth transition
                Tone.Transport.bpm.rampTo(newBPM, 0.5);
            }

            console.log(`[MusicConductor] Applied tempo stretch ${multiplier}x to all active players`);

            // Handle tempo changes and buffer adjustments
            // oldTempoStretch is now defined at the beginning of the method

            // Handle tempo changes without buffer
            if (multiplier < oldTempoStretch) {
                // Calculate tempo change ratio for logging
                const tempoChangeRatio = oldTempoStretch / multiplier;
                // No buffer for tempo decreases
                const bufferTime = 0;

                // Disable buffer
                this.needsTransitionBuffer = false;
                this.currentBufferTime = bufferTime;

                // Update debug HUD if available
                if (this.debugMode && this.debugHUD) {
                    this.debugHUD.updateBuffer(bufferTime);
                }

                console.log(`[MusicConductor] No buffer for tempo decrease:`);
                console.log(`  - Tempo change: ${oldTempoStretch.toFixed(2)}x → ${multiplier.toFixed(2)}x (ratio: ${tempoChangeRatio.toFixed(2)})`);
                console.log(`  - Buffer time: ${bufferTime}ms (disabled)`);
            }
            // For tempo increases, we don't need a buffer
            else if (multiplier > oldTempoStretch) {
                // No buffer needed for tempo increases
                const bufferTime = 0; // 0ms buffer for tempo increases

                // We still set the flag but with zero buffer time
                this.needsTransitionBuffer = false;
                this.currentBufferTime = bufferTime;

                // Update debug HUD if available
                if (this.debugMode && this.debugHUD) {
                    this.debugHUD.updateBuffer(bufferTime);
                }

                console.log(`[MusicConductor] No buffer needed for tempo increase:`);
                console.log(`  - Tempo change: ${oldTempoStretch.toFixed(2)}x → ${multiplier.toFixed(2)}x`);
                console.log(`  - Buffer time: ${bufferTime}ms (disabled)`);
            }

            // Only reschedule if we have a scheduled loop and tempo has changed
            if (this.nextLoopScheduled && this.nextLoopTimerId && multiplier !== oldTempoStretch) {
                try {
                    // Get the current time
                    const now = Date.now();

                    // Calculate how much time is left at the old tempo
                    const remainingTimeOld = this.nextLoopScheduledTime - now;

                    if (remainingTimeOld > 0) {
                        // Calculate tempo ratio for adjustment
                        // For slowing down: ratio > 1, for speeding up: ratio < 1
                        const tempoRatio = oldTempoStretch / multiplier;

                        // Calculate the new remaining time based on tempo ratio
                        // We don't add a buffer here because we'll apply it in _playNextLoopInChain
                        const remainingTimeNew = remainingTimeOld * tempoRatio;

                        // Log the adjustment without buffer (buffer will be applied later)
                        const bufferNote = this.needsTransitionBuffer ?
                            `(Additional buffer of ${this.currentBufferTime}ms will be applied at loop start)` :
                            "(No additional buffer needed)";

                        const changeType = multiplier < oldTempoStretch ? "SLOWING DOWN" : "SPEEDING UP";
                        console.log(`[MusicConductor] ${changeType} - Adjusting next loop timing:`);
                        console.log(`  - Tempo change: ${oldTempoStretch.toFixed(2)}x → ${multiplier.toFixed(2)}x (ratio: ${tempoRatio.toFixed(2)})`);
                        console.log(`  - Old remaining: ${remainingTimeOld.toFixed(0)}ms`);
                        console.log(`  - New remaining: ${remainingTimeNew.toFixed(0)}ms`);
                        console.log(`  - ${bufferNote}`);

                        // Cancel the existing timer
                        clearTimeout(this.nextLoopTimerId);

                        // Create a new timer with the adjusted time
                        this.nextLoopTimerId = setTimeout(() => {
                            // Reset scheduled flag
                            this.nextLoopScheduled = false;
                            this.nextLoopScheduledTime = 0;

                            // Increment loop index, wrapping around to 0 if we reach the end
                            this.currentLoopIndex = (this.currentLoopIndex + 1) % this.loopChain.length;

                            // Play the next loop
                            this._playNextLoopInChain();
                        }, remainingTimeNew);

                        // Update the scheduled time
                        this.nextLoopScheduledTime = now + remainingTimeNew;
                    }
                } catch (error) {
                    console.warn("[MusicConductor] Error adjusting loop timing:", error);
                }
            } else {
                console.log(`[MusicConductor] Tempo unchanged: ${this.currentTempoStretch.toFixed(2)}x → ${multiplier.toFixed(2)}x`);
                console.log(`  - No timing adjustment needed`);
            }
        } catch (error) {
            console.warn("[MusicConductor] Tempo stretching failed:", error);
        }

        return true;
    }

    /**
     * Activate or deactivate a leitmotif layer
     * @param {string} name - Leitmotif name
     * @param {boolean} active - Whether to activate or deactivate
     * @param {number} fadeTime - Fade time in seconds
     */
    setLeitmotifActive(name, active, fadeTime = 2) {
        if (!this.initialized || !this.ready) {
            console.warn("[MusicConductor] Not ready to handle leitmotifs");
            return false;
        }

        // Check if leitmotif exists for current area
        if (!this.currentArea || !MusicData[this.currentArea].leitmotifs || !MusicData[this.currentArea].leitmotifs[name]) {
            console.warn(`[MusicConductor] No leitmotif '${name}' for current area: ${this.currentArea}`);
            return false;
        }

        console.log(`[MusicConductor] ${active ? 'Activating' : 'Deactivating'} leitmotif: ${name}`);

        // Get leitmotif path
        const leitmotifPath = MusicData[this.currentArea].leitmotifs[name];

        // Create player if it doesn't exist
        if (!this.leitmotifPlayers[name]) {
            this.leitmotifPlayers[name] = new Tone.Player({
                url: leitmotifPath,
                loop: true,
                autostart: false,
                volume: -Infinity, // Start silent
                onload: () => console.log(`[MusicConductor] Leitmotif '${name}' loaded`)
            }).connect(this.effects.leitmotifChain);
        }

        const player = this.leitmotifPlayers[name];

        if (active) {
            // Activate leitmotif
            if (!this.activeLeitmotifs.has(name)) {
                // Start player if not already playing
                if (player.state !== "started") {
                    player.start();
                }

                // Fade in
                player.volume.rampTo(-10, fadeTime); // Fade to -10dB

                // Add to active set
                this.activeLeitmotifs.add(name);
            }
        } else {
            // Deactivate leitmotif
            if (this.activeLeitmotifs.has(name)) {
                // Fade out
                player.volume.rampTo(-Infinity, fadeTime);

                // Stop after fade
                setTimeout(() => {
                    if (!this.activeLeitmotifs.has(name)) {
                        player.stop();
                    }
                }, fadeTime * 1000);

                // Remove from active set
                this.activeLeitmotifs.delete(name);
            }
        }

        return true;
    }

    /**
     * Play a stinger sound (one-shot)
     * @param {string} name - Stinger name
     */
    async triggerStinger(name) {
        if (!this.initialized || !this.ready) {
            console.warn("[MusicConductor] Not ready to play stingers");
            return false;
        }

        // Check if stinger exists
        if (!this.stingerPaths[name]) {
            console.warn(`[MusicConductor] No stinger with name: ${name}`);
            return false;
        }

        console.log(`[MusicConductor] Triggering stinger: ${name}`);

        // Get stinger path
        const stingerPath = this.stingerPaths[name];

        // Make sure we have a valid player
        if (!this.stingerPlayer) {
            console.warn(`[MusicConductor] Stinger player not initialized`);
            return false;
        }

        // Play stinger
        try {
            // Stop if already playing
            if (this.stingerPlayer.state === "started") {
                this.stingerPlayer.stop();
            }

            // Load the stinger
            await this.stingerPlayer.load(stingerPath);

            // Start the stinger
            this.stingerPlayer.start();

            // Get stinger duration
            const stingerDuration = this.stingerPlayer.buffer ? this.stingerPlayer.buffer.duration : 2;

            // Clean up after stinger finishes
            setTimeout(() => {
                try {
                    // Stop the player if it's still playing
                    if (this.stingerPlayer.state === "started") {
                        this.stingerPlayer.stop();
                    }

                    // Clear the buffer to prevent memory leaks
                    if (this.stingerPlayer.buffer) {
                        this.stingerPlayer.buffer = null;
                    }
                } catch (cleanupError) {
                    console.warn(`[MusicConductor] Error cleaning up stinger: ${name}:`, cleanupError);
                }
            }, stingerDuration * 1000 + 500); // Add 500ms buffer

            return true;
        } catch (error) {
            console.error(`[MusicConductor] Failed to play stinger: ${name}:`, error);
            return false;
        }
    }

    /**
     * Clean up existing loop players
     * @private
     */
    _cleanupLoopPlayers() {
        // Stop and dispose all existing players
        for (const player of this.loopPlayers) {
            try {
                // Check if player is valid and has state
                if (player) {
                    // Only stop if the player is actually playing
                    if (player.state === 'started' && typeof player.stop === 'function') {
                        player.stop();
                    }

                    // Clear the buffer to prevent memory leaks
                    if (player.buffer) {
                        player.buffer = null;
                    }

                    // Dispose the player to free resources
                    if (typeof player.dispose === 'function') {
                        player.dispose();
                    }
                }
            } catch (error) {
                console.warn("[MusicConductor] Error cleaning up player:", error);
                // Continue with other players even if one fails
            }
        }

        // Clear any scheduled timers
        if (this.nextLoopTimerId) {
            clearTimeout(this.nextLoopTimerId);
            this.nextLoopTimerId = null;
        }

        // Reset state
        this.loopPlayers = [];
        this.currentLoopPlayer = null;
        this.nextLoopScheduled = false;
        this.nextLoopScheduledTime = 0;
        this.originalLoopDuration = 0;
        this.loopStartTime = 0;
        this.loopEndTime = 0;
    }

    /**
     * Stop all active players
     * @private
     */
    _stopAllPlayers() {
        // Stop all loop players
        for (const player of this.loopPlayers) {
            try {
                if (player && player.state === 'started' && typeof player.stop === 'function') {
                    player.stop();
                }
            } catch (error) {
                console.warn("[MusicConductor] Error stopping player:", error);
            }
        }

        // Stop special players
        try {
            if (this.interruptPlayer) {
                if (this.interruptPlayer.state === 'started' && typeof this.interruptPlayer.stop === 'function') {
                    this.interruptPlayer.stop();
                }

                // Clear the buffer to prevent memory leaks
                if (this.interruptPlayer.buffer) {
                    this.interruptPlayer.buffer = null;
                }
            }
        } catch (error) {
            console.warn("[MusicConductor] Error stopping interrupt player:", error);
        }

        try {
            if (this.insertionPlayer) {
                if (this.insertionPlayer.state === 'started' && typeof this.insertionPlayer.stop === 'function') {
                    this.insertionPlayer.stop();
                }

                // Clear the buffer to prevent memory leaks
                if (this.insertionPlayer.buffer) {
                    this.insertionPlayer.buffer = null;
                }
            }
        } catch (error) {
            console.warn("[MusicConductor] Error stopping insertion player:", error);
        }

        try {
            if (this.transitionPlayer) {
                if (this.transitionPlayer.state === 'started' && typeof this.transitionPlayer.stop === 'function') {
                    this.transitionPlayer.stop();
                }

                // Clear the buffer to prevent memory leaks
                if (this.transitionPlayer.buffer) {
                    this.transitionPlayer.buffer = null;
                }
            }
        } catch (error) {
            console.warn("[MusicConductor] Error stopping transition player:", error);
        }

        // Also stop any stingers that might be playing
        try {
            if (this.stingerPlayer && this.stingerPlayer.state === 'started' && typeof this.stingerPlayer.stop === 'function') {
                this.stingerPlayer.stop();

                // Clear the buffer
                if (this.stingerPlayer.buffer) {
                    this.stingerPlayer.buffer = null;
                }
            }
        } catch (error) {
            console.warn("[MusicConductor] Error stopping stinger player:", error);
        }
    }

    /**
     * Play the next loop in the chain
     * @private
     */
    async _playNextLoopInChain() {
        try {
            // Check if we have pending events to process
            await this._processPendingEvents();

            // Buffer system is now disabled, but we'll keep the code structure
            // in case we want to re-enable it in the future
            if (this.needsTransitionBuffer && this.currentBufferTime > 0) {
                // This block should never execute now, but we'll keep it for future reference
                const bufferTime = this.currentBufferTime;
                console.log(`[MusicConductor] Buffer system is disabled, not adding delay`);

                // Reset the flag and buffer time
                this.needsTransitionBuffer = false;
                this.currentBufferTime = 0;

                // Update debug HUD if available
                if (this.debugMode && this.debugHUD) {
                    this.debugHUD.updateBuffer(0);
                }
            } else {
                // Just ensure buffer state is reset
                this.needsTransitionBuffer = false;
                this.currentBufferTime = 0;

                // Update debug HUD if available
                if (this.debugMode && this.debugHUD && this.debugHUD.updateBuffer) {
                    this.debugHUD.updateBuffer(0);
                }
            }

            // Get the current loop index
            const index = this.currentLoopIndex;

            // Get the player for this loop
            const player = this.loopPlayers[index];

            if (!player) {
                console.error(`[MusicConductor] No player found for loop index ${index}`);
                return false;
            }

            // Make sure the buffer is loaded before playing
            if (!player.buffer || !player.buffer.loaded) {
                console.log(`[MusicConductor] Waiting for buffer to load for loop index ${index}`);

                // Wait for buffer to load if needed
                await new Promise((resolve) => {
                    // If buffer exists but not loaded, wait for load event
                    if (player.buffer && !player.buffer.loaded) {
                        player.buffer.onload = resolve;
                    } else {
                        // If no buffer, wait a short time and resolve anyway
                        setTimeout(resolve, 500);
                    }
                });

                // Check again after waiting
                if (!player.buffer || !player.buffer.loaded) {
                    console.error(`[MusicConductor] Buffer failed to load for loop index ${index}`);
                    return false;
                }
            }

            // Set as current player
            this.currentLoopPlayer = player;

            // Set the playback rate before starting
            if (typeof player.playbackRate === 'object' && player.playbackRate !== null) {
                player.playbackRate.value = this.currentTempoStretch;
            } else if (typeof player.playbackRate === 'number') {
                player.playbackRate = this.currentTempoStretch;
            }

            // Start the player
            player.start();

            console.log(`[MusicConductor] Started loop ${index} with playback rate ${this.currentTempoStretch.toFixed(2)}x`);

            // Record start time - use Date.now() for consistency with other timing calculations
            this.loopStartTime = Date.now();

            // Calculate end time based on loop duration, adjusted for tempo stretch
            let baseDuration = player.buffer ? player.buffer.duration : this.barDuration * 4;

            // Store the original duration for tempo change calculations
            this.originalLoopDuration = baseDuration;

            // Adjust duration based on current tempo stretch
            const adjustedDuration = baseDuration / this.currentTempoStretch;

            // Calculate end time in milliseconds
            this.loopEndTime = this.loopStartTime + (adjustedDuration * 1000);

            console.log(`[MusicConductor] Playing loop ${index} (base duration: ${baseDuration.toFixed(2)}s, adjusted for ${this.currentTempoStretch}x tempo: ${adjustedDuration.toFixed(2)}s)`);

            // Update debug HUD if available
            if (this.debugMode && this.debugHUD) {
                const loopPath = this.loopChain[index];
                this.debugHUD.updateLoop(index, loopPath, adjustedDuration);
            }

            // Schedule the next loop
            this._scheduleNextLoop(adjustedDuration);

            return true;
        } catch (error) {
            console.error("[MusicConductor] Error playing next loop in chain:", error);
            return false;
        }
    }

    /**
     * Schedule the next loop in the chain
     * @param {number} currentLoopDuration - Duration of the current loop in seconds
     * @private
     */
    _scheduleNextLoop(currentLoopDuration) {
        if (this.nextLoopScheduled) {
            return; // Already scheduled
        }

        // Mark as scheduled
        this.nextLoopScheduled = true;

        // Calculate timeout in milliseconds - no buffer for normal transitions
        const timeoutMs = currentLoopDuration * 1000;

        // Get the current time
        const now = Date.now();

        // Store information about the scheduled loop for potential tempo updates
        this.nextLoopScheduledTime = now + timeoutMs;
        this.scheduledLoopIndex = this.currentLoopIndex;

        console.log(`[MusicConductor] Scheduling next loop: ${currentLoopDuration.toFixed(2)}s`);

        // We don't update originalLoopDuration or loopStartTime here because
        // those are set in _playNextLoopInChain and should remain consistent
        // throughout the entire loop playback

        console.log(`[MusicConductor] Scheduling next loop in ${timeoutMs.toFixed(0)}ms at tempo stretch ${this.currentTempoStretch.toFixed(2)}x`);
        console.log(`  - Current loop index: ${this.currentLoopIndex}`);
        console.log(`  - Original duration: ${this.originalLoopDuration.toFixed(2)}s`);
        console.log(`  - Adjusted duration: ${currentLoopDuration.toFixed(2)}s`);

        // Schedule the next loop to play when this one ends
        const timerId = setTimeout(() => {
            // Reset scheduled flag
            this.nextLoopScheduled = false;
            this.nextLoopScheduledTime = 0;

            // Increment loop index, wrapping around to 0 if we reach the end
            this.currentLoopIndex = (this.currentLoopIndex + 1) % this.loopChain.length;

            // Play the next loop
            this._playNextLoopInChain();
        }, timeoutMs);

        // Store the timer ID so we can cancel it if needed
        this.nextLoopTimerId = timerId;
    }

    /**
     * Process any pending events at the loop boundary
     * @private
     */
    async _processPendingEvents() {
        // Check if we have any pending events
        if (this.pendingEvents.length === 0) {
            return false;
        }

        // Sort events by priority (higher priority first)
        this.pendingEvents.sort((a, b) => b.priority - a.priority);

        // Get the highest priority event
        const event = this.pendingEvents.shift();

        console.log(`[MusicConductor] Processing pending event: ${event.type}`);

        // Process the event based on type
        switch (event.type) {
            case 'interrupt':
                await this._playInterrupt(event.name);
                break;

            case 'insertion':
                await this._playInsertion(event.name);
                break;

            case 'transition':
                await this.transitionTo(event.areaName, true); // Use immediate transition
                break;

            case 'special_room':
                await this.enterSpecialRoom(event.area, event.room, true); // Use immediate transition
                break;

            case 'exit_special_room':
                await this.exitSpecialRoom(event.area, event.room, true); // Use immediate transition
                break;

            default:
                console.warn(`[MusicConductor] Unknown event type: ${event.type}`);
                break;
        }

        return true;
    }

    /**
     * Play an interrupt loop (e.g., hit interrupt)
     * @param {string} interruptName - Name of the interrupt to play
     * @private
     */
    async _playInterrupt(interruptName) {
        if (!this.initialized || !this.ready) return false;

        // Check if we have this interrupt
        if (!MusicData[this.currentArea].interrupts || !MusicData[this.currentArea].interrupts[interruptName]) {
            console.warn(`[MusicConductor] No interrupt found: ${interruptName}`);
            return false;
        }

        const interruptData = MusicData[this.currentArea].interrupts[interruptName];
        const interruptPath = interruptData.path;

        console.log(`[MusicConductor] Playing interrupt: ${interruptName}`);

        try {
            // Stop current loop
            if (this.currentLoopPlayer) {
                this.currentLoopPlayer.stop();
            }

            // Load and play interrupt
            await this.interruptPlayer.load(interruptPath);
            this.interruptPlayer.start();

            // Mark as playing interrupt
            this.isPlayingInterrupt = true;

            // Get interrupt duration
            const interruptDuration = this.interruptPlayer.buffer ? this.interruptPlayer.buffer.duration : this.barDuration * 4;

            // Schedule resuming the loop chain after the interrupt
            setTimeout(() => {
                this.isPlayingInterrupt = false;
                this._playNextLoopInChain();
            }, interruptDuration * 1000);

            return true;
        } catch (error) {
            console.error(`[MusicConductor] Failed to play interrupt: ${interruptName}`, error);
            this.isPlayingInterrupt = false;
            return false;
        }
    }

    /**
     * Play a temporal insertion loop
     * @param {string} insertionName - Name of the insertion to play
     * @private
     */
    async _playInsertion(insertionName) {
        if (!this.initialized || !this.ready) return false;

        // Check if we have this insertion
        if (!MusicData[this.currentArea].temporalInsertions || !MusicData[this.currentArea].temporalInsertions[insertionName]) {
            console.warn(`[MusicConductor] No insertion found: ${insertionName}`);
            return false;
        }

        const insertionData = MusicData[this.currentArea].temporalInsertions[insertionName];
        const insertionPath = insertionData.path;

        console.log(`[MusicConductor] Playing insertion: ${insertionName} (type: ${insertionData.type})`);

        try {
            // Stop current loop
            if (this.currentLoopPlayer) {
                this.currentLoopPlayer.stop();
            }

            // Load and play insertion
            await this.insertionPlayer.load(insertionPath);
            this.insertionPlayer.start();

            // Mark as playing insertion
            this.isPlayingInsertion = true;

            // Get insertion duration
            const insertionDuration = this.insertionPlayer.buffer ? this.insertionPlayer.buffer.duration : this.barDuration * 4;

            // Schedule resuming the loop chain after the insertion
            setTimeout(() => {
                this.isPlayingInsertion = false;
                this._playNextLoopInChain();
            }, insertionDuration * 1000);

            return true;
        } catch (error) {
            console.error(`[MusicConductor] Failed to play insertion: ${insertionName}`, error);
            this.isPlayingInsertion = false;
            return false;
        }
    }

    /**
     * Handle player being hit
     * Increases pressure level and queues a hit interrupt to play at the next loop boundary
     */
    onPlayerHit() {
        if (!this.initialized || !this.ready) return false;

        console.log("[MusicConductor] Player hit event");

        // Increase pressure level when player is hit
        if (this.inCombat && this.enemyCount > 0) {
            // Add pressure when hit during combat
            const pressureIncrease = 20; // Add 20 pressure points per hit
            this.pressureLevel = Math.min(100, this.pressureLevel + pressureIncrease);
            console.log(`[MusicConductor] Pressure increased to ${this.pressureLevel.toFixed(1)} due to player damage`);

            // Store the time of damage
            this.lastDamageTime = Date.now() / 1000;

            // Update tempo immediately based on new pressure
            if (this.inCombat) {
                const pressureBasedIncrease = (this.pressureLevel / 100) * 0.25;
                const newTempoStretch = Math.min(1.25, 1.0 + pressureBasedIncrease);

                // Round to 2 decimal places
                const roundedTempo = Math.round(newTempoStretch * 100) / 100;

                // Apply if different from current
                if (roundedTempo !== this.currentTempoStretch) {
                    console.log(`[MusicConductor] Damage-based tempo increase: ${this.currentTempoStretch.toFixed(2)}x -> ${roundedTempo.toFixed(2)}x`);
                    this.setTempoStretch(roundedTempo);
                }
            }
        }

        // Temporarily disabled hit stinger and interrupt
        /*
        this.triggerStinger("player_hit");

        // Duck music volume temporarily
        const duckAmount = -10; // dB
        const duckTime = 0.1; // seconds
        const recoverTime = 0.5; // seconds

        this.effects.mainVolume.volume.rampTo(duckAmount, duckTime);

        // Apply distortion effect
        this.effects.distortion.wet.value = 0.5;

        // Recover after a short time
        setTimeout(() => {
            this.effects.mainVolume.volume.rampTo(0, recoverTime);
            this.effects.distortion.wet.rampTo(0, recoverTime);
        }, 300);

        // Queue hit interrupt to play at next loop boundary
        this.queueInterrupt('hit');
        */

        return true;
    }

    /**
     * Queue an interrupt to play at the next loop boundary
     * @param {string} interruptName - Name of the interrupt to play
     */
    queueInterrupt(interruptName) {
        if (!this.initialized || !this.ready) return false;

        // Check if we have this interrupt
        if (!MusicData[this.currentArea].interrupts || !MusicData[this.currentArea].interrupts[interruptName]) {
            console.warn(`[MusicConductor] No interrupt found: ${interruptName}`);
            return false;
        }

        const interruptData = MusicData[this.currentArea].interrupts[interruptName];
        const priority = interruptData.priority || 1;

        console.log(`[MusicConductor] Queuing interrupt: ${interruptName} (priority: ${priority})`);

        // Add to pending events
        this.pendingEvents.push({
            type: 'interrupt',
            name: interruptName,
            priority: priority
        });

        return true;
    }

    /**
     * Queue a temporal insertion to play at the next loop boundary
     * @param {string} insertionName - Name of the insertion to play
     */
    queueInsertion(insertionName) {
        if (!this.initialized || !this.ready) return false;

        // Check if we have this insertion
        if (!MusicData[this.currentArea].temporalInsertions || !MusicData[this.currentArea].temporalInsertions[insertionName]) {
            console.warn(`[MusicConductor] No insertion found: ${insertionName}`);
            return false;
        }

        const insertionData = MusicData[this.currentArea].temporalInsertions[insertionName];
        const priority = insertionData.priority || 1;

        console.log(`[MusicConductor] Queuing insertion: ${insertionName} (type: ${insertionData.type}, priority: ${priority})`);

        // Add to pending events
        this.pendingEvents.push({
            type: 'insertion',
            name: insertionName,
            priority: priority
        });

        return true;
    }

    /**
     * Queue a transition to a new area at the next loop boundary
     * @param {string} areaName - Name of the area to transition to
     */
    queueTransition(areaName) {
        if (!this.initialized || !this.ready) return false;

        // Check if area exists
        if (!MusicData[areaName]) {
            console.warn(`[MusicConductor] No music data for area: ${areaName}`);
            return false;
        }

        console.log(`[MusicConductor] Queuing transition to: ${areaName}`);

        // Add to pending events with highest priority
        this.pendingEvents.push({
            type: 'transition',
            areaName: areaName,
            priority: 10 // Transitions have highest priority
        });

        return true;
    }

    /**
     * Immediately reset tempo to normal
     */
    resetTempoToNormal() {
        if (!this.initialized) return false;

        // Clear any existing fade interval
        if (this.tempoFadeInterval) {
            clearInterval(this.tempoFadeInterval);
            this.tempoFadeInterval = null;
        }

        // Get current tempo stretch
        const startTempo = this.currentTempoStretch;
        const targetTempo = 1.0; // Normal tempo

        // Skip if already at normal tempo
        if (Math.abs(startTempo - targetTempo) < 0.01) {
            console.log(`[MusicConductor] Already at normal tempo (${startTempo.toFixed(2)}x)`);
            return true;
        }

        console.log(`[MusicConductor] Resetting tempo from ${startTempo.toFixed(2)}x to ${targetTempo.toFixed(2)}x immediately`);

        // Apply new tempo directly
        this._applyTempoStretchDirectly(targetTempo);

        return true;
    }

    /**
     * Apply tempo stretch directly without buffer logic
     * Private method used by resetTempoToNormal
     * @param {number} multiplier - Tempo multiplier
     * @private
     */
    _applyTempoStretchDirectly(multiplier) {
        if (!this.initialized) return false;

        // Update the current tempo stretch value
        this.currentTempoStretch = multiplier;

        // Apply to current loop player if available
        if (this.currentLoopPlayer && typeof this.currentLoopPlayer.playbackRate !== 'undefined') {
            // Check if playbackRate is an object with a value property or just a number
            if (typeof this.currentLoopPlayer.playbackRate === 'object' && this.currentLoopPlayer.playbackRate !== null) {
                this.currentLoopPlayer.playbackRate.value = multiplier;
            } else if (typeof this.currentLoopPlayer.playbackRate === 'number') {
                this.currentLoopPlayer.playbackRate = multiplier;
            }
        }

        // Apply to all loop players in the chain
        for (const player of this.loopPlayers) {
            if (player && typeof player.playbackRate !== 'undefined') {
                // Check if playbackRate is an object with a value property or just a number
                if (typeof player.playbackRate === 'object' && player.playbackRate !== null) {
                    player.playbackRate.value = multiplier;
                } else if (typeof player.playbackRate === 'number') {
                    player.playbackRate = multiplier;
                }
            }
        }

        // Apply to Tone.js Transport tempo if needed
        if (typeof Tone !== 'undefined' && Tone.Transport) {
            // Calculate new BPM based on original tempo and multiplier
            const newBPM = this.currentTempo * multiplier;
            // Ramp to new BPM over 0.1 seconds for smooth transition
            Tone.Transport.bpm.rampTo(newBPM, 0.1);
        }

        // Update debug HUD if available
        if (this.debugMode && this.debugHUD) {
            this.debugHUD.updateTempo();
        }

        return true;
    }

    /**
     * Update health-based filtering
     * @param {number} currentHealth - Current health value
     * @param {number} maxHealth - Maximum health value
     */
    updateHealth(currentHealth, maxHealth) {
        if (!this.initialized || !this.effects) return false;

        // Player should have fully open filter at 10 health
        const baseHealth = 10;

        // Calculate filter ratio based on health below baseHealth
        let filterRatio = 1.0; // Default to fully open filter

        // Only start filtering when health drops below baseHealth
        if (currentHealth < baseHealth) {
            // Calculate ratio (0-1) where 0 = no health, 1 = baseHealth
            filterRatio = Math.max(0, Math.min(1, currentHealth / baseHealth));
        }

        // Store normalized health for other systems
        this.playerHealth = Math.max(0, Math.min(1, currentHealth / maxHealth));

        // Map health to filter frequency
        // Full health (10+) = 20kHz, No health = 100Hz (much more extreme filtering)
        const minFreq = 100; // Changed from 500Hz to 100Hz for more extreme effect
        const maxFreq = 20000;
        const frequency = minFreq + (maxFreq - minFreq) * filterRatio;

        // console.log(`[MusicConductor] Health: ${currentHealth}/${maxHealth}, Filter: ${Math.round(frequency)}Hz`); // Debug log disabled

        // Apply filter
        if (this.effects.lowpass && this.effects.lowpass.frequency) {
            this.effects.lowpass.frequency.value = frequency;
        } else {
            console.warn("[MusicConductor] lowpass filter not available");
        }

        // Debug
        if (this.debugMode && this.debugHUD) {
            this.debugHUD.updateHealth(this.playerHealth, frequency);
        }

        return true;
    }

    /**
     * Update combat state based on player pressure and enemy kills
     * @param {number} enemyCount - Number of active enemies
     * @param {number} currentHealth - Current player health
     * @param {number} maxHealth - Maximum player health
     * @param {boolean} enemyKilled - Whether an enemy was just killed
     * @param {boolean} enteredRoom - Whether the player just entered a room
     */
    updateCombatState(enemyCount, currentHealth, maxHealth, enemyKilled = false, enteredRoom = false) {
        if (!this.initialized) return false;

        // Update health filter
        this.updateHealth(currentHealth, maxHealth);

        // Store previous enemy count for comparison
        const previousEnemyCount = this.enemyCount;

        // Update enemy count
        this.enemyCount = enemyCount;

        // Base tempo stretch (normal state)
        const baseTempo = 1.0;

        // Current tempo stretch
        let tempoStretch = this.currentTempoStretch;

        // Get current time for pressure decay
        const currentTime = Date.now() / 1000; // Convert to seconds

        // No pressure decay - we'll keep pressure until combat ends

        // Store current time for next update
        this.lastPressureUpdateTime = currentTime;

        // Handle different scenarios
        if (enteredRoom && enemyCount > 0) {
            // Player entered a room with enemies - very slight tempo increase
            // Base + smaller bump (0.05 = 5% increase)
            tempoStretch = baseTempo + 0.05;
            console.log(`[MusicConductor] Entered room with ${enemyCount} enemies, setting tempo to ${tempoStretch.toFixed(2)}x`);
        }
        else if (enemyKilled && enemyCount > 0) {
            // Enemy killed but not the last one - small tempo bump for satisfaction
            // Current tempo + smaller increment (0.015 = 1.5% increase per kill)
            tempoStretch = this.currentTempoStretch + 0.015;
            // Cap at reasonable maximum (1.15 = 15% increase)
            tempoStretch = Math.min(1.15, tempoStretch);
            console.log(`[MusicConductor] Enemy killed, small tempo bump to ${tempoStretch.toFixed(2)}x`);
        }
        else if (enemyKilled && enemyCount === 0) {
            // Last enemy killed - reset tempo immediately
            console.log(`[MusicConductor] Last enemy killed, resetting tempo to normal`);

            // Reset pressure
            this.pressureLevel = 0;

            // Reset tempo immediately
            tempoStretch = baseTempo;

            // Apply the tempo change right away
            this.setTempoStretch(tempoStretch);

            return true;
        }
        else if (enemyCount === 0 && previousEnemyCount === 0) {
            // No enemies and no change - maintain normal tempo
            tempoStretch = baseTempo;

            // Reset pressure when not in combat
            this.pressureLevel = 0;
        }
        else if (enemyCount > 0) {
            // In combat with enemies - calculate tempo based on pressure
            // Map pressure (0-100) to tempo increase (0-0.25)
            const pressureBasedIncrease = (this.pressureLevel / 100) * 0.25;
            tempoStretch = baseTempo + pressureBasedIncrease;

            // Cap at reasonable maximum (1.25 = 25% increase)
            tempoStretch = Math.min(1.25, tempoStretch);

            if (Math.abs(tempoStretch - this.currentTempoStretch) > 0.01) {
                console.log(`[MusicConductor] Pressure-based tempo: ${tempoStretch.toFixed(2)}x (pressure: ${this.pressureLevel.toFixed(1)})`);
            }
        }

        // Round to 2 decimal places to avoid tiny changes that don't affect audio
        tempoStretch = Math.round(tempoStretch * 100) / 100;

        // Only apply tempo stretch if it's actually different from current value
        if (tempoStretch !== this.currentTempoStretch) {
            console.log(`[MusicConductor] Combat state changed: ${previousEnemyCount} -> ${enemyCount} enemies, tempo: ${this.currentTempoStretch.toFixed(2)}x -> ${tempoStretch.toFixed(2)}x`);
            // Apply tempo stretch
            this.setTempoStretch(tempoStretch);
        }

        // Update combat state
        this.inCombat = enemyCount > 0;

        // Temporarily disabled danger interrupt
        /*
        if (enemyCount >= 3 && !this.isPlayingInterrupt && !this.isPlayingInsertion) {
            // Only queue if we have the danger interrupt defined
            if (MusicData[this.currentArea]?.interrupts?.danger) {
                this.queueInterrupt('danger');
            }
        }
        */

        // Debug
        if (this.debugMode && this.debugHUD) {
            this.debugHUD.updateCombat(enemyCount, tempoStretch, this.inCombat);
        }

        return true;
    }

    /**
     * Toggle debug mode
     */
    toggleDebug() {
        this.debugMode = !this.debugMode;

        if (this.debugMode && !this.debugHUD) {
            this.debugHUD = createMusicDebugHUD(this);
        }

        if (this.debugHUD) {
            this.debugHUD.toggle(this.debugMode);
        }

        console.log(`[MusicConductor] Debug mode ${this.debugMode ? 'enabled' : 'disabled'}`);
        return this.debugMode;
    }

    /**
     * Stop all music and clean up resources
     */
    dispose() {
        if (!this.initialized) return;

        console.log("[MusicConductor] Disposing resources");

        // Stop all players
        if (this.mainLoopPlayer) this.mainLoopPlayer.stop();
        if (this.transitionPlayer) this.transitionPlayer.stop();

        for (const player of Object.values(this.leitmotifPlayers)) {
            player.stop();
        }

        // Stop stinger player
        if (this.stingerPlayer && typeof this.stingerPlayer.stop === 'function') {
            this.stingerPlayer.stop();
        }

        // Dispose effects
        if (this.effects) {
            for (const effect of Object.values(this.effects)) {
                if (effect && typeof effect.dispose === 'function') {
                    effect.dispose();
                }
            }
        }

        // Dispose players
        if (this.mainLoopPlayer) this.mainLoopPlayer.dispose();
        if (this.transitionPlayer) this.transitionPlayer.dispose();

        for (const player of Object.values(this.leitmotifPlayers)) {
            player.dispose();
        }

        // Dispose stinger player
        if (this.stingerPlayer && typeof this.stingerPlayer.dispose === 'function') {
            this.stingerPlayer.dispose();
        }

        // Remove debug HUD
        if (this.debugHUD) {
            this.debugHUD.dispose();
        }

        // Clear any tempo fade timers
        if (this.tempoResetTimer) {
            clearTimeout(this.tempoResetTimer);
            this.tempoResetTimer = null;
        }

        if (this.tempoFadeInterval) {
            clearInterval(this.tempoFadeInterval);
            this.tempoFadeInterval = null;
        }

        // Reset state
        this.initialized = false;
        this.ready = false;

        console.log("[MusicConductor] Disposed");
    }
}
