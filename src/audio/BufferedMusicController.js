/**
 * BufferedMusicController.js
 * 
 * A controller that adds a configurable buffer to audio timing to ensure
 * projectiles are perfectly synced with the music. This controller combines
 * the functionality of the ComprehensiveMusicController with precise timing adjustments.
 */

export class BufferedMusicController {
  /**
   * Constructor for BufferedMusicController
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      debug: options.debug !== undefined ? options.debug : true,
      // Buffer time in milliseconds - how far ahead to trigger patterns
      // This compensates for audio processing and rendering delays
      bufferTimeMs: options.bufferTimeMs !== undefined ? options.bufferTimeMs : 200,
      // Additional speed multiplier to make projectiles faster
      speedMultiplier: options.speedMultiplier !== undefined ? options.speedMultiplier : 1.2,
      ...options
    };
    
    // Data
    this.data = null;
    this.timeline = [];
    this.screenShakeTimestamps = [];
    
    // Current state
    this.currentTime = 0;
    this.bufferedTime = 0; // Time with buffer applied
    this.isPlaying = false;
    this.lastTimelineIndex = -1;
    this.lastShakeIndex = -1;
    
    // Callbacks
    this.onPatternChangeCallback = null;
    this.onScreenShakeCallback = null;
    
    // For time logging
    this._lastLoggedTime = -1;
    
    if (this.options.debug) {
      console.log(`[BufferedMusicController] Created with ${this.options.bufferTimeMs}ms buffer and ${this.options.speedMultiplier}x speed multiplier`);
    }
  }
  
  /**
   * Load comprehensive music data
   * @param {String} dataPath - Path to the comprehensive data JSON file
   * @returns {Promise} - Resolves when data is loaded
   */
  async loadData(dataPath) {
    try {
      const response = await fetch(dataPath);
      if (!response.ok) {
        throw new Error(`Failed to load data: ${response.status} ${response.statusText}`);
      }
      
      this.data = await response.json();
      this.timeline = this.data.timeline || [];
      this.screenShakeTimestamps = this.data.screenShakeTimestamps || [];
      
      if (this.options.debug) {
        console.log(`[BufferedMusicController] Loaded data with ${this.timeline.length} timeline entries and ${this.screenShakeTimestamps.length} screen shake timestamps`);
      }
      
      return true;
    } catch (error) {
      console.error("[BufferedMusicController] Error loading data:", error);
      return false;
    }
  }
  
  /**
   * Start the controller
   * @returns {Boolean} Success
   */
  start() {
    if (!this.data) {
      console.error("[BufferedMusicController] Cannot start: No data loaded");
      return false;
    }
    
    this.isPlaying = true;
    this.currentTime = 0;
    this.bufferedTime = 0;
    this.lastTimelineIndex = -1;
    this.lastShakeIndex = -1;
    
    if (this.options.debug) {
      console.log("[BufferedMusicController] Started");
    }
    
    return true;
  }
  
  /**
   * Stop the controller
   */
  stop() {
    this.isPlaying = false;
    
    if (this.options.debug) {
      console.log("[BufferedMusicController] Stopped");
    }
  }
  
  /**
   * Update the controller with the current music time
   * @param {Number} currentTime - Current music time in seconds
   */
  update(currentTime) {
    if (!this.isPlaying || !this.data) {
      return;
    }
    
    // Store previous time for comparison
    const previousTime = this.currentTime;
    
    // Update current time
    this.currentTime = currentTime;
    
    // Calculate buffered time (add buffer in seconds)
    this.bufferedTime = this.currentTime + (this.options.bufferTimeMs / 1000);
    
    // Log current time every 5 seconds for debugging
    if (Math.floor(currentTime) % 5 === 0 && Math.floor(currentTime) !== this._lastLoggedTime) {
      this._lastLoggedTime = Math.floor(currentTime);
      if (this.options.debug) {
        console.log(`[BufferedMusicController] Current music time: ${currentTime.toFixed(1)}s, Buffered time: ${this.bufferedTime.toFixed(1)}s`);
      }
    }
    
    // Check for timeline changes using buffered time
    this._checkTimeline();
    
    // Check for screen shake timestamps using actual time (not buffered)
    // We don't want to buffer screen shakes as they should be precisely timed with the music
    this._checkScreenShakes();
  }
  
  /**
   * Set callback for pattern changes
   * @param {Function} callback - Function to call when a pattern should change
   */
  setPatternChangeCallback(callback) {
    this.onPatternChangeCallback = callback;
  }
  
  /**
   * Set callback for screen shake events
   * @param {Function} callback - Function to call when a screen shake should occur
   */
  setScreenShakeCallback(callback) {
    this.onScreenShakeCallback = callback;
  }
  
  /**
   * Get the current timeline entry
   * @returns {Object|null} Current timeline entry or null
   */
  getCurrentTimelineEntry() {
    if (!this.timeline || this.timeline.length === 0) {
      return null;
    }
    
    for (let i = 0; i < this.timeline.length; i++) {
      const entry = this.timeline[i];
      if (this.bufferedTime >= entry.startTime && this.bufferedTime < entry.endTime) {
        return entry;
      }
    }
    
    return null;
  }
  
  /**
   * Check for timeline changes and trigger callbacks
   * @private
   */
  _checkTimeline() {
    if (!this.timeline || !this.onPatternChangeCallback) {
      return;
    }
    
    // Find current timeline entry using buffered time
    let currentIndex = -1;
    
    for (let i = 0; i < this.timeline.length; i++) {
      const entry = this.timeline[i];
      if (this.bufferedTime >= entry.startTime && this.bufferedTime < entry.endTime) {
        currentIndex = i;
        break;
      }
    }
    
    // If timeline entry changed
    if (currentIndex !== -1 && currentIndex !== this.lastTimelineIndex) {
      const entry = this.timeline[currentIndex];
      
      // Only trigger if we're close to the start of the entry
      // This prevents triggering when jumping to a new position
      if (Math.abs(this.bufferedTime - entry.startTime) < 0.1) {
        this._triggerPatternChange(entry);
      }
      
      this.lastTimelineIndex = currentIndex;
    }
    
    // Reset if we loop back (for looping music)
    if (this.lastTimelineIndex >= 0 && 
        this.bufferedTime < this.timeline[this.lastTimelineIndex].startTime) {
      this.lastTimelineIndex = -1;
    }
    
    // Check for pattern triggers within the current timeline entry
    if (currentIndex !== -1) {
      this._checkPatternTriggers(this.timeline[currentIndex]);
    }
  }
  
  /**
   * Check for pattern triggers within a timeline entry
   * @param {Object} entry - Timeline entry
   * @private
   */
  _checkPatternTriggers(entry) {
    // Calculate time within the current entry
    const entryTime = this.bufferedTime - entry.startTime;
    
    // Calculate if we should trigger a pattern based on the entry's trigger intervals
    const minInterval = entry.triggerIntervalMin || 1.0;
    const maxInterval = entry.triggerIntervalMax || 2.0;
    
    // Use a deterministic approach based on entry time
    // This ensures patterns are triggered at consistent times
    const interval = minInterval + (Math.sin(entryTime * 3.14159) * 0.5 + 0.5) * (maxInterval - minInterval);
    
    // Check if we should trigger a pattern
    if (Math.floor(entryTime / interval) !== Math.floor((entryTime - 0.016) / interval)) {
      this._triggerPatternChange(entry, true);
    }
  }
  
  /**
   * Trigger a pattern change
   * @param {Object} entry - Timeline entry
   * @param {Boolean} isIntervalTrigger - Whether this is triggered by an interval
   * @private
   */
  _triggerPatternChange(entry, isIntervalTrigger = false) {
    if (this.onPatternChangeCallback) {
      // Select a pattern from the entry's patterns
      // If there's only one pattern, always use that one
      // Otherwise, randomly select one from the array
      let pattern;
      if (entry.patterns.length === 1) {
        pattern = entry.patterns[0];
      } else {
        const patternIndex = Math.floor(Math.random() * entry.patterns.length);
        pattern = entry.patterns[patternIndex];
      }
      
      // Select a projectile type from the entry's projectile types
      let projectileType;
      if (entry.projectileTypes.length === 1) {
        projectileType = entry.projectileTypes[0];
      } else {
        const projectileIndex = Math.floor(Math.random() * entry.projectileTypes.length);
        projectileType = entry.projectileTypes[projectileIndex];
      }
      
      // Apply the speed multiplier to make projectiles faster
      const adjustedSpeedMultiplier = entry.speedMultiplier * this.options.speedMultiplier;
      
      this.onPatternChangeCallback({
        pattern,
        projectileType,
        intensity: entry.intensity,
        speedMultiplier: adjustedSpeedMultiplier,
        fromTimeline: true
      });
      
      if (this.options.debug) {
        console.log(`[BufferedMusicController] Pattern change triggered at ${this.currentTime.toFixed(2)}s (buffered: ${this.bufferedTime.toFixed(2)}s): ${pattern} (${entry.description}), speed: ${adjustedSpeedMultiplier.toFixed(2)}x`);
      }
    }
  }
  
  /**
   * Check for screen shake timestamps and trigger callbacks
   * @private
   */
  _checkScreenShakes() {
    if (!this.screenShakeTimestamps || !this.onScreenShakeCallback) {
      return;
    }
    
    // Check each timestamp using actual time (not buffered)
    for (let i = 0; i < this.screenShakeTimestamps.length; i++) {
      const timestamp = this.screenShakeTimestamps[i];
      
      // If we just passed this timestamp (within a small tolerance)
      if (this.currentTime >= timestamp.time && 
          this.currentTime < timestamp.time + 0.1 && 
          i > this.lastShakeIndex) {
        
        // Trigger the screen shake
        this._triggerScreenShake(timestamp);
        this.lastShakeIndex = i;
        
        // Only trigger one shake per update to avoid multiple shakes
        break;
      }
    }
    
    // Reset processed index if we loop back (for looping music)
    if (this.lastShakeIndex >= 0 && 
        this.currentTime < this.screenShakeTimestamps[this.lastShakeIndex].time) {
      this.lastShakeIndex = -1;
    }
  }
  
  /**
   * Trigger a screen shake
   * @param {Object} timestamp - Timestamp data
   * @private
   */
  _triggerScreenShake(timestamp) {
    if (this.onScreenShakeCallback) {
      this.onScreenShakeCallback(
        timestamp.intensity || 0.25, 
        timestamp.duration || 300
      );
      
      if (this.options.debug) {
        console.log(`[BufferedMusicController] Screen shake triggered at ${timestamp.time.toFixed(2)}s`);
      }
    }
  }
}
