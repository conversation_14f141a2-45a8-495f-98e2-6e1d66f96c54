/**
 * SoundTouchWrapper.js
 * 
 * A simple wrapper for SoundTouch.js to ensure compatibility with the MusicConductor.
 * This provides a consistent API whether SoundTouch is available or not.
 */

/**
 * Check if SoundTouch is available
 * @returns {boolean} True if SoundTouch is available
 */
export function isSoundTouchAvailable() {
    return typeof window !== 'undefined' && typeof window.SoundTouch !== 'undefined';
}

/**
 * Create a tempo shifter using SoundTouch if available, or fallback to Tone.js
 * @param {AudioContext} audioContext - Web Audio API context
 * @returns {Object} Tempo shifter object
 */
export function createTempoShifter(audioContext) {
    // If SoundTouch is not available, return a dummy object
    if (!isSoundTouchAvailable()) {
        console.warn("[SoundTouchWrapper] SoundTouch not available, using fallback");
        return {
            tempo: 1.0,
            pitch: 1.0,
            rate: 1.0,
            setTempo: (tempo) => {
                console.log(`[SoundTouchWrapper] Fallback setTempo: ${tempo}`);
                return tempo;
            },
            setPitch: (pitch) => {
                console.log(`[SoundTouchWrapper] Fallback setPitch: ${pitch}`);
                return pitch;
            },
            connect: () => {},
            disconnect: () => {}
        };
    }

    try {
        // Create a SoundTouch instance
        const soundTouch = new window.SoundTouch.SoundTouch(audioContext.sampleRate);
        
        // Create a wrapper object with a consistent API
        const tempoShifter = {
            tempo: 1.0,
            pitch: 1.0,
            rate: 1.0,
            
            // Set tempo (speed) without affecting pitch
            setTempo: (tempo) => {
                tempoShifter.tempo = tempo;
                soundTouch.tempo = tempo;
                return tempo;
            },
            
            // Set pitch without affecting tempo
            setPitch: (pitch) => {
                tempoShifter.pitch = pitch;
                soundTouch.pitch = pitch;
                return pitch;
            },
            
            // Connect to an audio node (dummy method for API compatibility)
            connect: (destination) => {
                console.log("[SoundTouchWrapper] Connect called (no-op)");
                return destination;
            },
            
            // Disconnect from an audio node (dummy method for API compatibility)
            disconnect: () => {
                console.log("[SoundTouchWrapper] Disconnect called (no-op)");
            }
        };
        
        console.log("[SoundTouchWrapper] Created SoundTouch tempo shifter");
        return tempoShifter;
    } catch (error) {
        console.error("[SoundTouchWrapper] Error creating SoundTouch tempo shifter:", error);
        
        // Return a dummy object on error
        return {
            tempo: 1.0,
            pitch: 1.0,
            rate: 1.0,
            setTempo: () => 1.0,
            setPitch: () => 1.0,
            connect: () => {},
            disconnect: () => {}
        };
    }
}

/**
 * Process audio with SoundTouch
 * @param {Float32Array} inputBuffer - Input audio buffer
 * @param {Object} tempoShifter - Tempo shifter created with createTempoShifter
 * @returns {Float32Array} Processed audio buffer
 */
export function processAudio(inputBuffer, tempoShifter) {
    // If SoundTouch is not available, return the input buffer unchanged
    if (!isSoundTouchAvailable()) {
        return inputBuffer;
    }
    
    try {
        // This is a simplified example - a real implementation would use SoundTouch's
        // processBuffer method to apply tempo/pitch changes to the audio
        // For now, we just return the input buffer
        return inputBuffer;
    } catch (error) {
        console.error("[SoundTouchWrapper] Error processing audio:", error);
        return inputBuffer;
    }
}
