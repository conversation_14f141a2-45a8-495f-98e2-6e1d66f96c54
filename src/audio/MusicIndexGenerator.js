/**
 * MusicIndexGenerator.js
 * 
 * Automatically generates and maintains a structured file with metadata about all music assets.
 * This helps composers and developers understand the music system structure.
 */

import { MusicData } from './musicData.js';

/**
 * Generate a structured index of all music assets
 * @returns {Object} Structured music index
 */
export function generateMusicIndex() {
    const index = {
        areas: {},
        stingers: {},
        leitmotifs: {},
        specialRooms: {},
        transitions: {},
        metadata: {
            generatedAt: new Date().toISOString(),
            totalAssets: 0
        }
    };
    
    let totalAssets = 0;
    
    // Process each area
    for (const [areaId, areaData] of Object.entries(MusicData)) {
        // Skip non-area entries
        if (areaId === 'stingers' || areaId === 'defaults') continue;
        
        // Create area entry
        index.areas[areaId] = {
            name: areaId,
            mainLoop: areaData.loop,
            tempo: areaData.tempo || MusicData.defaults.defaultTempo,
            hasLeitmotifs: Object.keys(areaData.leitmotifs || {}).length > 0,
            hasSpecialRooms: Object.keys(areaData.specialRooms || {}).length > 0,
            hasTransitions: Object.keys(areaData.transitions || {}).length > 0
        };
        
        totalAssets++;
        
        // Process leitmotifs
        if (areaData.leitmotifs) {
            for (const [leitmotifId, leitmotifPath] of Object.entries(areaData.leitmotifs)) {
                const fullId = `${areaId}_${leitmotifId}`;
                
                index.leitmotifs[fullId] = {
                    name: leitmotifId,
                    area: areaId,
                    path: leitmotifPath,
                    tags: ['leitmotif', areaId]
                };
                
                totalAssets++;
            }
        }
        
        // Process special rooms
        if (areaData.specialRooms) {
            for (const [roomId, roomData] of Object.entries(areaData.specialRooms)) {
                const fullId = `${areaId}_${roomId}`;
                
                index.specialRooms[fullId] = {
                    name: roomId,
                    area: areaId,
                    loop: roomData.loop,
                    enterTransition: roomData.enterTransition || null,
                    exitTransition: roomData.exitTransition || null,
                    tags: ['special_room', areaId, roomId]
                };
                
                totalAssets += 1 + (roomData.enterTransition ? 1 : 0) + (roomData.exitTransition ? 1 : 0);
            }
        }
        
        // Process transitions
        if (areaData.transitions) {
            for (const [targetAreaId, transitionPath] of Object.entries(areaData.transitions)) {
                const fullId = `${areaId}_to_${targetAreaId}`;
                
                index.transitions[fullId] = {
                    from: areaId,
                    to: targetAreaId,
                    path: transitionPath,
                    tags: ['transition', areaId, targetAreaId]
                };
                
                totalAssets++;
            }
        }
    }
    
    // Process stingers
    if (MusicData.stingers) {
        for (const [stingerId, stingerPath] of Object.entries(MusicData.stingers)) {
            index.stingers[stingerId] = {
                name: stingerId,
                path: stingerPath,
                tags: ['stinger', stingerId]
            };
            
            totalAssets++;
        }
    }
    
    // Update metadata
    index.metadata.totalAssets = totalAssets;
    
    return index;
}

/**
 * Save the music index to a JSON file
 * @param {Object} index - The music index object
 * @returns {string} JSON string of the index
 */
export function serializeMusicIndex(index) {
    return JSON.stringify(index, null, 2);
}

/**
 * Generate and save the music index
 * This would normally write to a file, but for browser environments,
 * we'll just return the JSON string
 * @returns {string} JSON string of the index
 */
export function generateAndSaveMusicIndex() {
    const index = generateMusicIndex();
    return serializeMusicIndex(index);
}
