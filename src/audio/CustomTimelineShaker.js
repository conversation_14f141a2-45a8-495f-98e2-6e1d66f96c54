/**
 * CustomTimelineShaker.js
 * 
 * A specialized module that triggers screen shakes only at specific timestamp transitions
 * in the catacomb boss timeline.
 */

export class CustomTimelineShaker {
    /**
     * Constructor for CustomTimelineShaker
     * @param {Object} options - Configuration options
     */
    constructor(options = {}) {
        // Store the dungeon handler for camera shake access
        this.dungeonHandler = options.dungeonHandler || null;
        
        // Debug mode
        this.debug = options.debug !== undefined ? options.debug : true;
        
        // Last processed time to avoid duplicate shakes
        this.lastProcessedTime = -1;
        
        // Define the exact timestamps where shakes should occur (from user's timeline)
        this.shakeTimestamps = [
            0, 4, 7, 9, 13, 16, 20, 24, 37, 42, 45, 48, 51, 64, 70, 76, 83, 90, 96, 
            103, 106, 109, 118, 131, 135, 138, 140, 144, 150, 158, 163, 170, 173, 176, 
            189, 192, 198, 202, 208, 211, 220, 224
        ];
        
        // Shake intensity and duration
        this.shakeIntensity = options.shakeIntensity || 0.25; // Default intensity
        this.shakeDuration = options.shakeDuration || 300; // Default duration in ms
        
        if (this.debug) {
            console.log("[CustomTimelineShaker] Created with", this.shakeTimestamps.length, "shake points");
        }
    }
    
    /**
     * Update the shaker based on current music time
     * @param {Number} time - Current music time in seconds
     */
    update(time) {
        // Round to 1 decimal place for more precise comparison
        const roundedTime = Math.round(time * 10) / 10;
        
        // Skip if we've already processed this time
        if (roundedTime === this.lastProcessedTime) {
            return;
        }
        
        // Check if current time is at or very close to any shake timestamp
        for (const timestamp of this.shakeTimestamps) {
            // Allow a small tolerance (0.1 seconds) for timing precision
            if (Math.abs(roundedTime - timestamp) <= 0.1) {
                this._triggerShake(timestamp);
                this.lastProcessedTime = roundedTime;
                break;
            }
        }
    }
    
    /**
     * Trigger a camera shake at the specified timestamp
     * @param {Number} timestamp - The timestamp that triggered the shake
     * @private
     */
    _triggerShake(timestamp) {
        if (!this.dungeonHandler || !this.dungeonHandler.cameraShake) {
            if (this.debug) {
                console.log("[CustomTimelineShaker] Cannot shake camera - dungeonHandler not available");
            }
            return;
        }
        
        if (this.debug) {
            console.log(`[CustomTimelineShaker] Triggering shake at timestamp ${timestamp}s`);
        }
        
        // Trigger the camera shake
        this.dungeonHandler.cameraShake(this.shakeIntensity, this.shakeDuration);
    }
    
    /**
     * Set the dungeon handler reference
     * @param {Object} dungeonHandler - The dungeon handler instance
     */
    setDungeonHandler(dungeonHandler) {
        this.dungeonHandler = dungeonHandler;
    }
}
