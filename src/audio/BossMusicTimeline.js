/**
 * BossMusicTimeline.js
 *
 * Handles timeline-based pattern triggering for boss battles.
 * This system allows for precise control over which patterns and projectiles
 * are used at specific time points in the music.
 */

export class BossMusicTimeline {
    /**
     * Constructor for BossMusicTimeline
     * @param {Object} options - Configuration options
     */
    constructor(options = {}) {
        // Timeline entries
        this.timeline = [];

        // Current state
        this.currentTime = 0;
        this.currentEntry = null;
        this.currentPattern = null;
        this.currentProjectileType = null;
        this.lastTriggerTime = 0;
        this.nextTriggerTime = 0;

        // Debug mode - enable by default for troubleshooting
        this.debug = options.debug !== undefined ? options.debug : true;

        // For time logging
        this._lastLoggedTime = -1;

        // Callbacks
        this.onPatternTrigger = null;
        this.onTimelineStart = null;
        this.onTimelineEnd = null;
        this.onTimelineEntryChange = null;

        if (this.debug) {
            console.log("[BossMusicTimeline] Created");
        }
    }

    /**
     * Load a timeline configuration
     * @param {Array} timeline - Array of timeline entries
     */
    loadTimeline(timeline) {
        if (!Array.isArray(timeline)) {
            console.error("[BossMusicTimeline] Invalid timeline format");
            return;
        }

        // Sort timeline by startTime
        this.timeline = [...timeline].sort((a, b) => a.startTime - b.startTime);

        // Reset state
        this.currentTime = 0;
        this.currentEntry = null;
        this.currentPattern = null;
        this.currentProjectileType = null;
        this.lastTriggerTime = 0;
        this.nextTriggerTime = 0;

        // Initialize with first entry's pattern if available
        if (this.timeline.length > 0) {
            const firstEntry = this.timeline[0];
            if (firstEntry.patterns && firstEntry.patterns.length > 0) {
                this.currentPattern = firstEntry.patterns[0];
            }
            if (firstEntry.projectileTypes && firstEntry.projectileTypes.length > 0) {
                this.currentProjectileType = firstEntry.projectileTypes[0];
            } else {
                this.currentProjectileType = 'shadow_bolt'; // Default
            }
        }

        if (this.debug) {
            console.log(`[BossMusicTimeline] Loaded timeline with ${this.timeline.length} entries`);
        }
    }

    /**
     * Update the timeline based on current music time
     * @param {Number} time - Current music time in seconds
     */
    update(time) {
        // Store previous entry for change detection
        const previousEntry = this.currentEntry;

        // Update current time
        this.currentTime = time;

        // Log current time every 5 seconds for debugging
        if (Math.floor(time) % 5 === 0 && Math.floor(time) !== this._lastLoggedTime) {
            this._lastLoggedTime = Math.floor(time);
            console.log(`[BossMusicTimeline] Current music time: ${time.toFixed(1)}s`);
        }

        // Find the current timeline entry
        this.currentEntry = this._findCurrentEntry(time);

        // Debug log the search result
        if (this.debug && !this.currentEntry) {
            console.log(`[BossMusicTimeline] No timeline entry found for time ${time.toFixed(1)}s`);
        }

        // Check if entry changed
        if (previousEntry !== this.currentEntry) {
            if (this.currentEntry) {
                // Reset trigger time when entry changes
                this.lastTriggerTime = time;
                this._calculateNextTriggerTime();

                // Set the current pattern and projectile type immediately when entry changes
                // This ensures they're available for music sync even before the first trigger
                if (this.currentEntry.patterns && this.currentEntry.patterns.length > 0) {
                    // If there's only one pattern, use that
                    if (this.currentEntry.patterns.length === 1) {
                        this.currentPattern = this.currentEntry.patterns[0];
                    } else {
                        // Otherwise, randomly select one
                        const patternIndex = Math.floor(Math.random() * this.currentEntry.patterns.length);
                        this.currentPattern = this.currentEntry.patterns[patternIndex];
                    }
                }

                // Set the current projectile type
                if (this.currentEntry.projectileTypes && this.currentEntry.projectileTypes.length > 0) {
                    // If there's only one type, use that
                    if (this.currentEntry.projectileTypes.length === 1) {
                        this.currentProjectileType = this.currentEntry.projectileTypes[0];
                    } else {
                        // Otherwise, randomly select one
                        const typeIndex = Math.floor(Math.random() * this.currentEntry.projectileTypes.length);
                        this.currentProjectileType = this.currentEntry.projectileTypes[typeIndex];
                    }
                } else {
                    // Default to shadow_bolt if no projectile types specified
                    this.currentProjectileType = 'shadow_bolt';
                }

                // Log the entry change with pattern and projectile type
                console.log(`[BossMusicTimeline] Entered new section: ${this.currentEntry.description || 'Unnamed'} ` +
                           `(${this.currentEntry.startTime}s - ${this.currentEntry.endTime}s) ` +
                           `Pattern: ${this.currentPattern}, Projectile: ${this.currentProjectileType}`);

                // Notify about entry change
                if (this.onTimelineEntryChange) {
                    this.onTimelineEntryChange(this.currentEntry);
                }

                if (this.debug) {
                    console.log(`[BossMusicTimeline] Entered new section: ${this.currentEntry.description || 'Unnamed'} ` +
                               `(${this.currentEntry.startTime}s - ${this.currentEntry.endTime}s) ` +
                               `Pattern: ${this.currentPattern}, Projectile: ${this.currentProjectileType}`);
                }
            }
        }

        // Check if we should trigger a pattern
        if (this.currentEntry && time >= this.nextTriggerTime) {
            this._triggerPattern(this.currentEntry);
            this.lastTriggerTime = time;
            this._calculateNextTriggerTime();
        }

        // Check for timeline start
        if (previousEntry === null && this.currentEntry !== null) {
            if (this.onTimelineStart) {
                this.onTimelineStart();
            }
        }

        // Check for timeline end
        if (previousEntry !== null && this.currentEntry === null && this.timeline.length > 0) {
            if (this.onTimelineEnd) {
                this.onTimelineEnd();
            }
        }
    }

    /**
     * Find the current timeline entry based on time
     * @param {Number} time - Current time in seconds
     * @returns {Object|null} Current timeline entry or null if not found
     * @private
     */
    _findCurrentEntry(time) {
        let foundEntry = null;

        for (const entry of this.timeline) {
            if (time >= entry.startTime && time < entry.endTime) {
                foundEntry = entry;
                break;
            }
        }

        // Log entry changes for debugging
        if (foundEntry && (!this.currentEntry || foundEntry !== this.currentEntry)) {
            console.log(`[BossMusicTimeline] Found new timeline entry at ${time.toFixed(1)}s: ` +
                       `${foundEntry.description} (${foundEntry.startTime}s-${foundEntry.endTime}s)`);

            // Log all available entries for debugging
            if (this.debug) {
                console.log("[BossMusicTimeline] Available timeline entries:");
                this.timeline.forEach((e, i) => {
                    console.log(`  ${i+1}. ${e.description} (${e.startTime}s-${e.endTime}s)`);
                });
            }
        } else if (!foundEntry && this.debug && time > 5) {
            // Only log after 5 seconds to avoid spam during initialization
            console.log(`[BossMusicTimeline] No timeline entry found for time ${time.toFixed(1)}s`);
        }

        return foundEntry;
    }

    /**
     * Calculate the next time to trigger a pattern
     * @private
     */
    _calculateNextTriggerTime() {
        if (!this.currentEntry) return;

        const { triggerIntervalMin, triggerIntervalMax } = this.currentEntry;

        // Default intervals if not specified
        const minInterval = triggerIntervalMin !== undefined ? triggerIntervalMin : 1.0;
        const maxInterval = triggerIntervalMax !== undefined ? triggerIntervalMax : 2.0;

        // Calculate random interval
        const interval = minInterval + Math.random() * (maxInterval - minInterval);

        // Set next trigger time
        this.nextTriggerTime = this.lastTriggerTime + interval;

        if (this.debug) {
            console.log(`[BossMusicTimeline] Next pattern trigger at ${this.nextTriggerTime.toFixed(2)}s (interval: ${interval.toFixed(2)}s)`);
        }
    }

    /**
     * Trigger a pattern from a timeline entry
     * @param {Object} entry - Timeline entry
     * @private
     */
    _triggerPattern(entry) {
        console.log(`[BossMusicTimeline] _triggerPattern called with entry:`, JSON.stringify(entry));
        if (!this.onPatternTrigger) return;

        // Select a pattern from the entry's patterns
        // If there's only one pattern, always use that one
        // Otherwise, randomly select one from the array
        let pattern;
        if (entry.patterns.length === 1) {
            pattern = entry.patterns[0];
        } else {
            const patternIndex = Math.floor(Math.random() * entry.patterns.length);
            pattern = entry.patterns[patternIndex];
        }

        // Select a projectile type from the entry's projectileTypes
        // If there's only one type, always use that one
        // Otherwise, randomly select one from the array
        let projectileType;
        if (!entry.projectileTypes || entry.projectileTypes.length === 0) {
            // Default to shadow_bolt if no projectile types specified
            projectileType = 'shadow_bolt';
        } else if (entry.projectileTypes.length === 1) {
            projectileType = entry.projectileTypes[0];
        } else {
            const typeIndex = Math.floor(Math.random() * entry.projectileTypes.length);
            projectileType = entry.projectileTypes[typeIndex];
        }

        // Store the current pattern and projectile type for this timeline entry
        this.currentPattern = pattern;
        this.currentProjectileType = projectileType;

        // Log the pattern trigger for debugging
        if (this.debug) {
            console.log(`[BossMusicTimeline] Triggering pattern: ${pattern}, projectile: ${projectileType} at ${this.currentTime.toFixed(1)}s`);
        }

        // Trigger the pattern
        this.onPatternTrigger({
            pattern,
            projectileType,
            intensity: entry.intensity || 0.5,
            speedMultiplier: entry.speedMultiplier || 1.0,
            fromTimeline: true // Flag to indicate this came from the timeline
        });

        if (this.debug) {
            console.log(`[BossMusicTimeline] Triggered pattern ${pattern} with projectile ${projectileType} at ${this.currentTime.toFixed(2)}s`);
        }
    }

    /**
     * Set callback for pattern triggers
     * @param {Function} callback - Function to call when a pattern should be triggered
     */
    setPatternTriggerCallback(callback) {
        this.onPatternTrigger = callback;
    }

    /**
     * Set callback for timeline start
     * @param {Function} callback - Function to call when the timeline starts
     */
    setTimelineStartCallback(callback) {
        this.onTimelineStart = callback;
    }

    /**
     * Set callback for timeline end
     * @param {Function} callback - Function to call when the timeline ends
     */
    setTimelineEndCallback(callback) {
        this.onTimelineEnd = callback;
    }

    /**
     * Set callback for timeline entry changes
     * @param {Function} callback - Function to call when the timeline entry changes
     */
    setTimelineEntryChangeCallback(callback) {
        this.onTimelineEntryChange = callback;
    }

    /**
     * Get information about the current timeline state
     * @returns {Object} Timeline information
     */
    getTimelineInfo() {
        const totalDuration = this.timeline.length > 0 ?
            this.timeline[this.timeline.length - 1].endTime : 0;

        return {
            currentTime: this.currentTime,
            currentEntry: this.currentEntry,
            progress: totalDuration > 0 ? this.currentTime / totalDuration : 0,
            totalDuration,
            nextTriggerTime: this.nextTriggerTime,
            currentPattern: this.currentPattern,
            currentProjectileType: this.currentProjectileType
        };
    }
}
