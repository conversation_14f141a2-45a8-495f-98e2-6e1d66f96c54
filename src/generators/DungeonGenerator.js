// src/generators/DungeonGenerator.js
import * as THREE from 'three'; // Import THREE namespace
// Room class is no longer imported, define it internally
// import { Room } from './Room.js'; 
// Remove incorrect imports - Tile is likely defined elsewhere or no longer used directly here
// import { Tile } from './Tile.js'; 
// Import theme/area helpers
// import { getThemeData, getRandomEnemyList } from './AreaThemes.js'; // OLD
import { getAreaData } from '../gameData/areas.js'; // NEW

// --- Constants ---
const MIN_ROOMS = 8;       // Minimum desired rooms (for check)
const MAX_ROOMS = 25;      // Target/Maximum number of rooms to generate
const MAX_PLACEMENT_ATTEMPTS = 100; // Attempts to find a spot for a new room
const MAX_GENERATION_RETRIES = 60; // Max attempts to regenerate if below MIN_ROOMS

// Add this new constant for room shapes
const ROOM_SHAPES = [
    'SQUARE_1X1',    // Default square shape
    'L_SHAPE',       // L-shaped room
    'T_SHAPE',       // T-shaped room
    'RECTANGULAR',   // Rectangular room
    'CROSS_SHAPE',   // Cross-shaped room
    'BOSS_ARENA'     // Special boss arena shape
    // 'CIRCULAR'       // Circular room (REMOVED)
];

// --- Room Class (Internal Helper for Generator Graph) ---
class Room {
    constructor(id) {
        this.id = id; // Unique numerical ID (e.g., 0, 1, 2...)
        this.type = 'Normal'; // Type: 'Start', 'Normal', 'Boss', 'Item', 'Shop', etc.
        this.shapeKey = 'SQUARE_1X1'; // <<< NEW: Add shape identifier
        this.neighbors = { n: null, s: null, e: null, w: null }; // Stores IDs of connected rooms
        this.coords = { x: 0, y: 0 }; // Store grid coords for layout visualization/distance calculation
        this.visited = false; 
        
        // Initialize roomData object which will be stored in the final layout map
        this.roomData = { 
            id: this.id, 
            type: this.type, 
            shapeKey: this.shapeKey, // <<< Add shapeKey to roomData
            connections: this.neighbors, // <<< USE 'connections' key
            coords: this.coords,
            visited: this.visited, // Store visited status here too?
            state: { // Initialize state structure
                enemiesCleared: false,
                initialEnemies: [],
                area: null // Will be set later
            },
            // Helper function to get center (assuming fixed room size for now)
            // This might belong better directly in DungeonHandler or use theme data?
            getCenter: function() { 
                const ROOM_WORLD_SIZE = 14; // TODO: Get this from theme or config?
                // Using grid coords directly might be wrong if grid isn't world coords
                // Assuming center of the *visual* room for now
                return { x: 0, y: 0, z: 0 }; // Placeholder - needs correct logic based on how rooms are positioned
            }
        };
    }

    // Check how many connections this room has
    getConnectionCount() {
        return Object.values(this.neighbors).filter(n => n !== null).length;
    }

    // Sync basic properties to roomData before returning layout?
    // Call this before adding roomData to floorLayout?
    syncRoomData() {
        this.roomData.id = this.id;
        this.roomData.type = this.type;
        this.roomData.shapeKey = this.shapeKey; // <<< Sync shapeKey
        this.roomData.connections = this.neighbors; // <<< USE 'connections' key
        this.roomData.coords = this.coords; // Reference same object
        this.roomData.visited = this.visited;
        // State is managed separately
    }
}

// Helper function for random integer in a range
function randInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Helper to get a random available direction from a room
function getRandomAvailableDirection(room, layout) {
    const directions = ['n', 's', 'e', 'w'];
    const available = directions.filter(dir => room.neighbors[dir] === null);
    if (available.length === 0) return null;
    return available[Math.floor(Math.random() * available.length)];
}

// Helper to get opposite direction
function getOppositeDirection(dir) {
    switch (dir) {
        case 'n': return 's';
        case 's': return 'n';
        case 'e': return 'w';
        case 'w': return 'e';
        default: return null;
    }
}

// --- DungeonGenerator Class (Graph-based) ---
class DungeonGenerator {
    constructor() { // Removed seed parameter
        // this.seed = seed; // Seed not currently used
        this.layout = new Map(); // Map<RoomID, Room Instance>
        this.nextRoomId = 0;
        // Keep track of room positions for placement logic
        this.roomPositions = new Set(); // Stores "x,y" strings
        // this.grid = []; // Grid representation not currently used in this version
        this.rooms = []; // List of Room instances (used for length check?)
        this.floorLayout = new Map(); // Map<RoomID, RoomData Object> - This is returned
        // this.currentTheme = null; // Store the current theme data - Renamed
        this.currentArea = null; // Store the current area data
        this.areaId = null; // Store the current area ID
    }

    _addRoom(x, y, type = 'Normal') {
        const roomId = this.nextRoomId++;
        const newRoom = new Room(roomId); // Use the internal Room class
        newRoom.type = type;
        newRoom.coords = { x, y }; // Set grid coordinates
        
        // --- Assign Room Shape based on Area --- 
        let assignedShape = 'SQUARE_1X1'; // Default shape
        if (type === 'Normal' && this.currentArea && this.currentArea.allowedShapes && this.currentArea.allowedShapes.length > 0) {
            // Select a random shape from the area's allowed list
            const shapes = this.currentArea.allowedShapes;
            assignedShape = shapes[Math.floor(Math.random() * shapes.length)];
            // console.log(` -> Room ${roomId}: Assigned shape '${assignedShape}' from area '${this.areaId}'`);
        } else {
            // Keep special rooms (Start, Boss) or rooms in areas without defined shapes as squares
            assignedShape = 'SQUARE_1X1';
            // if (type === 'Normal') {
            //     console.warn(` -> Room ${roomId}: Area '${this.areaId}' has no allowedShapes. Defaulting to '${assignedShape}'.`);
            // }
        }
        newRoom.shapeKey = assignedShape;
        // --- End Shape Assignment ---
        
        newRoom.roomData.type = type; // Sync type to roomData
        newRoom.roomData.coords = newRoom.coords; // Sync coords to roomData
        newRoom.roomData.shapeKey = newRoom.shapeKey; // Sync shapeKey to roomData

        // Remove incorrect boundary check based on undefined width/depth
        /*
        // Check boundaries
        if (x + newRoom.width >= this.gridSize || y + newRoom.depth >= this.gridSize || x < 0 || y < 0) {
            console.warn(`Room placement failed: Out of bounds at ${x},${y}`);
            return null;
        }
        */

        // --- Setup roomData State --- 
        let initialEnemies = [];
        // Populate enemies based on area for non-Start/Boss rooms
        if (type === 'Normal' && this.currentArea && this.currentArea.enemies && this.currentArea.enemies.length > 0) {
             // Simple weighted random selection (example: choose 1-3 enemies)
             const numEnemies = randInt(1, 3);
             const enemyPool = this.currentArea.enemies;
             const totalWeight = enemyPool.reduce((sum, enemy) => sum + enemy.weight, 0);

             if (totalWeight > 0) {
                 for (let i = 0; i < numEnemies; i++) {
                     let randomWeight = Math.random() * totalWeight;
                     for (const enemy of enemyPool) {
                         randomWeight -= enemy.weight;
                         if (randomWeight <= 0) {
                             initialEnemies.push(enemy.type); // Add the chosen enemy type string
                             break;
                         }
                     }
                 }
             }
             // console.log(`   - Room ${newRoom.id} (${type}) enemies: [${initialEnemies.join(', ')}] (Area: ${this.areaId})`);
        }

        newRoom.roomData.state = {
            enemiesCleared: false,
            initialEnemies: initialEnemies, 
            // theme: this.themeName // OLD: Store the theme name for the handler
            area: this.areaId // NEW: Store the area ID
        };
        // --- End State Setup ---

        this.layout.set(roomId, newRoom); // Store Room instance internally
        this.roomPositions.add(`${x},${y}`);
        this.rooms.push(newRoom);

        // Sync basic Room properties to roomData before storing in floorLayout
        newRoom.syncRoomData(); // Make sure roomData has latest ID, type, neighbors, coords, shape

        this.floorLayout.set(newRoom.id, newRoom.roomData); // Store final RoomData object
        return newRoom; // Return the Room instance for graph building
    }

    _canPlaceRoom(x, y) {
        // Check if the coordinate is already occupied
        return !this.roomPositions.has(`${x},${y}`);
    }

    // --- Main Generation Method (Growing Tree Algorithm) ---
    generateLayout(areaId = 'catacombs', _retryCount = 0) { // NEW
        
        // --- Retry Limit Check --- 
        if (_retryCount > MAX_GENERATION_RETRIES) {
            console.error(`DungeonGenerator: CRITICAL - Exceeded max retries (${MAX_GENERATION_RETRIES}) trying to generate at least ${MIN_ROOMS} rooms. Returning last attempt with ${this.layout.size} rooms.`);
            // Return the potentially too-small layout to prevent infinite loop
            return this.floorLayout; 
        }
        // --- End Retry Limit Check ---
        
        // --- Calculate Target Room Count for this specific generation --- 
        const currentTargetRooms = randInt(MIN_ROOMS, MAX_ROOMS);
        // console.log(`--- Generating Dungeon Layout (Theme: ${themeName}, Attempt: ${_retryCount + 1}, Target Rooms: ${currentTargetRooms}) ---`); // OLD
        console.log(`--- Generating Dungeon Layout (Area: ${areaId}, Attempt: ${_retryCount + 1}, Target Rooms: ${currentTargetRooms}) ---`); // NEW
        // -------------------------------------------------------------
        
        // this.themeName = themeName; // Store the theme name - OLD
        this.areaId = areaId; // Store the area ID - NEW
        // this.currentTheme = getThemeData(themeName); // OLD
        this.currentArea = getAreaData(areaId); // NEW
        // if (!this.currentTheme) { // OLD
        if (!this.currentArea) { // NEW
            // console.error(`Error: Theme "${themeName}" not found! Defaulting...`); // OLD
            console.error(`Error: Area "${areaId}" not found! Defaulting...`); // NEW
            // this.themeName = 'CATACUMBS'; // Fallback? - OLD
            this.areaId = 'catacombs'; // Fallback - NEW
            // this.currentTheme = getThemeData(this.themeName); // OLD
            this.currentArea = getAreaData(this.areaId); // NEW
            // if (!this.currentTheme) { // OLD
            if (!this.currentArea) { // NEW
                throw new Error("Default area 'catacombs' also not found!");
            }
        }

        console.log("DungeonGenerator: Starting graph generation (Growing Tree)...");
        // Reset state for new generation
        this.layout.clear();
        this.floorLayout.clear();
        this.roomPositions.clear();
        this.rooms = [];
        this.nextRoomId = 0;

        const directions = {
            n: { x: 0, y: -1 }, // Assuming Y decreases going North
            s: { x: 0, y: 1 },  // Assuming Y increases going South
            e: { x: 1, y: 0 },
            w: { x: -1, y: 0 },
        };

        // 1. Create the starting room
        const startRoom = this._addRoom(0, 0, 'Start');
        startRoom.visited = true;
        startRoom.roomData.visited = true; // Sync visited status
        const activeRooms = [startRoom]; // List of rooms to expand from

        let attempts = 0;
        const maxTotalAttempts = MAX_PLACEMENT_ATTEMPTS * currentTargetRooms; // Adjust attempt limit based on target?

        // 2. Grow the dungeon tree - Use currentTargetRooms in condition
        while (activeRooms.length > 0 && this.layout.size < currentTargetRooms && attempts < maxTotalAttempts) {
            attempts++;
            // Choose a room to expand from (newest for longer corridors)
            const currentRoom = activeRooms[activeRooms.length - 1];

            // Find a random available direction from the current room
            const availableDirections = Object.keys(directions).filter(dir => {
                const nx = currentRoom.coords.x + directions[dir].x;
                const ny = currentRoom.coords.y + directions[dir].y;
                return this._canPlaceRoom(nx, ny); // Check if the target cell is empty
            });

            if (availableDirections.length > 0) {
                // Pick a random direction
                const chosenDir = availableDirections[Math.floor(Math.random() * availableDirections.length)];
                const oppositeDir = getOppositeDirection(chosenDir);

                // Coordinates for the new room
                const newX = currentRoom.coords.x + directions[chosenDir].x;
                const newY = currentRoom.coords.y + directions[chosenDir].y;

                // Create the new room
                const newRoom = this._addRoom(newX, newY);

                // Connect the rooms (both Room instance and roomData neighbors)
                currentRoom.neighbors[chosenDir] = newRoom.id;
                newRoom.neighbors[oppositeDir] = currentRoom.id;
                // Ensure roomData neighbors are also updated (they should reference the same object)
                // currentRoom.roomData.neighbors[chosenDir] = newRoom.id; 
                // newRoom.roomData.neighbors[oppositeDir] = currentRoom.id;

                // Add the new room to the active list
                activeRooms.push(newRoom);
            } else {
                // If no available directions, backtrack
                activeRooms.pop();
            }
        }

        // --- Safeguard: Ensure Start Room (ID 0) has an exit if possible --- 
        const startRoomInstance = this.layout.get(0);
        if (startRoomInstance && startRoomInstance.getConnectionCount() === 0 && MAX_ROOMS >= 2) {
            console.warn(`DungeonGenerator: Start Room 0 has no connections after initial generation. Attempting to force one...`);
            let forcedConnection = false;
            const directions = {
                n: { x: 0, y: -1 }, s: { x: 0, y: 1 }, e: { x: 1, y: 0 }, w: { x: -1, y: 0 },
            };
            // Shuffle directions to avoid bias towards one direction
            const shuffledDirs = Object.keys(directions).sort(() => Math.random() - 0.5);

            for (const dir of shuffledDirs) {
                const nx = startRoomInstance.coords.x + directions[dir].x;
                const ny = startRoomInstance.coords.y + directions[dir].y;
                console.log(` -> [Safeguard Check] Trying dir: ${dir}. Target coords: (${nx}, ${ny})`); // Log check
                if (this._canPlaceRoom(nx, ny)) {
                    console.log(` -> [Safeguard Check] Coords (${nx}, ${ny}) are available. Attempting to add forced room...`); // Log availability
                    const forcedRoom = this._addRoom(nx, ny, 'Normal'); // Add a normal room
                    if (forcedRoom) {
                        console.log(` -> [Safeguard Success] Added forced room ID: ${forcedRoom.id} at (${nx}, ${ny})`); // Log success
                        const oppositeDir = getOppositeDirection(dir);
                        startRoomInstance.neighbors[dir] = forcedRoom.id;
                        forcedRoom.neighbors[oppositeDir] = startRoomInstance.id;
                        // Ensure roomData neighbors are updated (if syncRoomData wasn't run yet, or run it again)
                        startRoomInstance.syncRoomData(); 
                        forcedRoom.syncRoomData(); 
                        forcedConnection = true;
                        break; // Only force one connection
                    } else {
                         console.error(` -> [Safeguard Error] Failed to add forced room instance at (${nx}, ${ny}) even though _canPlaceRoom was true!`); // Log error
                    }
                } else {
                     console.log(` -> [Safeguard Check] Coords (${nx}, ${ny}) are already occupied.`); // Log failure
                }
            }
            if (!forcedConnection) {
                 console.error(`DungeonGenerator: CRITICAL - Failed to force a connection for Start Room 0! Player might be trapped.`);
            }
        }
        // --- End Safeguard --- 

        // Log if the *random target* wasn't reached (can happen due to space/attempts)
        if (this.layout.size < currentTargetRooms && _retryCount === 0) { 
            console.warn(`DungeonGenerator: Could only generate ${this.layout.size} rooms (target: ${currentTargetRooms}). Limit: ${maxTotalAttempts} attempts.`);
        }

        // --- Post-processing --- 
        this._assignBossRoom();
        // TODO: Assign Item/Shop/Secret rooms 
        this._addLoops(); // Add loops to reduce dead ends

        // Final sync before returning?
        this.layout.forEach(room => room.syncRoomData());

        console.log(`--- Layout Generation Attempt Complete: ${this.layout.size} rooms ---`);
        
        // --- Final Size Check & Retry Logic (Based on MIN_ROOMS) --- 
        if (this.layout.size < MIN_ROOMS) {
            console.warn(`DungeonGenerator: Generated layout only has ${this.layout.size} rooms (min: ${MIN_ROOMS}). Retrying... (Attempt ${_retryCount + 2})`);
            return this.generateLayout(areaId, _retryCount + 1); // Recursive call
        }
        // --- End Size Check & Retry Logic ---
        
        // If minimum size is met (or retries exhausted)
        console.log(`Final floorLayout Map (${this.layout.size} rooms):`, this.floorLayout);
        return this.floorLayout; // Return the map of RoomData objects
    }

    // Assign Boss Room to the furthest room from Start (simple distance)
     _assignBossRoom() {
        console.log("=== Boss Room Assignment Debug ===");
        if (this.layout.size <= 1) {
            console.warn("Cannot assign Boss room, only Start room exists.");
            return; 
        }

        // Logic to find a suitable dead-end room furthest from the start
        let furthestRoom = null;
        let maxDistance = -1;

        const startRoomCoords = this.layout.get(0)?.coords || { x: 0, y: 0 };
        console.log(`Start room coordinates: (${startRoomCoords.x}, ${startRoomCoords.y})`);

        for (const room of this.layout.values()) {
            if (room.type === 'Start') continue;
            
            const connectionCount = room.getConnectionCount();
            console.log(`Checking room ${room.id} - Connections: ${connectionCount}, Coords: (${room.coords.x}, ${room.coords.y})`);
            
            if (connectionCount === 1) { // Dead end
                 const distance = Math.abs(room.coords.x - startRoomCoords.x) + Math.abs(room.coords.y - startRoomCoords.y);
                 console.log(`Found dead end room ${room.id} at distance ${distance}`);
                 if (distance > maxDistance) {
                     maxDistance = distance;
                     furthestRoom = room;
                     console.log(`New furthest room found: Room ${room.id} at distance ${distance}`);
                 }
            }
        }

        if (furthestRoom) {
            console.log(`Assigning Room ${furthestRoom.id} as Boss Room (Distance: ${maxDistance})`);
            furthestRoom.type = 'Boss';
            furthestRoom.roomData.type = 'Boss'; // Sync to roomData
            furthestRoom.shapeKey = 'BOSS_ARENA'; // Set the shape to BOSS_ARENA
            furthestRoom.roomData.shapeKey = 'BOSS_ARENA'; // Sync to roomData
            furthestRoom.roomData.state.initialEnemies = ['catacombs_overlord']; // Set boss enemy
            console.log(`Boss room data:`, {
                id: furthestRoom.id,
                type: furthestRoom.type,
                shapeKey: furthestRoom.shapeKey,
                coords: furthestRoom.coords,
                connections: furthestRoom.neighbors
            });
        } else {
            console.warn("No suitable room found for boss room assignment");
        }
        console.log("=== End Boss Room Assignment ===");
    }

    // Add extra connections (loops) to reduce dead ends
    _addLoops(probability = 0.25, maxDoors = 3) {
        console.log(` -> Adding loops with ~${probability*100}% probability...`);
        if (this.layout.size < 3) return; // Need at least 3 rooms for potential loops

        const roomIds = Array.from(this.layout.keys());
        let loopsAdded = 0;

        // Define potential relative offsets for loop candidates (adjacent to adjacent)
        const loopOffsets = [
            { x: 0, y: -2 }, { x: 0, y: 2 }, { x: 2, y: 0 }, { x: -2, y: 0 }, // Cardinal 2 steps
            // { x: 1, y: -1 }, { x: 1, y: 1 }, { x: -1, y: 1 }, { x: -1, y: -1 } // Diagonal (ignore for now?)
        ];

        // Keep track of coords to Room object for easier lookup
        const coordToRoomMap = new Map();
        this.layout.forEach(room => {
            coordToRoomMap.set(`${room.coords.x},${room.coords.y}`, room);
        });

        for (const roomId of roomIds) {
            const roomA = this.layout.get(roomId);
            if (!roomA) continue;

            // Shuffle offsets to check in random order
            loopOffsets.sort(() => Math.random() - 0.5);

            for (const offset of loopOffsets) {
                if (roomA.getConnectionCount() >= maxDoors) break; 

                const potentialNeighborX = roomA.coords.x + offset.x;
                const potentialNeighborY = roomA.coords.y + offset.y;
                const roomB = coordToRoomMap.get(`${potentialNeighborX},${potentialNeighborY}`);

                if (roomB && roomB.id !== roomA.id && roomB.getConnectionCount() < maxDoors) {
                    let alreadyConnected = Object.values(roomA.neighbors).includes(roomB.id);

                    if (!alreadyConnected && Math.random() < probability) {
                        // Determine connection direction based on offset
                        let dirA = null, dirB = null;
                        if (offset.x === 2) { dirA = 'e'; dirB = 'w'; }
                        else if (offset.x === -2) { dirA = 'w'; dirB = 'e'; }
                        else if (offset.y === 2) { dirA = 's'; dirB = 'n'; }
                        else if (offset.y === -2) { dirA = 'n'; dirB = 's'; }

                        if (dirA && roomA.neighbors[dirA] === null && roomB.neighbors[dirB] === null) {
                             roomA.neighbors[dirA] = roomB.id;
                             roomB.neighbors[dirB] = roomA.id;
                             // Neighbors should be synced automatically as they reference the same object
                             loopsAdded++;
                             console.log(` --> Added loop connection between Room ${roomA.id} (${dirA}) and Room ${roomB.id} (${dirB})`);
                             if (roomA.getConnectionCount() >= maxDoors) break; // Stop checking offsets for roomA
                        }
                    }
                }
            }
        }
         console.log(` -> Added ${loopsAdded} loop connections.`);
    }

    // Helper to get room by coordinates (if needed, seems unused currently)
    _getRoomAt(x, y) { 
        return this.rooms.find(room => room.coords.x === x && room.coords.y === y);
    }

    // Debug function to print layout to console
    printLayout() {
        // Find grid bounds
        let minX = 0, maxX = 0, minY = 0, maxY = 0;
        for (const room of this.layout.values()) {
            minX = Math.min(minX, room.coords.x);
            maxX = Math.max(maxX, room.coords.x);
            minY = Math.min(minY, room.coords.y);
            maxY = Math.max(maxY, room.coords.y);
        }

        console.log("--- Dungeon Layout --- (Grid Coords)");
        for (let y = minY; y <= maxY; y++) {
            let row = "";
            for (let x = minX; x <= maxX; x++) {
                const room = this._getRoomAt(x, y);
                if (room) {
                    let char = 'N'; // Normal
                    if (room.type === 'Start') char = 'S';
                    else if (room.type === 'Boss') char = 'B';
                    row += `[${char}${room.id}]`;
                } else {
                    row += " .  ";
                }
            }
            console.log(row);
        }
        console.log("---------------------");
    }
}

// Only export DungeonGenerator from this file
export { DungeonGenerator };
