// src/generators/prefabs/vineObject.js
import * as THREE from 'three';
import {
    VOXEL_SIZE,
    vineMaterialPrimary, vineMaterialSecondary, darkVineMaterialPrimary, darkVineMaterialSecondary,
    mulberry32, getOrCreateGeometry
} from './shared.js';

/**
 * Creates a single vine strand object (group) with leaves.
 * Uses the simplified logic previously in stonebrickWall.js.
 * Needs position and rotation from the placement logic.
 * @param {object} args - Configuration arguments.
 * @param {string} [args.vineType='standard'] - 'standard' or 'dark'.
 * @param {number} [args.maxLength=8] - Max length in segments.
 * @param {number} [args.leafProbability=0.05] - Chance of a leaf per segment.
 * @param {number} [args.seed=Date.now()] - Seed for randomization.
 * @returns {THREE.Group} The vine group.
 */
export function createVineObject(args = {}) {
    const {
        vineType = 'standard',
        maxLength = 8, // Default max length
        leafProbability = 0.05,
        seed = Date.now()
    } = args;

    const random = mulberry32(seed);
    const group = new THREE.Group();
    const tempMatrix = new THREE.Matrix4();

    const vinePrimary = (vineType === 'dark') ? darkVineMaterialPrimary : vineMaterialPrimary;
    const vineSecondary = (vineType === 'dark') ? darkVineMaterialSecondary : vineMaterialSecondary;

    // Use cached geometries (these should match the latest desired dimensions)
    const segmentHeight = VOXEL_SIZE * 3.0;
    const driftMagnitude = VOXEL_SIZE * (1.5 + random() * 6.0); // Random magnitude per vine

    const vineSegmentGeo = getOrCreateGeometry(
        `vine_segment_chunky_v4_${VOXEL_SIZE.toFixed(4)}`, 
        () => new THREE.BoxGeometry(VOXEL_SIZE * 3.5, segmentHeight, VOXEL_SIZE * 3.5)
    );
    const leafGeo = getOrCreateGeometry(
        `leaf_chunky_v4_${VOXEL_SIZE.toFixed(4)}`, 
        () => new THREE.BoxGeometry(VOXEL_SIZE * 4, segmentHeight, VOXEL_SIZE * 4)
    );

    const vineLengthVoxels = Math.max(2, Math.floor(random() * maxLength));
    let currentVineX = 0; // Start at origin, placement logic handles world pos
    let currentVineY = 0; 
    const floorY = -Infinity; // Placement logic should handle floor collision

    for (let v = 0; v < vineLengthVoxels; v++) {
        const segmentY = currentVineY - v * segmentHeight; 
        
        // Apply horizontal drift 
        const drift = (random() - 0.5) * driftMagnitude; 
        currentVineX += drift;
        // Clamp drift ? - Maybe let placement logic handle bounds
        // currentVineX = Math.max(-width/2 + VOXEL_SIZE*2, Math.min(width/2 - VOXEL_SIZE*2, currentVineX));

        // Add vine segment
        tempMatrix.makeTranslation(currentVineX, segmentY, 0); // Z is handled by placement
        const vineMaterial = random() < 0.6 ? vinePrimary : vineSecondary; 
        const segmentMesh = new THREE.Mesh(vineSegmentGeo, vineMaterial);
        segmentMesh.applyMatrix4(tempMatrix);
        segmentMesh.castShadow = true;
        group.add(segmentMesh);

        // Add leaf?
        if (v > 0 && random() < leafProbability) { 
            const leafXOffset = (random() < 0.5 ? -1 : 1) * VOXEL_SIZE * 1.5; 
            const leafYOffset = (random() - 0.5) * segmentHeight * 0.5; 
            const leafZOffset = (random() - 0.5) * VOXEL_SIZE * 1.5; 
            tempMatrix.makeTranslation(currentVineX + leafXOffset, segmentY + leafYOffset, leafZOffset); 
            const leafMesh = new THREE.Mesh(leafGeo, vineMaterial);
            leafMesh.applyMatrix4(tempMatrix);
            leafMesh.castShadow = true;
            group.add(leafMesh); 
        }
    }
    
    group.name = "vineObject";
    // Add userData for identification by placement logic?
    group.userData.isInteriorObject = true;
    group.userData.objectType = 'vine';

    return group;
} 