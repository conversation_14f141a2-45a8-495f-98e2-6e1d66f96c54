import * as THREE from 'three';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    torchHandleMaterial, emissiveFlameMaterials,
    getOrCreateGeometry
} from './shared.js';

/**
 * Creates a single wall torch object (group) with handle and flame.
 * Uses logic previously in stonebrickWall.js.
 * Placement logic will handle position and rotation.
 * Returns the torch group and the flame's LOCAL position (for light placement).
 * @returns {{group: THREE.Group, flameLocalPosition: THREE.Vector3}}
 */
export function createTorchObject(options = {}) {
    const group = new THREE.Group();
    const tempMatrix = new THREE.Matrix4();

    const scale = ENVIRONMENT_PIXEL_SCALE * 1.5; // Match wall scale potentially
    const voxelSize = VOXEL_SIZE * scale;

    // Use cached torch geometry
    const handleGeo = getOrCreateGeometry(
        `torch_handle_${voxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE * 2, VOXEL_SIZE) // Keep handle size based on base VOXEL_SIZE?
    );
    const flameGeo = getOrCreateGeometry(
        `torch_flame_${voxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(VOXEL_SIZE * 1.5, VOXEL_SIZE * 1.5, VOXEL_SIZE * 1.5)
    );

    // Handle (Positioned at origin of the group)
    const handleMesh = new THREE.Mesh(handleGeo, torchHandleMaterial);
    handleMesh.castShadow = true;
    group.add(handleMesh);

    // Flame (Positioned relative to handle)
    const flameY = VOXEL_SIZE * 2.5; // Y offset from handle base
    const flameLocalPosition = new THREE.Vector3(0, flameY, 0);
    const flameMaterial = emissiveFlameMaterials[Math.floor(Math.random() * emissiveFlameMaterials.length)];
    const flameMesh = new THREE.Mesh(flameGeo, flameMaterial);
    flameMesh.position.copy(flameLocalPosition);
    group.add(flameMesh);

    group.name = "torchObject";
    group.userData.isInteriorObject = true;
    group.userData.objectType = 'torch';

    return { group, flameLocalPosition };
}