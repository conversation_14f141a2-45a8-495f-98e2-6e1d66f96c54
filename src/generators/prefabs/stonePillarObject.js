import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js'; // <-- Import BufferGeometryUtils
// Import needed constants and materials from shared.js
import { 
    VOXEL_SIZE, 
    mulberry32, 
    mainStoneMaterial,      // PILLAR_BASE_MATERIAL
    lightStoneMaterial,     // PILLAR_SHAFT_MATERIAL
    midLightStoneMaterial,  // PILLAR_DETAIL_MATERIAL
    getOrCreateGeometry,      // <-- Import geometry helper
    _getMaterialByHex_Cached // <-- Import material helper
} from './shared.js'; 
// import VoxelBuilder from '../utils/VoxelBuilder.js'; // <-- REMOVE VoxelBuilder import
// import { WALL_HEIGHT } from '/src/utils/constants.js'; // <-- REMOVE import

// Define pillar materials using imported materials
const PILLAR_BASE_MATERIAL = mainStoneMaterial; 
const PILLAR_SHAFT_MATERIAL = lightStoneMaterial;
const PILLAR_DETAIL_MATERIAL = midLightStoneMaterial;

// Define dimensions relative to VOXEL_SIZE
const BASE_WIDTH_VOXELS = 5;
const BASE_DEPTH_VOXELS = 5;
const BASE_HEIGHT_VOXELS = 2;
const SHAFT_WIDTH_VOXELS = 4;
const SHAFT_DEPTH_VOXELS = 4;
const TOP_HEIGHT_VOXELS = BASE_HEIGHT_VOXELS;

/**
 * Creates a 3D stone pillar object using merged geometry.
 * @param {object} options - Options object.
 * @param {number} options.seed - Random seed.
 * @param {number} options.wallHeight - The target height for the pillar.
 * @returns {object} - Object containing the THREE.Group for the pillar.
 */
export function createStonePillarObject(options = {}) {
    const { seed = 12345, wallHeight = 3 } = options;
    const random = mulberry32(seed);
    
    const pillarGroup = new THREE.Group();
    const geometriesByMaterial = {}; // Store geometries by hex color string
    const tempMatrix = new THREE.Matrix4();
    const voxelGeo = getOrCreateGeometry('voxel_pillar', () => new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE));

    // Helper to add a voxel
    const addVoxel = (x_vox, y_vox, z_vox, material) => {
        const hex = material.color.getHexString();
        if (!geometriesByMaterial[hex]) {
            geometriesByMaterial[hex] = [];
        }
        const geo = voxelGeo.clone();
        tempMatrix.makeTranslation(
            x_vox * VOXEL_SIZE,
            y_vox * VOXEL_SIZE,
            z_vox * VOXEL_SIZE
        );
        geo.applyMatrix4(tempMatrix);
        geometriesByMaterial[hex].push(geo);
    };

    // Calculate Total Height in Voxels based on passed wallHeight
    const totalHeightVoxels = Math.floor(wallHeight / VOXEL_SIZE);
    const shaftHeightVoxels = totalHeightVoxels - BASE_HEIGHT_VOXELS - TOP_HEIGHT_VOXELS;

    // --- Ensure shaft height is at least 1 voxel ---
    if (shaftHeightVoxels < 1) {
        console.warn(`[StonePillar] Calculated shaft height too small (${shaftHeightVoxels} voxels) for wallHeight ${wallHeight}. Clamping.`);
        // Adjust base/top or clamp shaft height? For now, just log.
        // Maybe adjust TOP_HEIGHT_VOXELS dynamically?
        // shaftHeightVoxels = 1;
        // Re-calculate topStartY if clamping shaft height
    }
    // ---------------------------------------------

    // Voxel coordinates are relative to the center base (0,0,0)
    const halfBaseW = (BASE_WIDTH_VOXELS - 1) / 2;
    const halfBaseD = (BASE_DEPTH_VOXELS - 1) / 2;
    const halfShaftW = (SHAFT_WIDTH_VOXELS - 1) / 2;
    const halfShaftD = (SHAFT_DEPTH_VOXELS - 1) / 2;

    // --- Collect Voxel Data for Destruction ---
    const originalVoxels = [];
    const recordVoxel = (x_vox, y_vox, z_vox, material) => {
        originalVoxels.push({ x: x_vox, y: y_vox, z: z_vox, c: material.color.getHexString() });
    };
    // --- Re-run the building logic just to record data ---
    // Base
    for (let y = 0; y < BASE_HEIGHT_VOXELS; y++) {
        for (let x = -halfBaseW; x <= halfBaseW; x++) {
            for (let z = -halfBaseD; z <= halfBaseD; z++) {
                const isDetail = y >= 0 && Math.abs(x) <= halfBaseW - 0.5 && Math.abs(z) <= halfBaseD - 0.5;
                const mat = isDetail ? PILLAR_DETAIL_MATERIAL : PILLAR_BASE_MATERIAL;
                recordVoxel(x, y, z, mat);
            }
        }
    }
    // Shaft
    const shaftStartY = BASE_HEIGHT_VOXELS;
    for (let y = 0; y < shaftHeightVoxels; y++) {
        const currentY = shaftStartY + y;
        for (let x = -halfShaftW; x <= halfShaftW; x++) {
            for (let z = -halfShaftD; z <= halfShaftD; z++) {
                const isDetailLocation = (Math.abs(x) === halfShaftW && Math.abs(z) < halfShaftD) || (Math.abs(z) === halfShaftD && Math.abs(x) < halfShaftW);
                const mat = isDetailLocation ? PILLAR_DETAIL_MATERIAL : PILLAR_SHAFT_MATERIAL;
                recordVoxel(x, currentY, z, mat);
            }
        }
    }
    // Top
    const topStartY = shaftStartY + shaftHeightVoxels;
    for (let y = 0; y < TOP_HEIGHT_VOXELS; y++) {
        const currentY = topStartY + y;
        for (let x = -halfBaseW; x <= halfBaseW; x++) {
            for (let z = -halfBaseD; z <= halfBaseD; z++) {
                const isDetail = y < TOP_HEIGHT_VOXELS && Math.abs(x) <= halfBaseW - 0.5 && Math.abs(z) <= halfBaseD - 0.5;
                const mat = isDetail ? PILLAR_DETAIL_MATERIAL : PILLAR_BASE_MATERIAL;
                recordVoxel(x, currentY, z, mat);
            }
        }
    }
    // --- End Voxel Data Collection ---

    // --- Merge Geometries --- 
    for (const hex in geometriesByMaterial) {
        const mergedGeo = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[hex], false);
        if (mergedGeo) {
            const material = _getMaterialByHex_Cached(hex); // Use cached material getter
            const mesh = new THREE.Mesh(mergedGeo, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            pillarGroup.add(mesh);
        } else {
            console.warn(`[StonePillar] Failed to merge geometry for material hex: ${hex}`);
        }
    }

    // --- Set UserData (Important for destruction) ---
    pillarGroup.userData = {
        ...(pillarGroup.userData || {}),
        objectType: 'stone_pillar',
        isDestructible: true,       // As per definition
        destructionEffect: 'collapse', // As per definition
        health: 1,                  // As per definition
        originalVoxels: originalVoxels, // <-- Add voxel data
        voxelScale: VOXEL_SIZE         // <-- Add voxel scale
    };

    // Adjust Group Position: Built relative to base center at 0,0,0.
    // Room generator expects origin at y=0 (floor level).
    // Shift the group content up by half the total height.
    pillarGroup.position.y = (totalHeightVoxels / 2) * VOXEL_SIZE; 

    return { group: pillarGroup };
}