import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    caveFloorBaseMaterial, caveFloorVariant1Material, caveFloorCrackMaterial, dustMaterial,
    mulberry32, _getMaterialByHex_Cached, getOrCreateGeometry
} from './shared.js';

/**
 * Creates a cave-like floor surface using randomized brown/earth tone voxels.
 * Uses base VOXEL_SIZE for detail and adds overlay patches.
 * @param {number} width World units width (X-axis).
 * @param {number} depth World units depth (Z-axis).
 * @param {object} roomData The room data object containing the ID for seeding.
 * @returns {THREE.Mesh|THREE.Group|null} The merged floor mesh/group or null if empty.
 */
export function createCaveFloor(width, depth, roomData) {
    const geometriesByMaterial = {}; 
    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };
    const tempMatrix = new THREE.Matrix4();

    // Use double environment scale for floors to reduce voxel count
    const FLOOR_SCALE = ENVIRONMENT_PIXEL_SCALE * 2;
    
    // Grid dimensions
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / FLOOR_SCALE);
    const numZ_env = Math.ceil(numZ / FLOOR_SCALE);

    // Centering offsets
    const offsetX = (numX - 1) * VOXEL_SIZE / 2; 
    const offsetZ = (numZ - 1) * VOXEL_SIZE / 2;
    const floorY = 0; // Base floor Y position
    const overlayOffsetY = 0.01; // Small offset for overlay layer
    
    // Overlay patch parameters
    const overlayPatchSpawnProbability = 0.06; // Increased chance since we have fewer env cells now
    const crackOverlayFraction = 0.4; 
    const minPatchSize = 2; // Min env cells in a patch (reduced from 4)
    const maxPatchSize = 6; // Max env cells in a patch (reduced from 12)

    // Use cached geometry for floor voxels
    const floorVoxelSize = VOXEL_SIZE * FLOOR_SCALE;
    const floorVoxelGeo = getOrCreateGeometry(
        `floor_${floorVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(floorVoxelSize, VOXEL_SIZE, floorVoxelSize)
    );
    
    const overlayGrid = Array(numZ_env).fill(null).map(() => Array(numX_env).fill(false)); // Track overlay placement

    // --- Use Seeded PRNG ---
    const roomSeed = roomData ? roomData.id * 47 + 29 : Date.now(); // Use room ID for seed
    const random = mulberry32(roomSeed);
    // ---------------------

    // --- Pass 1: Generate Base Floor --- 
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            let baseMaterial;
            const roll = random();
            if (roll < 0.15) baseMaterial = caveFloorCrackMaterial;
            else if (roll < 0.5) baseMaterial = caveFloorVariant1Material;
            else baseMaterial = caveFloorBaseMaterial;

            // Place a single large floor voxel instead of multiple small ones
            const posX = ex * floorVoxelSize - offsetX;
            const posZ = ez * floorVoxelSize - offsetZ;
            tempMatrix.makeTranslation(posX, floorY, posZ);
            addGeometry(floorVoxelGeo, baseMaterial, tempMatrix);
        }
    }
    // --- End Pass 1 --- 

    // --- Pass 2: Generate Overlay Patches --- 
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            // Check if already overlaid OR random chance fails
            if (overlayGrid[ez][ex] || random() > overlayPatchSpawnProbability) {
                continue;
            }

            // Start a new patch
            const patchSize = minPatchSize + Math.floor(random() * (maxPatchSize - minPatchSize + 1));
            const overlayMaterial = (random() < crackOverlayFraction) 
                                      ? caveFloorCrackMaterial 
                                      : dustMaterial;
            const currentPatchCells = [];
            const queue = [[ex, ez]]; // Start flood fill / random walk queue
            overlayGrid[ez][ex] = true; // Mark starting cell as overlaid
            currentPatchCells.push([ex, ez]);

            let attempts = 0; // Prevent infinite loops if area is small
            const maxAttempts = patchSize * 5; 

            while (currentPatchCells.length < patchSize && queue.length > 0 && attempts < maxAttempts) {
                attempts++;
                const [cx, cz] = queue.shift(); // Get current cell

                // Explore neighbours randomly
                const neighbours = [
                    [cx + 1, cz], [cx - 1, cz], [cx, cz + 1], [cx, cz - 1]
                ].sort(() => random() - 0.5); // Shuffle neighbours

                for (const [nx, nz] of neighbours) {
                    // Check bounds and if already part of an overlay
                    if (nx >= 0 && nx < numX_env && nz >= 0 && nz < numZ_env && !overlayGrid[nz][nx]) {
                        overlayGrid[nz][nx] = true; // Mark as overlaid
                        currentPatchCells.push([nx, nz]);
                        queue.push([nx, nz]); // Add to queue for further exploration
                        if (currentPatchCells.length >= patchSize) break; // Reached desired size
                    }
                }
            }

            // Add geometry for all cells in this patch
            for (const [patchEX, patchEZ] of currentPatchCells) {
                const posX = patchEX * floorVoxelSize - offsetX;
                const posZ = patchEZ * floorVoxelSize - offsetZ;
                tempMatrix.makeTranslation(posX, floorY + overlayOffsetY, posZ); // Apply Y offset
                addGeometry(floorVoxelGeo, overlayMaterial, tempMatrix);
            }
        }
    }
    // --- End Pass 2 --- 

    // --- Merge Geometries with Polygon Offset --- 
    const finalMeshes = [];
    const baseHex = caveFloorBaseMaterial.color.getHexString();
    const variant1Hex = caveFloorVariant1Material.color.getHexString();
    const crackHex = caveFloorCrackMaterial.color.getHexString(); // May exist in both layers
    const dustHex = dustMaterial.color.getHexString();
    
    for (const colorHex in geometriesByMaterial) {
        const originalMaterial = _getMaterialByHex_Cached(colorHex);
        if (!originalMaterial) continue;

        // Clone the material to apply offset without modifying the shared instance
        const finalMaterial = originalMaterial.clone();
        
        // Apply polygon offset based on material type to help prevent z-fighting
        if (colorHex === baseHex || colorHex === variant1Hex) {
            // Push base materials away slightly
            finalMaterial.polygonOffset = true;
            finalMaterial.polygonOffsetFactor = 1.0; 
            finalMaterial.polygonOffsetUnits = 1.0; 
        } else if (colorHex === dustHex) {
            // Pull dust overlay towards camera slightly
            finalMaterial.polygonOffset = true;
            finalMaterial.polygonOffsetFactor = -1.0; 
            finalMaterial.polygonOffsetUnits = -1.0;
        } 
        // Crack material (crackHex) is left without offset as it can be in either layer.

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
             finalMeshes.push(new THREE.Mesh(mergedGeometry, finalMaterial)); // Use the potentially modified finalMaterial
        } else {
            console.warn(`Failed to merge geometry for cave floor, material: ${colorHex}`);
        }
    }

    if (finalMeshes.length === 0) return null;

    // Return single mesh if only one material type ended up being used
    if (finalMeshes.length === 1) {
        finalMeshes[0].receiveShadow = true; // Ensure floor parts receive shadow
        return finalMeshes[0];
    }

    // Otherwise, return a group containing all meshes
    const finalGroup = new THREE.Group();
    finalGroup.name = "caveFloor";
    finalMeshes.forEach(mesh => {
        mesh.receiveShadow = true; // Ensure floor parts receive shadow
        finalGroup.add(mesh);
    });
    return finalGroup; 
} 