import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, SKELETON_VOXEL_SIZE, HEAD_VOXEL_SCALE, HEAD_VOXEL_SIZE,
    boneMaterials, eyeMaterial, bowMaterials, skeletonBowMaterial,
    darkArmorMaterial1, darkArmorMaterial2, darkArmorMaterial3,
    goldAccentMaterial1, goldAccentMaterial2, redEyeMaterial,
    mulberry32, _getMaterialByHex_Cached, materialCacheByHex
} from './shared.js';

/**
 * Creates a voxel-based Catacomb Overlord boss model.
 * Inspired by a dark, imposing figure with a large axe/halberd weapon.
 * Includes named groups for animation.
 * @param {number} [scale=1.0] Optional overall scale factor.
 * @returns {THREE.Group} The Catacomb Overlord boss model group.
 */
export function createCatacombOverlordModel(scale = 1.0) {
    const finalGroup = new THREE.Group();
    finalGroup.name = "CatacombOverlord";

    // Nested structure: geometriesByMaterial[groupName][hexColor] = []
    const geometriesByMaterial = {
        core: {}, head: {}, leftArm: {}, rightArm: {}, leftLeg: {}, rightLeg: {}, weapon: {}
    };

    // Create a seeded random function for consistent appearance
    const random = mulberry32(12345);

    // Create template geometries
    const voxelGeo = new THREE.BoxGeometry(SKELETON_VOXEL_SIZE, SKELETON_VOXEL_SIZE, SKELETON_VOXEL_SIZE);
    const headVoxelGeo = new THREE.BoxGeometry(HEAD_VOXEL_SIZE, HEAD_VOXEL_SIZE, HEAD_VOXEL_SIZE);

    // Temporary matrix for transformations
    const tempMatrix = new THREE.Matrix4();

    // Define key Y positions (absolute)
    const floorY = 0;
    const pelvisY = 4; // Higher pelvis for taller figure
    const shoulderY = pelvisY + 6; // Taller torso
    const neckY = shoulderY + 1;
    const headY = neckY + 1;
    const spineHeight = shoulderY - pelvisY - 1;

    // Helper function to get random bone material
    const getRandomBoneMat = () => {
        // Use darker bone materials for the boss
        return boneMaterials[Math.floor(random() * 2) + 2]; // Use darker variants (index 2-3)
    };

    // Helper function to get dark armor material
    const getDarkArmorMat = () => {
        // Use predefined dark armor materials
        const darkArmorMaterials = [darkArmorMaterial1, darkArmorMaterial2, darkArmorMaterial3];
        const materialIndex = Math.floor(random() * darkArmorMaterials.length);
        return darkArmorMaterials[materialIndex];
    };

    // Helper function to get gold accent material
    const getGoldAccentMat = () => {
        // Use predefined gold accent materials
        const goldAccentMaterials = [goldAccentMaterial1, goldAccentMaterial2];
        const materialIndex = Math.floor(random() * goldAccentMaterials.length);
        return goldAccentMaterials[materialIndex];
    };

    // Use the shared redEyeMaterial for glowing red eyes

    // Helper to add voxels to the appropriate group's geometry collection
    const addVoxel = (groupName, x, y, z, material, matrixOverride = null) => {
        const colorHex = material.color.getHexString().toLowerCase();
        if (!geometriesByMaterial[groupName][colorHex]) {
            geometriesByMaterial[groupName][colorHex] = [];
        }

        const geometry = voxelGeo.clone();
        // Coordinates are RELATIVE TO PIVOT
        const matrix = matrixOverride || tempMatrix.makeTranslation(
            x * SKELETON_VOXEL_SIZE,
            y * SKELETON_VOXEL_SIZE,
            z * SKELETON_VOXEL_SIZE
        );
        geometry.applyMatrix4(matrix);
        geometriesByMaterial[groupName][colorHex].push(geometry);
    };

    // Helper to add head voxels (smaller scale for detail)
    const addHeadVoxel = (x, y, z, material) => {
        const colorHex = material.color.getHexString().toLowerCase();
        if (!geometriesByMaterial['head'][colorHex]) {
            geometriesByMaterial['head'][colorHex] = [];
        }

        const geometry = headVoxelGeo.clone();
        const matrix = tempMatrix.makeTranslation(
            x * HEAD_VOXEL_SIZE,
            y * HEAD_VOXEL_SIZE,
            z * HEAD_VOXEL_SIZE
        );
        geometry.applyMatrix4(matrix);
        geometriesByMaterial['head'][colorHex].push(geometry);
    };

    // === Create the Groups ===
    const coreGroup = new THREE.Group();
    coreGroup.name = 'core';

    const headGroup = new THREE.Group();
    headGroup.name = 'head';
    headGroup.position.set(0, neckY * SKELETON_VOXEL_SIZE, 0);

    const leftArmGroup = new THREE.Group();
    leftArmGroup.name = 'leftArm';
    leftArmGroup.position.set(-3 * SKELETON_VOXEL_SIZE, shoulderY * SKELETON_VOXEL_SIZE, 0);

    const rightArmGroup = new THREE.Group();
    rightArmGroup.name = 'rightArm';
    rightArmGroup.position.set(3 * SKELETON_VOXEL_SIZE, shoulderY * SKELETON_VOXEL_SIZE, 0);

    const leftLegGroup = new THREE.Group();
    leftLegGroup.name = 'leftLeg';
    leftLegGroup.position.set(-1 * SKELETON_VOXEL_SIZE, pelvisY * SKELETON_VOXEL_SIZE, 0);

    const rightLegGroup = new THREE.Group();
    rightLegGroup.name = 'rightLeg';
    rightLegGroup.position.set(1 * SKELETON_VOXEL_SIZE, pelvisY * SKELETON_VOXEL_SIZE, 0);

    const weaponGroup = new THREE.Group();
    weaponGroup.name = 'weapon';

    // === Pelvis (Part of Core - Y coords are absolute, relative to feet=0) ===
    // Make 3 wide (X), 2 deep (Z)
    for (let x = -1; x <= 1; x++) {
        for (let z = 0; z >= -1; z--) {
            addVoxel('core', x, pelvisY, z, getDarkArmorMat()); // Use dark armor material
        }
    }

    // === Spine (Part of Core - Absolute Y) ===
    // Make 2x2 thick with material variation
    for (let y = 1; y <= spineHeight; y++) {
        for (let x = 0; x <= 1; x++) { // Centered slightly forward
            for (let z = 0; z >= -1; z--) {
                const mat = (random() < 0.7) ? getDarkArmorMat() : getGoldAccentMat(); // More chance for dark armor
                addVoxel('core', x - 0.5, pelvisY + y, z, mat); // Use absolute Y
            }
        }
    }

    // === Ribs (Part of Core - Absolute Y) ===
    const ribsStartY = pelvisY + 2;
    const ribsEndY = shoulderY - 1;
    const ribWidth = 2; // How far ribs extend sideways from spine (wider for boss)
    for (let y = ribsStartY; y <= ribsEndY; y += 2) {
        const ribZOffset = (y % 4 === 0) ? -0.5 : 0; // Slight curve in Z
        for (let xOff = 1; xOff <= ribWidth; xOff++) {
            const mat1 = (random() < 0.7) ? getDarkArmorMat() : getGoldAccentMat();
            const mat2 = (random() < 0.7) ? getDarkArmorMat() : getGoldAccentMat();
            // Ribs extend from center spine (approx x=0)
            addVoxel('core', -xOff, y, ribZOffset, mat1); // Left rib
            addVoxel('core', xOff, y, ribZOffset, mat1); // Right rib
            addVoxel('core', -xOff, y, ribZOffset - 1, mat2); // Add depth
            addVoxel('core', xOff, y, ribZOffset - 1, mat2); // Add depth
        }
    }

    // === Shoulders (Part of Core - Absolute Y) ===
    // Make wider shoulders for imposing figure
    for (let x = -3; x <= 3; x++) {
        for (let z = 0; z >= -1; z--) {
            const mat = (Math.abs(x) >= 2) ? getDarkArmorMat() : getGoldAccentMat(); // Gold accents on inner shoulders
            addVoxel('core', x, shoulderY, z, mat);
        }
    }

    // === Skull (Head Group - Y=0 is neck pivot) ===
    // Approx 4x4x4 using sub-voxels for detail (larger head)
    const headOffsetY_rel = 1; // Offset head slightly above neck pivot
    const headSizeX = 4; // Base size in standard voxels (larger)
    const headSizeY = 4; // Taller
    const headSizeZ = 4; // Deeper

    // Create a more imposing skull shape
    for (let ly = 0; ly < headSizeY; ly++) { // Loop large Y
        for (let lx = -Math.floor(headSizeX/2); lx <= Math.floor(headSizeX/2); lx++) { // Loop large X
            for (let lz = 1; lz >= -(headSizeZ - 2); lz--) { // Loop large Z (front is +Z)
                // Base material for this large voxel
                let mat = getDarkArmorMat(); // Default dark armor

                // Skip some voxels to create a more interesting shape
                if ((ly === 0 && Math.abs(lx) > 1 && lz < 0) ||
                    (ly === headSizeY-1 && Math.abs(lx) > 1 && lz < 0)) {
                    continue; // Skip corners to create a more rounded shape
                }

                // Add horns on top
                if (ly === headSizeY-1 && Math.abs(lx) === 1 && lz === 0) {
                    mat = getGoldAccentMat(); // Gold horns
                }

                // Create the large voxel using sub-voxels
                const subSize = HEAD_VOXEL_SCALE; // How many sub-voxels per large voxel
                for (let sy = 0; sy < subSize; sy++) {
                    for (let sx = 0; sx < subSize; sx++) {
                        for (let sz = 0; sz < subSize; sz++) {
                            // Calculate sub-voxel position
                            const subX = (lx * subSize) + sx;
                            const subY = (ly * subSize) + sy + (headOffsetY_rel * subSize);
                            const subZ = (lz * subSize) + sz;

                            // Skip some sub-voxels for detail
                            if ((ly === 0 && sy < 2) || (ly === headSizeY-1 && sy > subSize-3)) {
                                if (random() < 0.3) continue; // Random gaps for texture
                            }

                            addHeadVoxel(subX, subY, subZ, mat);
                        }
                    }
                }
            }
        }
    }

    // === Eyes (Head Group - Using sub-voxels) ===
    // Add glowing red eyes
    const eyeY = 2 * HEAD_VOXEL_SCALE + (headOffsetY_rel * HEAD_VOXEL_SCALE);
    const eyeZ = 1 * HEAD_VOXEL_SCALE - 1;

    // Left eye (larger and glowing)
    for (let ey = 0; ey < 2; ey++) {
        for (let ex = 0; ex < 2; ex++) {
            addHeadVoxel(-5 + ex, eyeY + ey, eyeZ, redEyeMaterial);
        }
    }

    // Right eye (larger and glowing)
    for (let ey = 0; ey < 2; ey++) {
        for (let ex = 0; ex < 2; ex++) {
            addHeadVoxel(3 + ex, eyeY + ey, eyeZ, redEyeMaterial);
        }
    }

    // === Crown/Helmet (Head Group - Using sub-voxels) ===
    // Add crown/helmet details
    const crownY = 3 * HEAD_VOXEL_SCALE + (headOffsetY_rel * HEAD_VOXEL_SCALE);

    // Crown base
    for (let cx = -6; cx <= 6; cx++) {
        for (let cz = -6; cz <= 2; cz++) {
            // Skip some voxels for shape
            if (Math.abs(cx) > 4 && Math.abs(cz) > 4) continue;

            const mat = getGoldAccentMat();
            addHeadVoxel(cx, crownY, cz, mat);
        }
    }

    // Crown spikes
    for (let spike = -2; spike <= 2; spike++) {
        if (spike === 0) continue; // Skip middle

        const spikeX = spike * 2;
        const mat = getGoldAccentMat();

        // Vertical spike
        for (let sy = 0; sy < 3; sy++) {
            addHeadVoxel(spikeX, crownY + sy + 1, 0, mat);
        }
    }

    // === Arms (Left & Right Arm Groups - Relative to shoulder joint) ===
    // Left Arm (holding weapon)
    const leftArmLength = 6; // Longer arm
    const leftArmWidth = 2; // Thicker arm

    // Upper arm segment (vertical, relative to shoulder)
    for (let y = -1; y >= -leftArmLength/2; y--) {
        for (let x = -leftArmWidth/2; x < leftArmWidth/2; x++) {
            for (let z = -leftArmWidth/2; z < leftArmWidth/2; z++) {
                const mat = getDarkArmorMat();
                addVoxel('leftArm', x, y, z, mat);
            }
        }
    }

    // Lower arm segment (angled forward, relative to elbow)
    const elbowY = -leftArmLength/2;
    for (let i = 1; i <= leftArmLength/2; i++) {
        const y = elbowY - i * 0.7; // Angle downward
        const z = i * 0.7; // Angle forward

        for (let x = -leftArmWidth/2; x < leftArmWidth/2; x++) {
            for (let zOff = -leftArmWidth/2; zOff < leftArmWidth/2; zOff++) {
                const mat = getDarkArmorMat();
                addVoxel('leftArm', x, y, z + zOff, mat);
            }
        }
    }

    // Right Arm (similar structure)
    const rightArmLength = 6;
    const rightArmWidth = 2;

    // Upper arm segment
    for (let y = -1; y >= -rightArmLength/2; y--) {
        for (let x = -rightArmWidth/2; x < rightArmWidth/2; x++) {
            for (let z = -rightArmWidth/2; z < rightArmWidth/2; z++) {
                const mat = getDarkArmorMat();
                addVoxel('rightArm', x, y, z, mat);
            }
        }
    }

    // Lower arm segment
    const rightElbowY = -rightArmLength/2;
    for (let i = 1; i <= rightArmLength/2; i++) {
        const y = rightElbowY - i * 0.7;
        const z = i * 0.7;

        for (let x = -rightArmWidth/2; x < rightArmWidth/2; x++) {
            for (let zOff = -rightArmWidth/2; zOff < rightArmWidth/2; zOff++) {
                const mat = getDarkArmorMat();
                addVoxel('rightArm', x, y, z + zOff, mat);
            }
        }
    }

    // === Legs (Left & Right Leg Groups - Relative to hip joint) ===
    // Left Leg
    const legLength = 4;
    const legWidth = 2;

    // Upper leg segment
    for (let y = -1; y >= -legLength/2; y--) {
        for (let x = -legWidth/2; x < legWidth/2; x++) {
            for (let z = -legWidth/2; z < legWidth/2; z++) {
                const mat = getDarkArmorMat();
                addVoxel('leftLeg', x, y, z, mat);
            }
        }
    }

    // Lower leg segment
    const kneeY = -legLength/2;
    for (let i = 1; i <= legLength/2; i++) {
        const y = kneeY - i;

        for (let x = -legWidth/2; x < legWidth/2; x++) {
            for (let z = -legWidth/2; z < legWidth/2; z++) {
                const mat = getDarkArmorMat();
                addVoxel('leftLeg', x, y, z, mat);
            }
        }
    }

    // Right Leg (mirror of left)
    // Upper leg segment
    for (let y = -1; y >= -legLength/2; y--) {
        for (let x = -legWidth/2; x < legWidth/2; x++) {
            for (let z = -legWidth/2; z < legWidth/2; z++) {
                const mat = getDarkArmorMat();
                addVoxel('rightLeg', x, y, z, mat);
            }
        }
    }

    // Lower leg segment
    const rightKneeY = -legLength/2;
    for (let i = 1; i <= legLength/2; i++) {
        const y = rightKneeY - i;

        for (let x = -legWidth/2; x < legWidth/2; x++) {
            for (let z = -legWidth/2; z < legWidth/2; z++) {
                const mat = getDarkArmorMat();
                addVoxel('rightLeg', x, y, z, mat);
            }
        }
    }

    // === Weapon (Halberd/Axe) ===
    // Create a large halberd/axe weapon
    const weaponGeometriesByMaterial = {};

    // Helper to add weapon voxels
    const addWeaponVoxel = (x, y, z, material) => {
        const colorHex = material.color.getHexString().toLowerCase();
        if (!weaponGeometriesByMaterial[colorHex]) {
            weaponGeometriesByMaterial[colorHex] = [];
        }

        const geometry = voxelGeo.clone();
        const matrix = tempMatrix.makeTranslation(
            x * SKELETON_VOXEL_SIZE,
            y * SKELETON_VOXEL_SIZE,
            z * SKELETON_VOXEL_SIZE
        );
        geometry.applyMatrix4(matrix);
        weaponGeometriesByMaterial[colorHex].push(geometry);
    };

    // Weapon handle (long staff)
    const handleLength = 12;
    for (let y = 0; y < handleLength; y++) {
        const mat = (y % 3 === 0) ? getGoldAccentMat() : getDarkArmorMat(); // Gold bands
        addWeaponVoxel(0, y, 0, mat);
    }

    // Axe head (large, asymmetrical)
    const axeWidth = 5;
    const axeHeight = 4;

    // Main axe blade
    for (let x = 1; x <= axeWidth; x++) {
        for (let y = handleLength - axeHeight; y < handleLength; y++) {
            // Skip some voxels for shape
            if (x === axeWidth && y < handleLength - 1) continue;
            if (x === axeWidth - 1 && y < handleLength - 2) continue;

            const mat = (x === axeWidth || y === handleLength - 1) ? getGoldAccentMat() : getDarkArmorMat();
            addWeaponVoxel(x, y, 0, mat);
        }
    }

    // Spike on top
    for (let y = 0; y < 2; y++) {
        addWeaponVoxel(0, handleLength + y, 0, getGoldAccentMat());
    }

    // Merge weapon geometries
    for (const colorHex in weaponGeometriesByMaterial) {
        if (weaponGeometriesByMaterial[colorHex].length > 0) {
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(
                weaponGeometriesByMaterial[colorHex]
            );
            const material = _getMaterialByHex_Cached(colorHex);
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            weaponGroup.add(mesh);

            // Debug log to check material
            console.log(`Weapon material for color ${colorHex}:`, material);
        }
    }

    // Position the weapon in the left hand
    weaponGroup.position.set(0, -leftArmLength * SKELETON_VOXEL_SIZE, 1 * SKELETON_VOXEL_SIZE);
    leftArmGroup.add(weaponGroup);

    // === Cape/Cloak ===
    // Add a flowing cape/cloak to the back
    const capeWidth = 5;
    const capeHeight = 8;

    for (let x = -Math.floor(capeWidth/2); x <= Math.floor(capeWidth/2); x++) {
        for (let y = 0; y < capeHeight; y++) {
            // Skip some voxels at the edges for a tattered look
            if (Math.abs(x) === Math.floor(capeWidth/2) && random() < 0.3) continue;
            if (y === capeHeight - 1 && random() < 0.5) continue;

            const z = -2 - Math.floor(y/3); // Make cape flow backward
            const mat = getDarkArmorMat();
            addVoxel('core', x, shoulderY - y, z, mat);
        }
    }

    // Helper function to merge geometries for a group
    const mergeGroupGeometries = (groupName, targetGroup) => {
        for (const colorHex in geometriesByMaterial[groupName]) {
            if (geometriesByMaterial[groupName][colorHex].length > 0) {
                const mergedGeometry = BufferGeometryUtils.mergeGeometries(
                    geometriesByMaterial[groupName][colorHex]
                );
                const material = _getMaterialByHex_Cached(colorHex);
                const mesh = new THREE.Mesh(mergedGeometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                targetGroup.add(mesh);

                // Debug log to check material
                console.log(`${groupName} material for color ${colorHex}:`, material);
            }
        }
    };

    // Call merge for each group
    mergeGroupGeometries('core', coreGroup);
    mergeGroupGeometries('head', headGroup);
    mergeGroupGeometries('leftArm', leftArmGroup);
    mergeGroupGeometries('rightArm', rightArmGroup);
    mergeGroupGeometries('leftLeg', leftLegGroup);
    mergeGroupGeometries('rightLeg', rightLegGroup);

    // Add all groups to the final group
    finalGroup.add(coreGroup);
    finalGroup.add(headGroup);
    finalGroup.add(leftArmGroup);
    finalGroup.add(rightArmGroup);
    finalGroup.add(leftLegGroup);
    finalGroup.add(rightLegGroup);

    // Dispose the geometry templates
    voxelGeo.dispose();
    headVoxelGeo.dispose();

    // Apply overall scale if needed
    finalGroup.scale.set(scale, scale, scale);

    // Rotate group to face forward (Z+) by default
    finalGroup.rotation.y = Math.PI;

    // Add type information to ensure proper animation handling
    finalGroup.userData.type = 'catacombs_overlord';

    // Add attack hitbox data
    finalGroup.userData.attackHitbox = {
        radius: 16.0 * scale, // Reduced from 25.0 to match the smaller boss size
        damage: 1, // 1 damage as per user preference
        knockback: 10.0 // Strong knockback
    };

    console.log("Created Catacomb Overlord Boss Model");
    return finalGroup;
}
