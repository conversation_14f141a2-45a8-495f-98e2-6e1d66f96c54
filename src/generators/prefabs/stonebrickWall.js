import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    stonebrickMaterialPrimary, stonebrickMaterialSecondary, stonebrickCenterMaterial, stonebrickMortarMaterial,
    mossMaterialPrimary, mossMaterialSecondary,
    mulberry32, _getMaterialByHex_Cached, getOrCreateGeometry
} from './shared.js';

/**
 * Creates a Stonebrick voxel wall segment with a pixel art inspired pattern.
 * Uses Geometry Merging for performance.
 * Does NOT include vines or torches anymore.
 * Returns an object: { group: THREE.Group }
 */
export function createStonebrickWallSegment(
    width, height, depth, roomData,
) {
    // --- INPUT VALIDATION ---
    if (!width || !height || !depth || width <= 0 || height <= 0 || depth <= 0) {
        console.error(`createStonebrickWallSegment received invalid dimensions: w=${width}, h=${height}, d=${depth}. Skipping segment.`);
        return { group: new THREE.Group() }; // NEW Return (no torch positions)
    }
    // --- END INPUT VALIDATION ---

    const finalGroup = new THREE.Group();
    const geometriesByMaterial = {}; 

    // --- Use larger scale for walls to reduce voxel count ---
    const WALL_SCALE = ENVIRONMENT_PIXEL_SCALE * 1.5; 

    // --- Calculate Dimensions ---
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numY = Math.ceil(height / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / WALL_SCALE);
    const numY_env = Math.ceil(numY / WALL_SCALE);
    // --- End Calculation ---

    // --- Safeguard Dimensions (Ensure >= 1) ---
    const safeNumX = Math.max(1, numX);
    const safeNumY = Math.max(1, numY);
    const safeNumZ = Math.max(1, numZ);
    const safeNumXEnv = Math.max(1, numX_env);
    const safeNumYEnv = Math.max(1, numY_env);
    if (numX <= 0 || numY <= 0 || numZ <= 0 || numX_env <= 0 || numY_env <= 0) {
        console.warn(`Clamped dimension in createStonebrickWallSegment: w=${width}, h=${height}. Original base(${numX},${numY}), env(${numX_env},${numY_env}). Using safe(${safeNumX},${safeNumY}), env(${safeNumXEnv},${safeNumYEnv})`);
    }
    // --- End Safeguard ---

    const filledVoxels_env = Array(safeNumYEnv).fill(null).map(() => Array(safeNumXEnv).fill(false)); 

    const offsetX = (safeNumX - 1) * VOXEL_SIZE / 2;
    const offsetY = (safeNumY - 1) * VOXEL_SIZE / 2;
    const offsetZ = (safeNumZ - 1) * VOXEL_SIZE / 2;
    const mossProbability = 0.15; 

    // --- Use Seeded PRNG ---
    const roomSeed = roomData ? roomData.id * 31 + 17 : Date.now();
    const random = mulberry32(roomSeed);
    // --- End Seeded PRNG ---

    // --- Block Templates --- 
    const blockTemplates = [
        { w: 4, h: 3 },  
        { w: 3, h: 3 }, 
        { w: 5, h: 2 },  
        { w: 3, h: 4 },  
        { w: 4, h: 3 }, 
    ];
    // -----------------------

    // Get cached wall voxel geometry 
    const wallVoxelSize = VOXEL_SIZE * WALL_SCALE;
    const largeWallGeo = getOrCreateGeometry(
        `wall_${wallVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, VOXEL_SIZE)
    );

    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };
    const tempMatrix = new THREE.Matrix4();

    // --- Wall Generation using Environment Blocks ---
    let voxelsAdded = 0;
    for (let ey = 0; ey < safeNumYEnv; ey++) {
        for (let ex = 0; ex < safeNumXEnv; ex++) {
            if (!filledVoxels_env[ey][ex]) {
                let blockPlaced = false;
                const shuffledTemplates = [...blockTemplates].sort(() => random() - 0.5);

                for (const template of shuffledTemplates) {
                    if (ey + template.h <= safeNumYEnv && ex + template.w <= safeNumXEnv) {
                        let canPlace = true;
                        // Basic overlap check
                        for (let checkY = 0; checkY < template.h; checkY++) {
                            for (let checkX = 0; checkX < template.w; checkX++) {
                                if (filledVoxels_env[ey + checkY][ex + checkX]) {
                                    canPlace = false; break;
                                }
                            }
                            if (!canPlace) break;
                        }

                        if (canPlace) {
                            for (let blockEY = 0; blockEY < template.h; blockEY++) {
                                for (let blockEX = 0; blockEX < template.w; blockEX++) {
                                    const currentEY = ey + blockEY;
                                    const currentEX = ex + blockEX;
                                    filledVoxels_env[currentEY][currentEX] = true;

                                    // --- Determine material based on ENV coords ---
                                    let blockMaterial;
                                    const isOuterBottomEdge = (blockEY === template.h - 1);
                                    const isOuterRightEdge = (blockEX === template.w - 1);

                                    if (isOuterBottomEdge || isOuterRightEdge) {
                                        blockMaterial = stonebrickMortarMaterial;
                                    } else {
                                        const centerX = (template.w - 1) / 2;
                                        const centerY = (template.h - 1) / 2;
                                        const normDx = (blockEX - centerX) / Math.max(1, centerX);
                                        const normDy = (blockEY - centerY) / Math.max(1, centerY);
                                        const distSq = normDx * normDx + normDy * normDy;
                                        const clampedDistSq = Math.min(1.0, distSq);
                                        const edgeNoiseProbability = clampedDistSq * 0.8;
                                        const edgeMossProbability = clampedDistSq * mossProbability; // Use mossProbability

                                        const roll = random(); 
                                        if (roll < edgeMossProbability) { 
                                            blockMaterial = random() < 0.5 ? mossMaterialPrimary : mossMaterialSecondary;
                                        } else if (roll < edgeNoiseProbability) { 
                                            const brickTypeRoll = random(); 
                                            blockMaterial = brickTypeRoll < 0.7 ? stonebrickMaterialPrimary : stonebrickMaterialSecondary;
                                        } else { 
                                            blockMaterial = stonebrickCenterMaterial; 
                                        }
                                    }
                                    // --- End Material Logic ---

                                    // Add a single large wall voxel instead of many small ones
                                    const posX = currentEX * wallVoxelSize - offsetX;
                                    const posY = currentEY * wallVoxelSize - offsetY;
                                    
                                    // For depth, use a single layer
                                    for (let z = 0; z < safeNumZ; z += safeNumZ - 1) {
                                        const posZ = z * VOXEL_SIZE - offsetZ;
                                        tempMatrix.makeTranslation(posX, posY, posZ);
                                        addGeometry(largeWallGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;
                                    }

                                    // Add side caps for east and west faces of wall voxels
                                    // For east face (right side of wall)
                                    if (currentEX === safeNumXEnv - 1 || (blockEX === template.w - 1 && ex + template.w === safeNumXEnv)) {
                                        const sideFaceGeo = getOrCreateGeometry(
                                            `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                                            () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                                        );
                                        const rightPosX = posX + wallVoxelSize/2 + VOXEL_SIZE * 0.1;
                                        tempMatrix.makeTranslation(rightPosX, posY, 0);
                                        addGeometry(sideFaceGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;
                                    }

                                    // For west face (left side of wall)
                                    if (currentEX === 0 || blockEX === 0) {
                                        const sideFaceGeo = getOrCreateGeometry(
                                            `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                                            () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                                        );
                                        const leftPosX = posX - wallVoxelSize/2 - VOXEL_SIZE * 0.1;
                                        tempMatrix.makeTranslation(leftPosX, posY, 0);
                                        addGeometry(sideFaceGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;
                                    }

                                    // For north face (front side of wall)
                                    if (true) { // Always add north and south caps since they're always visible from top-down view
                                        const northFaceGeo = getOrCreateGeometry(
                                            `wall_ns_cap_${wallVoxelSize.toFixed(4)}`,
                                            () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, VOXEL_SIZE * 0.2)
                                        );
                                        const frontPosZ = -offsetZ - VOXEL_SIZE * 0.1;
                                        tempMatrix.makeTranslation(posX, posY, frontPosZ);
                                        addGeometry(northFaceGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;
                                        
                                        // For south face (back side of wall)
                                        const backPosZ = offsetZ + VOXEL_SIZE * 0.1;
                                        tempMatrix.makeTranslation(posX, posY, backPosZ);
                                        addGeometry(northFaceGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;
                                    }

                                    // Add top face for the top row of voxels
                                    if (currentEY === safeNumYEnv - 1 || (blockEY === template.h - 1 && ey + template.h === safeNumYEnv)) {
                                        // Create a top cap that covers the entire top surface
                                        const topCapGeo = getOrCreateGeometry(
                                            `wall_top_cap_${wallVoxelSize.toFixed(4)}`,
                                            () => new THREE.BoxGeometry(wallVoxelSize, VOXEL_SIZE * 0.2, depth)
                                        );
                                        
                                        // Position at the top of the wall voxel
                                        const topPosY = posY + wallVoxelSize/2 + VOXEL_SIZE * 0.1;
                                        
                                        // Add a single top cap that spans the full depth
                                        tempMatrix.makeTranslation(posX, topPosY, 0);
                                        addGeometry(topCapGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;
                                    }
                                }
                            }
                            blockPlaced = true; break; 
                        }
                    } 
                } 

                // Fill gaps if no block was placed
                if (!blockPlaced && !filledVoxels_env[ey][ex]) {
                    const posX = ex * wallVoxelSize - offsetX;
                    const posY = ey * wallVoxelSize - offsetY;
                    
                    // Use two voxels for depth (front and back faces)
                    for (let z = 0; z < safeNumZ; z += safeNumZ - 1) {
                        const posZ = z * VOXEL_SIZE - offsetZ;
                        tempMatrix.makeTranslation(posX, posY, posZ);
                        addGeometry(largeWallGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }
                    
                    // Add side caps for east and west faces of gap filler voxels
                    // For east face (right side of wall)
                    if (ex === safeNumXEnv - 1) {
                        const sideFaceGeo = getOrCreateGeometry(
                            `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                            () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                        );
                        const rightPosX = posX + wallVoxelSize/2 + VOXEL_SIZE * 0.1;
                        tempMatrix.makeTranslation(rightPosX, posY, 0);
                        addGeometry(sideFaceGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }

                    // For west face (left side of wall)
                    if (ex === 0) {
                        const sideFaceGeo = getOrCreateGeometry(
                            `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                            () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                        );
                        const leftPosX = posX - wallVoxelSize/2 - VOXEL_SIZE * 0.1;
                        tempMatrix.makeTranslation(leftPosX, posY, 0);
                        addGeometry(sideFaceGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }
                    
                    // For north face (front side of wall)
                    if (true) { // Always add north and south caps since they're always visible from top-down view
                        const northFaceGeo = getOrCreateGeometry(
                            `wall_ns_cap_${wallVoxelSize.toFixed(4)}`,
                            () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, VOXEL_SIZE * 0.2)
                        );
                        const frontPosZ = -offsetZ - VOXEL_SIZE * 0.1;
                        tempMatrix.makeTranslation(posX, posY, frontPosZ);
                        addGeometry(northFaceGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;
                        
                        // For south face (back side of wall)
                        const backPosZ = offsetZ + VOXEL_SIZE * 0.1;
                        tempMatrix.makeTranslation(posX, posY, backPosZ);
                        addGeometry(northFaceGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }

                    // Add top face for the top row of voxels
                    if (ey === safeNumYEnv - 1) {
                        // Create a top cap that covers the entire top surface
                        const topCapGeo = getOrCreateGeometry(
                            `wall_top_cap_${wallVoxelSize.toFixed(4)}`,
                            () => new THREE.BoxGeometry(wallVoxelSize, VOXEL_SIZE * 0.2, depth)
                        );
                        
                        // Position at the top of the wall voxel
                        const topPosY = posY + wallVoxelSize/2 + VOXEL_SIZE * 0.1;
                        
                        // Add a single top cap that spans the full depth
                        tempMatrix.makeTranslation(posX, topPosY, 0);
                        addGeometry(topCapGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }
                }
            }
        }
    }
    console.log(`Added ${voxelsAdded} wall voxels total.`);

    // --- Merge Geometries by Material ---
    for (const colorHex in geometriesByMaterial) {
        const material = _getMaterialByHex_Cached(colorHex);
        if (!material) continue;
        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            finalGroup.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for stonebrick wall, material: ${colorHex}`);
        }
    }
    // --- End Merge ---

    return { 
        group: finalGroup 
    };
} 