import * as THREE from 'three';
import {
    VOXEL_SIZE,
    aetherTorchHandleMaterial, // NEW: White handle
    elementalCoreMaterial,    // Use bright cyan for flame ('a0ffff')
    getOrCreateGeometry
} from './shared.js';

/**
 * Creates a single Aether Torch object (group) with handle and flame.
 * Designed to be placed on the ground.
 * Emits a blue light.
 * @returns {{group: THREE.Group, lightLocalPosition: THREE.Vector3, lightColor: number, lightIntensity: number, lightDistance: number}}
 */
export function createAetherTorchObject() {
    const group = new THREE.Group();
    
    const handleHeight = VOXEL_SIZE * 4; // Make it a bit taller than wall torch?
    const flameHeight = VOXEL_SIZE * 1.5;
    const flameY = handleHeight / 2 + flameHeight / 2 - VOXEL_SIZE * 0.5; // Flame sits slightly above handle top

    // Use cached geometry - make keys unique for aether torch
    const handleGeo = getOrCreateGeometry(
        `aether_torch_handle_${VOXEL_SIZE.toFixed(4)}`,
        () => new THREE.BoxGeometry(VOXEL_SIZE * 1.5, handleHeight, VOXEL_SIZE * 1.5) // Slightly wider base
    );
    const flameGeo = getOrCreateGeometry(
        `aether_torch_flame_${VOXEL_SIZE.toFixed(4)}`,
        () => new THREE.BoxGeometry(VOXEL_SIZE * 1.5, flameHeight, VOXEL_SIZE * 1.5)
    );

    // Handle (Base centered at origin Y=0)
    const handleMesh = new THREE.Mesh(handleGeo, aetherTorchHandleMaterial);
    handleMesh.position.y = handleHeight / 2; // Lift handle so base is at y=0
    handleMesh.castShadow = true;
    group.add(handleMesh);
    
    // Flame (Positioned relative to handle top)
    const flameLocalPosition = new THREE.Vector3(0, flameY, 0);
    // Use the emissive cyan material
    const flameMaterial = elementalCoreMaterial; 
    const flameMesh = new THREE.Mesh(flameGeo, flameMaterial);
    flameMesh.position.copy(flameLocalPosition);
    // No shadow from flame itself
    flameMesh.castShadow = false;
    flameMesh.receiveShadow = false; 
    group.add(flameMesh);

    group.name = "aetherTorchObject";
    group.userData.isInteriorObject = true;
    group.userData.objectType = 'aether_torch';

    // Define light properties to be used by the room generator
    const lightLocalPosition = flameLocalPosition.clone(); // Light originates from flame
    const lightColor = 0x60a0ff; // Soft Blue light
    const lightIntensity = 0.8; 
    const lightDistance = 5.0;

    return {
        group,
        lightLocalPosition,
        lightColor,
        lightIntensity,
        lightDistance
    };
} 