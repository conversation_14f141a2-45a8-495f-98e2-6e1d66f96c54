import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    stonebrickMaterialPrimary, stonebrickMaterialSecondary, stonebrickCenterMaterial,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Creates a stone archway door prefab.
 * Uses ENVIRONMENT_PIXEL_SCALE for chunky voxels.
 * @param {number} width World width of the opening.
 * @param {number} height World height of the opening.
 * @param {number} depth World depth of the arch structure.
 * @returns {THREE.Group} Group containing the archway mesh(es).
 */
export function createStoneArchwayDoor(width, height, depth) {
    const group = new THREE.Group();
    const geometriesByMaterial = {};
    const tempMatrix = new THREE.Matrix4();

    // Reuse existing stone materials
    const mat1 = stonebrickMaterialPrimary;
    const mat2 = stonebrickMaterialSecondary;
    const mat3 = stonebrickCenterMaterial;

    // --- Use Consistent Scaled Voxels ---
    const ARCH_VOXEL_SIZE = VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE;
    const archGeo = new THREE.BoxGeometry(ARCH_VOXEL_SIZE, ARCH_VOXEL_SIZE, ARCH_VOXEL_SIZE);
    // ----------------------------------

    // Calculate all dimensions in scaled voxels
    const numX_scaled = Math.ceil(width / ARCH_VOXEL_SIZE);
    const numY_scaled = Math.ceil(height / ARCH_VOXEL_SIZE);
    const numZ_scaled = Math.max(1, Math.ceil(depth / ARCH_VOXEL_SIZE)); // Ensure depth is at least 1 scaled voxel

    const pillarWidthVoxels_scaled = Math.max(1, Math.floor(numX_scaled * 0.15)); 
    const archOpeningWidth_scaled = numX_scaled - pillarWidthVoxels_scaled * 2;
    if (archOpeningWidth_scaled <= 0) {
        console.error(`Archway dimensions too small for pillars: w=${width}, pillar_scale=${pillarWidthVoxels_scaled}`);
        archGeo.dispose();
        return group; // Return empty group if dimensions are invalid
    }
    const archRadius_scaled = archOpeningWidth_scaled / 2;
    const archCenterY_scaled = numY_scaled - archRadius_scaled; 

    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };

    // Function to get a random stone material
    const getRandomStoneMat = () => {
        const rand = Math.random();
        // Adjust probabilities: 45% noisy1, 45% noisy2, 10% center
        if (rand < 0.45) return mat1;
        if (rand < 0.90) return mat2;
        return mat3;
    };

    // --- Build Pillars (using scaled voxels) ---
    const halfWidth_world = width / 2;
    const pillarBaseY_world = 0; // Pillars start at Y=0
    const pillarTopY_world = archCenterY_scaled * ARCH_VOXEL_SIZE;

    // Iterate Y (scaled height) and Z (scaled depth) for pillars
    for (let y = 0; y < archCenterY_scaled; y++) {
        // Iterate Z layers based on scaled depth
        for (let zLayerIndex = 0; zLayerIndex < numZ_scaled; zLayerIndex++) { 
            for (let xOffset = 0; xOffset < pillarWidthVoxels_scaled; xOffset++) {
                const mat = getRandomStoneMat();
                const posY = (y + 0.5) * ARCH_VOXEL_SIZE; // World position (center of voxel)
                // Position Z layers relative to the center of the depth
                const posZ = (zLayerIndex - (numZ_scaled - 1) / 2.0) * ARCH_VOXEL_SIZE; 

                // Left Pillar (World position based on scaled pillar width offset)
                const lx = -halfWidth_world + (xOffset + 0.5) * ARCH_VOXEL_SIZE;
                tempMatrix.makeTranslation(lx, posY, posZ);
                addGeometry(archGeo, mat, tempMatrix);

                // Right Pillar
                const rx = halfWidth_world - (xOffset + 0.5) * ARCH_VOXEL_SIZE;
                tempMatrix.makeTranslation(rx, posY, posZ);
                addGeometry(archGeo, mat, tempMatrix);
            }
        }
    }

    // --- Build Arch (Curve in XY plane, Depth along Z - Scaled) ---
    const archSteps = Math.ceil(archRadius_scaled * Math.PI * 0.75); // Steps based on scaled radius 
    const angleStep = Math.PI / Math.max(1, archSteps - 1);
    const archBaseY_world = pillarTopY_world; // Arch starts where pillars end
    const archCenterX_world = 0; // Arch is centered at X=0

    for (let i = 0; i < archSteps; i++) {
        const angle = i * angleStep;
        if (angle > Math.PI) continue; // Don't go past 180 degrees

        const currentRadius_world = archRadius_scaled * ARCH_VOXEL_SIZE;

        // Calculate X and Y offset based on angle (relative to arch center)
        const vx = Math.cos(angle) * currentRadius_world;
        const vy = Math.sin(angle) * currentRadius_world;

        const worldX = archCenterX_world - vx; // X coordinate in world space (cosine is positive on right)
        const worldY = archBaseY_world + vy; // Y coordinate in world space

        // Iterate through depth (Z axis using scaled size)
        for (let zLayerIndex = 0; zLayerIndex < numZ_scaled; zLayerIndex++) { 
             const mat = getRandomStoneMat();
             // Position Z layers relative to the center of the depth
             const posZ = (zLayerIndex - (numZ_scaled - 1) / 2.0) * ARCH_VOXEL_SIZE; 

             tempMatrix.makeTranslation(worldX, worldY, posZ);
             addGeometry(archGeo, mat, tempMatrix);
         }

         // Add mid-step points to fill gaps if radius is large enough
         if (i < archSteps - 1 && archRadius_scaled > 1) { 
             const midAngle = angle + angleStep / 2;
             if (midAngle <= Math.PI) {
                 const midVX = Math.cos(midAngle) * currentRadius_world;
                 const midVY = Math.sin(midAngle) * currentRadius_world;
                 const midWorldX = archCenterX_world - midVX;
                 const midWorldY = archBaseY_world + midVY;
                 for (let zLayerIndex = 0; zLayerIndex < numZ_scaled; zLayerIndex++) {
                     const mat = getRandomStoneMat();
                     const posZ = (zLayerIndex - (numZ_scaled - 1) / 2.0) * ARCH_VOXEL_SIZE;
                     tempMatrix.makeTranslation(midWorldX, midWorldY, posZ);
                     addGeometry(archGeo, mat, tempMatrix);
                 }
             }
         }
    }

    archGeo.dispose(); // Dispose the temporary scaled geometry

    // --- Merge Geometries --- 
    for (const colorHex in geometriesByMaterial) {
        const material = _getMaterialByHex_Cached(colorHex);
        if (!material) continue;
        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
             const mesh = new THREE.Mesh(mergedGeometry, material);
             mesh.castShadow = true;
             mesh.receiveShadow = true; // Arch might receive shadow
             group.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for stone archway, material: ${colorHex}`);
        }
    }
    
    group.name = "stoneArchwayDoor"; // Name the group

    // --- Add Black Plug Behind Opening (using ExtrudeGeometry) ---
    const openingWidth_world = width - 2 * (pillarWidthVoxels_scaled * ARCH_VOXEL_SIZE);
    const openingHeight_straight = pillarTopY_world;
    const openingArchRadius_world = archRadius_scaled * ARCH_VOXEL_SIZE;

    const openingShape = new THREE.Shape();
    const halfOpeningWidth = openingWidth_world / 2;
    const bottomYOffset = -0.05; // Extend slightly below floor level

    // Define shape path
    openingShape.moveTo(-halfOpeningWidth, bottomYOffset); 
    openingShape.lineTo(-halfOpeningWidth, openingHeight_straight);
    openingShape.absarc(0, openingHeight_straight, openingArchRadius_world, Math.PI, 0, true); // Set clockwise to TRUE
    openingShape.lineTo(halfOpeningWidth, bottomYOffset);
    // Shape closes

    // Use ExtrudeGeometry with full depth
    const extrudeSettings = { depth: depth, bevelEnabled: false }; // Use full depth passed to function
    const openingExtrudedGeo = new THREE.ExtrudeGeometry(openingShape, extrudeSettings);

    const openingPlaneMat = new THREE.MeshBasicMaterial({ color: 0x000000 }); // No DoubleSide needed
    const openingPlaneMesh = new THREE.Mesh(openingExtrudedGeo, openingPlaneMat);

    // Position extrusion to start slightly behind the front face
    const frontFaceOffset = -depth / 2 + 0.001; 
    openingPlaneMesh.position.set(0, 0, frontFaceOffset); 
    
    openingPlaneMesh.name = "doorwayBlockerPlug"; // Renamed for clarity
    openingPlaneMesh.castShadow = false;
    openingPlaneMesh.receiveShadow = false;
    group.add(openingPlaneMesh);
    // --- End Black Plug ---

    return group;
} 