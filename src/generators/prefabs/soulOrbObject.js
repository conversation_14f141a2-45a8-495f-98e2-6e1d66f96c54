/**
 * Creates a voxel-based soul orb object that can be dropped by enemies.
 */

import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// Soul orb colors
const SOUL_CORE_COLOR = 0x00ffff; // Cyan core
const SOUL_GLOW_COLOR = 0x80ffff; // Lighter cyan glow
const SOUL_OUTER_COLOR = 0xc0ffff; // Almost white outer glow

// Animation constants
const HOVER_HEIGHT = 0.5; // Height above ground
const BOB_HEIGHT = 0.2; // How much it bobs up and down
const BOB_SPEED = 2.0; // Speed of bobbing animation
const ROTATION_SPEED = 1.0; // Speed of rotation

/**
 * Creates a voxel-based soul orb object
 * @param {object} options - Configuration options
 * @param {THREE.Vector3} options.position - Position of the orb
 * @param {number} options.value - Value of the orb (affects size)
 * @param {number} options.seed - Random seed for variations
 * @returns {THREE.Group} The soul orb object
 */
export function createSoulOrbObject(options = {}) {
    const position = options.position || new THREE.Vector3(0, 0, 0);
    const value = options.value || 1;
    const seed = options.seed || Math.random() * 10000;
    
    // Create a seeded random function
    const rng = mulberry32(seed);
    
    // Create a group to hold the orb
    const group = new THREE.Group();
    
    // Set initial position with hover height
    const spawnPos = position.clone();
    spawnPos.y = HOVER_HEIGHT;
    group.position.copy(spawnPos);
    
    // Scale based on value
    const baseScale = 0.5;
    const valueScale = 0.2 * Math.min(value, 5); // Cap at value 5
    const scale = baseScale + valueScale;
    
    // Create the voxel model
    const orbVoxelSize = VOXEL_SIZE * 0.5; // Smaller voxels for detail
    
    // Create geometries for each color
    const geometriesByMaterial = {};
    const tempMatrix = new THREE.Matrix4();
    const baseGeometry = new THREE.BoxGeometry(orbVoxelSize, orbVoxelSize, orbVoxelSize);
    
    // Define the orb shape - a sphere with 3 layers of different colors
    const radius = 3; // Base radius in voxels
    const coreRadius = radius - 2; // Inner core radius
    
    // Create the orb voxels
    for (let x = -radius; x <= radius; x++) {
        for (let y = -radius; y <= radius; y++) {
            for (let z = -radius; z <= radius; z++) {
                // Calculate distance from center
                const distSq = x*x + y*y + z*z;
                const dist = Math.sqrt(distSq);
                
                // Skip voxels outside the sphere
                if (dist > radius) continue;
                
                // Determine color based on distance from center
                let color;
                if (dist <= coreRadius) {
                    color = SOUL_CORE_COLOR; // Core
                } else if (dist <= radius - 1) {
                    color = SOUL_GLOW_COLOR; // Middle layer
                } else {
                    color = SOUL_OUTER_COLOR; // Outer layer
                }
                
                // Add some variation to make it less uniform
                if (rng() < 0.3 && dist > coreRadius) {
                    // Skip some voxels for a more ethereal look
                    continue;
                }
                
                // Get or create material for this color
                const material = _getMaterialByHex_Cached(color);
                const materialKey = color.toString();
                
                if (!geometriesByMaterial[materialKey]) {
                    geometriesByMaterial[materialKey] = [];
                }
                
                // Position this voxel
                tempMatrix.makeTranslation(
                    x * orbVoxelSize,
                    y * orbVoxelSize,
                    z * orbVoxelSize
                );
                
                const clonedGeo = baseGeometry.clone();
                clonedGeo.applyMatrix4(tempMatrix);
                geometriesByMaterial[materialKey].push(clonedGeo);
            }
        }
    }
    
    // Combine geometries by material and create meshes
    for (const materialKey in geometriesByMaterial) {
        if (geometriesByMaterial[materialKey].length > 0) {
            const combinedGeometry = BufferGeometryUtils.mergeGeometries(
                geometriesByMaterial[materialKey]
            );
            
            const material = _getMaterialByHex_Cached(parseInt(materialKey));
            // Make the material emissive for a glowing effect
            material.emissive = new THREE.Color(parseInt(materialKey));
            material.emissiveIntensity = 0.7;
            
            const mesh = new THREE.Mesh(combinedGeometry, material);
            group.add(mesh);
        }
    }
    
    // Add a point light to make it glow
    const light = new THREE.PointLight(SOUL_CORE_COLOR, 1.5, 2);
    light.position.set(0, 0, 0);
    group.add(light);
    
    // Scale the group
    group.scale.set(scale, scale, scale);
    
    // Add animation data with fixed parameters
    group.userData = {
        type: 'soul_orb',
        value: value,
        isItem: true,
        creationTime: Date.now(),
        animation: {
            baseHeight: HOVER_HEIGHT,
            bobHeight: BOB_HEIGHT,
            bobSpeed: BOB_SPEED,
            rotationSpeed: ROTATION_SPEED
        },
        light: light
    };
    
    return group;
}
