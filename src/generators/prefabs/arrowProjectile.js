import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    ARROW_VOXEL_SIZE,
    shaftMaterial, headMaterial, fletchingMaterial, 
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Creates a voxel-based arrow projectile model.
 * Arrow is built along the +Z axis relative to its group,
 * then the group is rotated to point along +X by default.
 * @returns {THREE.Group} The arrow projectile model group.
 */
export function createArrowProjectileModel() {
    const arrowGroup = new THREE.Group();
    arrowGroup.name = "arrowProjectile";

    const arrowVoxelGeo = new THREE.BoxGeometry(ARROW_VOXEL_SIZE, ARROW_VOXEL_SIZE, ARROW_VOXEL_SIZE);
    const geometriesByMaterial = {};
    const tempMatrix = new THREE.Matrix4();

    const addArrowGeo = (x, y, z, material) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const geometry = arrowVoxelGeo.clone();
        // Position relative to arrow group origin
        const matrix = tempMatrix.makeTranslation(x * ARROW_VOXEL_SIZE, y * ARROW_VOXEL_SIZE, z * ARROW_VOXEL_SIZE);
        geometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(geometry);
    };

    // Build arrow along +Z axis
    const shaftLength = 6;
    const headLength = 2; // Not used directly, defines shape
    const fletchingLength = 2; // Not used directly, defines shape

    // Shaft
    for (let z = 0; z < shaftLength; z++) {
        addArrowGeo(0, 0, z, shaftMaterial);
    }

    // Head (Pointy shape at +Z end)
    const headBaseZ = shaftLength; // Z where the wider part of the head starts
    addArrowGeo(0, 0, headBaseZ + 1, headMaterial); // Center tip (furthest point)
    addArrowGeo(0, 0, headBaseZ, headMaterial);     // Center base
    addArrowGeo(1, 0, headBaseZ, headMaterial);     // Side base (+X)
    addArrowGeo(-1, 0, headBaseZ, headMaterial);    // Side base (-X)
    addArrowGeo(0, 1, headBaseZ, headMaterial);     // Top base (+Y)
    addArrowGeo(0, -1, headBaseZ, headMaterial);    // Bottom base (-Y)

    // Fletching (at the back, -Z end)
    const fletchBaseZ = -1; // Z where fletching starts
    for (let zOff = 0; zOff < 2; zOff++) {
        const currentZ = fletchBaseZ - zOff;
        addArrowGeo(1, 0, currentZ, fletchingMaterial); // +X
        addArrowGeo(-1, 0, currentZ, fletchingMaterial); // -X
        addArrowGeo(0, 1, currentZ, fletchingMaterial); // +Y
        addArrowGeo(0, -1, currentZ, fletchingMaterial); // -Y
    }

    // --- Merge Geometries --- 
    for (const colorHex in geometriesByMaterial) {
        const material = _getMaterialByHex_Cached(colorHex); // Get from cache
        if (!material) {
            console.warn(`Material not found for arrow: ${colorHex}`);
            continue;
        }
        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, material);
            // Shadows for arrows might be overkill unless they are large or slow
            // mesh.castShadow = true;
            // mesh.receiveShadow = true; 
            arrowGroup.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for arrow, material: ${colorHex}`);
        }
    }

    arrowVoxelGeo.dispose(); // Dispose the template geometry

    // Rotate the whole arrow model so its default direction (+Z) aligns with world +X
    // This makes using lookAt(target) simpler, as +X is often the default forward
    arrowGroup.rotation.y = -Math.PI / 2;

    return arrowGroup;
} 