// src/generators/prefabs/stoneFloor.js
import * as THREE from 'three';
import {
    stonebrickMortarMaterial // Use a simple existing material for now
} from './shared.js';

/**
 * Creates a simple, flat stone floor mesh.
 * @param {number} width World units width (X-axis).
 * @param {number} depth World units depth (Z-axis).
 * @param {object} roomData Room data (unused for now, but good practice).
 * @returns {THREE.Mesh} The floor mesh.
 */
export function createStoneFloor(width, depth, roomData) {
    const geometry = new THREE.PlaneGeometry(width, depth);
    // Clone the material and make it double-sided
    const material = stonebrickMortarMaterial.clone(); 
    material.side = THREE.DoubleSide; // Ensure it renders regardless of face direction

    const floorMesh = new THREE.Mesh(geometry, material);
    floorMesh.rotation.x = -Math.PI / 2; // Rotate plane to be horizontal
    floorMesh.position.y = 0; // Position at base Y level
    floorMesh.receiveShadow = true; // Floors should receive shadows
    floorMesh.name = "stoneFloor";

    return floorMesh;
} 