import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    elementalCoreMaterial, // For the bright glow ('a0ffff')
    ritualCircleBaseMaterial // For the darker base ('208080')
} from './shared.js';

// --- Voxel Data for Ritual Circle Symbol ---
// Derived from the pixel art reference.
// Grid seems ~40x40. Centered at (0,0) for x,z.
// Base layer (darker teal) at y=0, glowing parts (bright cyan) at y=1.
const ritualCircleShape = [
    // Base layer (y=0, color '208080') - Optional, gives thickness
    // Let's skip the base layer for now and make it flat first based on "on the ground"
    // If needed later, we can add a y=0 layer.

    // Glowing Layer (y=0, color 'a0ffff')
    // Outer Circle (Approx radius 19-20) - Simplified representation
    // Using <PERSON>resenham's circle algorithm idea or similar pixel approximation
    // Center (0,0)
    { x: 0, y: 0, z: 20, c: 'a0ffff'}, { x: 0, y: 0, z:-20, c: 'a0ffff'}, { x: 20, y: 0, z: 0, c: 'a0ffff'}, { x:-20, y: 0, z: 0, c: 'a0ffff'},
    { x: 4, y: 0, z: 20, c: 'a0ffff'}, { x:-4, y: 0, z: 20, c: 'a0ffff'}, { x: 4, y: 0, z:-20, c: 'a0ffff'}, { x:-4, y: 0, z:-20, c: 'a0ffff'},
    { x: 8, y: 0, z: 18, c: 'a0ffff'}, { x:-8, y: 0, z: 18, c: 'a0ffff'}, { x: 8, y: 0, z:-18, c: 'a0ffff'}, { x:-8, y: 0, z:-18, c: 'a0ffff'},
    { x: 11, y: 0, z: 17, c: 'a0ffff'},{ x:-11, y: 0, z: 17, c: 'a0ffff'},{ x: 11, y: 0, z:-17, c: 'a0ffff'},{ x:-11, y: 0, z:-17, c: 'a0ffff'},
    { x: 14, y: 0, z: 14, c: 'a0ffff'},{ x:-14, y: 0, z: 14, c: 'a0ffff'},{ x: 14, y: 0, z:-14, c: 'a0ffff'},{ x:-14, y: 0, z:-14, c: 'a0ffff'},
    { x: 17, y: 0, z: 11, c: 'a0ffff'},{ x:-17, y: 0, z: 11, c: 'a0ffff'},{ x: 17, y: 0, z:-11, c: 'a0ffff'},{ x:-17, y: 0, z:-11, c: 'a0ffff'},
    { x: 18, y: 0, z: 8, c: 'a0ffff'}, { x:-18, y: 0, z: 8, c: 'a0ffff'}, { x: 18, y: 0, z:-8, c: 'a0ffff'}, { x:-18, y: 0, z:-8, c: 'a0ffff'},
    { x: 20, y: 0, z: 4, c: 'a0ffff'}, { x:-20, y: 0, z: 4, c: 'a0ffff'}, { x: 20, y: 0, z:-4, c: 'a0ffff'}, { x:-20, y: 0, z:-4, c: 'a0ffff'},
    // Fill gaps slightly
    { x: 2, y: 0, z: 20, c: 'a0ffff'}, { x:-2, y: 0, z: 20, c: 'a0ffff'}, { x: 2, y: 0, z:-20, c: 'a0ffff'}, { x:-2, y: 0, z:-20, c: 'a0ffff'},
    { x: 6, y: 0, z: 19, c: 'a0ffff'}, { x:-6, y: 0, z: 19, c: 'a0ffff'}, { x: 6, y: 0, z:-19, c: 'a0ffff'}, { x:-6, y: 0, z:-19, c: 'a0ffff'},
    { x: 10, y: 0, z: 17, c: 'a0ffff'},{ x:-10, y: 0, z: 17, c: 'a0ffff'},{ x: 10, y: 0, z:-17, c: 'a0ffff'},{ x:-10, y: 0, z:-17, c: 'a0ffff'},
    { x: 13, y: 0, z: 15, c: 'a0ffff'},{ x:-13, y: 0, z: 15, c: 'a0ffff'},{ x: 13, y: 0, z:-15, c: 'a0ffff'},{ x:-13, y: 0, z:-15, c: 'a0ffff'},
    { x: 15, y: 0, z: 13, c: 'a0ffff'},{ x:-15, y: 0, z: 13, c: 'a0ffff'},{ x: 15, y: 0, z:-13, c: 'a0ffff'},{ x:-15, y: 0, z:-13, c: 'a0ffff'},
    { x: 17, y: 0, z: 10, c: 'a0ffff'},{ x:-17, y: 0, z: 10, c: 'a0ffff'},{ x: 17, y: 0, z:-10, c: 'a0ffff'},{ x:-17, y: 0, z:-10, c: 'a0ffff'},
    { x: 19, y: 0, z: 6, c: 'a0ffff'}, { x:-19, y: 0, z: 6, c: 'a0ffff'}, { x: 19, y: 0, z:-6, c: 'a0ffff'}, { x:-19, y: 0, z:-6, c: 'a0ffff'},
    { x: 20, y: 0, z: 2, c: 'a0ffff'}, { x:-20, y: 0, z: 2, c: 'a0ffff'}, { x: 20, y: 0, z:-2, c: 'a0ffff'}, { x:-20, y: 0, z:-2, c: 'a0ffff'},

    // Inner Ankh/Cross Shape
    // Vertical Bar (x=0, z ranges ~ -16 to 11)
    ...Array.from({ length: 28 }, (_, i) => ({ x: 0, y: 0, z: i - 16, c: 'a0ffff'})),
    { x:-1, y: 0, z: -16, c: 'a0ffff'}, { x: 1, y: 0, z: -16, c: 'a0ffff'}, // Base T
    { x:-1, y: 0, z: -15, c: 'a0ffff'}, { x: 1, y: 0, z: -15, c: 'a0ffff'},
    { x:-1, y: 0, z: -14, c: 'a0ffff'}, { x: 1, y: 0, z: -14, c: 'a0ffff'},
    { x:-2, y: 0, z: -13, c: 'a0ffff'}, { x: 2, y: 0, z: -13, c: 'a0ffff'},
    { x:-2, y: 0, z: -12, c: 'a0ffff'}, { x: 2, y: 0, z: -12, c: 'a0ffff'},

    // Horizontal Bar (z=0, x ranges ~ -15 to 15)
    ...Array.from({ length: 31 }, (_, i) => ({ x: i - 15, y: 0, z: 0, c: 'a0ffff'})),
    { x:-15, y: 0, z: -1, c: 'a0ffff'}, { x: 15, y: 0, z: -1, c: 'a0ffff'}, // Ends thicker
    { x:-15, y: 0, z: 1, c: 'a0ffff'}, { x: 15, y: 0, z: 1, c: 'a0ffff'},
    { x:-14, y: 0, z: -1, c: 'a0ffff'}, { x: 14, y: 0, z: -1, c: 'a0ffff'},
    { x:-14, y: 0, z: 1, c: 'a0ffff'}, { x: 14, y: 0, z: 1, c: 'a0ffff'},

    // Top Loop (Above z=11) - Semi circle centered approx (0, 15) radius 4
    { x: 0, y: 0, z: 12, c: 'a0ffff'}, // Connects to vertical bar
    { x: 1, y: 0, z: 12, c: 'a0ffff'}, { x: -1, y: 0, z: 12, c: 'a0ffff'},
    { x: 2, y: 0, z: 13, c: 'a0ffff'}, { x: -2, y: 0, z: 13, c: 'a0ffff'},
    { x: 3, y: 0, z: 14, c: 'a0ffff'}, { x: -3, y: 0, z: 14, c: 'a0ffff'},
    { x: 4, y: 0, z: 15, c: 'a0ffff'}, { x: -4, y: 0, z: 15, c: 'a0ffff'},
    { x: 4, y: 0, z: 16, c: 'a0ffff'}, { x: -4, y: 0, z: 16, c: 'a0ffff'},
    { x: 3, y: 0, z: 17, c: 'a0ffff'}, { x: -3, y: 0, z: 17, c: 'a0ffff'},
    { x: 2, y: 0, z: 18, c: 'a0ffff'}, { x: -2, y: 0, z: 18, c: 'a0ffff'},
    { x: 1, y: 0, z: 19, c: 'a0ffff'}, { x: -1, y: 0, z: 19, c: 'a0ffff'},
    { x: 0, y: 0, z: 19, c: 'a0ffff'}, // Top point

    // Triangle on Top
    { x: 0, y: 0, z: 20, c: 'a0ffff'}, // Connects to circle top
    { x: 1, y: 0, z: 21, c: 'a0ffff'}, { x: -1, y: 0, z: 21, c: 'a0ffff'},
    { x: 2, y: 0, z: 22, c: 'a0ffff'}, { x: -2, y: 0, z: 22, c: 'a0ffff'},
    { x: 3, y: 0, z: 23, c: 'a0ffff'}, { x: -3, y: 0, z: 23, c: 'a0ffff'},
    { x: 0, y: 0, z: 23, c: 'a0ffff'}, // Center top line
    { x: 1, y: 0, z: 23, c: 'a0ffff'}, { x: -1, y: 0, z: 23, c: 'a0ffff'},
    { x: 2, y: 0, z: 23, c: 'a0ffff'}, { x: -2, y: 0, z: 23, c: 'a0ffff'},

    // Side Wing/Crescent Shapes (Simplified)
    // Right side (Positive X) - Centered ~ (x=17, z=-5), arc
    { x: 16, y: 0, z: -10, c: 'a0ffff'}, { x: 17, y: 0, z: -11, c: 'a0ffff'}, { x: 18, y: 0, z: -10, c: 'a0ffff'},
    { x: 17, y: 0, z: -9, c: 'a0ffff'}, { x: 18, y: 0, z: -8, c: 'a0ffff'}, { x: 19, y: 0, z: -7, c: 'a0ffff'},
    { x: 19, y: 0, z: -6, c: 'a0ffff'}, { x: 20, y: 0, z: -5, c: 'a0ffff'}, { x: 20, y: 0, z: -4, c: 'a0ffff'},
    { x: 20, y: 0, z: -3, c: 'a0ffff'}, { x: 19, y: 0, z: -2, c: 'a0ffff'}, { x: 19, y: 0, z: -1, c: 'a0ffff'},
    { x: 18, y: 0, z: 0, c: 'a0ffff'}, // Connects to horizontal bar? No, bar ends at 15. Extend bar visually.
    { x: 17, y: 0, z: 1, c: 'a0ffff'}, { x: 18, y: 0, z: 2, c: 'a0ffff'}, { x: 17, y: 0, z: 3, c: 'a0ffff'},
    { x: 16, y: 0, z: 4, c: 'a0ffff'}, { x: 15, y: 0, z: 5, c: 'a0ffff'}, { x: 14, y: 0, z: 4, c: 'a0ffff'},

    // Left side (Negative X) - Mirror of right side
    { x: -16, y: 0, z: -10, c: 'a0ffff'}, { x: -17, y: 0, z: -11, c: 'a0ffff'}, { x: -18, y: 0, z: -10, c: 'a0ffff'},
    { x: -17, y: 0, z: -9, c: 'a0ffff'}, { x: -18, y: 0, z: -8, c: 'a0ffff'}, { x: -19, y: 0, z: -7, c: 'a0ffff'},
    { x: -19, y: 0, z: -6, c: 'a0ffff'}, { x: -20, y: 0, z: -5, c: 'a0ffff'}, { x: -20, y: 0, z: -4, c: 'a0ffff'},
    { x: -20, y: 0, z: -3, c: 'a0ffff'}, { x: -19, y: 0, z: -2, c: 'a0ffff'}, { x: -19, y: 0, z: -1, c: 'a0ffff'},
    { x: -18, y: 0, z: 0, c: 'a0ffff'}, // Mirror
    { x: -17, y: 0, z: 1, c: 'a0ffff'}, { x: -18, y: 0, z: 2, c: 'a0ffff'}, { x: -17, y: 0, z: 3, c: 'a0ffff'},
    { x: -16, y: 0, z: 4, c: 'a0ffff'}, { x: -15, y: 0, z: 5, c: 'a0ffff'}, { x: -14, y: 0, z: 4, c: 'a0ffff'},

    // Adding more points to fill gaps in the outer circle (Radius ~20)
    // Octant 1 (0 to Pi/4)
    { x: 0, y: 0, z: 20, c: 'a0ffff'}, { x: 1, y: 0, z: 20, c: 'a0ffff'}, { x: 2, y: 0, z: 20, c: 'a0ffff'}, { x: 3, y: 0, z: 20, c: 'a0ffff'}, { x: 4, y: 0, z: 20, c: 'a0ffff'},
    { x: 5, y: 0, z: 19, c: 'a0ffff'}, { x: 6, y: 0, z: 19, c: 'a0ffff'}, { x: 7, y: 0, z: 19, c: 'a0ffff'},
    { x: 8, y: 0, z: 18, c: 'a0ffff'}, { x: 9, y: 0, z: 18, c: 'a0ffff'},
    { x: 10, y: 0, z: 17, c: 'a0ffff'},{ x: 11, y: 0, z: 17, c: 'a0ffff'},
    { x: 12, y: 0, z: 16, c: 'a0ffff'},{ x: 13, y: 0, z: 15, c: 'a0ffff'},
    { x: 14, y: 0, z: 14, c: 'a0ffff'},{ x: 15, y: 0, z: 13, c: 'a0ffff'},
    { x: 16, y: 0, z: 12, c: 'a0ffff'},
    // Octant 2 (Pi/4 to Pi/2)
    { x: 17, y: 0, z: 11, c: 'a0ffff'},{ x: 17, y: 0, z: 10, c: 'a0ffff'},
    { x: 18, y: 0, z: 9, c: 'a0ffff'}, { x: 18, y: 0, z: 8, c: 'a0ffff'},
    { x: 19, y: 0, z: 7, c: 'a0ffff'}, { x: 19, y: 0, z: 6, c: 'a0ffff'}, { x: 19, y: 0, z: 5, c: 'a0ffff'},
    { x: 20, y: 0, z: 4, c: 'a0ffff'}, { x: 20, y: 0, z: 3, c: 'a0ffff'}, { x: 20, y: 0, z: 2, c: 'a0ffff'}, { x: 20, y: 0, z: 1, c: 'a0ffff'}, { x: 20, y: 0, z: 0, c: 'a0ffff'},
    
    // Apply symmetry to other 7 octants
    ...[...Array(15)].flatMap((_, i) => { // Points from Octant 1 & 2 excluding endpoints shared with axes
        const points = [
            { x: 1, y: 0, z: 20, c: 'a0ffff'}, { x: 2, y: 0, z: 20, c: 'a0ffff'}, { x: 3, y: 0, z: 20, c: 'a0ffff'}, { x: 4, y: 0, z: 20, c: 'a0ffff'},
            { x: 5, y: 0, z: 19, c: 'a0ffff'}, { x: 6, y: 0, z: 19, c: 'a0ffff'}, { x: 7, y: 0, z: 19, c: 'a0ffff'},
            { x: 8, y: 0, z: 18, c: 'a0ffff'}, { x: 9, y: 0, z: 18, c: 'a0ffff'},
            { x: 10, y: 0, z: 17, c: 'a0ffff'},{ x: 11, y: 0, z: 17, c: 'a0ffff'},
            { x: 12, y: 0, z: 16, c: 'a0ffff'},{ x: 13, y: 0, z: 15, c: 'a0ffff'},
            { x: 14, y: 0, z: 14, c: 'a0ffff'},{ x: 15, y: 0, z: 13, c: 'a0ffff'},
            { x: 16, y: 0, z: 12, c: 'a0ffff'},
            { x: 17, y: 0, z: 11, c: 'a0ffff'},{ x: 17, y: 0, z: 10, c: 'a0ffff'},
            { x: 18, y: 0, z: 9, c: 'a0ffff'}, { x: 18, y: 0, z: 8, c: 'a0ffff'},
            { x: 19, y: 0, z: 7, c: 'a0ffff'}, { x: 19, y: 0, z: 6, c: 'a0ffff'}, { x: 19, y: 0, z: 5, c: 'a0ffff'},
            { x: 20, y: 0, z: 4, c: 'a0ffff'}, { x: 20, y: 0, z: 3, c: 'a0ffff'}, { x: 20, y: 0, z: 2, c: 'a0ffff'}, { x: 20, y: 0, z: 1, c: 'a0ffff'}
        ];
        // We already have x=0, z=20 and x=20, z=0
        // Reflect across x, z, and diagonals
        return points.flatMap(p => [
            { ...p, x: -p.x },              // Reflect across Z axis
            { ...p, z: -p.z },              // Reflect across X axis
            { ...p, x: -p.x, z: -p.z },      // Reflect across origin
            { x: p.z, y: p.y, z: p.x, c: p.c },   // Reflect across y=x
            { x: -p.z, y: p.y, z: p.x, c: p.c }, // Reflect across y=x then Z axis
            { x: p.z, y: p.y, z: -p.x, c: p.c },  // Reflect across y=x then X axis
            { x: -p.z, y: p.y, z: -p.x, c: p.c }  // Reflect across y=x then origin
        ]);
    }).filter((v,i,a)=>a.findIndex(t=>(t.x === v.x && t.z === v.z))===i), // Deduplicate
    // Add back axis points explicitly
    { x: 0, y: 0, z:-20, c: 'a0ffff'}, { x:-20, y: 0, z: 0, c: 'a0ffff'},

    // Inner Ankh/Cross Shape (Keep as is for now, looks solid enough)
    // ... (Vertical Bar points) ...
    ...Array.from({ length: 28 }, (_, i) => ({ x: 0, y: 0, z: i - 16, c: 'a0ffff'})),
    { x:-1, y: 0, z: -16, c: 'a0ffff'}, { x: 1, y: 0, z: -16, c: 'a0ffff'}, // Base T
    { x:-1, y: 0, z: -15, c: 'a0ffff'}, { x: 1, y: 0, z: -15, c: 'a0ffff'},
    { x:-1, y: 0, z: -14, c: 'a0ffff'}, { x: 1, y: 0, z: -14, c: 'a0ffff'},
    { x:-2, y: 0, z: -13, c: 'a0ffff'}, { x: 2, y: 0, z: -13, c: 'a0ffff'},
    { x:-2, y: 0, z: -12, c: 'a0ffff'}, { x: 2, y: 0, z: -12, c: 'a0ffff'},

    // ... (Horizontal Bar points) ...
    ...Array.from({ length: 31 }, (_, i) => ({ x: i - 15, y: 0, z: 0, c: 'a0ffff'})),
    { x:-15, y: 0, z: -1, c: 'a0ffff'}, { x: 15, y: 0, z: -1, c: 'a0ffff'}, // Ends thicker
    { x:-15, y: 0, z: 1, c: 'a0ffff'}, { x: 15, y: 0, z: 1, c: 'a0ffff'},
    { x:-14, y: 0, z: -1, c: 'a0ffff'}, { x: 14, y: 0, z: -1, c: 'a0ffff'},
    { x:-14, y: 0, z: 1, c: 'a0ffff'}, { x: 14, y: 0, z: 1, c: 'a0ffff'},

    // ... (Top Loop points - seems dense enough) ...
    { x: 0, y: 0, z: 12, c: 'a0ffff'},{ x: 1, y: 0, z: 12, c: 'a0ffff'}, { x: -1, y: 0, z: 12, c: 'a0ffff'},
    { x: 2, y: 0, z: 13, c: 'a0ffff'}, { x: -2, y: 0, z: 13, c: 'a0ffff'},
    { x: 3, y: 0, z: 14, c: 'a0ffff'}, { x: -3, y: 0, z: 14, c: 'a0ffff'},
    { x: 4, y: 0, z: 15, c: 'a0ffff'}, { x: -4, y: 0, z: 15, c: 'a0ffff'},
    { x: 4, y: 0, z: 16, c: 'a0ffff'}, { x: -4, y: 0, z: 16, c: 'a0ffff'},
    { x: 3, y: 0, z: 17, c: 'a0ffff'}, { x: -3, y: 0, z: 17, c: 'a0ffff'},
    { x: 2, y: 0, z: 18, c: 'a0ffff'}, { x: -2, y: 0, z: 18, c: 'a0ffff'},
    { x: 1, y: 0, z: 19, c: 'a0ffff'}, { x: -1, y: 0, z: 19, c: 'a0ffff'},
    { x: 0, y: 0, z: 19, c: 'a0ffff'},

    // ... (Triangle on Top points - seems dense enough) ...
    { x: 0, y: 0, z: 20, c: 'a0ffff'},{ x: 1, y: 0, z: 21, c: 'a0ffff'}, { x: -1, y: 0, z: 21, c: 'a0ffff'},
    { x: 2, y: 0, z: 22, c: 'a0ffff'}, { x: -2, y: 0, z: 22, c: 'a0ffff'},
    { x: 3, y: 0, z: 23, c: 'a0ffff'}, { x: -3, y: 0, z: 23, c: 'a0ffff'},
    { x: 0, y: 0, z: 23, c: 'a0ffff'},{ x: 1, y: 0, z: 23, c: 'a0ffff'}, { x: -1, y: 0, z: 23, c: 'a0ffff'},
    { x: 2, y: 0, z: 23, c: 'a0ffff'}, { x: -2, y: 0, z: 23, c: 'a0ffff'},

    // Side Wing/Crescent Shapes - Adding points to fill gaps
    // Right side (Positive X)
    { x: 15, y: 0, z: -11, c: 'a0ffff'}, // Added
    { x: 16, y: 0, z: -10, c: 'a0ffff'}, { x: 17, y: 0, z: -11, c: 'a0ffff'}, { x: 18, y: 0, z: -10, c: 'a0ffff'},
    { x: 17, y: 0, z: -9, c: 'a0ffff'}, { x: 18, y: 0, z: -9, c: 'a0ffff'}, // Added
    { x: 18, y: 0, z: -8, c: 'a0ffff'}, { x: 19, y: 0, z: -8, c: 'a0ffff'}, // Added
    { x: 19, y: 0, z: -7, c: 'a0ffff'},
    { x: 19, y: 0, z: -6, c: 'a0ffff'}, { x: 20, y: 0, z: -6, c: 'a0ffff'}, // Added
    { x: 20, y: 0, z: -5, c: 'a0ffff'}, { x: 20, y: 0, z: -4, c: 'a0ffff'},
    { x: 20, y: 0, z: -3, c: 'a0ffff'}, { x: 20, y: 0, z: -2, c: 'a0ffff'}, // Added
    { x: 19, y: 0, z: -2, c: 'a0ffff'}, { x: 19, y: 0, z: -1, c: 'a0ffff'},
    { x: 18, y: 0, z: 0, c: 'a0ffff'}, // Connects to horizontal bar? No, bar ends at 15. Extended visually.
    { x: 17, y: 0, z: 0, c: 'a0ffff'}, // Added
    { x: 17, y: 0, z: 1, c: 'a0ffff'}, { x: 18, y: 0, z: 1, c: 'a0ffff'}, // Added
    { x: 18, y: 0, z: 2, c: 'a0ffff'}, { x: 17, y: 0, z: 3, c: 'a0ffff'},
    { x: 17, y: 0, z: 4, c: 'a0ffff'}, // Added
    { x: 16, y: 0, z: 4, c: 'a0ffff'}, { x: 15, y: 0, z: 5, c: 'a0ffff'}, { x: 14, y: 0, z: 4, c: 'a0ffff'},
    { x: 14, y: 0, z: 5, c: 'a0ffff'}, // Added

    // Left side (Negative X) - Mirror of right side
    { x: -15, y: 0, z: -11, c: 'a0ffff'}, // Added mirror
    { x: -16, y: 0, z: -10, c: 'a0ffff'}, { x: -17, y: 0, z: -11, c: 'a0ffff'}, { x: -18, y: 0, z: -10, c: 'a0ffff'},
    { x: -17, y: 0, z: -9, c: 'a0ffff'}, { x: -18, y: 0, z: -9, c: 'a0ffff'}, // Added mirror
    { x: -18, y: 0, z: -8, c: 'a0ffff'}, { x: -19, y: 0, z: -8, c: 'a0ffff'}, // Added mirror
    { x: -19, y: 0, z: -7, c: 'a0ffff'},
    { x: -19, y: 0, z: -6, c: 'a0ffff'}, { x: -20, y: 0, z: -6, c: 'a0ffff'}, // Added mirror
    { x: -20, y: 0, z: -5, c: 'a0ffff'}, { x: -20, y: 0, z: -4, c: 'a0ffff'},
    { x: -20, y: 0, z: -3, c: 'a0ffff'}, { x: -20, y: 0, z: -2, c: 'a0ffff'}, // Added mirror
    { x: -19, y: 0, z: -2, c: 'a0ffff'}, { x: -19, y: 0, z: -1, c: 'a0ffff'},
    { x: -18, y: 0, z: 0, c: 'a0ffff'}, // Mirror
    { x: -17, y: 0, z: 0, c: 'a0ffff'}, // Added mirror
    { x: -17, y: 0, z: 1, c: 'a0ffff'}, { x: -18, y: 0, z: 1, c: 'a0ffff'}, // Added mirror
    { x: -18, y: 0, z: 2, c: 'a0ffff'}, { x: -17, y: 0, z: 3, c: 'a0ffff'},
    { x: -17, y: 0, z: 4, c: 'a0ffff'}, // Added mirror
    { x: -16, y: 0, z: 4, c: 'a0ffff'}, { x: -15, y: 0, z: 5, c: 'a0ffff'}, { x: -14, y: 0, z: 4, c: 'a0ffff'},
    { x: -14, y: 0, z: 5, c: 'a0ffff'}, // Added mirror
];


// --- Main Prefab Function ---
export function createRitualCircleObject(options = {}) {
    const group = new THREE.Group();

    // Voxel size should be consistent with other environment elements
    const circleVoxelSize = VOXEL_SIZE; // Use the shared base voxel size
    const baseGeometry = getOrCreateGeometry('ritual_circle_voxel', () => new THREE.BoxGeometry(circleVoxelSize, circleVoxelSize, circleVoxelSize));
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    ritualCircleShape.forEach(voxel => {
        let hexColor = voxel.c;
        const material = _getMaterialByHex_Cached(hexColor);
        const materialKey = hexColor; // Use hex string as the key

        if (!geometriesByMaterial[materialKey]) {
            geometriesByMaterial[materialKey] = [];
        }

        // Create matrix for voxel position
        // Y position is kept at 0 for "on the ground" feel for now.
        // -- MODIFIED: Added small Y offset to prevent Z-fighting with floor --
        // -- MODIFIED AGAIN: Increased Y offset further --
        const yOffset = 0.5 * circleVoxelSize; // Increased from 0.1
        tempMatrix.makeTranslation(
            voxel.x * circleVoxelSize,
            (voxel.y * circleVoxelSize) + yOffset, // Apply offset here
            voxel.z * circleVoxelSize
        );

        // Clone base geometry, apply transform, and add to list
        const clonedGeo = baseGeometry.clone();
        clonedGeo.applyMatrix4(tempMatrix);
        geometriesByMaterial[materialKey].push(clonedGeo);
    });

    // Merge geometries and add meshes
    for (const materialKey in geometriesByMaterial) {
        if (geometriesByMaterial[materialKey].length > 0) {
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[materialKey], false);
            if (mergedGeometry) {
                const material = _getMaterialByHex_Cached(materialKey);
                const mesh = new THREE.Mesh(mergedGeometry, material);
                // Ritual circle probably shouldn't cast shadows, but might receive them
                mesh.castShadow = false;
                mesh.receiveShadow = true;
                mesh.name = `ritual_circle_part_${materialKey}`;
                group.add(mesh);

                // If the material is emissive (our 'a0ffff'), add a point light?
                // Or rely on bloom effect post-processing? Let's rely on bloom for now.
            } else {
                 console.warn(`[RitualCircle] Merged geometry for material ${materialKey} resulted in null`);
            }
        }
    }

    group.name = "ritualCircleObject";
    // --- Add userData ---
    group.userData = {
        ...(options.userData || {}), // Preserve any existing userData
        objectType: 'ritual_circle',
        isDestructible: false, // Ritual circles usually aren't destructible
        isInteractible: false, // Placeholder for potential future interaction
        isInteriorObject: true // Identify as an interior object for placement logic
        // No originalVoxels needed if not destructible
    };
    // --- End UserData ---

    // Position the group - currently centered at 0,0,0 which is likely floor level
    // No offset needed if all voxels are y=0

    return group;
} 