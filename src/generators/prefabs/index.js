// Wall prefabs
export { createStonebrickWallSegment } from './stonebrickWall.js';

// Floor prefabs
export { createFloorOverlay } from './floorOverlay.js';
export { createCaveFloor } from './caveFloor.js';
export { createStoneFloor } from './stoneFloor.js';

// Door prefabs
export { createStoneArchwayDoor } from './stoneArchwayDoor.js';

// Character prefabs
export { createElementalPlayerModel } from './elementalPlayer.js';
export { createSkeletonEnemyModel } from './skeletonEnemy.js';
export { createBatEnemyModel } from './batEnemy.js';
export { createZombieEnemyModel } from './zombieEnemy.js';
export { createMagmaGolemEnemyModel } from './magmaGolemEnemy.js';
export { createCatacombOverlordModel } from './catacombOverlordBoss.js';

// Projectile prefabs
export { createArrowProjectileModel } from './arrowProjectile.js';

// Interior object prefabs
export { createVineObject } from './vineObject.js';
export { createTorchObject } from './torchObject.js';
export { createStoneVaseObject } from './stoneVaseObject.js';
export { createStonePillarObject } from './stonePillarObject.js';
export { createStoneRubbleObject } from './stoneRubbleObject.js';
export { createRitualCircleObject } from './ritualCircleObject.js';
export { createAetherTorchObject } from './aetherTorchObject.js';
export { createSoulOrbObject } from './soulOrbObject.js';