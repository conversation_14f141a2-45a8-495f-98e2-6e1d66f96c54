import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

// Bat colors
const BAT_COLORS = {
    BODY: '222222', // Dark gray/black
    WING: '333333', // Slightly lighter gray for wings
    EYE: 'ff0000',  // Red eyes
    FANG: 'ffffff'  // White fangs
};

/**
 * Creates a voxel-based bat enemy model with animation-ready parts.
 * @param {number} [scale=1.0] Optional overall scale factor.
 * @returns {THREE.Group} The bat enemy model group.
 */
export function createBatEnemyModel(scale = 1.0) {
    const finalGroup = new THREE.Group();
    finalGroup.name = "BatEnemy";

    // Use a smaller voxel size for the bat
    const batVoxelSize = VOXEL_SIZE * 0.6;

    // Create animation-ready groups
    const bodyGroup = new THREE.Group();
    bodyGroup.name = "body";

    const leftWingGroup = new THREE.Group();
    leftWingGroup.name = "leftWing";

    const rightWingGroup = new THREE.Group();
    rightWingGroup.name = "rightWing";

    // Add groups to final group with proper hierarchy
    finalGroup.add(bodyGroup);
    bodyGroup.add(leftWingGroup);
    bodyGroup.add(rightWingGroup);

    // Position wings relative to body
    leftWingGroup.position.set(-0.5 * batVoxelSize, 0, 0);
    rightWingGroup.position.set(0.5 * batVoxelSize, 0, 0);

    // Store geometries by material for each group
    const geometriesByMaterial = {
        body: {},
        leftWing: {},
        rightWing: {}
    };

    // Store original voxel data for destruction effects
    const originalVoxels = [];

    // Create template geometry
    const voxelGeo = getOrCreateGeometry('bat_voxel', () => new THREE.BoxGeometry(batVoxelSize, batVoxelSize, batVoxelSize));
    const tempMatrix = new THREE.Matrix4();

    // Helper to add voxels to specific groups
    const addVoxel = (groupName, x, y, z, colorHex) => {
        if (!geometriesByMaterial[groupName][colorHex]) {
            geometriesByMaterial[groupName][colorHex] = [];
        }

        tempMatrix.makeTranslation(
            x * batVoxelSize,
            y * batVoxelSize,
            z * batVoxelSize
        );

        const clonedGeo = voxelGeo.clone();
        clonedGeo.applyMatrix4(tempMatrix);
        geometriesByMaterial[groupName][colorHex].push(clonedGeo);

        // Store original voxel data for destruction effects
        originalVoxels.push({
            x: x,
            y: y,
            z: z,
            c: colorHex // Store color hex
        });
    };

    // Helper to merge geometries for a group
    const mergeGroupGeometries = (groupName, targetGroup) => {
        for (const colorHex in geometriesByMaterial[groupName]) {
            if (geometriesByMaterial[groupName][colorHex].length > 0) {
                const mergedGeometry = BufferGeometryUtils.mergeGeometries(
                    geometriesByMaterial[groupName][colorHex],
                    false
                );

                if (mergedGeometry) {
                    const material = _getMaterialByHex_Cached(colorHex);
                    const mesh = new THREE.Mesh(mergedGeometry, material);
                    mesh.castShadow = true;
                    mesh.receiveShadow = true;
                    targetGroup.add(mesh);
                }
            }
        }
    };

    // Create bat body (small oval shape)
    // Body core
    for (let y = -1; y <= 1; y++) {
        for (let x = -1; x <= 1; x++) {
            // Skip corners for oval shape
            if (Math.abs(x) === 1 && Math.abs(y) === 1) continue;

            // Main body
            for (let z = -1; z <= 1; z++) {
                // Skip corners for oval shape
                if (Math.abs(z) === 1 && (Math.abs(x) === 1 || Math.abs(y) === 1)) continue;

                addVoxel('body', x, y, z, BAT_COLORS.BODY);
            }
        }
    }

    // Add eyes (red dots)
    addVoxel('body', -0.5, 0, 1.1, BAT_COLORS.EYE);
    addVoxel('body', 0.5, 0, 1.1, BAT_COLORS.EYE);

    // Add fangs
    addVoxel('body', -0.3, -0.5, 1.1, BAT_COLORS.FANG);
    addVoxel('body', 0.3, -0.5, 1.1, BAT_COLORS.FANG);

    // Create left wing - LONGER WINGS
    for (let x = -5; x <= -1; x++) { // Extended from -3 to -5 for longer wings
        // Wing shape (thinner at tips)
        const wingWidth = Math.max(1, 4 - Math.abs(x) * 0.7); // Adjusted for better wing shape

        for (let z = -wingWidth; z <= wingWidth; z++) {
            // Skip some voxels for wing shape
            if (Math.abs(z) === wingWidth && x <= -4) continue;

            // Vary y position for wing curvature - more dramatic curve
            const y = Math.sin((x + 5) / 5 * Math.PI) * 0.7; // Increased curve amplitude

            addVoxel('leftWing', x, y, z, BAT_COLORS.WING);
        }
    }

    // Add wing membrane details for more texture
    for (let x = -4; x <= -2; x += 2) {
        for (let z = -1; z <= 1; z += 2) {
            const y = Math.sin((x + 5) / 5 * Math.PI) * 0.7 - 0.2;
            addVoxel('leftWing', x, y, z, BAT_COLORS.BODY); // Darker color for membrane details
        }
    }

    // Create right wing (mirror of left) - LONGER WINGS
    for (let x = 1; x <= 5; x++) { // Extended from 3 to 5 for longer wings
        // Wing shape (thinner at tips)
        const wingWidth = Math.max(1, 4 - Math.abs(x) * 0.7); // Adjusted for better wing shape

        for (let z = -wingWidth; z <= wingWidth; z++) {
            // Skip some voxels for wing shape
            if (Math.abs(z) === wingWidth && x >= 4) continue;

            // Vary y position for wing curvature - more dramatic curve
            const y = Math.sin((x - 1) / 5 * Math.PI) * 0.7; // Increased curve amplitude

            addVoxel('rightWing', x, y, z, BAT_COLORS.WING);
        }
    }

    // Add wing membrane details for more texture
    for (let x = 2; x <= 4; x += 2) {
        for (let z = -1; z <= 1; z += 2) {
            const y = Math.sin((x - 1) / 5 * Math.PI) * 0.7 - 0.2;
            addVoxel('rightWing', x, y, z, BAT_COLORS.BODY); // Darker color for membrane details
        }
    }

    // Merge geometries for each group
    mergeGroupGeometries('body', bodyGroup);
    mergeGroupGeometries('leftWing', leftWingGroup);
    mergeGroupGeometries('rightWing', rightWingGroup);

    // Dispose the template geometry
    voxelGeo.dispose();

    // Apply overall scale
    finalGroup.scale.set(scale, scale, scale);

    // Set initial wing positions for animation
    leftWingGroup.rotation.z = Math.PI / 6;  // Slightly up
    rightWingGroup.rotation.z = -Math.PI / 6; // Slightly up

    // Add animation data to userData with enhanced values
    finalGroup.userData.animationData = {
        wingFlapSpeed: 6.0,         // Increased from 5.0 for faster flapping
        wingFlapAmplitude: Math.PI / 3, // Increased from PI/4 for wider flaps
        bodyBobAmplitude: 0.15,     // Increased from 0.1 for more movement
        bodyBobSpeed: 2.5,          // Increased from 2.0 for faster bobbing
        attackAnimationDuration: 0.5
    };

    // Add type information to ensure proper animation handling
    finalGroup.userData.type = 'bat';

    // Add attack hitbox data
    finalGroup.userData.attackHitbox = {
        radius: 2.5 * scale, // Increased from 0.8 to 2.5 to ensure hits connect
        damage: 5,
        knockback: 5.0
    };

    // Add destruction data
    finalGroup.userData.isDestructible = true;
    finalGroup.userData.objectType = 'bat';
    finalGroup.userData.originalVoxels = originalVoxels;
    finalGroup.userData.voxelScale = batVoxelSize;

    console.log("Created Bat Enemy Model");
    return finalGroup;
}
