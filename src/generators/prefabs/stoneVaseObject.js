import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached,
    // Use NEW stone grain materials + crack material for band
    stoneGrain1, // 958272
    stoneGrain2, // 9D8A7A
    stoneGrain3, // A59080
    stoneGrain4, // AA9586
    caveFloorCrackMaterial // 4a3a2a (For the carved band)
} from './shared.js';

// --- Voxel Data for Stone Pot Shape ---

// Store grain colors in an array for easier random selection
const STONE_GRAIN_COLORS = ['958272', '9d8a7a', 'a59080', 'aa9586'];
const BAND_COLOR = '4a3a2a'; // Dark color for carved band

// Define the shape using voxel coordinates
const stonePotShape = [
    // Base (Radius 1)
    ...generateSolidLayer(0, 1, STONE_GRAIN_COLORS[0]), // Use base grain color

    // Lower Body (Radius 2)
    ...generateSolidLayer(1, 2, STONE_GRAIN_COLORS[0]),
    ...generateSolidLayer(2, 2, STONE_GRAIN_COLORS[0]),
    ...generateSolidLayer(3, 2, STONE_GRAIN_COLORS[0]),

    // Main Body (Radius 2)
    ...generateSolidLayer(4, 2, STONE_GRAIN_COLORS[0]),
    ...generateSolidLayer(5, 2, STONE_GRAIN_COLORS[0]),

    // --- NEW: Carved Band (Radius 2, Dark Color) ---
    ...generateSolidLayer(6, 2, BAND_COLOR),

    // Neck (Taper in, Radius 1) - Shifted up
    ...generateSolidLayer(7, 1, STONE_GRAIN_COLORS[0]),
    ...generateSolidLayer(8, 1, STONE_GRAIN_COLORS[0]),

    // Rim (Square shape, 5x5 outer, 3x3 inner hole) - Shifted up, Larger opening
    ...generateRimLayer(9, 2, STONE_GRAIN_COLORS[0]), // Outer radius 2 (5x5)
    ...generateRimLayer(10, 2, STONE_GRAIN_COLORS[0]),
];

// Helper function to generate a solid square layer
function generateSolidLayer(y, radius, color) {
    const layer = [];
    for (let x = -radius; x <= radius; x++) {
        for (let z = -radius; z <= radius; z++) {
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// Helper function to generate a square rim layer
function generateRimLayer(y, radius, color) {
    const layer = [];
    for (let x = -radius; x <= radius; x++) {
        for (let z = -radius; z <= radius; z++) {
            // Only add voxels on the perimeter (not the center for radius > 0)
            if (radius === 0 || Math.abs(x) === radius || Math.abs(z) === radius) {
                layer.push({ x, y, z, c: color });
            }
        }
    }
    return layer;
}


// --- REMOVED OLD SHAPES AND HELPERS ---
// const patternVase1Shape = [...];
// const patternVase2Shape = [...];
// function generatePatternBand(...) { ... }

// Use only the new stone pot shape
const vaseShapes = [stonePotShape];

// --- Main Prefab Function ---
export function createStoneVaseObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 591);

    // Only one shape now, no need for random selection
    const selectedShapeData = vaseShapes[0];

    // Use a slightly larger voxel size for vases for visibility compared to base VOXEL_SIZE
    const vaseVoxelSize = VOXEL_SIZE * 2.5;
    const baseGeometry = getOrCreateGeometry('stone_vase_voxel', () => new THREE.BoxGeometry(vaseVoxelSize, vaseVoxelSize, vaseVoxelSize));
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    selectedShapeData.forEach(voxel => {
        let hexColor = voxel.c; // Color from shape (either grain base or band color)

        // Apply random grain color ONLY if it's not the band color
        if (hexColor !== BAND_COLOR) {
             const grainIndex = Math.floor(rng() * STONE_GRAIN_COLORS.length);
             hexColor = STONE_GRAIN_COLORS[grainIndex];
        }

        const material = _getMaterialByHex_Cached(hexColor);
        const materialKey = hexColor;

        if (!geometriesByMaterial[materialKey]) {
            geometriesByMaterial[materialKey] = [];
        }

        tempMatrix.makeTranslation(
            voxel.x * vaseVoxelSize,
            voxel.y * vaseVoxelSize,
            voxel.z * vaseVoxelSize
        );
        const clonedGeo = baseGeometry.clone();
        clonedGeo.applyMatrix4(tempMatrix);
        geometriesByMaterial[materialKey].push(clonedGeo);
    });

    // Merge geometries and add meshes
    for (const materialKey in geometriesByMaterial) {
        if (geometriesByMaterial[materialKey].length > 0) {
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[materialKey], false);
            if (mergedGeometry) {
                const material = _getMaterialByHex_Cached(materialKey);
                const mesh = new THREE.Mesh(mergedGeometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                mesh.name = `vase_part_${materialKey}`;
                group.add(mesh);
            } else {
                 console.warn(`[StoneVase] Merged geometry for material ${materialKey} resulted in null`);
            }
        }
    }

    group.name = "stoneVaseObject";
    // --- Add userData for destructibility ---
    // Helper function to get final color including grain/band for originalVoxels
    const getFinalVoxelColor = (originalColor, voxelRng) => {
        if (originalColor === BAND_COLOR) {
            return BAND_COLOR;
        } else {
            const grainIndex = Math.floor(voxelRng() * STONE_GRAIN_COLORS.length);
            return STONE_GRAIN_COLORS[grainIndex];
        }
    };

    // Log the options for debugging
    console.log('Stone vase options:', options);

    group.userData = {
        ...(options.userData || {}),
        objectType: 'stone_vase',
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : true, // Default to true if not specified
        destructionEffect: options.destructionEffect || 'collapse',
        health: options.health || 1,
        isInteriorObject: true,
        // Update originalVoxels to reflect new grain/band logic
        originalVoxels: selectedShapeData.map(v => {
            // Need a stable RNG per voxel for destruction consistency
            const voxelRng = mulberry32(seed * 13 + v.x * 5 + v.y * 7 + v.z * 11);
            return {...v, c: getFinalVoxelColor(v.c, voxelRng)};
        }),
        voxelScale: vaseVoxelSize
    };

    // Log the userData for debugging
    console.log('Stone vase userData:', group.userData);
    // --- End UserData Addition ---

    return group;
}