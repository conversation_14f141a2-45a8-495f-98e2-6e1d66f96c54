import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    PLAYER_PIXEL_SCALE_XY, PLAYER_PIXEL_SCALE_Z,
    PLAYER_VOXEL_WIDTH, PLAYER_VOXEL_HEIGHT, PLAYER_VOXEL_DEPTH,
    elementalCoreMaterial, elementalFlameLightMaterial, elementalFlameMidMaterial, elementalFlameDarkMaterial,
} from './shared.js';

/**
 * Creates a voxel-based player model resembling a blue fire elemental (v10: Deterministic).
 * Includes named groups for limb-like flame wisps for animation compatibility.
 * Uses cubic macro voxels for a chunky, 3D look.
 * @returns {THREE.Group} The player model group.
 */
export function createElementalPlayerModel() {
    const playerGroup = new THREE.Group();
    playerGroup.name = "ElementalPlayer";

    // --- Define Player Voxel Geometry ---
    const playerVoxelGeo = new THREE.BoxGeometry(
        PLAYER_VOXEL_WIDTH, 
        PLAYER_VOXEL_HEIGHT, 
        PLAYER_VOXEL_DEPTH
    );
    // ---------------------------------

    // --- Define Pivot Points (in Player Macro Voxel units, relative to feet center 0,0,0) ---
    const armPivotMacroX = 2; // Sideways offset for arm pivot from center
    const legPivotMacroX = 1; // Sideways offset for leg pivot from center
    // --- Calculate Body Base Y first ---
    const lowerBodyBaseY = 3; // Base Y coord of the lower body block
    const lowerBodyHeight = 5; // Height in voxels of the lower body block
    const torsoBaseY = lowerBodyBaseY + lowerBodyHeight; // Base Y coord of the torso block (8)
    const armPivotMacroY = torsoBaseY + 1; // Raise arm pivot 1 higher than torso base (9)
    // --- NOW set leg pivot based on body ---
    const legPivotMacroY = lowerBodyBaseY; // Legs pivot from the base of the lower body (3)
    // --- Calculate World Pivot Coordinates --- 
    const armPivotWorldY = armPivotMacroY * PLAYER_VOXEL_HEIGHT; // Calculate World Y based on Macro Y
    const legPivotWorldY = legPivotMacroY * PLAYER_VOXEL_HEIGHT;
    // -------------------------------------------------------------------------------------

    // --- Voxel Data Generation --- 
    const voxelData = []; 
    const random = Math.random; // Use standard Math.random for player build variations

    // Layer Y=0 to legHeight-1 relative to leg pivot (downwards)
    const legMacroHeight = 7; // Increased leg height
    // Loop y from 0 to height-3 for the deterministic branchy part
    for(let y = 0; y < legMacroHeight - 2; y++) { 
        const baseMatKey = (y > legMacroHeight - 5) ? 'flameDark' : 'flameMid';
        for(let z = 0; z >= -1; z--) { // Depth 2
            let xOff = 0; // No random offset
            
            let matKey = (z > -1) ? baseMatKey : 'flameDark';
            // Apply slight random variation
            if (matKey === 'flameMid' && random() < 0.1) matKey = 'flameLight';
            else if (matKey === 'flameDark' && random() < 0.15) matKey = 'flameMid';

            // Coordinates are relative to the leg pivot point
            voxelData.push({ x: xOff, y: -y, z: z, matKey: matKey, groupName: 'leftLeg' }); 
            voxelData.push({ x: xOff, y: -y, z: z, matKey: matKey, groupName: 'rightLeg' }); 
        }
    }
    // Explicitly add the bottom two layers (ankle/shoe) relative to leg pivot
    const ankleY_rel = legMacroHeight - 2;
    const shoeY_rel = legMacroHeight - 1;
    let ankleMatKey = 'flameMid'; 
    let shoeMatKey = 'flameDark';
    // Apply variation to ankle/shoe layers too
    if (random() < 0.1) ankleMatKey = 'flameDark';
    if (random() < 0.2) shoeMatKey = 'flameMid';

    for(let z = 0; z >= -1; z--) { // Solid 1x2 base relative to pivot
        voxelData.push({ x: 0, y: -ankleY_rel, z: z, matKey: ankleMatKey, groupName: 'leftLeg' }); 
        voxelData.push({ x: 0, y: -ankleY_rel, z: z, matKey: ankleMatKey, groupName: 'rightLeg' }); 
        voxelData.push({ x: 0, y: -shoeY_rel, z: z, matKey: shoeMatKey, groupName: 'leftLeg' }); 
        voxelData.push({ x: 0, y: -shoeY_rel, z: z, matKey: shoeMatKey, groupName: 'rightLeg' }); 
    }

    // Layer Y=lowerBodyBaseY to lowerBodyBaseY+lowerBodyHeight-1: Lower Body (Absolute Y coords)
    for(let y = lowerBodyBaseY; y < lowerBodyBaseY + lowerBodyHeight; y++) {
        for (let x = -2; x <= 2; x++) { 
             const baseMatKey = 'flameDark'; // Lower body is primarily dark
             for(let z = 0; z >= -2; z--) { // Depth 3
                 let matKey = baseMatKey;
                 let add = false;
                 if (z === 0) { add = true; } // Front layer solid
                 else if (z === -1) { add = Math.abs(x) <= 1; } // Middle layer narrower
                 else { add = Math.abs(x) === 0; matKey = 'flameDark'; } // Back center column only, ensure dark
                 
                 // Apply variation only if it should be added
                 if (add) {
                     if (matKey === 'flameDark' && random() < 0.15) matKey = 'flameMid'; // Chance to become mid-blue
                     voxelData.push({ x: x, y: y, z: z, matKey: matKey, groupName: 'core' }); // Add to core group
                 }
             }
        }
    }
    
    // Layer Y=torsoBaseY to torsoBaseY+armMacroHeight-1: Torso/Arms Area
    const armMacroHeight = 5; // Increased arm height
    for(let y_rel=0; y_rel < armMacroHeight; y_rel++) { // Relative Y within this section
        const coreY = torsoBaseY + y_rel; // Absolute Y for core
        
        // Core part of torso (Absolute Y coords)
        for (let x = -1; x <= 1; x++) { 
             const baseMatKey = 'flameMid'; // Torso is primarily mid-blue
             for(let z = 0; z >= -2; z--) { // Depth 3
                 let matKey = (z > -2 || x !== 0) ? baseMatKey : 'flameMid'; // Keep back center mid
                 // Apply variation
                 if (matKey === 'flameMid' && random() < 0.1) matKey = 'flameLight';
                 else if (matKey === 'flameMid' && random() < 0.08) matKey = 'flameDark'; // Lower chance to go dark

                 voxelData.push({ x: x, y: coreY, z: z, matKey: matKey, groupName: 'core' });
             }
        }

        // Arms (Relative Y coords to arm pivot, downwards)
        const armY_rel = -y_rel - 1; // Y starts at -1 and goes down
        const armBaseMatKey = 'flameMid'; // Arms primarily mid-blue
        for(let z = 0; z >= -2; z--) { // Depth 3
            let matKey = (z > -1) ? armBaseMatKey : 'flameDark'; // Darker back layers
            // Apply variation
            if (matKey === 'flameMid' && random() < 0.15) matKey = 'flameLight';
            else if (matKey === 'flameDark' && random() < 0.2) matKey = 'flameMid';

            // Coordinates are relative to the arm pivot point (X=0)
            voxelData.push({ x: 0, y: armY_rel, z: z, matKey: matKey, groupName: 'leftArm' });
            voxelData.push({ x: 0, y: armY_rel, z: z, matKey: matKey, groupName: 'rightArm' });
        }
    }

    // Layer Y=headBaseY to headBaseY+headHeight-1: Head/Upper Flames (Absolute Y)
    const headBaseY = torsoBaseY + armMacroHeight; // Base Y of head (13)
    const headHeight = 6;
    for(let y=headBaseY; y < headBaseY + headHeight; y++) {
        const layerWidth = Math.max(1, 3 - Math.floor((y - headBaseY) / 1.5)); // Head tapers upwards
        for (let x = -layerWidth; x <= layerWidth; x++) { 
             const distToCenterSq = x*x + (y-(headBaseY+2))*(y-(headBaseY+2)); // Distance from head center
             let baseMatKey = 'flameMid'; // Default outer material
             if (distToCenterSq <= 1) { baseMatKey = 'core'; } // Inner core material
             else if (distToCenterSq <= 4) { baseMatKey = 'flameLight'; } // Middle flame material
             // Removed random variation for outer flames

             for(let z = 0; z >= -2; z--) { // Depth 3
                 let matKey = baseMatKey; 

                 if (z === -1) { // Middle layer brightness adjustment
                    if (matKey === 'core') matKey = 'flameLight'; 
                 } else if (z === -2) { // Back layer brightness adjustment
                    if (matKey === 'core' || matKey === 'flameLight') matKey = 'flameMid'; 
                 }
                 
                 voxelData.push({ x: x, y: y, z: z, matKey: matKey, groupName: 'core' }); // Add to core group
             }
        }
    }
    // --- End Voxel Data Generation ---
    
    // --- Structure Assembly --- 

    // Create groups for animatable parts
    const leftArmGroup = new THREE.Group();
    leftArmGroup.name = "leftArm";
    const rightArmGroup = new THREE.Group();
    rightArmGroup.name = "rightArm";
    const leftLegGroup = new THREE.Group();
    leftLegGroup.name = "leftLeg";
    const rightLegGroup = new THREE.Group();
    rightLegGroup.name = "rightLeg";
    
    // Position limb groups at their WORLD pivot points (Offset from player base 0,0,0)
    leftArmGroup.position.set(-armPivotMacroX * PLAYER_VOXEL_WIDTH, armPivotWorldY, 0);
    rightArmGroup.position.set(armPivotMacroX * PLAYER_VOXEL_WIDTH, armPivotWorldY, 0);
    leftLegGroup.position.set(-legPivotMacroX * PLAYER_VOXEL_WIDTH, legPivotWorldY, 0);
    rightLegGroup.position.set(legPivotMacroX * PLAYER_VOXEL_WIDTH, legPivotWorldY, 0);

    // Add limb groups to the main player group
    playerGroup.add(leftArmGroup, rightArmGroup, leftLegGroup, rightLegGroup);

    // Stores geometries temporarily, keyed by material KEY and then by group name
    const geomsByMaterialKey = {
        core: { core: [], leftArm: [], rightArm: [], leftLeg: [], rightLeg: [] },
        flameLight: { core: [], leftArm: [], rightArm: [], leftLeg: [], rightLeg: [] },
        flameMid: { core: [], leftArm: [], rightArm: [], leftLeg: [], rightLeg: [] },
        flameDark: { core: [], leftArm: [], rightArm: [], leftLeg: [], rightLeg: [] },
    };
    
    const tempMatrix = new THREE.Matrix4();

    // Process generated voxel data to create geometries
    for (const data of voxelData) {
        // Use the standard playerVoxelGeo for all parts
        const geometry = playerVoxelGeo.clone(); 

        // Calculate world position based on absolute Y for core, relative for limbs
        let posX, posY, posZ;
        if (data.groupName === 'core') {
            posX = data.x * PLAYER_VOXEL_WIDTH;
            posY = data.y * PLAYER_VOXEL_HEIGHT; // Absolute Y stored in data
            posZ = data.z * PLAYER_VOXEL_DEPTH;
        } else { // Limbs: coordinates are relative to pivot
            posX = data.x * PLAYER_VOXEL_WIDTH;
            posY = data.y * PLAYER_VOXEL_HEIGHT; // Relative Y stored in data
            posZ = data.z * PLAYER_VOXEL_DEPTH;
        }
        
        tempMatrix.makeTranslation(posX, posY, posZ);
        geometry.applyMatrix4(tempMatrix); // Apply matrix IN PLACE
        
        // Add to the correct list based on group and material
        if (geomsByMaterialKey[data.matKey] && geomsByMaterialKey[data.matKey][data.groupName]) {
            geomsByMaterialKey[data.matKey][data.groupName].push(geometry);
        } else {
             console.warn(`Invalid matKey or groupName in player voxelData: ${data.matKey}, ${data.groupName}`);
             geometry.dispose(); // Dispose if not used
        }
    }
    playerVoxelGeo.dispose(); // Dispose the template macro geo
    // --- End Geometry Creation from Voxel Data ---

    // --- Merge Geometries and Create Meshes --- 
    const mergeAndAddMeshes = (geometrySource, targetGroup) => {
        // Map keys to actual material instances
        const materialMap = {
            core: elementalCoreMaterial,
            flameLight: elementalFlameLightMaterial,
            flameMid: elementalFlameMidMaterial,
            flameDark: elementalFlameDarkMaterial
        };
        
        for (const matKey in materialMap) {
            const geometries = geometrySource[matKey];
            const material = materialMap[matKey];

            if (!material || !geometries || geometries.length === 0) continue; 
            
            // Geometries are already positioned correctly (core absolute, limbs relative to pivot)
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries, false);
            
            // Dispose original geometries used in merge
            geometries.forEach(g => g.dispose());

            if (mergedGeometry) {
                const mesh = new THREE.Mesh(mergedGeometry, material);
                mesh.castShadow = true; 
                targetGroup.add(mesh);
            } else if (geometries.length > 0) { 
                console.warn("Failed to merge player geometry for material key:", matKey, "in group:", targetGroup.name);
            }
        }
    };

    // Prepare geometry sources for each group
    const groupSources = {
        core: {}, leftArm: {}, rightArm: {}, leftLeg: {}, rightLeg: {}
    };
    for (const matKey in geomsByMaterialKey) {
        groupSources.core[matKey] = geomsByMaterialKey[matKey].core;
        groupSources.leftArm[matKey] = geomsByMaterialKey[matKey].leftArm;
        groupSources.rightArm[matKey] = geomsByMaterialKey[matKey].rightArm;
        groupSources.leftLeg[matKey] = geomsByMaterialKey[matKey].leftLeg;
        groupSources.rightLeg[matKey] = geomsByMaterialKey[matKey].rightLeg;
    }

    // Merge and add meshes to the correct groups
    mergeAndAddMeshes(groupSources.core, playerGroup); // Core meshes added directly to playerGroup (absolute positions)
    mergeAndAddMeshes(groupSources.leftArm, leftArmGroup); // Limb meshes added to their respective groups (relative positions)
    mergeAndAddMeshes(groupSources.rightArm, rightArmGroup);
    mergeAndAddMeshes(groupSources.leftLeg, leftLegGroup);
    mergeAndAddMeshes(groupSources.rightLeg, rightLegGroup);
        
    // Adjust core light position based on head position
    const coreLightY = (headBaseY + 2) * PLAYER_VOXEL_HEIGHT; // Center of head Y
    const coreLight = new THREE.PointLight(0x60c0ff, 0.8, 5 * PLAYER_PIXEL_SCALE_XY); 
    // Position light relative to the player group's origin (absolute position)
    coreLight.position.set(0, coreLightY, PLAYER_VOXEL_DEPTH * 0.5); // Slightly forward 
    playerGroup.add(coreLight);

    return playerGroup;
} 