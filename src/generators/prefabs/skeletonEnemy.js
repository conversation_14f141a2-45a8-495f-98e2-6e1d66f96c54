import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, SKELETON_VOXEL_SIZE, HEAD_VOXEL_SCALE, HEAD_VOXEL_SIZE,
    boneMaterials, eyeMaterial, bowMaterials, skeletonBowMaterial, // Use exported materials
    mulberry32, _getMaterialByHex_Cached
} from './shared.js';

/**
 * Creates a voxel-based skeleton enemy model with a bow.
 * Uses standard VOXEL_SIZE with sub-voxels for the head.
 * Includes named groups for animation.
 * @param {number} [scale=1.0] Optional overall scale factor.
 * @returns {THREE.Group} The skeleton enemy model group.
 */
export function createSkeletonEnemyModel(scale = 1.0) {
    const finalGroup = new THREE.Group();
    finalGroup.name = "SkeletonArcher";
    // Nested structure: geometriesByMaterial[groupName][hexColor] = []
    const geometriesByMaterial = {
        core: {}, head: {}, leftArm: {}, rightArm: {}, leftLeg: {}, rightLeg: {}
    };

    // --- Geometry Templates ---
    const voxelGeo = new THREE.BoxGeometry(SKELETON_VOXEL_SIZE, SKELETON_VOXEL_SIZE, SKELETON_VOXEL_SIZE);
    const headVoxelGeo = new THREE.BoxGeometry(HEAD_VOXEL_SIZE, HEAD_VOXEL_SIZE, HEAD_VOXEL_SIZE);
    // ------------------------

    const tempMatrix = new THREE.Matrix4();
    const random = mulberry32(Date.now() + 1); // Use seeded random with a different seed offset

    // Updated addVoxel to include groupName and optional geometry template
    // Coordinates x, y, z are RELATIVE TO THE GROUP'S PIVOT
    const addVoxel = (groupName, x, y, z, material, matrixOverride = null, geometryTemplate = voxelGeo) => {
        if (!geometriesByMaterial[groupName]) {
            console.warn(`Invalid groupName passed to addVoxel: ${groupName}`);
            geometriesByMaterial[groupName] = {}; // Initialize if missing
        }
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[groupName][colorHex]) {
            geometriesByMaterial[groupName][colorHex] = [];
        }
        const geometry = geometryTemplate.clone();
        // Coordinates are RELATIVE TO PIVOT
        const matrix = matrixOverride || tempMatrix.makeTranslation(
            x * SKELETON_VOXEL_SIZE, // Positioning based on standard size for relative coords
            y * SKELETON_VOXEL_SIZE,
            z * SKELETON_VOXEL_SIZE
        );
        geometry.applyMatrix4(matrix);
        geometriesByMaterial[groupName][colorHex].push(geometry);
    };

    // --- Helper to get random bone material ---
    const getRandomBoneMat = () => boneMaterials[Math.floor(random() * boneMaterials.length)];

    // --- Define Pivot Points & Voxel Dimensions ---
    const legHeight = 7;
    const spineHeight = 6;
    const pelvisY = legHeight; // Pelvis starts where legs end (relative to feet at 0)
    const shoulderY = pelvisY + spineHeight; // Shoulders above spine
    const skullBaseY = shoulderY + 1; // Head starts just above shoulders

    // Pivots (in world space relative to finalGroup origin at feet center)
    const hipPivotY = pelvisY * SKELETON_VOXEL_SIZE;
    const hipPivotX = 1 * SKELETON_VOXEL_SIZE; // Sideways offset for hips
    const shoulderPivotY = shoulderY * SKELETON_VOXEL_SIZE;
    const shoulderPivotX = 2 * SKELETON_VOXEL_SIZE; // Wider shoulder offset
    const neckPivotY = shoulderY * SKELETON_VOXEL_SIZE; // Head pivots at shoulder height

    // --- Create Groups ---
    const coreGroup = new THREE.Group(); coreGroup.name = "core";
    const headGroup = new THREE.Group(); headGroup.name = "head";
    const leftArmGroup = new THREE.Group(); leftArmGroup.name = "leftArm";
    const rightArmGroup = new THREE.Group(); rightArmGroup.name = "rightArm";
    const leftLegGroup = new THREE.Group(); leftLegGroup.name = "leftLeg";
    const rightLegGroup = new THREE.Group(); rightLegGroup.name = "rightLeg";
    // -------------------

    // --- Build Skeleton (RELATIVE coordinates to pivots) ---

    // === Legs (Y=0 is hip pivot) - Straightened & Thicker ===
    const legTopOffset = -1; // Start slightly below hip pivot for connection
    for (let y = legTopOffset; y > legTopOffset - legHeight; y--) {
        // Build straight down, centered relative to pivot (X=0, Z=0)
        addVoxel('leftLeg',  0, y, 0, getRandomBoneMat()); // Main column
        addVoxel('leftLeg', -1, y, 0, getRandomBoneMat()); // Add thickness X left

        addVoxel('rightLeg', 0, y, 0, getRandomBoneMat()); // Main column
        addVoxel('rightLeg', 1, y, 0, getRandomBoneMat()); // Add thickness X right
        // Optional depth:
        addVoxel('leftLeg',  0, y, -1, getRandomBoneMat());
        addVoxel('rightLeg', 0, y, -1, getRandomBoneMat());
    }

    // === Feet (Relative to hip pivot) - Pointing Forward ===
    const footY_rel = legTopOffset - legHeight; // Y position relative to hip pivot
    const footLength = 2; // How many voxels forward (Z)
    const footWidth = 2; // How many voxels wide (X)
    const footOffsetX_rel = -(footWidth - 1) / 2; // Center the foot block relative to X=0

    for (let fx = 0; fx < footWidth; fx++) {
        for (let fz = 0; fz < footLength; fz++) { // Loop forward in Z
            const xPos_rel = footOffsetX_rel + fx;
            addVoxel('leftLeg', xPos_rel, footY_rel, fz, boneMaterials[0]); // Use lightest bone for feet
            addVoxel('rightLeg', xPos_rel, footY_rel, fz, boneMaterials[0]);
        }
    }

    // === Pelvis (Part of Core - Y coords are absolute, relative to feet=0) ===
    // Make 3 wide (X), 2 deep (Z)
    for (let x = -1; x <= 1; x++) {
        for (let z = 0; z >= -1; z--) {
            addVoxel('core', x, pelvisY, z, getRandomBoneMat()); // Use absolute pelvisY
        }
    }

    // === Spine (Part of Core - Absolute Y) ===
    // Make 2x2 thick with material variation
    for (let y = 1; y <= spineHeight; y++) {
        for (let x = 0; x <= 1; x++) { // Centered slightly forward
            for (let z = 0; z >= -1; z--) {
                const mat = (random() < 0.4) ? getRandomBoneMat() : boneMaterials[3]; // More chance for darker core bones
                addVoxel('core', x - 0.5, pelvisY + y, z, mat); // Use absolute Y
            }
        }
    }

    // === Ribs (Part of Core - Absolute Y) ===
    const ribsStartY = pelvisY + 2;
    const ribsEndY = shoulderY -1;
    const ribWidth = 1; // How far ribs extend sideways from spine
    for (let y = ribsStartY; y <= ribsEndY; y += 2) {
         const ribZOffset = (y % 4 === 0) ? -0.5 : 0; // Slight curve in Z
         for (let xOff = 1; xOff <= ribWidth; xOff++) {
            const mat1 = getRandomBoneMat();
            const mat2 = getRandomBoneMat();
            // Ribs extend from center spine (approx x=0)
            addVoxel('core', -xOff, y, ribZOffset, mat1); // Left rib
            addVoxel('core',  xOff, y, ribZOffset, mat1); // Right rib
            addVoxel('core', -xOff, y, ribZOffset - 1, mat2); // Add depth
            addVoxel('core',  xOff, y, ribZOffset - 1, mat2); // Add depth
         }
    }

    // === Shoulders (Part of Core - Absolute Y) ===
    // Make 2x2 thick, placed to align with shoulder pivots
    for (let xOff = -1; xOff <= 1; xOff += 2) { // Left (-1) and Right (+1) sides
         for (let x = 0; x <= 1; x++) { // Thickness along X
            for (let z = 0; z >= -1; z--) { // Thickness along Z
                // Position based on wider shoulderPivotX (which is 2 * size)
                addVoxel('core', xOff * shoulderPivotX / SKELETON_VOXEL_SIZE + x - 0.5, shoulderY, z, getRandomBoneMat());
            }
        }
     }

    // === Skull (Head Group - Y=0 is neck pivot) ===
    // Approx 3x3x3 using sub-voxels for detail
    const headOffsetY_rel = 1; // Offset head slightly above neck pivot
    const headSizeX = 3; // Base size in standard voxels
    const headSizeY = 3;
    const headSizeZ = 3;
    for (let ly = 0; ly < headSizeY; ly++) { // Loop large Y
         for (let lx = -Math.floor(headSizeX/2); lx <= Math.floor(headSizeX/2); lx++) { // Loop large X
            for (let lz = 1; lz >= -(headSizeZ - 2); lz--) { // Loop large Z (front is +Z)
                // Base material for this large voxel
                let mat = boneMaterials[1]; // Default mid-bone
                if (ly > 1 || (lz === 1 && Math.abs(lx) <= 0)) mat = boneMaterials[0]; // Lighter top/center front
                else if (lz < 0 && random() < 0.3) mat = boneMaterials[2]; // Darker sides/back chance

                // Chance to skip a large voxel for ruggedness (not on front layer)
                if (lz < 1 && random() < 0.05) continue;

                // --- Sub-Voxel Logic for Skull ---
                const baseOffsetX_rel = lx * SKELETON_VOXEL_SIZE;
                const baseOffsetY_rel = (ly + headOffsetY_rel) * SKELETON_VOXEL_SIZE;
                const baseOffsetZ_rel = lz * SKELETON_VOXEL_SIZE;
                for (let sx = 0; sx < HEAD_VOXEL_SCALE; sx++) {
                    for (let sy = 0; sy < HEAD_VOXEL_SCALE; sy++) {
                        for (let sz = 0; sz < HEAD_VOXEL_SCALE; sz++) {
                            // Center coords of the sub-voxel relative to the head pivot
                            const subVoxelCenterX_rel = baseOffsetX_rel + (sx - (HEAD_VOXEL_SCALE - 1) / 2.0) * HEAD_VOXEL_SIZE;
                            const subVoxelCenterY_rel = baseOffsetY_rel + (sy - (HEAD_VOXEL_SCALE - 1) / 2.0) * HEAD_VOXEL_SIZE;
                            const subVoxelCenterZ_rel = baseOffsetZ_rel + (sz - (HEAD_VOXEL_SCALE - 1) / 2.0) * HEAD_VOXEL_SIZE;
                            const subMatrix = tempMatrix.makeTranslation(subVoxelCenterX_rel, subVoxelCenterY_rel, subVoxelCenterZ_rel);

                            // Determine Material for Sub-Voxel
                            let subVoxelMaterial = mat; // Default to the large voxel material
                            const centerIndex = Math.floor(HEAD_VOXEL_SCALE / 2); // 1 for scale 2
                            const frontIndex = HEAD_VOXEL_SCALE - 1; // 1 for scale 2
                            // If it's the CENTER, BOTTOM, FRONT sub-voxel, use lightest bone (teeth)
                            if (sx === centerIndex && sy === 0 && sz === frontIndex) {
                                subVoxelMaterial = boneMaterials[0];
                            }

                            // Add the sub-voxel using its specific matrix and small geometry
                            addVoxel('head', 0, 0, 0, subVoxelMaterial, subMatrix, headVoxelGeo);
                        }
                    }
                }
            }
         }
    }

    // === Eyes (Head Group - relative to neck pivot) ===
    const eyeY_rel = headOffsetY_rel + 1; // Relative Y position of eyes
    const eyeZ_rel = 1; // Place eyes on the front large-voxel layer
    [-1, 1].forEach(eyeX_large => { // Loop for left (-1) and right (+1) large-voxel X position
        const baseOffsetX_rel = eyeX_large * SKELETON_VOXEL_SIZE;
        const baseOffsetY_rel = eyeY_rel * SKELETON_VOXEL_SIZE;
        const baseOffsetZ_rel = eyeZ_rel * SKELETON_VOXEL_SIZE;
        for (let sx = 0; sx < HEAD_VOXEL_SCALE; sx++) {
            for (let sy = 0; sy < HEAD_VOXEL_SCALE; sy++) {
                for (let sz = 0; sz < HEAD_VOXEL_SCALE; sz++) {
                    const subVoxelCenterX_rel = baseOffsetX_rel + (sx - (HEAD_VOXEL_SCALE - 1) / 2.0) * HEAD_VOXEL_SIZE;
                    const subVoxelCenterY_rel = baseOffsetY_rel + (sy - (HEAD_VOXEL_SCALE - 1) / 2.0) * HEAD_VOXEL_SIZE;
                    const subVoxelCenterZ_rel = baseOffsetZ_rel + (sz - (HEAD_VOXEL_SCALE - 1) / 2.0) * HEAD_VOXEL_SIZE;
                    const subMatrix = tempMatrix.makeTranslation(subVoxelCenterX_rel, subVoxelCenterY_rel, subVoxelCenterZ_rel);

                    // Determine Material for Eye Sub-Voxel
                    let subVoxelMaterial = boneMaterials[1]; // Default to standard bone
                    const centerIndex = Math.floor(HEAD_VOXEL_SCALE / 2);
                    const frontIndex = HEAD_VOXEL_SCALE - 1;
                    const backIndex = frontIndex -1; // One layer behind front
                    // Make a 2x1x2 (W x H x D in sub-voxels) eye socket
                    if ((sx === centerIndex || sx === centerIndex -1) && // Center two X indices
                        (sy === centerIndex) &&                         // Center Y index
                        (sz === frontIndex || sz === backIndex) ) {     // Front two Z indices
                        subVoxelMaterial = eyeMaterial; // Use eye material for socket
                    }

                    addVoxel('head', 0, 0, 0, subVoxelMaterial, subMatrix, headVoxelGeo);
                }
            }
        }
    });

    // --- Bow & Arms (Bow attached to Left Arm) ---
    const bowGroup = new THREE.Group();
    bowGroup.name = 'SkeletonBow';
    const bowGeometriesByMaterial = {};

    // Helper to add voxels relative to the bowGroup's origin
    const addBowVoxel = (x, y, z, material) => {
         const colorHex = material.color.getHexString();
         if (!bowGeometriesByMaterial[colorHex]) {
             bowGeometriesByMaterial[colorHex] = [];
         }
         const geometry = voxelGeo.clone(); // Use standard skeleton voxel geo
         const matrix = tempMatrix.makeTranslation(x * SKELETON_VOXEL_SIZE, y * SKELETON_VOXEL_SIZE, z * SKELETON_VOXEL_SIZE);
         geometry.applyMatrix4(matrix);
         bowGeometriesByMaterial[colorHex].push(geometry);
     };

    // Bow Materials
    const bowGripMaterial = bowMaterials[3]; // Darkest bow color for grip
    const bowStringMaterial = boneMaterials[3]; // Dark bone color for string

    // Bow Dimensions (Thinner)
    const bowLimbLength = 5;
    const bowThickness = 1; // Reduced thickness

    // Bow Grip (Centered at Bow Group's 0,0,0)
    // Loop adjusted for thickness 1
    addBowVoxel(0, 0, 0, bowGripMaterial);

    // Bow Limbs (Along Y axis, curve in X, relative to Bow Group's 0,0,0)
    for (let i = 1; i <= bowLimbLength; i++) {
         const curveX = -Math.floor(i * i * 0.3 / bowLimbLength);
         // Loop adjusted for thickness 1
         addBowVoxel(curveX,  i, 0, skeletonBowMaterial); // Use specific blue
         addBowVoxel(curveX, -i, 0, skeletonBowMaterial);
    }

    // String (Along Y axis, behind curve, relative to Bow Group's 0,0,0)
    const maxCurveX = -Math.floor(bowLimbLength * bowLimbLength * 0.3 / bowLimbLength);
    const stringX = maxCurveX - 1; // String is behind the curve
    for (let y = -bowLimbLength + 1; y <= bowLimbLength - 1; y++) {
         addBowVoxel(stringX, y, 0, bowStringMaterial); // Centered Z = 0
     }

    // Merge BOW geometries into the bowGroup
    for (const hexColor in bowGeometriesByMaterial) {
        const geometries = bowGeometriesByMaterial[hexColor];
        if (geometries && geometries.length > 0) {
            const mergedBowGeometry = BufferGeometryUtils.mergeGeometries(geometries, false);
            geometries.forEach(geo => geo.dispose()); // Dispose originals
            if (mergedBowGeometry) {
                const material = _getMaterialByHex_Cached(hexColor);
                if (material) {
                    const bowMesh = new THREE.Mesh(mergedBowGeometry, material);
                    bowMesh.castShadow = true;
                    bowMesh.receiveShadow = true;
                    bowGroup.add(bowMesh);
                } else { console.warn(`Bow material not found: ${hexColor}`); }
            } else { console.warn(`Failed to merge bow geo: ${hexColor}`); }
        }
    }

    // Position and Rotate Bow Group RELATIVE TO LEFT ARM PIVOT
    bowGroup.rotation.y = -Math.PI / 2; // Rotate to point forward
    const bowGripOffsetRelX = -0.5; // Closer X to shoulder pivot
    const bowGripOffsetRelY = -1.8; // Lower Y relative to shoulder pivot
    const bowGripOffsetRelZ = 2.5;  // Further forward Z relative to shoulder pivot
    bowGroup.position.set(
        bowGripOffsetRelX * SKELETON_VOXEL_SIZE,
        bowGripOffsetRelY * SKELETON_VOXEL_SIZE,
        bowGripOffsetRelZ * SKELETON_VOXEL_SIZE
    );
    leftArmGroup.add(bowGroup); // Add bow to the left arm

    // --- Arms Holding Rotated Bow (Relative coords to shoulder pivots) ---
    const upperArmLength = 4;
    const forearmSegments = 2; // Fewer segments for forearm interpolation

    // --- Left Arm ---
    addVoxel('leftArm', 0, 0, 0, getRandomBoneMat()); // Shoulder joint bone at pivot
    addVoxel('leftArm', 0, 0, -1, getRandomBoneMat()); // Add depth to joint

    // Upper arm - Thicker, extending DOWN and slightly FORWARD
    for (let yOff = 1; yOff <= upperArmLength; yOff++) {
        addVoxel('leftArm', -0.5, -yOff, -0.5, getRandomBoneMat()); // Main bone column
        addVoxel('leftArm',  0.5, -yOff, -0.5, getRandomBoneMat()); // Thickness X
    }
    // Start point for forearm (relative to left shoulder pivot)
    const forearmStartX_rel = 0;
    const forearmStartY_rel = -upperArmLength;
    const forearmStartZ_rel = -0.5;

    // Target point for forearm is the bow group's position (already relative to left arm pivot)
    const forearmTargetX_rel = bowGripOffsetRelX;
    const forearmTargetY_rel = bowGripOffsetRelY;
    const forearmTargetZ_rel = bowGripOffsetRelZ;

    // Interpolate forearm voxels
    for (let i = 1; i <= forearmSegments; i++) {
        const t = i / forearmSegments;
        const currentX_rel = forearmStartX_rel + (forearmTargetX_rel - forearmStartX_rel) * t;
        const currentY_rel = forearmStartY_rel + (forearmTargetY_rel - forearmStartY_rel) * t;
        const currentZ_rel = forearmStartZ_rel + (forearmTargetZ_rel - forearmStartZ_rel) * t;
        addVoxel('leftArm', currentX_rel, currentY_rel, currentZ_rel, getRandomBoneMat());
    }
    // Ensure final endpoint is placed
    addVoxel('leftArm', forearmTargetX_rel, forearmTargetY_rel, forearmTargetZ_rel, getRandomBoneMat());

    // --- Right Arm ---
    addVoxel('rightArm', 0, 0, 0, getRandomBoneMat()); // Shoulder joint bone at pivot
    addVoxel('rightArm', 0, 0, -1, getRandomBoneMat()); // Add depth to joint

    // Upper arm - Thicker, extending DOWN and slightly FORWARD
    for (let yOff = 1; yOff <= upperArmLength; yOff++) {
        addVoxel('rightArm',  0.5, -yOff, -0.5, getRandomBoneMat()); // Main bone column
        addVoxel('rightArm', -0.5, -yOff, -0.5, getRandomBoneMat()); // Thickness X
    }
    // Start point for forearm (relative to right shoulder pivot)
    const rForearmStartX_rel = 0;
    const rForearmStartY_rel = -upperArmLength;
    const rForearmStartZ_rel = -0.5;

    // --- Calculate Right Hand Target (String Pullback) ---
    // This needs world space calculations, performed temporarily
    const tempLeftArmPos = new THREE.Vector3(-shoulderPivotX, shoulderPivotY, 0);
    const tempRightArmPos = new THREE.Vector3(shoulderPivotX, shoulderPivotY, 0);
    leftArmGroup.position.copy(tempLeftArmPos); // Temporarily set world pos
    rightArmGroup.position.copy(tempRightArmPos); // Temporarily set world pos
    finalGroup.add(coreGroup, headGroup, leftArmGroup, rightArmGroup, leftLegGroup, rightLegGroup); // Temporarily add all groups
    finalGroup.updateMatrixWorld(true); // Update world matrices for calculations

    const stringPullbackOffset = 1.5; // How far back hand pulls string (in skeleton voxels)
    const stringPullbackBowLocalX = stringX - stringPullbackOffset;
    const stringPullbackBowLocalY = 0; // Y level with grip center in bow space
    const stringPullbackBowLocalZ = 0; // Z level is 0 for thin string

    const stringPullbackBowLocal = new THREE.Vector3(
        stringPullbackBowLocalX * SKELETON_VOXEL_SIZE,
        stringPullbackBowLocalY * SKELETON_VOXEL_SIZE,
        stringPullbackBowLocalZ * SKELETON_VOXEL_SIZE
    );

    const bowWorldMatrix = new THREE.Matrix4().copy(bowGroup.matrixWorld);
    const stringPullbackWorld = stringPullbackBowLocal.clone().applyMatrix4(bowWorldMatrix);

    const invRightArmWorldMatrix = new THREE.Matrix4().copy(rightArmGroup.matrixWorld).invert();
    const stringPullbackRightArmLocal = stringPullbackWorld.clone().applyMatrix4(invRightArmWorldMatrix);

    // Convert Right Arm Local (world units) back to Voxel coordinates relative to pivot
    const rightHandTargetX_rel = stringPullbackRightArmLocal.x / SKELETON_VOXEL_SIZE;
    const rightHandTargetY_rel = stringPullbackRightArmLocal.y / SKELETON_VOXEL_SIZE;
    const rightHandTargetZ_rel = stringPullbackRightArmLocal.z / SKELETON_VOXEL_SIZE;

    // Clean up temporary group additions
    finalGroup.remove(coreGroup, headGroup, leftArmGroup, rightArmGroup, leftLegGroup, rightLegGroup);
    // --- END Hand target calculation ---

    // Interpolate forearm voxels for right arm
    for (let i = 1; i <= forearmSegments; i++) {
        const t = i / forearmSegments;
        const currentX_rel = rForearmStartX_rel + (rightHandTargetX_rel - rForearmStartX_rel) * t;
        const currentY_rel = rForearmStartY_rel + (rightHandTargetY_rel - rForearmStartY_rel) * t;
        const currentZ_rel = rForearmStartZ_rel + (rightHandTargetZ_rel - rForearmStartZ_rel) * t;
        addVoxel('rightArm', currentX_rel, currentY_rel, currentZ_rel, getRandomBoneMat());
    }
    // Add hand voxels (ensure endpoint is covered)
    addVoxel('rightArm', rightHandTargetX_rel, rightHandTargetY_rel, rightHandTargetZ_rel, getRandomBoneMat());
    addVoxel('rightArm', rightHandTargetX_rel + 0.5, rightHandTargetY_rel, rightHandTargetZ_rel, getRandomBoneMat()); // Hand thickness
    addVoxel('rightArm', rightHandTargetX_rel, rightHandTargetY_rel + 0.5, rightHandTargetZ_rel, getRandomBoneMat());

    // --- Position Groups at Pivots ---
    // Core stays at origin (0,0,0) relative to finalGroup
    headGroup.position.set(0, neckPivotY, 0);
    leftArmGroup.position.set(-shoulderPivotX, shoulderPivotY, 0);
    rightArmGroup.position.set(shoulderPivotX, shoulderPivotY, 0);
    leftLegGroup.position.set(-hipPivotX, hipPivotY, 0);
    rightLegGroup.position.set(hipPivotX, hipPivotY, 0);

    // Add final groups to the main hierarchy
    finalGroup.add(coreGroup); // Core contains pelvis, spine, ribs, shoulders
    finalGroup.add(headGroup);
    finalGroup.add(leftArmGroup); // Contains bow
    finalGroup.add(rightArmGroup);
    finalGroup.add(leftLegGroup);
    finalGroup.add(rightLegGroup);

    // --- Merge Geometries PER GROUP ---
    const mergeGroupGeometries = (groupName, targetGroup) => {
        const groupGeometrySource = geometriesByMaterial[groupName];
        if (!groupGeometrySource) {
            console.warn(`No geometry data found for skeleton group: ${groupName}`);
            return;
        }

        for (const hexColor in groupGeometrySource) {
            const geometries = groupGeometrySource[hexColor];
            if (geometries.length > 0) {
                // Geometries are already relative to the group's pivot
                const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries, false);
                geometries.forEach(geo => geo.dispose()); // Dispose originals

                if (mergedGeometry) {
                    const material = _getMaterialByHex_Cached(hexColor);
                    if (material) {
                        const mesh = new THREE.Mesh(mergedGeometry, material);
                        mesh.castShadow = true;
                        mesh.receiveShadow = true;
                        targetGroup.add(mesh);
                    } else {
                        console.warn(`Material not found in cache for skeleton: ${hexColor}`);
                        mergedGeometry.dispose(); // Dispose if material fails
                    }
                } else {
                     console.warn(`Failed to merge skeleton geometries for ${groupName}, material ${hexColor}.`);
                }
            }
        }
    };

    // Call merge for each group
    mergeGroupGeometries('core', coreGroup);
    mergeGroupGeometries('head', headGroup);
    mergeGroupGeometries('leftArm', leftArmGroup); // Merges arm bones, bow is already added as meshes
    mergeGroupGeometries('rightArm', rightArmGroup);
    mergeGroupGeometries('leftLeg', leftLegGroup);
    mergeGroupGeometries('rightLeg', rightLegGroup);

    // Dispose the geometry templates
    voxelGeo.dispose();
    headVoxelGeo.dispose();

    // Apply overall scale if needed
    finalGroup.scale.set(scale, scale, scale);

    // Rotate group to face forward (Z+) by default
    finalGroup.rotation.y = Math.PI;

    console.log("Created Skeleton Enemy Model Group");
    return finalGroup;
}