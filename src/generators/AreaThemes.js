// src/generators/AreaThemes.js

/*
 * ==================================
 *      HOW TO ADD A NEW AREA THEME
 * ==================================
 * To add a new visual and gameplay theme for dungeon floors (e.g., Forest, Cave, Temple):
 * 
 * 1.  **Create Prefab Functions:** 
 *     - In `src/generators/voxelPrefabs.js` (or a similar file), create new functions - make the system modular (as you can see there are already multiple modular)
 *       that return stylized `THREE.Group` objects for walls and floors specific to the theme.
 *       Example: `createForestWallSegment(width, height)` and `createMossyFloor(width, depth)`.
 *     - These functions should ideally handle variations, textures, and potentially place theme-specific props (like vines on walls).
 * 
 * 2.  **Define Enemy Types (If Needed):**
 *     - If the new theme introduces unique enemies, define them in `src/entities/EnemyTypes.js`,
 *       specifying their stats, appearance (`geometry`, `material`), and AI behavior (`aiType`).
 * 
 * 3.  **Add Theme Entry in `AREA_THEMES` (Below):
 *     - Add a new key-value pair to the `AREA_THEMES` object below (e.g., `FOREST: { ... }`).
 *     - `name`: A display name (e.g., 'Mossy Forest').
 *     - `wallPrefab`: Reference the wall creation function (e.g., `createForestWallSegment`).
 *     - `floorPrefab`: Reference the floor creation function (e.g., `createMossyFloor`).
 *     - `enemyPool`: An array of enemy type strings (from `EnemyTypes.js`) that can spawn in this theme 
 *       (e.g., `['goblin', 'spider', 'skeleton']`).
 *     - `minEnemiesPerRoom`, `maxEnemiesPerRoom`: Control the number of enemies spawning in 'Normal' rooms.
 *     - (Optional) Add other theme-specific properties like ambient light color, fog settings, background music references, etc.
 * 
 * 4.  **Select Theme During Generation:**
 *     - In `src/scenes/DungeonHandler.js` (or wherever the floor generation is triggered, likely in `generateNewFloor`),
 *       modify the call to `generator.generateLayout()` to pass the new theme's key name.
 *       Example: `this.floorLayout = generator.generateLayout('FOREST');`
 *     - This selection logic will likely become more dynamic later, based on game progress (e.g., after defeating a boss).
 *
 * The `DungeonGenerator` and `DungeonHandler` are designed to use the theme data provided here
 * to automatically create the correct visuals and spawn the appropriate enemies.
 * ==================================
 */

import {
    createStonebrickWallSegment,
    createFloorOverlay,
    createCaveFloor,
    createStoneArchwayDoor
} from './prefabs/index.js';

/**
 * Defines the properties and assets associated with different dungeon area themes.
 */
const AREA_THEMES = {
    CATACUMBS: {
        name: 'Catacombs',
        displayName: 'Ancient Catacombs',
        // Prefab functions for generating visuals
        wallPrefab: createStonebrickWallSegment,
        floorPrefab: createCaveFloor,
        doorPrefab: createStoneArchwayDoor,
        // Enemy types that can spawn in this area
        enemyPool: [
            'skeleton_archer',
            'bat',
            'zombie',
            'magma_golem'
        ],
        // Min/Max enemies per normal room
        minEnemiesPerRoom: 1,
        maxEnemiesPerRoom: 3,
        // Removed TODO for theme-specific props
        // ambientLightColor: 0x404080,
        // fogColor: 0x050510,
        // fogDensity: 0.03,
    },
    // --- Add more themes below ---
    /*
    FOREST: {
        name: 'Forest',
        wallPrefab: createForestWallSegment, // Example
        floorPrefab: createMossyFloor,      // Example
        enemyPool: ['goblin', 'spider'],  // Example
        minEnemiesPerRoom: 2,
        maxEnemiesPerRoom: 4,
    }
    */
};

/**
 * Helper function to get a theme by name.
 * @param {string} themeName - The name of the theme (e.g., 'CATACUMBS').
 * @returns {object | null} The theme object or null if not found.
 */
function getThemeData(themeName) {
    return AREA_THEMES[themeName] || null;
}

// Helper function for random integer in a range (needed here too)
function randInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}


/**
 * Gets a random list of enemies for a room based on the theme.
 * @param {object} theme - The area theme object.
 * @returns {string[]} An array of enemy type strings.
 */
function getRandomEnemyList(theme) {
    const enemyList = [];
    if (!theme || !theme.enemyPool || theme.enemyPool.length === 0) {
        return enemyList; // Return empty if no pool defined
    }
    const count = randInt(theme.minEnemiesPerRoom, theme.maxEnemiesPerRoom);
    for (let i = 0; i < count; i++) {
        const randomIndex = Math.floor(Math.random() * theme.enemyPool.length);
        enemyList.push(theme.enemyPool[randomIndex]);
    }
    return enemyList;
}


export { AREA_THEMES, getThemeData, getRandomEnemyList };
