import * as THREE from 'three';
import { VOXEL_SIZE } from './prefabs/shared.js'; // Import the global voxel size
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js'; // Ensure this is imported

// Mapping user answers to colors (Adjust colors for flatter look)
const colorMap = {
    origin: {
        default: 0x888888, // Grey
        forest: 0x556B2F, // DarkOliveGreen
        mountain: 0xA0522D, // Sienna
        city: 0x708090, // SlateGray
    },
    companion: {
        default: 0xAAAAAA,
        animal: 0x8B4513, // SaddleBrown
        spirit: 0xADD8E6, // LightBlue
        none: 0x444444,
    },
    power: {
        default: 0xCCCCCC,
        strength: 0xB22222, // Firebrick
        magic: 0x9370DB, // MediumPurple
        knowledge: 0x4682B4, // SteelBlue
    },
    drive: {
        default: 0xEEEEEE,
        revenge: 0xFF4500, // OrangeRed
        justice: 0xFFD700, // Gold
        survival: 0x006400, // DarkGreen
    },
};

// Soul Warrior Colors (v5 - No Stripe, Noise Body)
const soulMainBlue = 0x33CCFF; // Bright cyan-blue
const soulHighlightCyan = 0xE0FFFF; // Light cyan / whiteish (Used for Head)
const soulDarkerBlue = 0x00AABB; // Darker blue shade

class CharacterGenerator {
    constructor() {
        console.log("CharacterGenerator Initialized");
        // Cache materials to avoid recreating them constantly
        this.materialCache = {};
        // Pre-cache Soul Warrior materials (v4)
        this._getMaterial(soulMainBlue);
        this._getMaterial(soulHighlightCyan);
        this._getMaterial(soulDarkerBlue); // Pre-cache darker blue
        // No face color needed
    }

    _getMaterial(color) {
        const colorHex = color instanceof THREE.Color ? color.getHex() : color;
        if (!this.materialCache[colorHex]) {
            // Use MeshLambertMaterial now
            this.materialCache[colorHex] = new THREE.MeshLambertMaterial({ color: colorHex });
        }
        return this.materialCache[colorHex];
    }

    createPlayerModel() {
        console.log("Generating Soul Warrior player model v5 (No Stripe, Noise Body)..."); // Updated log
        const playerGroup = new THREE.Group();
        
        // Geometry stores for non-limb parts
        const bodyGeometries = {}; 
        const headGeometries = {}; 
        // Removed hairGeometries
        const neckGeometries = {}; 
        
        // Geometry stores for individual limbs
        const leftLegGeometries = {};
        const rightLegGeometries = {};
        const leftArmGeometries = {};
        const rightArmGeometries = {};

        const tempMatrix = new THREE.Matrix4();
        const voxelGeo = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

        // --- Materials and Dimensions (v5) ---
        const skinMatForHead = this._getMaterial(soulHighlightCyan);
        const skinMatDefault = this._getMaterial(soulMainBlue);
        const pantsMat = this._getMaterial(soulMainBlue);
        const darkerBlueMat = this._getMaterial(soulDarkerBlue);

        const legWidthVoxels = 6;
        const legHeightVoxels = 14;
        const legDepthVoxels = 6;
        const legSpacingVoxels = 0;
        const bodyWidthVoxels = 11;
        const bodyHeightVoxels = 14;
        const headWidthVoxels = 9; // Changed to 9
        const headHeightVoxels = 8;
        const headDepthVoxels = 10;
        const bodyDepthVoxels = 11;
        const armWidthVoxels = 5;
        const armHeightVoxels = 13;
        const armDepthVoxels = 5;
        const neckWidthVoxels = 5;
        const neckHeightVoxels = 3;
        const neckDepthVoxels = 5;
        const shoeWidthVoxels = 8;
        const shoeHeightVoxels = 3;
        const shoeDepthVoxels = 8;

        // --- Define Pivot Points (Relative to character base 0,0,0) --- 
        // Hip Pivot (Top-center of leg block)
        const hipPivotY = (shoeHeightVoxels + legHeightVoxels - 0.5) * VOXEL_SIZE;
        const hipPivotZ = (bodyDepthVoxels - 1) / 2 * VOXEL_SIZE; // Aligned with body center Z
        // Shoulder Pivot (Approx top-center of where arm connects to body)
        const shoulderPivotY = (shoeHeightVoxels + legHeightVoxels + bodyHeightVoxels - 1.5) * VOXEL_SIZE; // Near top of body
        // Shoulder X needs to be at the calculated arm start X centers
        // const shoulderPivotXOffset = (bodyWidthVoxels -1)/2 * VOXEL_SIZE; // This was body edge, not arm center
        const shoulderPivotZ = (bodyDepthVoxels - 1) / 2 * VOXEL_SIZE; // Aligned with body center Z
        

        // --- Modified Add Geometry Helper --- 
        // Takes world coords, target store, and world pivot offset
        const addVoxelGeometryPivoted = (worldX, worldY, worldZ, material, targetStore, pivotOffset) => {
            const colorHex = material.color.getHexString();
            if (!targetStore[colorHex]) {
                targetStore[colorHex] = [];
            }
            const transformedGeo = voxelGeo.clone();
            // Translate geometry relative to the pivot point for the limb group
            tempMatrix.makeTranslation(
                worldX - pivotOffset.x,
                worldY - pivotOffset.y,
                worldZ - pivotOffset.z
            );
            transformedGeo.applyMatrix4(tempMatrix);
            targetStore[colorHex].push(transformedGeo);
        };
        // Original helper for non-pivoted parts (body, head, etc.)
         const addVoxelGeometryCentered = (x, y, z, material, targetStore) => {
            const colorHex = material.color.getHexString();
            if (!targetStore[colorHex]) {
                targetStore[colorHex] = [];
            }
            const transformedGeo = voxelGeo.clone();
            tempMatrix.makeTranslation(x * VOXEL_SIZE, y * VOXEL_SIZE, z * VOXEL_SIZE);
            transformedGeo.applyMatrix4(tempMatrix);
            targetStore[colorHex].push(transformedGeo);
        };

        // --- Build Legs/Pants --- (Use addVoxelGeometryPivoted for L/R legs)
        const legStartY = shoeHeightVoxels;
        const halfSpacing = legSpacingVoxels / 2;
        const halfLegWidth = legWidthVoxels / 2;
        const halfLegDepth = legDepthVoxels / 2;
        const bodyCenterZ = (bodyDepthVoxels - 1) / 2;
        const leftLegCenterX = -halfSpacing - halfLegWidth + 0.5;
        const rightLegCenterX = halfSpacing + halfLegWidth - 0.5;
        // Define hip offsets using corrected Y
        const leftHipOffset = new THREE.Vector3(leftLegCenterX * VOXEL_SIZE, hipPivotY, hipPivotZ);
        const rightHipOffset = new THREE.Vector3(rightLegCenterX * VOXEL_SIZE, hipPivotY, hipPivotZ);

        for (let y = 0; y < legHeightVoxels; y++) {
            const currentLegY = legStartY + y;
            for (let xOffset = 0; xOffset < legWidthVoxels; xOffset++) {
                const legLocalX = xOffset - halfLegWidth + 0.5;
                for (let zOffset = 0; zOffset < legDepthVoxels; zOffset++) {
                    const legLocalZ = zOffset - halfLegDepth + 0.5;
                    const finalZ = bodyCenterZ + legLocalZ;
                    // Calculate World Coordinates for the voxel
                    const voxelWorldY = currentLegY * VOXEL_SIZE + VOXEL_SIZE * 0.5; // Center Y
                    const voxelWorldZ = finalZ * VOXEL_SIZE; // Z is already centered
                    const voxelWorldXL = (leftLegCenterX + legLocalX) * VOXEL_SIZE;
                    const voxelWorldXR = (rightLegCenterX + legLocalX) * VOXEL_SIZE;

                    // Pass world coords to pivoted helper
                    addVoxelGeometryPivoted(voxelWorldXL, voxelWorldY, voxelWorldZ, pantsMat, leftLegGeometries, leftHipOffset);
                    addVoxelGeometryPivoted(voxelWorldXR, voxelWorldY, voxelWorldZ, pantsMat, rightLegGeometries, rightHipOffset);
                }
            }
        }

        // --- Build Shoes --- (Modify to add to leg stores using pivoted helper)
        const shoeMaterial = pantsMat;
        const halfShoeWidth = shoeWidthVoxels / 2;
        const halfShoeDepth = shoeDepthVoxels / 2;
        for (let y = 0; y < shoeHeightVoxels; y++) {
            const currentShoeY = y;
            for (let xOffset = 0; xOffset < shoeWidthVoxels; xOffset++) {
                const shoeLocalX = xOffset - halfShoeWidth + 0.5;
                for (let zOffset = 0; zOffset < shoeDepthVoxels; zOffset++) {
                    const shoeLocalZ = zOffset - halfShoeDepth + 0.5;
                    const finalZ = bodyCenterZ + shoeLocalZ;
                    
                    // Calculate World Coordinates for the shoe voxel
                    const voxelWorldY = currentShoeY * VOXEL_SIZE + VOXEL_SIZE * 0.5; 
                    const voxelWorldZ = finalZ * VOXEL_SIZE;
                    const voxelWorldXL = (leftLegCenterX + shoeLocalX) * VOXEL_SIZE;
                    const voxelWorldXR = (rightLegCenterX + shoeLocalX) * VOXEL_SIZE;
                    
                    // Add shoe voxels to the corresponding leg geometry store using pivoted helper
                    addVoxelGeometryPivoted(voxelWorldXL, voxelWorldY, voxelWorldZ, shoeMaterial, leftLegGeometries, leftHipOffset);
                    addVoxelGeometryPivoted(voxelWorldXR, voxelWorldY, voxelWorldZ, shoeMaterial, rightLegGeometries, rightHipOffset);
                }
            }
        }

        // --- Build Arms --- (Use addVoxelGeometryPivoted for L/R arms)
        const bodyStartY = legStartY + legHeightVoxels;
        const armStartY = bodyStartY + (bodyHeightVoxels - armHeightVoxels); 
        const armStartXLeft = -(bodyWidthVoxels - 1) / 2 - armWidthVoxels; 
        const armStartXRight = (bodyWidthVoxels - 1) / 2 + 1; 
        const armCenterZ = (bodyDepthVoxels - 1) / 2; 
        const halfArmDepth = armDepthVoxels / 2;
        const handHeightVoxels = 3; 
        // Define shoulder offsets using corrected Y and calculated arm X centers
        const leftShoulderPivotX = (armStartXLeft + armWidthVoxels / 2 - 0.5) * VOXEL_SIZE;
        const rightShoulderPivotX = (armStartXRight + armWidthVoxels / 2 - 0.5) * VOXEL_SIZE;
        const leftShoulderOffset = new THREE.Vector3(leftShoulderPivotX, shoulderPivotY, shoulderPivotZ);
        const rightShoulderOffset = new THREE.Vector3(rightShoulderPivotX, shoulderPivotY, shoulderPivotZ);

        for (let y = 0; y < armHeightVoxels; y++) {
            const currentArmY = armStartY + y;
            const armMaterial = this._getMaterial(soulMainBlue);
            for (let xOffset = 0; xOffset < armWidthVoxels; xOffset++) {
                for (let zOffset = 0; zOffset < armDepthVoxels; zOffset++) {
                    const armLocalZ = zOffset - halfArmDepth + 0.5;
                    const finalZ = armCenterZ + armLocalZ;
                     // Calculate World Coordinates for the voxel
                    const voxelWorldY = currentArmY * VOXEL_SIZE + VOXEL_SIZE * 0.5; // Center Y
                    const voxelWorldZ = finalZ * VOXEL_SIZE;
                    const voxelWorldXL = (armStartXLeft + xOffset) * VOXEL_SIZE;
                    const voxelWorldXR = (armStartXRight + xOffset) * VOXEL_SIZE;

                    // Pass world coords to pivoted helper
                    addVoxelGeometryPivoted(voxelWorldXL, voxelWorldY, voxelWorldZ, armMaterial, leftArmGeometries, leftShoulderOffset);
                    addVoxelGeometryPivoted(voxelWorldXR, voxelWorldY, voxelWorldZ, armMaterial, rightArmGeometries, rightShoulderOffset);
                }
            }
        }

        // --- Build Body/Shirt --- (v5 - Noise instead of stripe)
        // Removed bodyHighlightWidthVoxels

        for (let y = 0; y < bodyHeightVoxels; y++) {
             const currentY = bodyStartY + y;
             for (let x = 0; x < bodyWidthVoxels; x++) {
                 const currentX = x - (bodyWidthVoxels - 1) / 2;
                 // Determine material based on random chance
                 let bodyMaterial = (Math.random() < 0.6) // Adjust probability as needed (e.g., 60% main blue)
                                     ? skinMatDefault // soulMainBlue
                                     : darkerBlueMat;  // soulDarkerBlue

                 for (let zOffset = 0; zOffset < bodyDepthVoxels; zOffset++) { 
                     const bodyLocalZ = zOffset - (bodyDepthVoxels - 1) / 2 + 0.5;
                     const finalZ = bodyCenterZ + bodyLocalZ;
                     addVoxelGeometryCentered(currentX, currentY, finalZ, bodyMaterial, bodyGeometries);
                 }
             }
         }

        // --- Build Neck --- (Use addVoxelGeometryCentered, add to neckGeometries)
        const neckStartY = bodyStartY + bodyHeightVoxels; 
        const neckCenterZ = (bodyDepthVoxels - 1) / 2; 
        const halfNeckDepth = neckDepthVoxels / 2;
        for (let y = 0; y < neckHeightVoxels; y++) {
            const currentNeckY = neckStartY + y;
            const neckMaterial = skinMatDefault;
            for (let x = 0; x < neckWidthVoxels; x++) {
                const currentNeckX = x - (neckWidthVoxels - 1) / 2;
                for (let zOffset = 0; zOffset < neckDepthVoxels; zOffset++) {
                    const neckLocalZ = zOffset - halfNeckDepth + 0.5;
                    const finalZ = neckCenterZ + neckLocalZ;
                    addVoxelGeometryCentered(currentNeckX, currentNeckY, finalZ, neckMaterial, neckGeometries);
                }
            }
        }
        
        // --- Build Head/Face --- (v4 - Specific Pattern)
        const headStartY = neckStartY + neckHeightVoxels; 
        // const headStartX = -(headWidthVoxels - 1) / 2; // Not needed for centered loop
        const halfHeadDepth = headDepthVoxels / 2;

        for (let y = 0; y < headHeightVoxels; y++) {
            const currentHeadY = headStartY + y;
            const isTopLayer = (y === headHeightVoxels - 1);
            const isSecondLayer = (y === headHeightVoxels - 2);
            
            for (let x = 0; x < headWidthVoxels; x++) {
                const currentHeadX = x - (headWidthVoxels - 1) / 2; // Range -4 to 4 for width 9
                for (let zOffset = 0; zOffset < headDepthVoxels; zOffset++) {
                    let voxelMaterial;
                    const isFrontFace = (zOffset === 0);
                    const headLocalZ = zOffset - halfHeadDepth + 0.5;
                    const finalZ = bodyCenterZ + headLocalZ;

                    if (isTopLayer) {
                        voxelMaterial = skinMatDefault; // Main blue for top
                    } else if (isSecondLayer) {
                        voxelMaterial = darkerBlueMat; // Darker blue for second layer
                    } else {
                        // Layers below second
                        if (isFrontFace) {
                            // Front face coloring
                            const distFromCenterX = Math.abs(currentHeadX);
                            if (distFromCenterX <= 2) { // Central highlight stripe (width 5)
                                voxelMaterial = skinMatForHead; // Highlight cyan
                            } else { // Outer edges of front face
                                voxelMaterial = darkerBlueMat; // Darker blue
                            }
                        } else {
                            // Sides and back
                            voxelMaterial = skinMatDefault; // Main blue
                        }
                    }
                    
                    addVoxelGeometryCentered(currentHeadX, currentHeadY, finalZ, voxelMaterial, headGeometries);
                }
            }
        }

        // --- Merge Geometries, Create Meshes, Apply Offsets --- 
        
        // Updated Function: Merges geometry, creates mesh, adds to a named group.
        const processAndAddPart = (geoStore, targetGroup, partName) => {
            const group = new THREE.Group();
            group.name = partName;
            let hasGeometry = false;
            for (const colorHex in geoStore) {
                const material = this._getMaterial(parseInt(colorHex, 16));
                if (!material) continue;
                const mergedGeometry = BufferGeometryUtils.mergeGeometries(geoStore[colorHex], false);
                if (mergedGeometry) {
                    const mesh = new THREE.Mesh(mergedGeometry, material);
                    group.add(mesh);
                    hasGeometry = true;
                }
            }
            if (hasGeometry) {
                 targetGroup.add(group); 
            }
            return group; // Return the created group (important for limbs)
        };
        
        // Process and add NON-PIVOTED parts (Added directly to playerGroup)
        processAndAddPart(bodyGeometries, playerGroup, "body");
        processAndAddPart(headGeometries, playerGroup, "head");
        // Removed hairGeometries processing
        processAndAddPart(neckGeometries, playerGroup, "neck");

        // Process and add PIVOTED limbs (Geometry relative to pivot)
        const leftLegGroup = processAndAddPart(leftLegGeometries, playerGroup, "leftLeg");
        const rightLegGroup = processAndAddPart(rightLegGeometries, playerGroup, "rightLeg");
        const leftArmGroup = processAndAddPart(leftArmGeometries, playerGroup, "leftArm");
        const rightArmGroup = processAndAddPart(rightArmGeometries, playerGroup, "rightArm");

        // Position the limb groups at their calculated pivot points
        leftLegGroup.position.copy(leftHipOffset);
        rightLegGroup.position.copy(rightHipOffset);
        leftArmGroup.position.copy(leftShoulderOffset);
        rightArmGroup.position.copy(rightShoulderOffset);
        
        voxelGeo.dispose();

        console.log(`Soul Warrior v5 merged player model generated (No Stripe, Noise Body).`); // Updated log
        return playerGroup;
    }
    
    // Method to clean up cached materials if needed (e.g., on full reset)
    disposeMaterials() {
        Object.values(this.materialCache).forEach(material => material.dispose());
        this.materialCache = {};
    }
}

export default CharacterGenerator; 