// Test script for item drop and enemy spawn systems

import itemDropManager from './src/systems/ItemDropManager.js';
import enemySpawnManager from './src/systems/EnemySpawnManager.js';
import { getItemData } from './src/entities/ItemTypes.js';

// Enable debug mode
itemDropManager.setDebugMode(true);
enemySpawnManager.setDebugMode(true);

// Test item drop system
console.log('=== TESTING ITEM DROP SYSTEM ===');

// Test different enemy types and room types
const testCases = [
    { enemyType: 'normal', roomType: 'normal', biome: 'catacombs', seed: 12345 },
    { enemyType: 'elite', roomType: 'elite', biome: 'crystal_caves', seed: 23456 },
    { enemyType: 'mini_boss', roomType: 'mini_boss', biome: 'ancient_library', seed: 34567 },
    { enemyType: 'boss', roomType: 'boss', biome: 'obsidian_fortress', seed: 45678 }
];

// Run tests
testCases.forEach(testCase => {
    const { enemyType, roomType, biome, seed } = testCase;
    console.log(`\nTesting drop for ${enemyType} in ${roomType} room (${biome}):`);
    
    // Get a random drop
    const itemType = itemDropManager.getRandomDrop(enemyType, roomType, biome, seed);
    
    if (itemType) {
        const itemData = getItemData(itemType);
        console.log(`- Selected item: ${itemData.name} (${itemData.rarity})`);
        console.log(`- Description: ${itemData.description}`);
        console.log(`- Category: ${itemData.category}`);
    } else {
        console.log('- No item dropped');
    }
    
    // Get drop table preview
    const preview = itemDropManager.getDropTablePreview({ enemyType, roomType, biome });
    console.log('- Drop table preview:', preview);
});

// Test enemy spawn system
console.log('\n\n=== TESTING ENEMY SPAWN SYSTEM ===');

// Test different room types and biomes
const enemyTestCases = [
    { roomType: 'normal', biome: 'catacombs', floorLevel: 1, seed: 12345 },
    { roomType: 'elite', biome: 'crystal_caves', floorLevel: 3, seed: 23456 },
    { roomType: 'mini_boss', biome: 'ancient_library', floorLevel: 5, seed: 34567 },
    { roomType: 'boss', biome: 'obsidian_fortress', floorLevel: 7, seed: 45678 }
];

// Run tests
enemyTestCases.forEach(testCase => {
    const { roomType, biome, floorLevel, seed } = testCase;
    console.log(`\nTesting enemy group for ${roomType} room (${biome}, floor ${floorLevel}):`);
    
    // Set floor level
    enemySpawnManager.setFloorLevel(floorLevel);
    
    // Choose a balanced enemy group
    const enemyGroup = enemySpawnManager.chooseEnemyGroup({
        biome,
        roomType,
        floorLevel,
        seed
    });
    
    if (enemyGroup && enemyGroup.length > 0) {
        console.log(`- Selected enemies (${enemyGroup.length}): ${enemyGroup.join(', ')}`);
    } else {
        console.log('- No enemies generated');
    }
    
    // Get enemy spawn table
    const spawnTable = enemySpawnManager.getEnemySpawnTable(biome, floorLevel);
    console.log('- Available enemy types:', spawnTable);
});

console.log('\nTests completed!');
