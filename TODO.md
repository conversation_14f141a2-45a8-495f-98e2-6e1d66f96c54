
Build a modular item drop system AND an enemy spawn system for a procedurally generated dungeon crawler game. The game features alchemical, mythical, and spiritual themes, and all items and enemies exist as 3D voxel objects in the world.

=========================
ITEM DROP SYSTEM:
=========================

1. Drop Source Context:
- The system must support drop generation based on:
  - Current biome (e.g., Icy Caves, Magma Falls, Fallen Garden of Eden, Jungle Dungeon, Castle) 
    - Some items may be marked for multiple biomes or "any biome"
  - Room type (e.g., normal room, elite room, trap room, secret room, soul door room, mini-boss room, boss room) 
    - NOTE: All current rooms are normal rooms because we haven’t added other types yet
  - Enemy type (normal, elite, mini-boss, boss)

2. Rarity System (Weighted Logic):
- Each item has a rarity: Common, Rare, Epic, or Legendary. Drop chances should be weighted:
  - Normal enemies: 90% Common, 10% Rare
  - Mini-bosses: 85% Rare, 15% Legendary
  - Bosses: Always drop at least one Legendary item
  - Secret rooms: Prioritize Rare or Unique utility items
  - Soul Door rooms: Prioritize Enlightening, Reflective, and Holy items

3. Player State Influence:
- Integrate Soul Weight mechanics:
  - Light Soul Weight → higher chance for Holy, Healing, Enlightening items
  - Dark Soul Weight → higher chance for Cursed, Vampiric, Fear-based items
  - Balanced Soul Weight → neutral odds
- Active relics can influence drop pool:
  - Zeituhr equipped → time-related items more likely
  - Golden Mirror activated → unlocks mirrored variant items
  - Family Photo held → unlocks unique story-based drops

4. World Representation:
- Dropped items must:
  - Be spawned as 3D voxel objects (styled like floor loot)
  - Use voxel models based on item type and rarity
  - Glow or shimmer based on rarity (e.g., gold glow for Legendary)
  - Optionally float or rotate for better visibility
  - Be interactable (e.g., walk over or press key)
  - On pickup: remove voxel object and add to player inventory

5. Conditions and Locks:
- Items may be excluded by:
  - `blockedBy` field (e.g., quest/story progression)
  - `startDate` / `endDate` (for timed/seasonal items)
  - Lore flags (e.g., item only drops after defeating specific boss)

6. Utility Functions (Item Drop System):
- getRandomDrop(enemyType, roomType, biome, soulWeight, activeRelics): returns filtered item
- spawnVoxelItem(item, worldPosition): spawns item with voxel visuals
- collectItem(item): adds item to player inventory, removes from world
- loadBiomeItemPool(biome): initializes appropriate item pool
- getDropTablePreview(context): shows drop probabilities (for debugging/balancing)

=========================
ENEMY SPAWN SYSTEM:
=========================

1. Spawn Source Context:
- The system must support enemy spawning based on:
  - Current biome (e.g., Jungle Dungeon, Icy Caves, Castle, Underwater Caves)
  - Room type (currently only normal rooms)
  - Floor level (deeper = stronger enemies)
  - Optional modifiers (e.g., cursed run, light soul run)

2. Biome-Based Enemy Tables:
- Each biome has its own enemy spawn pool:
  - Icy Caves → frost bats, Chrysalis Saint, frost leeches, ice golems
  - Jungle Dungeon → Serpent of Doubt, wild undead, venomous insects
  - Fallen Garden of Eden → Cherub Inverted, Eden, corrupted flora
  - Castle → Mirror Fiend, Soul Forger, spectral knights, Whispering Flames

3. Difficulty Scaling:
- Higher floors or room difficulty increases enemy tier:
  - Early floors → low-tier enemies
  - Deeper floors → stronger spawns, chance of mini-bosses
  - (Add support for elite tags in future updates)

4. Spawn Composition Logic:
- Create diverse enemy groups with role tags:
  - Support (e.g., Cadaver Prophet)
  - Swarm (e.g., bats, moths)
  - Heavy (e.g., Golem of Lead)
  - Elemental/Cursed/Spectral (for room flavor)
- Allow synergistic groups (e.g., buffer + minions)

5. Enemy World Representation:
- Enemies must:
  - Be rendered as 3D voxel models
  - Have animations, sounds, healthbars
  - Use particles or glow depending on element or rarity
  - Use tag indicators (e.g., “Mini-boss”, “Elite”)

6. Utility Functions (Enemy Spawn System):
- getEnemySpawnTable(biome, floorLevel): returns valid enemies for context
- spawnEnemy(enemyType, worldPosition): places voxel enemy in world
- chooseEnemyGroup(context): selects balanced enemy group for room
- getEnemyPreview(biome): outputs spawn list for debugging/balancing







# Dungeon Crawler Implementation Plan

**I. Core Dungeon Generation System**

*   [ ] **Floor Layout Generator:**
    *   [ ] Implement a grid-based map generation algorithm (e.g., random walk, cellular automata) to define room connectivity.
    *   [ ] Ensure a valid path exists from the Start Room to the Boss Room.
    *   [ ] Algorithm should allow for branching paths for optional rooms.
*   [ ] **Room Type Assignment:**
    *   [ ] Define distinct room types: `Start`, `Boss`, `Item`, `Shop`, `Challenge`, `Secret`, `Curse`, `Normal`, `MiniBoss`.
    *   [ ] Integrate type assignment into the floor layout generation (e.g., guarantee one Boss/Item room, place Shop strategically, ensure Secret rooms are adjacent to others).
*   [ ] **Connectivity & Doors:**
    *   [ ] Implement door objects/mechanics linking adjacent rooms based on the generated layout.
    *   [ ] Doors should visually represent their state (open, locked, secret, boss door, etc.).
    *   [ ] Handle player interaction with doors to trigger room transitions.
    *   [ ] Implement **Locked Doors** requiring Keys to open.

**II. Room Content & Population**

*   [ ] **Room Structure Generation:**
    *   [ ] Create a system for defining room layouts (either pre-designed templates or procedural generation *within* room boundaries).
    *   [ ] Procedural methods could include: simple obstacle scattering, cellular automata for cave-like layouts, block patterns.
    *   [ ] Ensure generated layouts are navigable and solvable (consider pathfinding checks).
*   [ ] **Obstacle Placement:**
    *   [ ] Integrate placement of obstacles (e.g., rocks, pits, blocks) based on room template or procedural rules.
    *   [ ] Some obstacles might be destructible (e.g., via Soul Bombs).
*   [ ] **Enemy Spawning System:**
    *   [ ] Define different enemy types with unique stats/AI/attacks.
    *   [ ] Create enemy spawn patterns/groups associated with room types and floor themes/difficulty.
    *   [ ] Implement logic to spawn enemies when the player enters certain rooms.
    *   [ ] Define and implement **Mini-boss** enemies and their potential spawn locations/rooms.
*   [ ] **Object & Pickup Spawning:**
    *   [ ] Define various pickups: Currency, **Keys**, **Soul Bombs**, **Potions (Health)**, Stat Boosters, **Chests**.
    *   [ ] Implement rules for spawning pickups (e.g., after room clear, from destroyed objects, in chests, random chance in rooms).
    *   [ ] Implement **Locked Chests** requiring Keys.

**III. Gameplay Systems**

*   [ ] **Combat Mechanics:**
    *   [ ] Implement player attack system (projectiles, melee?).
    *   [ ] Develop basic enemy AI (movement patterns, attack triggers).
*   [ ] **Health & Damage System:**
    *   [ ] Track health for player and enemies.
    *   [ ] Handle damage application from attacks and hazards (e.g., Curse Rooms, enemy projectiles).
*   [ ] **Item System:**
    *   [ ] Define a structure for items (passive stat boosts, activated abilities, unique effects).
    *   [ ] Create item pools for different sources (Item Room, Boss drop, Shop, Chests).
    *   [ ] Implement item pedestals in Item Rooms.
    *   [ ] Apply item effects to the player.
*   [ ] **Consumable System:**
    *   [ ] Implement inventory/tracking for **Keys**, **Soul Bombs**, **Potions**.
    *   [ ] Implement usage logic: Keys open locks, Bombs destroy obstacles/deal damage/reveal secrets, Potions restore health.
*   [ ] **Currency System:**
    *   [ ] Track player currency.
    *   [ ] Implement currency drops (enemies, chests, etc.).
*   [ ] **Room State & Logic:**
    *   [ ] Track room state (undiscovered, visited, locked, cleared).
    *   [ ] Implement logic to lock doors upon entering combat rooms.
    *   [ ] Implement logic to detect when all enemies are cleared.
    *   [ ] Unlock doors and trigger rewards (pickups, item spawns) upon room clear.

**IV. Player Progression & Floor Structure**

*   [ ] **Floor Themes:**
    *   [ ] Define multiple visual themes (e.g., Cave, Catacombs, Library, Necropolis).
    *   [ ] Associate themes with specific wall/floor textures, lighting, fog, music, and potentially unique obstacles/hazards.
    *   [ ] Assign specific enemy sets to each theme/floor depth.
*   [ ] **Progression Management:**
    *   [ ] Track the current floor number.
    *   [ ] System to transition between floors after defeating the boss (e.g., trapdoor, stairs).
*   [ ] **Hardscripted Events:**
    *   [ ] Implement a trigger system based on reaching specific floor numbers.
    *   [ ] Define events (e.g., introduce new mechanics, modify generation rules, narrative moments, guaranteed boss types).
*   [ ] **Boss Encounters:**
    *   [ ] Design unique Boss AI, attack patterns, and potentially multiple phases.
    *   [ ] Create dedicated Boss Room layouts.
    *   [ ] Implement boss health bars and specific boss drops upon defeat.

**V. Specific Room Implementations**

*   [ ] **Shop:** Implement shopkeeper logic, item stock generation (potentially using specific item pools), purchasing mechanics.
*   [ ] **Secret Room:** Implement generation logic (must be adjacent to >=2 rooms usually), hidden entrance mechanics (e.g., require a Bomb to open, look for visual cues like cracks). Contents often include pickups or special items.
*   [ ] **Challenge Room:** Implement logic for waves of harder enemies or specific challenges (e.g., timed survival), tied to specific rewards (often chests or items).
*   [ ] **Curse Room:** Implement entry cost (damage player). Logic for potential rewards (e.g., multiple chests, items) or penalties (e.g., teleport to bad room) inside. Sometimes has spiked doors requiring damage to exit too.
*   [ ] **MiniBoss Room:** Specific layout, locks player in, contains a Mini-boss enemy, provides a defined reward upon clearing.

**VI. Engine & Scene Management**

*   [ ] **Floor/Dungeon Manager:** Central class to hold the generated floor layout, current room state, and handle transitions.
*   [ ] **Room Transition Logic:** Smoothly load/unload room contents (enemies, objects, layout) and reposition the player as they move between rooms via doors. Might involve fading or visual effects.
*   [ ] **Persistence (Optional but recommended):** Save/load the state of the current run (floor layout, player stats/items/consumables, visited/cleared room states) if the player quits and resumes.