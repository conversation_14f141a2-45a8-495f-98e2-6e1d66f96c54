/**
 * Direct test script for the vibe-reactive, music-synced bullet hell boss system.
 * This script will directly create and initialize the boss system without relying on the game's initialization.
 */

console.log("Direct boss test script loaded");

// Import required modules
import { BossMusicAnalyzer } from './src/audio/BossMusicAnalyzer.js';
import { BulletPatternManager } from './src/projectiles/BulletPatternManager.js';
import { BossController } from './src/ai/brains/BossController.js';

// Function to initialize Tone.js
async function initTone() {
    if (typeof Tone !== 'undefined') {
        console.log("Initializing Tone.js...");
        try {
            await Tone.start();
            console.log("Tone.js initialized successfully");
            return true;
        } catch (error) {
            console.error("Failed to initialize Tone.js:", error);
            return false;
        }
    } else {
        console.error("Tone.js not found");
        return false;
    }
}

// Function to create a test boss
function createTestBoss(scene) {
    console.log("Creating test boss...");
    
    // Create a simple cube as the boss
    const geometry = new THREE.BoxGeometry(2, 2, 2);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });
    const boss = new THREE.Mesh(geometry, material);
    boss.position.set(0, 1, 0);
    
    // Add userData
    boss.userData = {
        id: 'test_boss',
        type: 'catacombs_overlord',
        health: 200,
        maxHealth: 200,
        baseSpeed: 1.8,
        projectileType: 'shadow_bolt',
        projectileSpeed: 8.0,
        musicReactive: true
    };
    
    scene.add(boss);
    console.log("Test boss created");
    
    return boss;
}

// Function to directly test the boss system
async function testBossSystem() {
    console.log("Starting direct boss system test...");
    
    // Wait for the game to initialize
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Get the scene and dungeonHandler
    const sceneManager = window.sceneManager;
    if (!sceneManager || !sceneManager.currentHandler) {
        console.error("Scene manager or current handler not found");
        return;
    }
    
    const dungeonHandler = sceneManager.currentHandler;
    const scene = dungeonHandler.scene;
    const audioManager = sceneManager.audioManager;
    
    // Initialize Tone.js
    await initTone();
    
    // Create a test boss if none exists
    let boss = null;
    if (dungeonHandler.activeBosses && dungeonHandler.activeBosses.length > 0) {
        boss = dungeonHandler.activeBosses[0];
        console.log("Using existing boss");
    } else {
        boss = createTestBoss(scene);
        if (dungeonHandler.activeBosses) {
            dungeonHandler.activeBosses.push(boss);
        } else {
            dungeonHandler.activeBosses = [boss];
        }
        console.log("Created new test boss");
    }
    
    // Create a boss controller
    console.log("Creating boss controller...");
    const bossController = new BossController(
        boss,
        dungeonHandler,
        audioManager,
        {
            visualEffects: true,
            screenShake: true,
            debugMode: true
        }
    );
    
    // Initialize the boss controller
    console.log("Initializing boss controller...");
    const initialized = await bossController.init();
    if (!initialized) {
        console.error("Failed to initialize boss controller");
        return;
    }
    
    // Store the boss controller in the boss userData
    if (!boss.userData.aiController) {
        boss.userData.aiController = {};
    }
    boss.userData.aiController.bossController = bossController;
    
    // Start the boss controller
    console.log("Starting boss controller...");
    const started = await bossController.start();
    if (!started) {
        console.error("Failed to start boss controller");
        return;
    }
    
    // Set up update loop
    console.log("Setting up update loop...");
    const updateInterval = setInterval(() => {
        if (bossController && bossController.active) {
            bossController.update(1/60); // Assume 60 FPS
        } else {
            console.warn("Boss controller not active");
            clearInterval(updateInterval);
        }
    }, 16); // ~60 FPS
    
    // Force a pattern to show it's working
    setTimeout(() => {
        console.log("Forcing initial pattern...");
        bossController.patternManager.triggerPattern("circle_ripple", 0.5);
    }, 2000);
    
    console.log("Direct boss system test complete!");
    
    // Update debug display
    setInterval(() => {
        try {
            if (!bossController || !bossController.active) return;
            
            const debugInfo = bossController.getDebugInfo();
            
            // Update debug display
            document.getElementById('intensity-display').textContent = `Music Intensity: ${Math.round(debugInfo.intensity)}%`;
            document.getElementById('pattern-display').textContent = `Current Pattern: ${debugInfo.lastPattern || 'None'}`;
            document.getElementById('beat-display').textContent = `Beat Detected: ${debugInfo.beatDetected ? 'Yes' : 'No'}`;
        } catch (error) {
            console.error("Error updating debug display:", error);
        }
    }, 100);
}

// Start the test
testBossSystem();
