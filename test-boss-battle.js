/**
 * Test script for the vibe-reactive, music-synced bullet hell boss system.
 * This script will teleport the player directly to the boss room to test the system.
 */

console.log("Boss battle test script loaded");

// Function to check if Tone.js is available and initialize it
async function initTone() {
    if (typeof Tone !== 'undefined') {
        console.log("Initializing Tone.js...");
        try {
            await Tone.start();
            console.log("Tone.js initialized successfully");
            return true;
        } catch (error) {
            console.error("Failed to initialize Tone.js:", error);
            return false;
        }
    } else {
        console.error("Tone.js not found");
        return false;
    }
}

// Function to teleport to the boss room
async function teleportToBossRoom() {
    console.log("Attempting to teleport to boss room...");

    // Get the DungeonHandler instance
    const dungeonHandler = window.sceneManager.currentHandler;
    if (!dungeonHandler || !dungeonHandler.floorLayout) {
        console.error("DungeonHandler not found or floor layout not initialized");
        return false;
    }

    // Find the boss room
    let bossRoomId = null;
    dungeonHandler.floorLayout.forEach((roomData, roomId) => {
        if (roomData.type === 'Boss') {
            bossRoomId = roomId;
            console.log(`Found boss room with ID: ${roomId}`);
        }
    });

    if (bossRoomId === null) {
        console.error("No boss room found in the floor layout");
        return false;
    }

    // Teleport to the boss room
    console.log(`Teleporting to boss room ${bossRoomId}...`);
    dungeonHandler.loadRoom(bossRoomId);

    // Log success message
    console.log("Teleported to boss room! The boss battle should start automatically.");
    console.log("The bullet patterns should react to the music's intensity and rhythm.");

    return true;
}

// Function to check if the boss is properly initialized
function checkBossInitialization() {
    console.log("Checking boss initialization...");

    const dungeonHandler = window.sceneManager.currentHandler;
    if (!dungeonHandler || !dungeonHandler.activeBosses || dungeonHandler.activeBosses.length === 0) {
        console.error("No active bosses found");
        return false;
    }

    const boss = dungeonHandler.activeBosses[0];
    if (!boss || !boss.userData) {
        console.error("Boss has no userData");
        return false;
    }

    if (!boss.userData.aiController) {
        console.error("Boss has no aiController");

        // Try to manually create a boss controller
        console.log("Attempting to manually create a boss controller...");

        // Import required modules
        import('./src/ai/brains/BossCombatAI.js').then(module => {
            const BossCombatAI = module.BossCombatAI;

            // Create a new boss AI controller
            const bossAI = new BossCombatAI(boss, dungeonHandler.scene);
            boss.userData.aiController = bossAI;

            console.log("Boss AI controller created manually");

            // Initialize boss controller
            if (bossAI._initBossController) {
                bossAI._initBossController(dungeonHandler, window.sceneManager.audioManager);
                console.log("Boss controller initialized manually");
            }
        }).catch(error => {
            console.error("Failed to import BossCombatAI:", error);
        });

        return false;
    }

    if (!boss.userData.aiController.bossController) {
        console.error("Boss has no bossController");

        // Try to manually initialize the boss controller
        if (boss.userData.aiController._initBossController) {
            console.log("Attempting to manually initialize boss controller...");
            boss.userData.aiController._initBossController(dungeonHandler, window.sceneManager.audioManager);
        }

        return false;
    }

    console.log("Boss is properly initialized!");
    return true;
}

// Function to manually spawn a boss if none exists
function spawnBoss() {
    console.log("Attempting to spawn a boss...");

    const dungeonHandler = window.sceneManager.currentHandler;
    if (!dungeonHandler) {
        console.error("DungeonHandler not found");
        return false;
    }

    // Spawn the boss
    const boss = dungeonHandler._spawnEnemy('catacombs_overlord');
    if (!boss) {
        console.error("Failed to spawn boss");
        return false;
    }

    console.log("Boss spawned successfully!");
    return true;
}

// Main initialization function
async function initialize() {
    console.log("Initializing boss battle test...");

    // Wait for the game to initialize
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Initialize Tone.js
    await initTone();

    // Teleport to the boss room
    const teleported = await teleportToBossRoom();
    if (!teleported) {
        console.error("Failed to teleport to boss room");
        return;
    }

    // Wait for the room to load
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if the boss is properly initialized
    let bossInitialized = checkBossInitialization();

    // If the boss is not initialized, try to spawn one
    if (!bossInitialized) {
        console.log("Boss not initialized, attempting to spawn one...");
        const bossSpawned = spawnBoss();

        if (bossSpawned) {
            // Wait for the boss to initialize
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Check again
            bossInitialized = checkBossInitialization();
        }
    }

    // Set up a periodic check to ensure the boss is working
    setInterval(() => {
        const dungeonHandler = window.sceneManager.currentHandler;
        if (!dungeonHandler || !dungeonHandler.activeBosses || dungeonHandler.activeBosses.length === 0) {
            console.log("No active bosses found, attempting to spawn one...");
            spawnBoss();
        }
    }, 5000);

    console.log("Boss battle test initialization complete!");
}

// Start the initialization process
initialize();
