#!/bin/bash

# Run the ultra-optimized boss battle test
echo "Starting ultra-optimized boss battle test..."
echo "Opening ultra-boss-test.html in your default browser..."

# Open the test HTML file in the default browser
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    open ultra-boss-test.html
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    xdg-open ultra-boss-test.html
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    start ultra-boss-test.html
else
    echo "Unsupported OS. Please open ultra-boss-test.html manually."
fi

echo "Test started! The boss battle should begin automatically after the game loads."
echo "The ultra-visible debug overlay should appear in the top-left corner."
echo "The bullet patterns are ultra-optimized to prevent lag."
