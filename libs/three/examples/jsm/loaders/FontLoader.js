import * as THREE from '../../../build/three.module.js';

class FontLoader extends THREE.Loader {

	constructor( manager ) {

		super( manager );

	}

	load( url, onLoad, onProgress, onError ) {

		const loader = new THREE.FileLoader( this.manager );
		loader.setPath( this.path );
		loader.setRequestHeader( this.requestHeader );
		loader.setWithCredentials( this.withCredentials );
		loader.load( url, ( text ) => {

			try {

				const font = this.parse( JSON.parse( text ) );
				if ( onLoad ) onLoad( font );

			} catch ( e ) {

				if ( onError ) {

					onError( e );

				} else {

					console.error( e );

				}

				this.manager.itemError( url );

			}

		}, onProgress, onError );

	}

	parse( json ) {

		return new Font( json );

	}

}

class Font {

	constructor( data ) {

		this.isFont = true;

		this.type = 'Font';

		this.data = data;

	}

	generateShapes( text, size = 100 ) {

		const shapes = [];
		const paths = createPaths( text, size, this.data );

		for ( let p = 0, pl = paths.length; p < pl; p ++ ) {

			shapes.push( ...paths[ p ].toShapes() );

		}

		return shapes;

	}

}

function createPaths( text, size, data ) {

	const chars = Array.from( text );
	const scale = size / data.resolution;
	const line_height = ( data.boundingBox.yMax - data.boundingBox.yMin + data.underlineThickness ) * scale;

	const paths = [];

	let offsetX = 0, offsetY = 0;

	for ( let i = 0; i < chars.length; i ++ ) {

		const char = chars[ i ];

		if ( char === '\n' ) {

			offsetX = 0;
			offsetY -= line_height;

		} else {

			const ret = createPath( char, scale, offsetX, offsetY, data );
			if ( ret ) {

				offsetX += ret.offsetX;
				paths.push( ret.path );

			}

		}

	}

	return paths;

}

function createPath( char, scale, offsetX, offsetY, data ) {

	const glyph = data.glyphs[ char ] || data.glyphs[ '?' ];

	if ( ! glyph ) {

		console.warn( 'THREE.Font: character "' + char + '" does not exists in font family ' + data.familyName + '.' );
		return;

	}

	const path = new THREE.ShapePath();

	let x, y, cpx, cpy, cpx1, cpy1, cpx2, cpy2;

	if ( glyph.o ) {

		const outline = glyph.o.split( ' ' );

		for ( let i = 0, l = outline.length; i < l; ) {

			const action = outline[ i ++ ];

			switch ( action ) {

				case 'm': // moveTo

					x = offsetX + parseFloat( outline[ i ++ ] ) * scale;
					y = offsetY + parseFloat( outline[ i ++ ] ) * scale;

					path.moveTo( x, y );

					break;

				case 'l': // lineTo

					x = offsetX + parseFloat( outline[ i ++ ] ) * scale;
					y = offsetY + parseFloat( outline[ i ++ ] ) * scale;

					path.lineTo( x, y );

					break;

				case 'q': // quadraticCurveTo

					cpx = offsetX + parseFloat( outline[ i ++ ] ) * scale;
					cpy = offsetY + parseFloat( outline[ i ++ ] ) * scale;
					cpx1 = offsetX + parseFloat( outline[ i ++ ] ) * scale;
					cpy1 = offsetY + parseFloat( outline[ i ++ ] ) * scale;

					path.quadraticCurveTo( cpx, cpy, cpx1, cpy1 );

					break;

				case 'b': // bezierCurveTo

					cpx = offsetX + parseFloat( outline[ i ++ ] ) * scale;
					cpy = offsetY + parseFloat( outline[ i ++ ] ) * scale;
					cpx1 = offsetX + parseFloat( outline[ i ++ ] ) * scale;
					cpy1 = offsetY + parseFloat( outline[ i ++ ] ) * scale;
					cpx2 = offsetX + parseFloat( outline[ i ++ ] ) * scale;
					cpy2 = offsetY + parseFloat( outline[ i ++ ] ) * scale;

					path.bezierCurveTo( cpx, cpy, cpx1, cpy1, cpx2, cpy2 );

					break;

			}

		}

	}

	return { offsetX: glyph.ha * scale, path: path };

}

// Note: ShapePath is not exported in this version, so it would need to be imported separately
// import { ShapePath } from 'three';

export { FontLoader, Font };