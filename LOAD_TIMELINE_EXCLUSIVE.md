# How to Load the Epic Battle Timeline in Exclusive Mode

If you're having issues with the integrated mode, you can try the exclusive timeline mode which will ensure only the timeline patterns are used.

## Instructions

1. Start a game and enter the catacomb boss room
2. Press F12 to open the browser's developer console
3. Enter the following command in the console:

```javascript
// Load the timeline in exclusive mode (only timeline patterns will be used)
game.dungeonHandler.currentRoom.boss.bossController.loadTimeline("epic_battle", true);
```

4. Press Enter to execute the command
5. The boss will now follow the epic battle timeline strictly

## What to Expect

In exclusive mode:

1. ONLY the patterns specified in the timeline will be used
2. The timeline will control both WHICH patterns to use and WHEN to trigger them
3. The music sync system will not override the timeline patterns
4. You'll see debug messages in the console showing the current timeline section, pattern, and projectile type

## Troubleshooting

If you still don't see the timeline patterns being used:

1. Check the console for any error messages
2. Make sure you're in a room with a boss
3. Try reloading the page and entering the command again
4. If you get an error, check the console for details

## Teleporting to the Boss

If you're not already at the boss, press F10 or T to teleport directly to the boss before loading the timeline.
