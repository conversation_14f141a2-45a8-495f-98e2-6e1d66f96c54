// Debug script to check boss object
(function debugBoss() {
    try {
        console.log("=== DEBUG BOSS OBJECT ===");
        
        // Check if dungeonHandler exists
        if (!game.dungeonHandler) {
            console.error("DungeonHandler not found");
            return;
        }
        
        console.log("DungeonHandler exists");
        
        // Check if there are active bosses
        if (!game.dungeonHandler.activeBosses || game.dungeonHandler.activeBosses.length === 0) {
            console.error("No active bosses found");
            return;
        }
        
        console.log(`Found ${game.dungeonHandler.activeBosses.length} active bosses`);
        
        // Get the first active boss
        const boss = game.dungeonHandler.activeBosses[0];
        console.log("Boss object:", boss);
        console.log("Boss position:", boss.position);
        console.log("Boss userData:", boss.userData);
        
        // Check if boss has a controller
        if (boss.userData.aiController) {
            console.log("Boss has AI controller");
            console.log("AI controller type:", boss.userData.aiController.constructor.name);
            
            // Check if boss has a boss controller
            if (boss.userData.aiController.bossController) {
                console.log("Boss has boss controller");
                console.log("Boss controller active:", boss.userData.aiController.bossController.active);
                console.log("Boss controller timeline:", boss.userData.aiController.bossController.currentTimelineName);
                
                // Check if boss has a pattern manager
                if (boss.userData.aiController.bossController.patternManager) {
                    console.log("Boss has pattern manager");
                    console.log("Pattern manager boss:", boss.userData.aiController.bossController.patternManager.boss ? "exists" : "null");
                    console.log("Pattern manager dungeonHandler:", boss.userData.aiController.bossController.patternManager.dungeonHandler ? "exists" : "null");
                    console.log("Pattern manager patterns:", boss.userData.aiController.bossController.patternManager.patterns ? boss.userData.aiController.bossController.patternManager.patterns.length : "null");
                } else {
                    console.error("Boss controller has no pattern manager");
                }
            } else {
                console.error("AI controller has no boss controller");
            }
        } else {
            console.error("Boss has no AI controller");
        }
        
        console.log("=== END DEBUG BOSS OBJECT ===");
    } catch (error) {
        console.error("Error in debugBoss:", error);
    }
})();
