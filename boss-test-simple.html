<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boss Battle Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #000;
        }
        #game-container {
            width: 100vw;
            height: 100vh;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    
    <!-- Load the game -->
    <script src="main.js"></script>
    
    <!-- Load the debug overlay -->
    <script src="load-boss-debug.js"></script>
    
    <!-- Simple script to teleport to boss room -->
    <script>
        // Wait for the game to initialize
        setTimeout(() => {
            try {
                console.log("Attempting to teleport to boss room...");
                
                // Get the DungeonHandler instance
                const dungeonHandler = window.sceneManager.currentHandler;
                if (!dungeonHandler || !dungeonHandler.floorLayout) {
                    console.error("DungeonHandler not found or floor layout not initialized");
                    return;
                }
                
                // Find the boss room
                let bossRoomId = null;
                dungeonHandler.floorLayout.forEach((roomData, roomId) => {
                    if (roomData.type === 'Boss') {
                        bossRoomId = roomId;
                        console.log(`Found boss room with ID: ${roomId}`);
                    }
                });
                
                if (bossRoomId === null) {
                    console.error("No boss room found in the floor layout");
                    return;
                }
                
                // Teleport to the boss room
                console.log(`Teleporting to boss room ${bossRoomId}...`);
                dungeonHandler.loadRoom(bossRoomId);
                
                // Log success message
                console.log("Teleported to boss room! The boss battle should start automatically.");
            } catch (error) {
                console.error("Error teleporting to boss room:", error);
            }
        }, 3000); // Wait 3 seconds for the game to initialize
    </script>
</body>
</html>
