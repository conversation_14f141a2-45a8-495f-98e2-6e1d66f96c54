// Debug script to manually start the boss controller
(function startBossController() {
    try {
        console.log("=== MANUALLY START BOSS CONTROLLER ===");
        
        // Check if dungeonHandler exists
        if (!game.dungeonHandler) {
            console.error("Dungeon<PERSON>andler not found");
            return;
        }
        
        console.log("Dungeon<PERSON>andler exists");
        
        // Check if there are active bosses
        if (!game.dungeonHandler.activeBosses || game.dungeonHandler.activeBosses.length === 0) {
            console.error("No active bosses found");
            return;
        }
        
        console.log(`Found ${game.dungeonHandler.activeBosses.length} active bosses`);
        
        // Get the first active boss
        const boss = game.dungeonHandler.activeBosses[0];
        console.log("Boss object:", boss);
        
        // Check if boss has a controller
        if (boss.userData.aiController) {
            console.log("Boss has AI controller");
            
            // Check if boss has a boss controller
            if (boss.userData.aiController.bossController) {
                console.log("Boss has boss controller");
                
                // Start the boss controller
                console.log("Starting boss controller...");
                boss.userData.aiController.bossController.start().then(success => {
                    if (success) {
                        console.log("Boss controller started successfully");
                    } else {
                        console.error("Failed to start boss controller");
                    }
                }).catch(error => {
                    console.error("Error starting boss controller:", error);
                });
            } else {
                console.error("AI controller has no boss controller");
            }
        } else {
            console.error("Boss has no AI controller");
        }
        
        console.log("=== END MANUALLY START BOSS CONTROLLER ===");
    } catch (error) {
        console.error("Error in startBossController:", error);
    }
})();
