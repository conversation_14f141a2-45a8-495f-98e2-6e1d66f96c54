# Boss System Guide

This comprehensive guide covers all aspects of the vibe-reactive, music-synced bullet hell boss system, including projectile types, patterns, and implementation details.

## Table of Contents

1. [Overview](#overview)
2. [Projectile Types](#projectile-types)
3. [Bullet Patterns](#bullet-patterns)
4. [Timeline System](#timeline-system)
5. [Boss Implementation](#boss-implementation)
6. [Technical Details](#technical-details)
7. [Debugging and Testing](#debugging-and-testing)
8. [How to Extend the System](#how-to-extend-the-system)

## Overview

The boss system features a vibe-reactive, music-synced bullet hell experience using real-time frequency analysis (FFT) that dynamically adjusts bullet patterns based on music intensity. Key features include:

- **Real-time Music Analysis**: Uses FFT to analyze music frequency and energy
- **Dynamic Bullet Patterns**: Multiple unique patterns that adapt to music intensity
- **Reactive Boss Behavior**: Boss speed and attack frequency sync with the music
- **Performance Optimized**: Adaptive complexity based on FPS and projectile count
- **Visual Effects**: Screen shake and background pulses on music beats
- **Timeline System**: Define specific patterns and projectiles for time segments
- **Extensive Projectile Types**: Wide variety of visually unique projectiles
- **Teleport Feature**: Quickly teleport to the boss with F10 or T key

## Projectile Types

### Original Projectiles
1. **Shadow Bolt** (`shadow_bolt`): Dark, basic projectile
2. **Fireball** (`fireball`): Orange/red, faster projectile
3. **Poison Cloud** (`poison_cloud`): Green, area-effect projectile
4. **Lightning Bolt** (`lightning_bolt`): White/blue, fastest projectile
5. **Default Soul Orb** (`default_soul_orb`): Player's default projectile
6. **Arrow** (`arrow`): Basic enemy projectile

### New Projectile Types
7. **Ash Cinders** (`ash_cinders`): Glowing embers that float and drift
8. **Eye Tears** (`eye_tears`): Teardrop-shaped bullets that fall diagonally
9. **Blood Threads** (`blood_threads`): Red strands that curve and twist
10. **Wax Drips** (`wax_drips`): Slow-falling, candle-like projectiles
11. **Gold Coins** (`gold_coins`): Spinning metallic discs
12. **Thorn Seeds** (`thorn_seeds`): Spiky projectiles that grow thorns
13. **Wind Spirits** (`wind_spirits`): Swirling, semi-transparent air currents
14. **Rust Flakes** (`rust_flakes`): Jagged orange flecks that fall like dust
15. **Teeth** (`teeth`): Sharp, bone-like projectiles that chatter
16. **Halo Fragments** (`halo_fragments`): Glowing, angelic ring segments
17. **Screamer Skulls** (`screamer_skulls`): Ghostly skull projectiles
18. **Iron Chains** (`iron_chains`): Heavy metal links that drag
19. **Lava Droplets** (`lava_droplets`): Fall from ceiling, land, then explode upward
20. **Shatter Spikes** (`shatter_spikes`): Icicle-shaped bullets that break into frozen shards
21. **Memory Echoes** (`memory_echoes`): Ghostly images that fade in and out
22. **Hex Strings** (`hex_strings`): Thin, magical strings that curve through the air
23. **Eclipsed Suns** (`eclipsed_suns`): Black orb bullets surrounded by solar flares

### Summoning Projectiles
24. **Alchemical Symbols** (`alchemical_symbols`): Magical symbols representing the four elements
25. **Rotating Sigils** (`rotating_sigils`): Mystical symbols that rotate and pulse
26. **Sacred Geometry** (`sacred_geometry`): Complex geometric patterns with mathematical significance

## Bullet Patterns

### Original Patterns
1. **Petal Spread** (`petal_spread`): Ambient, peaceful pattern with flower-like spread
2. **Circle Ripple** (`circle_ripple`): Pulsing beat pattern with circular waves
3. **Spiral Wave** (`spiral_wave`): Melodic pattern with rotating spiral arms
4. **Laser Grid** (`laser_grid`): Harsh high-frequency pattern with grid formation
5. **Boomerang Arcs** (`boomerang_arcs`): Projectiles that curve back toward their origin
6. **Inhale Exhale** (`inhale_exhale`): Alternating pattern of pulling in and pushing out
7. **Hellburst** (`hellburst`): Intense explosion of projectiles in all directions
8. **Lightning Chain** (`lightning_chain`): Connected projectiles that form a chain
9. **Rapid Fire** (`rapid_fire`): Fast stream of bullets in multiple directions

### Geometric Patterns
10. **Spiral Bloom** (`spiral_bloom`): Bullets spiral out in a flower shape, then collapse inward
11. **Vortex Wings** (`vortex_wings`): Butterfly-shaped patterns that flutter outward
12. **Spiral Rain** (`spiral_rain`): Bullets fall from above but spiral sideways

### Temporal Patterns
13. **Delay-Accelerate** (`delay_accelerate`): Projectiles float harmlessly, then suddenly rocket forward
14. **Fake-Out Pulse** (`fake_out_pulse`): Bullets pulse in and out, blinking off-screen
15. **Time Freeze** (`time_freeze`): Slow orbs that freeze nearby bullets when they explode

### Tracking Patterns
16. **Soul Magnetism** (`soul_magnetism`): Bullets home toward where player was a second ago
17. **Breath Trails** (`breath_trails`): Foggy trails that crystallize into bullets

### Reactive Patterns
18. **Alchemical Chain** (`alchemical_chain`): Bullets with different elements that react with each other
19. **Shifting Polarity** (`shifting_polarity`): Bullets change color and behavior periodically
20. **Divine Divide** (`divine_divide`): Wall of light that splits bullets in half

### Boss-Specific Patterns
21. **Lantern Flail** (`lantern_flail`): Boss swings a lantern in a 90-degree arc
22. **Soul Fire Toss** (`soul_fire_toss`): Boss throws fireballs with a chain of fire behind them
23. **Grave Summon** (`grave_summon`): Boss summons graves that spawn projectiles
24. **Chained Realm** (`chained_realm`): Boss creates chains that connect to the player
25. **Fire Kraken** (`fire_kraken`): Summons fire tentacles that reach out toward the player
26. **Big Fireball Charge** (`big_fireball_charge`): Boss charges a large fireball before releasing it
27. **Sarcophagus Minions** (`sarcophagus_minions`): Boss summons sarcophagi that spawn skeleton minions
28. **Fire Circle Closure** (`fire_circle_closure`): Boss creates a ring of fire that gradually closes in

### Summoning Patterns
29. **Alchemical Circle** (`alchemical_circle`): Summons alchemical symbols in a circle around the boss
30. **Sigil Array** (`sigil_array`): Summons rotating sigils in a triangular or pentagram formation
31. **Sacred Formation** (`sacred_formation`): Creates a complex 3D formation with orbiting sacred geometry

## Pattern-Projectile Mappings

The game has default mappings between patterns and projectile types:

```javascript
const patternToTypeMap = {
    // New summoning patterns
    "alchemical_circle": 'alchemical_symbols',
    "sigil_array": 'rotating_sigils',
    "sacred_formation": 'sacred_geometry',

    // Original patterns
    "rapid_fire": 'lightning_bolt',
    "petal_spread": intensity < 20 ? 'shadow_bolt' : 'poison_cloud',
    "circle_ripple": 'shadow_bolt',
    "spiral_wave": intensity > 40 ? 'fireball' : 'shadow_bolt',
    "laser_grid": 'lightning_bolt',
    "boomerang_arcs": 'fireball',
    "inhale_exhale": intensity > 50 ? 'lightning_bolt' : 'shadow_bolt',
    "hellburst": intensity > 80 ? 'lightning_bolt' : 'fireball',
    "lightning_chain": 'lightning_bolt',

    // Catacomb Boss patterns
    "lantern_flail": 'fireball',
    "soul_fire_toss": 'lava_droplets',
    "grave_summon": 'screamer_skulls',
    "chained_realm": 'iron_chains',

    // New patterns
    "fire_kraken": 'lava_droplets',
    "spiral_bloom": 'ash_cinders',
    "delay_accelerate": 'gold_coins',
    "fake_out_pulse": 'memory_echoes',
    "soul_magnetism": 'blood_threads',
    "alchemical_chain": 'fireball',
    "time_freeze": 'memory_echoes',
    "spiral_rain": 'wax_drips',
    "shifting_polarity": 'lightning_bolt',
    "breath_trails": 'ash_cinders',
    "vortex_wings": 'wind_spirits',
    "divine_divide": 'halo_fragments'
};
```

## Timeline System

The boss system includes a timeline feature that allows you to specify different patterns and projectiles at specific time segments in the music.

### Basic Timeline Format

```javascript
{
    startTime: 0,
    endTime: 30,
    patterns: ["spiral_bloom"],
    projectileTypes: ["ash_cinders"],
    intensity: 0.5,
    speedMultiplier: 1.0,
    triggerIntervalMin: 1.0,
    triggerIntervalMax: 2.0,
    description: "Custom pattern with new projectiles"
}
```

### Advanced Timeline Format

For more complex configurations, you can use the advanced timeline format which allows:

1. Multiple patterns with percentage weights
2. Specific projectile types assigned to each pattern

```javascript
{
    startTime: 0,
    endTime: 30,
    patternWeights: [
        { pattern: "spiral_bloom", weight: 70 },
        { pattern: "delay_accelerate", weight: 30 }
    ],
    patternProjectiles: {
        "spiral_bloom": "ash_cinders",
        "delay_accelerate": "gold_coins"
    },
    intensity: 0.5,
    speedMultiplier: 1.0,
    triggerIntervalMin: 1.0,
    triggerIntervalMax: 2.0,
    description: "Mixed patterns with specific projectiles"
}
```

### Showcase Timeline

A special timeline called "new_showcase" demonstrates all the new summoning patterns along with existing patterns:

1. 0-30 seconds: Alchemical Circle with Alchemical Symbols, plus Petal Spread and Circle Ripple
2. 30-60 seconds: Sigil Array with Rotating Sigils, plus Spiral Wave and Laser Grid
3. 60-90 seconds: Sacred Formation with Sacred Geometry, plus Boomerang Arcs and Inhale Exhale
4. 90-120 seconds: Combined Alchemical Circle and Sigil Array with Hellburst
5. 120-150 seconds: Combined Sacred Formation and Alchemical Circle with Soul Magnetism
6. 150-180 seconds: Combined Sigil Array and Sacred Formation with Spiral Bloom
7. 180-210 seconds: Grand finale with all summoning patterns combined
8. 210-240 seconds: Fire Kraken with tentacles reaching for the player

### Catacomb Overlord Timeline

The Catacomb Overlord boss fight progresses through these segments:

1. 0-30 seconds: Intro with lantern flail attacks
2. 30-60 seconds: Adding soul fire toss to the mix
3. 60-90 seconds: Summoning skeletal warriors from graves
4. 90-120 seconds: Combining lantern flail with grave summons
5. 120-150 seconds: Pulling player closer with soul tether
6. 150-180 seconds: Soul fire toss combined with soul tether
7. 180-210 seconds: Grand finale with all attack patterns

## Boss Implementation

### Catacomb Overlord Boss

The Catacomb Overlord is a powerful boss enemy that serves as a challenging encounter in the catacombs area. This boss features a custom model that is larger, stronger, and more evil-looking than the standard skeleton enemies.

#### Model Features

- **Size**: 5x larger than standard enemies
- **Appearance**: Dark armored figure with glowing red eyes and gold accents
- **Weapon**: Large halberd/axe with gold detailing
- **Cape/Cloak**: Flowing dark cape for an imposing silhouette

#### Boss Arena

The boss arena features stone monoliths with glowing red glyphs arranged in a triangle around the boss spawn point. These monoliths have collision detection so the boss and player cannot walk through them.

#### Technical Implementation

Each pattern is implemented as a method in the `BulletPatternManager.js` file:

- `_spawnLanternFlail`: Implements the lantern flail pattern
- `_spawnSoulFireToss`: Implements the soul fire toss pattern
- `_spawnGraveSummon`: Implements the grave summon pattern
- `_spawnChainedRealm`: Implements the chained realm pattern
- `_spawnBigFireballCharge`: Implements the big fireball charge pattern
- `_spawnSarcophagusMinions`: Implements the sarcophagus minions pattern
- `_spawnFireCircleClosure`: Implements the fire circle closure pattern

## Technical Details

### Core Components

The system consists of four main components:

1. **BossMusicAnalyzer**: Analyzes music in real-time using FFT (Fast Fourier Transform)
2. **BulletPatternManager**: Manages and spawns bullet patterns based on music intensity
3. **BossController**: Coordinates between the music analyzer and bullet patterns
4. **BossCombatAI**: Integrates with the game's AI system to control boss behavior

### Key Files

- `src/audio/BossMusicAnalyzer.js` - Real-time music analysis using FFT
- `src/projectiles/BulletPatternManager.js` - Bullet pattern management
- `src/ai/brains/BossController.js` - Main controller for the boss system
- `src/ai/brains/BossCombatAI.js` - Integration with game AI
- `src/data/bossTimelines.js` - Timeline definitions for boss fights
- `src/projectiles/ProjectileTypes.js` - Projectile type definitions

### Enhanced Music Analysis

The system uses several techniques to improve music synchronization:

- **Frequency Band Correlation Analysis**: Detects patterns across frequency bands
- **Predictive Analysis**: Anticipates upcoming beats and changes
- **Pitch Detection**: Identifies melody changes for better synchronization
- **Tempo Estimation**: Calculates BPM and confidence level

## Debugging and Testing

### Debug Overlay

Press F12 to toggle the debug overlay, which shows:

- Current music intensity
- Active patterns
- Projectile count
- FPS
- Timeline information

### Teleporting to the Boss

Press F10 or T to teleport directly to the boss for quick testing.

### Performance Optimization

If you experience lag:
- Reduce `maxProjectiles` in `BulletPatternManager.js`
- Increase pattern cooldowns
- Reduce projectile density

## How to Extend the System

### Adding New Projectile Types

Add a new entry to `ProjectileTypes` in `src/projectiles/ProjectileTypes.js`:

```javascript
my_new_projectile: {
    name: 'My Projectile',
    damage: 7,
    speed: 8.0,
    range: 14.0,
    size: 0.3,
    gravity: -3.0,
    color: 0xFF0000, // Red
    trailEffect: true,
    trailColor: 0xFF8800,
    trailLength: 10,
    impactEffect: 'my_impact',
    createMesh: (position) => {
        // Custom mesh creation
        const geometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
        const material = new THREE.MeshBasicMaterial({ color: 0xFF0000 });
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(position);
        return mesh;
    }
}
```

### Adding New Patterns

To add a new bullet pattern:

1. Define the pattern in the `patterns` array in `BulletPatternManager`
2. Create a new `_spawnXXX` method in `BulletPatternManager`
3. Add the pattern to the switch statement in `_spawnPattern`

```javascript
// Example of adding a new pattern
{ name: "new_pattern", minIntensity: 30, maxIntensity: 70, cooldown: 0.8 }

_spawnNewPattern(position, intensity, projectileType, scaleFactor = 1.0) {
    // Pattern implementation
}
```

### Creating Custom Timelines

Create a new timeline in `src/data/bossTimelines.js`:

```javascript
export const bossTimelines = {
    // Existing timelines...
    
    "my_custom_timeline": [
        {
            startTime: 0,
            endTime: 30,
            patterns: ["spiral_bloom"],
            projectileTypes: ["ash_cinders"],
            intensity: 0.5,
            speedMultiplier: 1.0,
            triggerIntervalMin: 1.0,
            triggerIntervalMax: 2.0,
            description: "Custom pattern with new projectiles"
        },
        // Add more timeline segments...
    ]
};
```

Load your custom timeline with:

```javascript
bossController.loadTimeline("my_custom_timeline", true);
```
