<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Item Drop and Enemy Spawn System Test</title>
    <style>
        body {
            font-family: monospace;
            background-color: #1e1e1e;
            color: #ddd;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #88ff88;
            border-bottom: 1px solid #444;
            padding-bottom: 10px;
        }
        #output {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .success {
            color: #88ff88;
        }
        .error {
            color: #ff8888;
        }
        .info {
            color: #8888ff;
        }
    </style>
</head>
<body>
    <h1>Item Drop and Enemy Spawn System Test</h1>
    <div id="output">Loading test results...</div>

    <script type="module">
        // Redirect console output to the output div
        const outputDiv = document.getElementById('output');
        outputDiv.textContent = ''; // Clear loading message
        
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const text = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
            ).join(' ');
            outputDiv.innerHTML += `<div>${text}</div>`;
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            const text = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
            ).join(' ');
            outputDiv.innerHTML += `<div class="error">${text}</div>`;
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            const text = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
            ).join(' ');
            outputDiv.innerHTML += `<div class="warning">${text}</div>`;
        };
        
        // Import and run the test script
        try {
            console.log('Starting tests...');
            await import('./test-item-drop.js');
        } catch (error) {
            console.error('Error running tests:', error);
        }
    </script>
</body>
</html>
