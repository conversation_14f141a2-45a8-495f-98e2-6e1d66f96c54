// Debug script to manually initialize the boss controller
(function initBossController() {
    try {
        console.log("=== MANUALLY INITIALIZE BOSS CONTROLLER ===");
        
        // Check if dungeonHandler exists
        if (!game.dungeonHandler) {
            console.error("Dungeon<PERSON>andler not found");
            return;
        }
        
        console.log("Dungeon<PERSON>andler exists");
        
        // Check if there are active bosses
        if (!game.dungeonHandler.activeBosses || game.dungeonHandler.activeBosses.length === 0) {
            console.error("No active bosses found");
            return;
        }
        
        console.log(`Found ${game.dungeonHandler.activeBosses.length} active bosses`);
        
        // Get the first active boss
        const boss = game.dungeonHandler.activeBosses[0];
        console.log("Boss object:", boss);
        
        // Check if boss has a controller
        if (boss.userData.aiController) {
            console.log("Boss has AI controller");
            
            // Check if boss has a boss controller
            if (boss.userData.aiController.bossController) {
                console.log("Boss has boss controller");
                
                // Initialize the boss controller
                console.log("Initializing boss controller...");
                boss.userData.aiController.bossController.init().then(success => {
                    if (success) {
                        console.log("Boss controller initialized successfully");
                        
                        // Load the timeline
                        console.log("Loading timeline...");
                        const timelineSuccess = boss.userData.aiController.bossController.loadTimeline("catacomb_overlord", false);
                        console.log(`Timeline load ${timelineSuccess ? 'successful' : 'failed'}`);
                        
                        // Start the boss controller
                        console.log("Starting boss controller...");
                        boss.userData.aiController.bossController.start().then(startSuccess => {
                            if (startSuccess) {
                                console.log("Boss controller started successfully");
                            } else {
                                console.error("Failed to start boss controller");
                            }
                        }).catch(error => {
                            console.error("Error starting boss controller:", error);
                        });
                    } else {
                        console.error("Failed to initialize boss controller");
                    }
                }).catch(error => {
                    console.error("Error initializing boss controller:", error);
                });
            } else {
                console.error("AI controller has no boss controller");
                
                // Create a new boss controller
                console.log("Creating new boss controller...");
                
                // Import required modules
                import('../../ai/brains/BossController.js').then(module => {
                    const BossController = module.BossController;
                    
                    // Create boss controller
                    const bossController = new BossController(
                        boss,
                        game.dungeonHandler,
                        game.sceneManager.audioManager,
                        {
                            visualEffects: true,
                            screenShake: true,
                            debugMode: true
                        }
                    );
                    
                    // Store reference to boss controller
                    boss.userData.aiController.bossController = bossController;
                    
                    // Initialize boss controller
                    console.log("Initializing boss controller...");
                    bossController.init().then(success => {
                        if (success) {
                            console.log("Boss controller initialized successfully");
                            
                            // Load the timeline
                            console.log("Loading timeline...");
                            const timelineSuccess = bossController.loadTimeline("catacomb_overlord", false);
                            console.log(`Timeline load ${timelineSuccess ? 'successful' : 'failed'}`);
                            
                            // Start the boss controller
                            console.log("Starting boss controller...");
                            bossController.start().then(startSuccess => {
                                if (startSuccess) {
                                    console.log("Boss controller started successfully");
                                } else {
                                    console.error("Failed to start boss controller");
                                }
                            }).catch(error => {
                                console.error("Error starting boss controller:", error);
                            });
                        } else {
                            console.error("Failed to initialize boss controller");
                        }
                    }).catch(error => {
                        console.error("Error initializing boss controller:", error);
                    });
                }).catch(error => {
                    console.error("Error importing BossController:", error);
                });
            }
        } else {
            console.error("Boss has no AI controller");
        }
        
        console.log("=== END MANUALLY INITIALIZE BOSS CONTROLLER ===");
    } catch (error) {
        console.error("Error in initBossController:", error);
    }
})();
