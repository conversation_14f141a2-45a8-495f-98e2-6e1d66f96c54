#!/bin/bash

# Run the direct boss system test
echo "Starting direct boss system test..."
echo "Opening direct-boss-test.html in your default browser..."

# Open the test HTML file in the default browser
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    open direct-boss-test.html
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    xdg-open direct-boss-test.html
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    start direct-boss-test.html
else
    echo "Unsupported OS. Please open direct-boss-test.html manually."
fi

echo "Test started! The boss system should initialize directly."
echo "Check the debug overlay in the top-left corner for real-time music analysis info."
echo "Use the buttons to force different bullet patterns if needed."
