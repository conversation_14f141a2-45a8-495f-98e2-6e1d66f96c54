<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vibe-Reactive Boss Battle Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #000;
        }
        #game-container {
            width: 100vw;
            height: 100vh;
        }
        #debug-overlay {
            position: fixed;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    <div id="debug-overlay">
        <h3>Boss Battle Debug</h3>
        <div id="intensity-display">Music Intensity: 0%</div>
        <div id="pattern-display">Current Pattern: None</div>
        <div id="beat-display">Beat Detected: No</div>
    </div>

    <!-- Load the game -->
    <script src="main.js"></script>

    <!-- Load our test script -->
    <script src="test-boss-battle.js"></script>

    <!-- Debug script to display music analysis info -->
    <script>
        // Update debug overlay with music analysis info
        setInterval(() => {
            try {
                // Make sure debug elements exist
                if (!document.getElementById('intensity-display')) {
                    console.error("Debug elements not found");
                    return;
                }

                // Check if we have access to the game
                if (!window.sceneManager || !window.sceneManager.currentHandler) {
                    document.getElementById('intensity-display').textContent = `Music Intensity: Waiting for game...`;
                    return;
                }

                const dungeonHandler = window.sceneManager.currentHandler;

                // Check if we have active bosses
                if (!dungeonHandler.activeBosses || dungeonHandler.activeBosses.length === 0) {
                    document.getElementById('intensity-display').textContent = `Music Intensity: No active boss`;
                    document.getElementById('pattern-display').textContent = `Current Pattern: Waiting for boss...`;
                    return;
                }

                // Get the first active boss
                const boss = dungeonHandler.activeBosses[0];
                if (!boss) {
                    document.getElementById('intensity-display').textContent = `Music Intensity: Boss not found`;
                    return;
                }

                // Check if boss has userData
                if (!boss.userData) {
                    document.getElementById('intensity-display').textContent = `Music Intensity: Boss has no userData`;
                    return;
                }

                // Check if boss has aiController
                if (!boss.userData.aiController) {
                    document.getElementById('intensity-display').textContent = `Music Intensity: Boss has no aiController`;
                    return;
                }

                // Check if boss has bossController
                if (!boss.userData.aiController.bossController) {
                    document.getElementById('intensity-display').textContent = `Music Intensity: Boss has no bossController`;
                    return;
                }

                // Get boss controller and debug info
                const bossController = boss.userData.aiController.bossController;
                const debugInfo = bossController.getDebugInfo();

                // Update debug display
                document.getElementById('intensity-display').textContent = `Music Intensity: ${Math.round(debugInfo.intensity)}%`;
                document.getElementById('pattern-display').textContent = `Current Pattern: ${debugInfo.lastPattern || 'None'}`;
                document.getElementById('beat-display').textContent = `Beat Detected: ${debugInfo.beatDetected ? 'Yes' : 'No'}`;

                // Add a button to force a pattern if none is active
                if (!debugInfo.lastPattern) {
                    if (!document.getElementById('force-pattern-btn')) {
                        const btn = document.createElement('button');
                        btn.id = 'force-pattern-btn';
                        btn.textContent = 'Force Pattern';
                        btn.style.marginTop = '10px';
                        btn.onclick = () => {
                            try {
                                bossController.patternManager.triggerPattern('circle_ripple', 0.5);
                                console.log('Forced pattern: circle_ripple');
                            } catch (e) {
                                console.error('Error forcing pattern:', e);
                            }
                        };
                        document.getElementById('debug-overlay').appendChild(btn);
                    }
                }
            } catch (error) {
                console.error("Error updating debug overlay:", error);
                document.getElementById('intensity-display').textContent = `Error: ${error.message}`;
            }
        }, 100);
    </script>
</body>
</html>
