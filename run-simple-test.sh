#!/bin/bash

# Run the simple boss battle test
echo "Starting simple boss battle test..."
echo "Opening boss-test-simple.html in your default browser..."

# Open the test HTML file in the default browser
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    open boss-test-simple.html
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    xdg-open boss-test-simple.html
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    start boss-test-simple.html
else
    echo "Unsupported OS. Please open boss-test-simple.html manually."
fi

echo "Test started! The boss battle should begin automatically after the game loads."
echo "The debug overlay should appear in the top-left corner."
echo "The bullet patterns should be optimized to prevent lag."
