// Debug script to manually trigger a pattern
(function triggerPattern() {
    try {
        console.log("=== MANUALLY TRIGGER PATTERN ===");
        
        // Check if dungeonHandler exists
        if (!game.dungeonHandler) {
            console.error("DungeonHandler not found");
            return;
        }
        
        console.log("DungeonHandler exists");
        
        // Check if there are active bosses
        if (!game.dungeonHandler.activeBosses || game.dungeonHandler.activeBosses.length === 0) {
            console.error("No active bosses found");
            return;
        }
        
        console.log(`Found ${game.dungeonHandler.activeBosses.length} active bosses`);
        
        // Get the first active boss
        const boss = game.dungeonHandler.activeBosses[0];
        console.log("Boss object:", boss);
        
        // Check if boss has a controller
        if (boss.userData.aiController) {
            console.log("Boss has AI controller");
            
            // Check if boss has a boss controller
            if (boss.userData.aiController.bossController) {
                console.log("Boss has boss controller");
                
                // Check if boss has a pattern manager
                if (boss.userData.aiController.bossController.patternManager) {
                    console.log("Boss has pattern manager");
                    
                    // Trigger a pattern
                    const patternName = "circle_ripple";
                    const intensity = 0.8;
                    const speedMultiplier = 1.0;
                    
                    console.log(`Triggering pattern: ${patternName}`);
                    boss.userData.aiController.bossController.patternManager.triggerPattern(patternName, intensity, speedMultiplier);
                    
                    console.log("Pattern triggered successfully");
                } else {
                    console.error("Boss controller has no pattern manager");
                }
            } else {
                console.error("AI controller has no boss controller");
            }
        } else {
            console.error("Boss has no AI controller");
        }
        
        console.log("=== END MANUALLY TRIGGER PATTERN ===");
    } catch (error) {
        console.error("Error in triggerPattern:", error);
    }
})();
