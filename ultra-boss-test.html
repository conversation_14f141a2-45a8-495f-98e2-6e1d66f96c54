<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra-Optimized Boss Battle Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #000;
        }
        #game-container {
            width: 100vw;
            height: 100vh;
        }
        /* Pre-define the debug overlay style to ensure it's visible */
        #ultra-debug-overlay {
            position: fixed;
            top: 10px;
            left: 10px;
            width: 250px;
            background-color: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            z-index: 9999;
            pointer-events: none;
            border: 2px solid #ff0000;
            box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    
    <!-- Pre-create the debug overlay to ensure it's visible -->
    <div id="ultra-debug-overlay">
        <div style="font-weight: bold; margin-bottom: 5px; text-align: center; color: #ff0000;">🎮 BOSS BATTLE DEBUG 🎮</div>
        <div id="intensity-display">🎵 Music Intensity: Loading...</div>
        <div id="pattern-display">🔄 Pattern: Loading...</div>
        <div id="beat-display">🥁 Beat: Loading...</div>
        <div id="fps-display">⚡ FPS: Loading...</div>
        <div id="projectile-display">🔫 Projectiles: Loading...</div>
    </div>
    
    <!-- Load the game -->
    <script src="main.js"></script>
    
    <!-- Load the ultra debug overlay -->
    <script src="ultra-debug.js"></script>
    
    <!-- Ultra-optimized teleport script -->
    <script>
        // Wait for the game to initialize
        setTimeout(() => {
            try {
                console.log("Attempting to teleport to boss room...");
                
                // Get the DungeonHandler instance
                const dungeonHandler = window.sceneManager.currentHandler;
                if (!dungeonHandler || !dungeonHandler.floorLayout) {
                    console.error("DungeonHandler not found or floor layout not initialized");
                    return;
                }
                
                // Find the boss room
                let bossRoomId = null;
                dungeonHandler.floorLayout.forEach((roomData, roomId) => {
                    if (roomData.type === 'Boss') {
                        bossRoomId = roomId;
                        console.log(`Found boss room with ID: ${roomId}`);
                    }
                });
                
                if (bossRoomId === null) {
                    console.error("No boss room found in the floor layout");
                    return;
                }
                
                // Teleport to the boss room
                console.log(`Teleporting to boss room ${bossRoomId}...`);
                dungeonHandler.loadRoom(bossRoomId);
                
                // Log success message
                console.log("Teleported to boss room! The boss battle should start automatically.");
                
                // Force optimization settings
                setTimeout(() => {
                    // Limit max projectiles globally
                    if (dungeonHandler.activeProjectiles && dungeonHandler.activeProjectiles.length > 30) {
                        console.log("Limiting initial projectiles to prevent lag");
                        dungeonHandler.activeProjectiles = dungeonHandler.activeProjectiles.slice(0, 30);
                    }
                    
                    // Force low graphics settings if available
                    if (window.sceneManager && window.sceneManager.renderer) {
                        console.log("Setting low graphics settings");
                        window.sceneManager.renderer.setPixelRatio(1);
                    }
                }, 1000);
            } catch (error) {
                console.error("Error teleporting to boss room:", error);
            }
        }, 3000); // Wait 3 seconds for the game to initialize
    </script>
</body>
</html>
