<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Soulpath</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* Basic styles to ensure canvas fills screen and hide initial elements */
        body { margin: 0; overflow: hidden; background-color: #000; color: #fff; font-family: sans-serif; }
        canvas { display: block; width: 100%; height: 100%; } /* Ensure canvas fills body */
        #ui-container {
            position: absolute;
            top: 0; left: 0; width: 100%; height: 100%;
            pointer-events: none; /* Allow clicks to pass through to canvas by default */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .clickable {
             pointer-events: auto; /* Make specific UI elements clickable */
             cursor: pointer;
        }
        /* Simple mute button style */
        #mute-button {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            background-color: rgba(255, 255, 255, 0.2);
            border: 1px solid #fff;
            border-radius: 5px;
            color: #fff;
            pointer-events: auto;
            cursor: pointer;
            z-index: 100;
        }
         #mute-button:hover {
            background-color: rgba(255, 255, 255, 0.4);
         }

        /* Fade Overlay */
        #fade-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            opacity: 0;
            pointer-events: none; /* Allow clicks through when invisible */
            z-index: 2000; /* Ensure it's above the dialogue */
            transition: opacity 1s ease-in-out; /* Changed from 0.8s */
            display: none; /* Start hidden */
        }
        #fade-overlay.active {
            opacity: 1;
            pointer-events: auto; /* Block clicks during fade */
        }

        /* Dialogue Box Styles */
        #dialogue-container {
            position: absolute;
            bottom: 10%;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            max-width: 600px;
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.8);
            border: 4px solid #fff;
            border-radius: 5px;
            color: #fff;
            font-family: 'Courier New', Courier, monospace; /* Pixel-style font */
            font-size: 20px;
            line-height: 1.4;
            z-index: 1000;
            opacity: 1;
            transition: opacity 0.5s ease-out;
        }
        #dialogue-container.hidden {
            opacity: 0;
            pointer-events: none;
        }
        #dialogue-text {
            margin: 0;
            padding: 0;
            white-space: pre-wrap; /* Preserve line breaks */
        }
        #dialogue-text::after {
            content: ''; /* Changed from '_' to empty */
            /* animation: blink 1s step-end infinite; */ /* Optionally remove blink */
        }
        @keyframes blink {
            50% { opacity: 0; }
        }

        #dialogue-options {
            margin-top: 15px;
            display: flex;
            flex-direction: column; /* Stack options vertically */
            align-items: flex-start; /* Align options left */
        }

        .dialogue-option-button {
            background: none;
            border: 2px solid #fff;
            border-radius: 3px;
            color: #ffff00; /* Yellow options like Undertale? */
            font-family: 'Courier New', Courier, monospace;
            font-size: 18px;
            padding: 5px 10px;
            margin-bottom: 8px;
            cursor: pointer;
            text-align: left;
            transition: background-color 0.2s, color 0.2s;
        }
        .dialogue-option-button:hover,
        .dialogue-option-button:focus {
            background-color: #fff;
            color: #000;
            outline: none;
        }
        /* Add this rule for keyboard highlighting */
        .dialogue-option-button.highlighted {
             background-color: #fff;
             color: #000;
             outline: none;
        }
         #dialogue-progress {
             position: absolute;
             top: 10px;
             right: 15px;
             font-size: 14px;
             color: #aaa;
         }

        /* Hide Canvas Initially */
        #webgl-canvas {
             position: absolute;
             top: 0;
             left: 0;
             width: 100%;
             height: 100%;
             z-index: 1; /* Behind UI */
             /* Start hidden */
             /* opacity: 0; */
             /* transition: opacity 0.5s ease-in; */
        }
        /* #webgl-canvas.visible { */
             /* opacity: 1; */
        /* } */

        /* --- Video Overlay Styles --- */
        #video-overlay {
            display: none; /* Initially hidden */
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.85); /* Semi-transparent black background */
            z-index: 1000; /* Ensure it's above canvas, maybe below dialogue */
            opacity: 0;
            transition: opacity 0.5s ease-in-out; /* Fade transition */
            justify-content: center; /* Center content horizontally */
            align-items: center; /* Center content vertically */
        }

        #video-overlay.visible {
            display: flex; /* Use flexbox for centering when visible */
        }

        #video-overlay.fading-in {
            opacity: 1;
        }

        #video-container {
            padding: 5px; /* <<< Keep slim padding */
            background-color: white; /* <<< Keep background */
            border: 2px solid #fff; /* <<< Keep slim border */
            border-radius: 5px; /* <<< Keep slight radius */
            /* box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); */
            /* Adjust max-width/max-height if needed to constrain size */
            /* max-width: 60%; */ /* <<< REMOVED */
            /* max-height: 60%; */ /* <<< REMOVED */
            display: inline-flex; /* <<< Changed to inline-flex to shrink-wrap */
            /* justify-content: center; */ /* No longer needed with inline-flex */
            /* align-items: center; */ /* No longer needed with inline-flex */
            /* Ensure it respects overlay centering */
            max-width: 90%; /* Added fallback max-width for container */
            max-height: 90%; /* Added fallback max-height for container */
        }

        /* Apply sizing to both videos using a common class */
        .video-player {
            display: block; /* Prevents extra space below video */
            max-width: 60vw; /* <<< Applied max-width directly using viewport units */
            max-height: 60vh; /* <<< Applied max-height directly using viewport units */
            height: auto; /* Maintain aspect ratio */
            width: auto; /* Maintain aspect ratio */
            object-fit: contain; /* Ensure video scales nicely within bounds */
        }
        /* --- End Video Overlay Styles --- */

        /* --- Minimap Styles --- */
        #minimap-container {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.6);
            border: 2px solid #555;
            padding: 5px;
            z-index: 1500; /* Above most UI */
        }
        #minimap-grid {
            display: grid;
            grid-auto-flow: dense; /* Ensures dense packing of grid items */
            gap: 2px; /* Consistent spacing between rooms */
            grid-auto-rows: 10px; /* Fixed height for rows */
        }
        .minimap-room {
            width: 10px;
            height: 10px;
            background-color: #333; /* Default unseen/invalid color */
            border: 1px solid #222;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }
        .minimap-room.visited {
            background-color: #666; /* Visited room color */
            border-color: #444;
        }
        .minimap-room.current {
            background-color: #fff; /* Current room color */
            border-color: #aaa;
            box-shadow: 0 0 3px 1px #fff; /* Glow effect */
        }
        /* --- End Minimap Styles --- */

        /* --- Health Orbs Styles --- */
        #health-orbs-container {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1500; /* Above most UI */
            pointer-events: none; /* Allow clicking through */
        }

        @keyframes pulse {
            0% { opacity: 0.4; }
            50% { opacity: 0.8; }
            100% { opacity: 0.4; }
        }
        /* --- End Health Orbs Styles --- */

        #area-name-display {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);

          /* Styling */
          padding: 15px 30px;
          background-color: rgba(0, 0, 0, 0.75);
          color: white;
          font-size: 2.5em;
          font-family: 'Arial', sans-serif;
          font-weight: bold;
          text-align: center;
          border-radius: 8px;

          /* Layering */
          z-index: 1500; /* Same as minimap, above dialogue/canvas */

          /* Initial State & Transition */
          opacity: 0; /* Start hidden */
          visibility: hidden; /* Start not visible */
          transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;

          /* Interaction */
          pointer-events: none; /* Don't block clicks */
        }

        #total-room-count-display {
          position: absolute;
          top: 20px;
          left: 20px;
          padding: 5px 10px;
          background-color: rgba(0, 0, 0, 0.6);
          border: 1px solid #555;
          color: #aaa;
          font-size: 1em;
          font-family: sans-serif;
          z-index: 1400; /* Below minimap/area name but above dialogue/canvas */
          opacity: 0; /* Start hidden */
          visibility: hidden; /* Start hidden */
          transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
          pointer-events: none;
        }

        /* --- Joystick Styles --- */
        .joystick-zone {
            display: none; /* Hidden by default */
            position: absolute;
            bottom: 1vh; /* Closer to bottom edge */
            width: 25vmin; /* Size relative to smaller viewport side */
            height: 25vmin;
            background-color: rgba(128, 128, 128, 0.3); /* Semi-transparent gray */
            border-radius: 50%; /* Make it circular */
            z-index: 1600; /* Increased to be above minimap/dialogue etc. */
            pointer-events: auto; /* Allow touch */
            /* Prevent text selection */
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        #joystick-left {
            left: 2vmin; /* Position closer to edge */
        }

        #joystick-right {
            right: 2vmin; /* Position closer to edge */
        }

        /* NippleJS specific styles (can be customized further) */
        .nipple .front {
             background-color: rgba(80, 80, 80, 0.6); /* Darker gray handle */
             width: 60px !important; /* Example size */
             height: 60px !important;
             border-radius: 50%;
             border: 2px solid rgba(200, 200, 200, 0.7);
        }
        .nipple .back {
             /* background-color: transparent !important; */ /* Hide default back */
             background-color: rgba(0, 0, 0, 0.2); /* Subtle dark background for the nipple area */
             border-radius: 50%;
             border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Show joysticks only on landscape touch devices */
        @media (orientation: landscape) and (pointer: coarse) {
            .joystick-zone {
                display: block;
            }
        }
         @media (orientation: landscape) and (hover: none) {
             /* Alternative for devices that don't report pointer: coarse reliably */
             .joystick-zone {
                 display: block;
             }
         }
        /* --- End Joystick Styles --- */

        /* --- Rotate Message Style --- */
        #rotate-message {
            display: none; /* Hidden by default */
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px 30px;
            background-color: rgba(50, 50, 50, 0.9);
            border: 2px solid #eee;
            color: #eee;
            font-size: 1.2em;
            text-align: center;
            border-radius: 8px;
            z-index: 1002; /* Above joysticks if they were shown */
            pointer-events: none; /* Don't block underlying clicks */
        }
        #rotate-message.active {
            display: block;
        }
        /* --- End Rotate Message Style --- */

        /* --- Adjust Dialogue for Mobile Landscape --- */
        @media (orientation: landscape) and (pointer: coarse), (orientation: landscape) and (hover: none) {
            #dialogue-container {
                width: 65%; /* Reduced from 80% */
                max-width: 500px; /* Reduced max-width */
                bottom: 18%; /* Raised from 10% to clear joysticks more */
                padding: 15px; /* Slightly smaller padding */
                font-size: 16px; /* Slightly smaller font */
            }
            .dialogue-option-button {
                 font-size: 14px; /* Smaller option font */
                 padding: 4px 8px;
            }
            #dialogue-progress {
                 font-size: 12px; /* Smaller progress font */
            }
        }
        /* --- End Dialogue Adjustment --- */

        /* After the #fade-overlay element, add the loading message element */
        #loading-message {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-family: 'Arial', sans-serif;
            font-size: 16px;
            z-index: 2000;
            display: none;
            pointer-events: none;
        }

    </style>
</head>
<body onclick="initAudioContext()">
    <div id="dialogue-container" class="hidden">
        <span id="dialogue-progress"></span>
        <p id="dialogue-text"></p>
        <div id="dialogue-options"></div>
    </div>

    <div id="ui-container">
        <!-- UI elements like progress indicators might go here if not fully 3D -->

        <!-- Health Orbs Container -->
        <div id="health-orbs-container"></div>

        <!-- Area Name Display Element -->
        <div id="area-name-display"></div>

        <!-- Total Room Count Display (for ESP) -->
        <div id="total-room-count-display"></div>

        <!-- Minimap Container -->
        <div id="minimap-container">
            <div id="minimap-grid"></div>
        </div>

        <!-- Other UI elements? -->
    </div>

    <div id="fade-overlay"></div>

    <!-- Video Overlay Structure -->
    <div id="video-overlay">
        <div id="video-container">
            <!-- Flicker Video -->
            <video id="intro-video" class="video-player" width="640" height="480" muted playsinline loop style="display: none;"> <!-- Added class -->
                <source src="assets/textures/characters/flicker.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <!-- Reborn Video -->
            <video id="reborn-video" class="video-player" width="640" height="480" muted playsinline style="display: none;"> <!-- Added class -->
                 <source src="assets/textures/animations/reborn.mp4" type="video/mp4">
                 Your browser does not support the video tag.
             </video>
        </div>
    </div>

    <!-- Canvas for Three.js -->
    <canvas id="webgl-canvas"></canvas>

    <!-- Audio Element for Chat Sound -->
    <audio id="chat-sound" src="assets/sounds/blip.wav" preload="auto"></audio>
    <!-- Audio Element for Footstep Sound -->
    <audio id="footstep-sound" src="assets/sounds/footstep.wav" preload="auto"></audio>
    <!-- Audio Element for Background Music -->
    <audio id="bg-music" src="assets/sounds/ambient_surround.wav" preload="auto" loop></audio>
    <!-- Audio Element for Second Music Track -->
    <audio id="ambient-music-track" src="assets/sounds/ambient_music.mp3" preload="auto" loop></audio>
    <!-- Audio Element for Reveal Sound -->
    <audio id="reveal-sound" src="assets/sounds/reveal.wav" preload="auto"></audio>
    <!-- Audio Element for Selection Sound -->
    <audio id="select-sound" src="assets/sounds/select.wav" preload="auto"></audio>
    <!-- Audio Element for Start Button -->
    <audio id="start-button-sound" src="assets/sounds/button_start.wav" preload="auto"></audio>
    <!-- NEW Audio Element for Creepy Noise -->
    <audio id="creepy-noise-sound" src="assets/sounds/creepy_noise.wav" preload="auto"></audio>
    <!-- NEW Audio Element for Flicker Laugh -->
    <audio id="flicker_laugh_sound" src="assets/sounds/flicker_laugh.wav" preload="auto"></audio>

    <!-- Joystick Zones -->
    <div id="joystick-left" class="joystick-zone"></div>
    <div id="joystick-right" class="joystick-zone"></div>

    <!-- Message for Mobile Portrait -->
    <div id="rotate-message">Please rotate your device to landscape mode to play.</div>

    <div id="loading-message">Preparing dungeon...</div>

    <script type="importmap">
        {
            "imports": {
                "three": "./libs/three/build/three.module.js",
                "three/addons/": "./libs/three/examples/jsm/"
            }
        }
    </script>
    <!-- Audio Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tone/14.8.49/Tone.js"></script>
    <!-- Using a different version of SoundTouch that doesn't use ES modules -->
    <script src="https://unpkg.com/soundtouchjs@0.1.25/dist/soundtouch.min.js"></script>

    <script>
        // Initialize audio context on user interaction
        let audioContextInitialized = false;
        function initAudioContext() {
            if (!audioContextInitialized && typeof Tone !== 'undefined') {
                console.log("Initializing Tone.js AudioContext from user interaction");
                Tone.start().then(() => {
                    console.log("Tone.js AudioContext started successfully");
                    audioContextInitialized = true;

                    // Remove the click handler once initialized
                    document.body.onclick = null;
                }).catch(error => {
                    console.error("Failed to start Tone.js AudioContext:", error);
                });
            }
        }
    </script>

    <script type="module" src="./main.js"></script>
    <!-- NippleJS Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/nipplejs/0.10.1/nipplejs.min.js"></script>
    <!-- Boss Debug Overlay -->
    <script src="./ultra-debug.js"></script>
    <!-- Teleport Debug Commands -->
    <script type="module" src="./src/debug/teleport-commands.js"></script>
</body>
</html>
