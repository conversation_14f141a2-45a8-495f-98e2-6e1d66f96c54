import * as THREE from 'three';

/**
 * Manages HDR, framerate, and bit depth settings
 */
class HDRFramerateManager {
    /**
     * Create a new HDR and Framerate Manager
     * @param {THREE.WebGLRenderer} renderer - The WebGL renderer
     * @param {THREE.Scene} scene - The scene
     * @param {THREE.Camera} camera - The camera
     */
    constructor(renderer, scene, camera) {
        this.renderer = renderer;
        this.scene = scene;
        this.camera = camera;

        // HDR settings
        this.hdrEnabled = false;
        this.peakBrightness = 100; // in nits
        this.toneMappingExposure = 1.0;

        // Configure renderer for HDR
        if (renderer) {
            // Set tone mapping (Uncharted2 works well for HDR simulation)
            renderer.toneMapping = THREE.ACESFilmicToneMapping;
            renderer.toneMappingExposure = this.toneMappingExposure;
            renderer.outputEncoding = THREE.sRGBEncoding;
        }

        // Framerate settings
        this.targetFramerate = 60;
        this.framerateLocked = false;
        this.lastFrameTime = 0;
        this.frameTimeTarget = 1000 / this.targetFramerate;

        // Bit depth settings
        this.bitDepth = 4.0; // Changed from 8 to 4.0
        this.ditherEnabled = true;
        this.ditherStrength = 0.1; // Will keep this as 0.1 as requested

        // Initialize presets
        this.presets = this._initializePresets();
        // Set NES as the default preset instead of modern
        this.currentPreset = 'nes';

        // Create post-processing shader for bit depth simulation
        this._initializePostProcessing();

        // Apply initial settings (after post-processing is initialized)
        this.applyPreset('nes'); // Always start with NES preset
    }

    /**
     * Initialize presets for different systems
     * @private
     */
    _initializePresets() {
        return {
            'modern': {
                peakBrightness: 1000,
                bitDepth: 10,
                targetFramerate: 60,
                ditherEnabled: false,
                toneMappingExposure: 1.0
            },
            'hdr_cinema': {
                peakBrightness: 1000,
                bitDepth: 10,
                targetFramerate: 24,
                ditherEnabled: false,
                toneMappingExposure: 0.8
            },
            'sdr_tv': {
                peakBrightness: 100,
                bitDepth: 8,
                targetFramerate: 60,
                ditherEnabled: true,
                toneMappingExposure: 1.0
            },
            'nes': {
                peakBrightness: 80,
                bitDepth: 4.0, // Changed from 6 to 4.0 as requested
                targetFramerate: 60,
                ditherEnabled: true,
                toneMappingExposure: 1.2
            },
            'snes': {
                peakBrightness: 90,
                bitDepth: 8,
                targetFramerate: 60,
                ditherEnabled: true,
                toneMappingExposure: 1.1
            },
            'genesis': {
                peakBrightness: 85,
                bitDepth: 8,
                targetFramerate: 60,
                ditherEnabled: true,
                toneMappingExposure: 1.1
            },
            'ps1': {
                peakBrightness: 100,
                bitDepth: 8,
                targetFramerate: 30,
                ditherEnabled: true,
                toneMappingExposure: 1.0
            },
            'n64': {
                peakBrightness: 100,
                bitDepth: 8,
                targetFramerate: 30,
                ditherEnabled: true,
                toneMappingExposure: 1.0
            },
            'dreamcast': {
                peakBrightness: 120,
                bitDepth: 8,
                targetFramerate: 60,
                ditherEnabled: true,
                toneMappingExposure: 1.0
            },
            'ps2': {
                peakBrightness: 150,
                bitDepth: 8,
                targetFramerate: 60,
                ditherEnabled: true,
                toneMappingExposure: 1.0
            },
            'xbox': {
                peakBrightness: 180,
                bitDepth: 8,
                targetFramerate: 60,
                ditherEnabled: true,
                toneMappingExposure: 1.0
            },
            'ps3': {
                peakBrightness: 300,
                bitDepth: 8,
                targetFramerate: 30,
                ditherEnabled: false,
                toneMappingExposure: 1.0
            },
            'xbox360': {
                peakBrightness: 300,
                bitDepth: 8,
                targetFramerate: 30,
                ditherEnabled: false,
                toneMappingExposure: 1.0
            }
        };
    }

    /**
     * Initialize post-processing for bit depth simulation
     * @private
     */
    _initializePostProcessing() {
        // Create a shader material for bit depth simulation
        this.bitDepthMaterial = new THREE.ShaderMaterial({
            uniforms: {
                tDiffuse: { value: null },
                bitDepth: { value: this.bitDepth },
                ditherEnabled: { value: this.ditherEnabled ? 1.0 : 0.0 },
                ditherStrength: { value: this.ditherStrength },
                time: { value: 0 }
            },
            vertexShader: `
                varying vec2 vUv;
                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform sampler2D tDiffuse;
                uniform float bitDepth;
                uniform float ditherEnabled;
                uniform float ditherStrength;
                uniform float time;
                varying vec2 vUv;

                // Pseudo-random function
                float random(vec2 co) {
                    return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);
                }

                void main() {
                    vec4 texel = texture2D(tDiffuse, vUv);

                    // Calculate max value based on bit depth
                    float maxVal = pow(2.0, bitDepth) - 1.0;

                    // Apply dithering if enabled
                    if (ditherEnabled > 0.5) {
                        // Bayer matrix dithering
                        float dither = random(vUv + vec2(time * 0.01)) * ditherStrength;
                        texel.rgb += dither - (ditherStrength * 0.5);
                    }

                    // Quantize colors based on bit depth
                    texel.r = floor(texel.r * maxVal) / maxVal;
                    texel.g = floor(texel.g * maxVal) / maxVal;
                    texel.b = floor(texel.b * maxVal) / maxVal;

                    gl_FragColor = texel;
                }
            `
        });

        // Create a full-screen quad for post-processing
        this.postProcessingScene = new THREE.Scene();
        this.postProcessingCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);

        const plane = new THREE.PlaneGeometry(2, 2);
        this.postProcessingQuad = new THREE.Mesh(plane, this.bitDepthMaterial);
        this.postProcessingScene.add(this.postProcessingQuad);

        // Create render target
        this.renderTarget = new THREE.WebGLRenderTarget(
            window.innerWidth,
            window.innerHeight,
            {
                minFilter: THREE.LinearFilter,
                magFilter: THREE.LinearFilter,
                format: THREE.RGBAFormat,
                stencilBuffer: false
            }
        );

        // Add window resize listener
        window.addEventListener('resize', this.updateResolution.bind(this));
    }

    /**
     * Update resolution when window is resized
     */
    updateResolution() {
        const width = window.innerWidth;
        const height = window.innerHeight;

        // Ensure we have valid dimensions
        if (width <= 0 || height <= 0) {
            console.warn('Invalid dimensions for HDR effect:', width, height);
            return;
        }

        // Update render target size
        this.renderTarget.setSize(width, height);
    }

    /**
     * Apply a preset configuration
     * @param {string} presetName - Name of the preset to apply
     */
    applyPreset(presetName) {
        const preset = this.presets[presetName];
        if (!preset) {
            console.warn(`Preset '${presetName}' not found`);
            return;
        }

        this.currentPreset = presetName;

        // Apply HDR settings
        this.peakBrightness = preset.peakBrightness;
        this.toneMappingExposure = preset.toneMappingExposure;

        // Update renderer exposure - scale based on peak brightness
        // This creates a more noticeable effect when changing brightness
        const normalizedBrightness = this.peakBrightness / 100; // Normalize to 100 nits as baseline
        const scaledExposure = this.toneMappingExposure * Math.sqrt(normalizedBrightness);
        this.renderer.toneMappingExposure = scaledExposure;

        console.log(`Applied brightness: ${this.peakBrightness} nits, Exposure: ${scaledExposure.toFixed(2)}`);

        // Apply framerate settings
        this.targetFramerate = preset.targetFramerate;
        this.frameTimeTarget = 1000 / this.targetFramerate;

        // Apply bit depth settings
        this.bitDepth = preset.bitDepth;
        this.ditherEnabled = preset.ditherEnabled;

        // Update shader uniforms if initialized
        if (this.bitDepthMaterial && this.bitDepthMaterial.uniforms) {
            this.bitDepthMaterial.uniforms.bitDepth.value = this.bitDepth;
            this.bitDepthMaterial.uniforms.ditherEnabled.value = this.ditherEnabled ? 1.0 : 0.0;
        }

        console.log(`Applied preset '${presetName}':`, preset);
    }

    /**
     * Apply custom settings
     * @param {Object} settings - Custom settings to apply
     */
    applyCustomSettings(settings) {
        // Track if we need to update exposure
        let updateExposure = false;

        if (settings.peakBrightness !== undefined) {
            this.peakBrightness = settings.peakBrightness;
            updateExposure = true;
        }

        if (settings.toneMappingExposure !== undefined) {
            this.toneMappingExposure = settings.toneMappingExposure;
            updateExposure = true;
        }

        // Update exposure if either brightness or exposure changed
        if (updateExposure) {
            const normalizedBrightness = this.peakBrightness / 100;
            const scaledExposure = this.toneMappingExposure * Math.sqrt(normalizedBrightness);
            this.renderer.toneMappingExposure = scaledExposure;
        }

        if (settings.targetFramerate !== undefined) {
            this.targetFramerate = settings.targetFramerate;
            this.frameTimeTarget = 1000 / this.targetFramerate;
        }

        if (settings.framerateLocked !== undefined) {
            this.framerateLocked = settings.framerateLocked;
        }

        if (settings.bitDepth !== undefined) {
            this.bitDepth = settings.bitDepth;
            if (this.bitDepthMaterial && this.bitDepthMaterial.uniforms) {
                this.bitDepthMaterial.uniforms.bitDepth.value = this.bitDepth;
            }
        }

        if (settings.ditherEnabled !== undefined) {
            this.ditherEnabled = settings.ditherEnabled;
            if (this.bitDepthMaterial && this.bitDepthMaterial.uniforms) {
                this.bitDepthMaterial.uniforms.ditherEnabled.value = this.ditherEnabled ? 1.0 : 0.0;
            }
        }

        if (settings.ditherStrength !== undefined) {
            this.ditherStrength = settings.ditherStrength;
            if (this.bitDepthMaterial && this.bitDepthMaterial.uniforms) {
                this.bitDepthMaterial.uniforms.ditherStrength.value = this.ditherStrength;
            }
        }

        this.currentPreset = 'custom';
    }

    /**
     * Get the list of available presets
     * @returns {string[]} Array of preset names
     */
    getPresetList() {
        return Object.keys(this.presets);
    }

    /**
     * Get the current preset name
     * @returns {string} Current preset name
     */
    getCurrentPreset() {
        return this.currentPreset;
    }

    /**
     * Check if we should render this frame based on framerate limiting
     * @param {number} time - Current time in milliseconds
     * @returns {boolean} - Whether to render this frame
     */
    shouldRenderFrame(time) {
        // Always render if framerate lock is disabled
        if (!this.framerateLocked) {
            return true;
        }

        // Check if enough time has passed since last frame
        const elapsed = time - this.lastFrameTime;
        if (elapsed < this.frameTimeTarget) {
            return false; // Skip this frame
        }

        // Update last frame time
        this.lastFrameTime = time;
        return true;
    }

    /**
     * Apply HDR settings to the renderer
     */
    applyHDRSettings() {
        try {
            // Update time uniform for animations
            if (this.bitDepthMaterial && this.bitDepthMaterial.uniforms) {
                this.bitDepthMaterial.uniforms.time.value = performance.now();
            }

            // Apply tone mapping settings with significantly increased brightness
            const normalizedBrightness = this.peakBrightness / 100;

            // Use an even more aggressive brightness scaling formula
            // This will make the effect much more noticeable and brighter overall
            const scaledExposure = this.toneMappingExposure * Math.sqrt(normalizedBrightness) * 3.5;

            // Apply to renderer with brighter tone mapping
            this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
            this.renderer.toneMappingExposure = scaledExposure;


        } catch (error) {
            console.error('Error applying HDR settings:', error);
        }
    }

    /**
     * Apply bit depth post-processing
     */
    applyBitDepthPostProcessing() {
        try {
            // Skip if not properly initialized
            if (!this.renderTarget || !this.postProcessingScene || !this.postProcessingCamera ||
                !this.bitDepthMaterial || !this.bitDepthMaterial.uniforms) {
                return;
            }

            // Get the current render target from the renderer
            const currentRenderTarget = this.renderer.getRenderTarget();

            // If we're rendering to a target, use that as input
            if (currentRenderTarget) {
                this.bitDepthMaterial.uniforms.tDiffuse.value = currentRenderTarget.texture;
            }
            // Otherwise use our own render target (render scene first)
            else {
                // Render scene to our render target
                this.renderer.setRenderTarget(this.renderTarget);
                this.renderer.render(this.scene, this.camera);
                this.bitDepthMaterial.uniforms.tDiffuse.value = this.renderTarget.texture;
            }

            // Apply bit depth post-processing
            this.renderer.setRenderTarget(null);
            this.renderer.render(this.postProcessingScene, this.postProcessingCamera);
        } catch (error) {
            console.error('Error applying bit depth post-processing:', error);

            // Fallback to normal rendering
            this.renderer.setRenderTarget(null);
            this.renderer.render(this.scene, this.camera);
        }
    }

    /**
     * Update the effect (call this in your animation loop)
     * @param {number} time - Current time in milliseconds
     */
    update(time) {
        try {
            // Check if we should render this frame
            if (!this.shouldRenderFrame(time)) {
                return false; // Skip this frame
            }

            // Apply HDR settings
            this.applyHDRSettings();

            // Check if all required components are initialized
            if (!this.renderTarget || !this.postProcessingScene || !this.postProcessingCamera) {
                console.warn('HDR effect not fully initialized, falling back to normal rendering');
                this.renderer.setRenderTarget(null);
                this.renderer.render(this.scene, this.camera);
                return true;
            }

            // Render scene to render target
            this.renderer.setRenderTarget(this.renderTarget);
            this.renderer.render(this.scene, this.camera);

            // Apply bit depth post-processing
            this.applyBitDepthPostProcessing();

            return true; // Signal that rendering occurred
        } catch (error) {
            console.error('Error in HDR effect update:', error);

            // Fallback to normal rendering
            this.renderer.setRenderTarget(null);
            this.renderer.render(this.scene, this.camera);

            return true; // Still signal that rendering occurred
        }
    }

    /**
     * Adjust a specific parameter
     * @param {string} param - Parameter name
     * @param {number|boolean} value - New value
     */
    adjustParameter(param, value) {
        switch (param) {
            case 'peakBrightness':
                this.peakBrightness = value;
                // Update exposure based on brightness
                const normalizedBrightness = this.peakBrightness / 100;
                const scaledExposure = this.toneMappingExposure * Math.sqrt(normalizedBrightness);
                this.renderer.toneMappingExposure = scaledExposure;
                break;
            case 'toneMappingExposure':
                this.toneMappingExposure = value;
                // Update exposure based on brightness
                const normalizedBrightness2 = this.peakBrightness / 100;
                const scaledExposure2 = value * Math.sqrt(normalizedBrightness2);
                this.renderer.toneMappingExposure = scaledExposure2;
                break;
            case 'targetFramerate':
                this.targetFramerate = value;
                this.frameTimeTarget = 1000 / value;
                break;
            case 'framerateLocked':
                this.framerateLocked = value;
                break;
            case 'bitDepth':
                this.bitDepth = value;
                if (this.bitDepthMaterial && this.bitDepthMaterial.uniforms) {
                    this.bitDepthMaterial.uniforms.bitDepth.value = value;
                }
                break;
            case 'ditherEnabled':
                this.ditherEnabled = value;
                if (this.bitDepthMaterial && this.bitDepthMaterial.uniforms) {
                    this.bitDepthMaterial.uniforms.ditherEnabled.value = value ? 1.0 : 0.0;
                }
                break;
            case 'ditherStrength':
                this.ditherStrength = value;
                if (this.bitDepthMaterial && this.bitDepthMaterial.uniforms) {
                    this.bitDepthMaterial.uniforms.ditherStrength.value = value;
                }
                break;
            default:
                console.warn(`Unknown parameter: ${param}`);
        }

        // Mark as using custom settings
        this.currentPreset = 'custom';
    }
}

export { HDRFramerateManager };
