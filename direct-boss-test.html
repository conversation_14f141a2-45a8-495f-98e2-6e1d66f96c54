<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Boss System Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #000;
        }
        #game-container {
            width: 100vw;
            height: 100vh;
        }
        #debug-overlay {
            position: fixed;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            z-index: 1000;
        }
        button {
            margin: 5px;
            padding: 5px 10px;
            background-color: #444;
            color: #fff;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #666;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    <div id="debug-overlay">
        <h3>Boss Battle Debug</h3>
        <div id="intensity-display">Music Intensity: 0%</div>
        <div id="pattern-display">Current Pattern: None</div>
        <div id="beat-display">Beat Detected: No</div>
        <div id="buttons">
            <button id="force-pattern-btn" onclick="forcePattern('circle_ripple')">Force Circle Pattern</button>
            <button id="force-spiral-btn" onclick="forcePattern('spiral_wave')">Force Spiral Pattern</button>
            <button id="force-hellburst-btn" onclick="forcePattern('hellburst')">Force Hellburst</button>
        </div>
    </div>

    <!-- Load the game -->
    <script src="main.js"></script>
    
    <!-- Helper function to force patterns -->
    <script>
        function forcePattern(patternName) {
            try {
                const dungeonHandler = window.sceneManager.currentHandler;
                if (!dungeonHandler || !dungeonHandler.activeBosses || dungeonHandler.activeBosses.length === 0) {
                    console.error("No active bosses found");
                    return;
                }
                
                const boss = dungeonHandler.activeBosses[0];
                if (!boss || !boss.userData || !boss.userData.aiController || !boss.userData.aiController.bossController) {
                    console.error("Boss controller not found");
                    return;
                }
                
                const bossController = boss.userData.aiController.bossController;
                bossController.patternManager.triggerPattern(patternName, 0.8);
                console.log(`Forced pattern: ${patternName}`);
            } catch (error) {
                console.error("Error forcing pattern:", error);
            }
        }
    </script>
    
    <!-- Load our direct test script as a module -->
    <script type="module" src="direct-boss-test.js"></script>
</body>
</html>
