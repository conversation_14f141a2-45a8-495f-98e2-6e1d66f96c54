<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <title>Game Dev Planner - Final</title>

  <!-- jsPDF (for PDF exports) -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.23/jspdf.plugin.autotable.min.js"></script>

  <!-- Force Graph (for Graph View) -->
  <script src="https://unpkg.com/force-graph"></script>

  <style>
    :root {
      --bg-color: #f8f9fa;
      --text-color: #212529;
      --header-bg: #e9ecef;
      --category-bg: #e9ecef;
      --category-text: #495057;
      --category-border: #ced4da;
      --card-bg: #ffffff;
      --card-border: #dee2e6;
      --card-shadow: rgba(0,0,0,0.05);
      --card-title-color: #007bff;
      --label-color: #6c757d;
      --button-primary-bg: #007bff;
      --button-primary-hover: #0056b3;
      --button-secondary-bg: #6c757d;
      --button-secondary-hover: #5a6268;
      --button-success-bg: #28a745;
      --button-success-hover: #218838;
      --button-info-bg: #17a2b8;
      --button-info-hover: #117a8b;
      --button-warning-bg: #ffc107;
      --button-warning-hover: #e0a800;
      --button-danger-bg: #dc3545;
      --button-danger-hover: #a51826;
      --button-purple-bg: #6f42c1;
      --button-purple-hover: #5a379b;
      --button-teal-bg: #20c997;
      --button-teal-hover: #18a179;
      --button-orange-bg: #fd7e14;
      --button-orange-hover: #e06200;
      --editable-hover-bg: #f0f8ff;
      --editable-focus-bg: #e7f3ff;
      --editable-focus-outline: #007bff;
      --scrollbar-track: #f1f1f1;
      --scrollbar-thumb: #ced4da;
      --scrollbar-thumb-hover: #adb5bd;
      --drag-over-border: #007bff;
      --drag-over-bg: rgba(0, 123, 255, 0.05);
      --tag-text-color: #ffffff;
      --pin-color-active: #ffc107;
      --pin-color-inactive: var(--label-color);
      --minimap-bg: rgba(233, 236, 239, 0.9);
      --minimap-item-bg: #ced4da;
      --minimap-item-hover-bg: #adb5bd;
      --card-completed-bg: #d4edda;
      --card-completed-border: #28a745;
    }

    /* Dark mode overrides */
    body.dark-mode {
      --bg-color: #212529;
      --text-color: #e9ecef;
      --header-bg: #343a40;
      --category-bg: #495057;
      --category-text: #ced4da;
      --category-border: #6c757d;
      --card-bg: #343a40;
      --card-border: #6c757d;
      --card-shadow: rgba(0,0,0,0.2);
      --card-title-color: #4dabf7;
      --label-color: #adb5bd;
      --editable-hover-bg: #495057;
      --editable-focus-bg: #505d6a;
      --editable-focus-outline: #4dabf7;
      --scrollbar-track: #495057;
      --scrollbar-thumb: #6c757d;
      --scrollbar-thumb-hover: #adb5bd;
      --drag-over-bg: rgba(77, 171, 247, 0.1);
      --tag-text-color: #212529;
      --pin-color-active: #ffc107;
      --pin-color-inactive: var(--label-color);
      --minimap-bg: rgba(52,58,64,0.9);
      --minimap-item-bg: #6c757d;
      --minimap-item-hover-bg: #adb5bd;
      --card-completed-bg: #2a4a34;
      --card-completed-border: #28a745;
    }

    body {
      margin: 0;
      padding: 15px;
      display: flex;
      flex-direction: column;
      background-color: var(--bg-color);
      color: var(--text-color);
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      transition: background-color 0.3s, color 0.3s;
    }

    .header {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
      background-color: var(--header-bg);
      padding: 10px;
      border-radius: 8px;
      gap: 10px;
      margin-bottom: 20px;
    }
    h1 {
      margin: 0;
      flex-grow: 1;
      text-align: center;
      font-size: 1.8em;
      color: var(--text-color);
    }
    .controls-group {
      display: flex;
      gap: 10px;
      align-items: center;
    }
    .controls-group button,
    .controls-group label {
      background-color: var(--button-primary-bg);
      color: white;
      border: none;
      border-radius: 5px;
      padding: 8px 15px;
      cursor: pointer;
      font-size: 0.9em;
      transition: background-color 0.2s ease;
      text-align: center;
      white-space: nowrap;
    }
    .controls-group button:hover,
    .controls-group label:hover {
      background-color: var(--button-primary-hover);
    }

    #load-file-input { display: none; }
    #export-md-button { background-color: var(--button-success-bg); }
    #export-md-button:hover { background-color: var(--button-success-hover); }
    #export-pdf-button { background-color: var(--button-danger-bg); }
    #export-pdf-button:hover { background-color: var(--button-danger-hover); }
    #dark-mode-toggle { background-color: var(--button-secondary-bg); }
    #dark-mode-toggle:hover { background-color: var(--button-secondary-hover); }
    #bionic-toggle-button { background-color: var(--button-purple-bg); }
    #bionic-toggle-button.active { background-color: var(--button-success-bg); }
    #bionic-toggle-button:hover { background-color: var(--button-purple-hover); }
    #speech-toggle-button { background-color: var(--button-info-bg); }
    #speech-toggle-button:hover { background-color: var(--button-info-hover); }
    #speech-toggle-button.listening {
      background-color: var(--button-danger-bg);
      animation: pulse 1.5s infinite;
    }

    #view-toggle-button,
    #kanban-toggle-button,
    #timeline-toggle-button {
      background-color: var(--button-warning-bg);
      color: var(--text-color);
    }
    #view-toggle-button:hover,
    #kanban-toggle-button:hover,
    #timeline-toggle-button:hover {
      background-color: var(--button-warning-hover);
    }

    #save-button .save-indicator {
      color: var(--button-warning-bg);
      margin-left: 5px;
      font-weight: bold;
    }
    #status-message {
      font-size: 0.9em;
      color: var(--label-color);
      min-height: 1.2em;
      text-align: right;
    }
    #search-input {
      padding: 6px 10px;
      font-size: 0.9em;
      border: 1px solid var(--category-border);
      border-radius: 5px;
      background-color: var(--card-bg);
      color: var(--text-color);
      margin-left: auto;
    }

    #minimap {
      position: fixed;
      top: 150px; 
      left: 0;
      width: 100px;
      max-height: 70vh;
      background-color: var(--minimap-bg);
      border-radius: 0 5px 5px 0;
      padding: 10px 5px;
      box-shadow: 2px 0 5px rgba(0,0,0,0.1);
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 4px;
      z-index: 99;
    }
    .minimap-item {
      height: 20px;
      background-color: var(--minimap-item-bg);
      border-radius: 3px;
      cursor: pointer;
      transition: background-color 0.2s;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      font-size: 0.7em;
      line-height: 20px;
      color: var(--bg-color);
    }
    .minimap-item:hover {
      background-color: var(--minimap-item-hover-bg);
    }

    #gdd-container-wrapper {
      position: relative;
      display: flex;
      align-items: flex-start;
      width: 100%;
    }
    #gdd-container {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      gap: 15px;
      padding: 10px;
      flex-grow: 1;
      min-height: 70vh;
      scroll-behavior: smooth;
      margin-left: 110px; 
    }
    #add-category-button {
      background-color: var(--button-secondary-bg);
      margin-left: 15px;
      flex-shrink: 0;
      align-self: flex-start;
    }
    #add-category-button:hover {
      background-color: var(--button-secondary-hover);
    }

    /* Category columns */
    .category {
      background-color: var(--category-bg);
      border-radius: 8px;
      padding: 15px;
      min-width: 320px;
      max-width: 400px;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      max-height: 80vh;
      cursor: grab;
      transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.3s;
    }
    .category.dragging {
      opacity: 0.5;
      cursor: grabbing;
      transform: scale(1.02);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    .category.filtered-out { display: none; }
    .category-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--category-border);
      padding-bottom: 10px;
      margin-bottom: 15px;
    }
    .category-header h2 {
      margin: 0;
      flex-grow: 1;
      text-align: center;
      font-size: 1.3em;
      color: var(--category-text);
    }
    .category-header h2 .editable-title { cursor: pointer; }
    .category-header .category-actions {
      display: flex;
      gap: 5px;
      align-items: center;
    }
    .category-actions button {
      background: none;
      border: none;
      font-size: 0.9em;
      padding: 2px 4px;
      border-radius: 3px;
      cursor: pointer;
      color: var(--label-color);
    }
    .category-actions button:hover {
      background-color: rgba(0,0,0,0.1);
      color: var(--text-color);
    }

    .cards-container {
      overflow-y: auto;
      flex-grow: 1;
      padding: 5px 5px 5px 0;
      border: 2px dashed transparent;
      transition: border-color 0.2s ease, background-color 0.2s ease,
                  max-height 0.3s ease, opacity 0.3s ease, padding 0.3s ease;
    }
    .category.collapsed .cards-container,
    .category.collapsed .add-card-button {
      max-height: 0;
      overflow: hidden;
      padding: 0;
      border-width: 0;
      margin-top: -10px;
      opacity: 0;
      pointer-events: none;
    }

    /* Cards */
    .card {
      background-color: var(--card-bg);
      border: 1px solid var(--card-border);
      border-radius: 5px;
      padding: 12px;
      margin-bottom: 10px;
      box-shadow: 0 2px 4px var(--card-shadow);
      word-wrap: break-word;
      cursor: grab;
      position: relative;
      transition: opacity 0.2s, border-color 0.2s, box-shadow 0.2s, background-color 0.3s;
    }
    .card.dragging { opacity: 0.4; cursor: grabbing; }
    .card.drag-over { border-top: 2px solid var(--drag-over-border); }
    .card.filtered-out { display: none; }
    .card.pinned { 
      order:-1; 
      border-left:4px solid var(--pin-color-active); 
    }
    .card.completed {
      background-color: var(--card-completed-bg);
      border-left:4px solid var(--card-completed-border);
    }
    /* blocked highlight if it depends on incomplete tasks */
    .card.blocked {
      border:2px dashed red !important;
    }
    /* overdue highlight if endDate < today */
    .card.overdue {
      background-color:#ffe5e5 !important;
    }

    .card-header {
      display:flex;
      justify-content:space-between;
      align-items:flex-start;
      margin-bottom:8px;
    }
    .card-header h3 {
      margin:0;
      font-size:1.1em;
      color:var(--card-title-color);
      flex-grow:1;
      padding-right:5px;
    }
    .card-actions {
      display:flex;
      gap:4px;
      align-items:center;
    }
    .card-actions button {
      background:none;
      border:none;
      font-size:1.1em;
      line-height:1;
      color:var(--label-color);
      cursor:pointer;
      padding:0 3px;
    }
    .card-actions button:hover { color:var(--text-color); }
    .complete-card-button {
      font-size:1.2em;
      color:var(--button-success-bg);
    }
    .pin-card-button {
      color:var(--pin-color-inactive);
    }
    .pin-card-button.active {
      color:var(--pin-color-active);
    }

    /* property container */
    .property-container {
      margin:5px 0;
    }
    .property-container strong {
      color: var(--label-color);
      font-weight:600;
      margin-right:5px;
      display:inline-block;
      vertical-align:top;
      white-space:nowrap;
    }
    .property-values {
      display:block;
      margin-left:25px;
    }
    .property-line {
      display:flex;
      align-items:center;
      margin-bottom:3px;
      white-space:pre-wrap;
      word-break:break-word;
    }
    .property-line button.remove-line-button {
      margin-left:5px;
    }
    .editable {
      cursor:text;
      padding:1px 2px;
      border-radius:3px;
      min-width:10px;
      display:inline-block;
      word-break:break-word;
    }
    .editable:hover {
      background-color:var(--editable-hover-bg);
    }
    .editable[contenteditable="true"] {
      outline:1px dashed var(--editable-focus-outline);
      background-color:var(--editable-focus-bg);
      box-shadow:0 0 5px rgba(0,123,255,0.2);
    }
    .editable-title,
    .editable-label {
      font-size:inherit;
      font-weight:inherit;
    }
    .editable-value {
      white-space:pre-wrap;
    }

    .add-property-button {
      background-color: var(--button-purple-bg);
    }
    .add-property-button:hover {
      background-color: var(--button-purple-hover);
    }
    .remove-property-button {
      color: var(--button-orange-bg);
      margin-left:3px;
    }
    .remove-property-button:hover {
      color: var(--button-orange-hover);
    }
    .add-line-button {
      color: var(--button-teal-bg);
      margin-left:10px;
    }
    .add-line-button:hover {
      color: var(--button-teal-hover);
    }

    .add-card-button {
      background-color: var(--button-success-bg);
      margin-top:10px;
      align-self:center;
    }
    .add-card-button:hover {
      background-color: var(--button-success-hover);
    }

    /* Graph container */
    #graph-container {
      width:100%;
      height:70vh;
      border:1px solid var(--category-border);
      margin-top:15px;
      display:none;
      background-color:var(--bg-color);
      position:relative;
    }

    /* 2D map for “Timeline” */
    #timeline-container {
      display:none;
      width:100%;
      height:80vh;
      border:1px solid var(--category-border);
      margin-top:15px;
      overflow:auto;
      background-color:var(--bg-color);
      position:relative;
      padding:20px;
    }
    #map-grid {
      display:grid;
      grid-template-columns:repeat(31,80px);
      grid-template-rows:repeat(12,80px);
      gap:6px;
    }
    .map-cell {
      width:80px;
      height:80px;
      background-color:var(--category-bg);
      border:1px dashed var(--category-border);
      border-radius:5px;
      position:relative;
      display:flex;
      align-items:center;
      justify-content:center;
      font-size:0.75em;
      color:var(--text-color);
      text-align:center;
      flex-direction:column;
      padding:2px;
      overflow:hidden;
    }

    /* Kanban container */
    #kanban-container {
      display:none;
      width:100%;
      margin-top:15px;
      border:1px solid var(--category-border);
      background-color:var(--bg-color);
      overflow-x:auto;
      padding:10px;
    }
    .kanban-column {
      width:300px;
      background:var(--category-bg);
      border-radius:5px;
      margin-right:10px;
      padding:10px;
      display:inline-block;
      vertical-align:top;
      position:relative;
      min-height:400px;
    }
    .kanban-column h3 {
      margin:0 0 10px 0;
      text-align:center;
    }
    .kanban-column.drag-over {
      border:2px dashed var(--drag-over-border);
    }

    /* schedule form in Kanban */
    .schedule-form {
      margin-top:6px;
      border-top:1px solid var(--category-border);
      padding-top:6px;
      font-size:0.85em;
      display:none;
    }
    .schedule-form .close-form {
      float:right;
      background-color: var(--button-warning-bg);
      color: var(--text-color);
      border:none;
      border-radius:3px;
      padding:2px 6px;
      cursor:pointer;
      font-size:0.8em;
      margin-top:-2px;
    }
    .schedule-form .close-form:hover {
      background-color: var(--button-warning-hover);
    }
    .schedule-form label {
      display:block;
      margin:4px 0 2px 0;
      color: var(--label-color);
      font-weight:600;
    }
    .schedule-form input[type="date"] {
      width:90%;
      margin-bottom:5px;
      padding:3px 6px;
      font-size:0.85em;
      box-sizing:border-box;
    }
    .schedule-form select {
      width:90%;
      margin-bottom:5px;
      font-size:0.85em;
    }
    .schedule-form .save-schedule-btn {
      display:inline-block;
      background-color: var(--button-success-bg);
      color:white;
      border:none;
      border-radius:3px;
      padding:3px 6px;
      cursor:pointer;
      font-size:0.8em;
      margin-top:5px;
    }
    .schedule-form .save-schedule-btn:hover {
      background-color: var(--button-success-hover);
    }

    /* Graph modal */
    #graph-modal-overlay {
      display:none;
      position:fixed;
      z-index:999;
      top:0;left:0;right:0;bottom:0;
      background-color:rgba(0,0,0,0.5);
      align-items:center;
      justify-content:center;
    }
    #graph-modal {
      background:var(--card-bg);
      color:var(--text-color);
      padding:20px;
      border-radius:8px;
      width:400px;
      max-height:80vh;
      overflow-y:auto;
      position:relative;
    }
    #graph-modal button.close-modal {
      position:absolute;
      top:10px;right:10px;
      background:none;
      border:none;
      font-size:1.2em;
      cursor:pointer;
      color:var(--label-color);
    }
    .modal-card-info {
      margin-top:20px;
    }
  </style>
</head>

<body>
  <div class="header">
    <h1>
      <span id="main-title" class="editable editable-title">
        Game Dev Planner - Final
      </span>
    </h1>

    <!-- File ops: save/load, exports -->
    <div class="controls-group file-ops">
      <input type="text" id="search-input" placeholder="Filter cards/categories..."/>
      <button id="save-button">
        Save <span class="save-indicator" style="display:none;">*</span>
      </button>
      <input type="file" id="load-file-input" accept=".json,.txt"/>
      <label for="load-file-input" id="load-button">Load</label>
      <button id="export-md-button" title="Export as Markdown">Export MD</button>
      <button id="export-pdf-button" title="Export as PDF">Export PDF</button>
    </div>

    <!-- Extra ops: dark mode, bionic reading, graph/timeline/kanban toggles -->
    <div class="controls-group extra-ops">
      <button id="dark-mode-toggle" title="Toggle Dark/Light Mode">🌙</button>
      <button id="bionic-toggle-button" title="Bionic Reading">Bionic</button>
      <button id="view-toggle-button" title="Toggle Graph View">Graph View</button>
      <button id="timeline-toggle-button" title="Toggle Timeline View">Timeline</button>
      <button id="kanban-toggle-button" title="Toggle Kanban View">Kanban</button>
    </div>

    <!-- Speech ops -->
    <div class="controls-group speech-ops">
      <button id="speech-toggle-button" title="Toggle voice input mode on/off">
        Voice Mode 🎤
      </button>
      <span id="speech-status" style="font-size:0.9em; color:#6c757d;"></span>
    </div>

    <div id="status-message" class="controls-group"></div>
  </div>

  <!-- Minimap on the left side -->
  <div id="minimap"></div>

  <!-- Main container for categories -->
  <div id="gdd-container-wrapper">
    <div id="gdd-container"></div>
    <button id="add-category-button">+ Category</button>
  </div>

  <!-- Graph, 2D map (timeline), Kanban -->
  <div id="graph-container"></div>
  <div id="timeline-container" style="overflow:auto;">
    <div id="map-grid"></div>
  </div>
  <div id="kanban-container"></div>

  <!-- Modal for Graph View Node (Card) Details -->
  <div id="graph-modal-overlay">
    <div id="graph-modal">
      <button class="close-modal" title="Close Modal">&times;</button>
      <h2 id="graph-modal-card-title"></h2>
      <div class="modal-card-info" id="graph-modal-card-info"></div>
    </div>
  </div>

  <script>
    const { jsPDF } = window.jspdf;

    // Global State
    let projectTitle = "Game Dev Planner - Final";
    let gddData = {};
    let categoryOrder = [];
    let tagColors = {};
    let isDirty = false;
    let isBionicMode = false;
    let currentView = 'cards';
    let graphInstance = null;
    let lastFocusedEditable = null;
    let isSpeechRecognitionActive = false;
    let recognition = null;
    let draggedItemData = null;
    let dragOverElement = null;

    // For Graph category colors
    const categoryColorMap = {};
    const predefinedCategoryColors = [
      '#e41a1c','#377eb8','#4daf4a','#984ea3','#ff7f00',
      '#ffff33','#a65628','#f781bf','#999999','#66c2a5'
    ];
    let nextCategoryColorIndex = 0;

    // Minimal card defaults (we store pinned, completed, kanbanStatus + schedule fields)
    const cardDefaults = {
      pinned: false,
      completed: false,
      kanbanStatus: "",
      startDate: "",
      endDate: "",
      blockedBy: [] // We'll store an array of card references (like {catKey, index})
    };

    //-----------------------------------------------------
    // DOM references
    //-----------------------------------------------------
    const mainTitleSpan = document.getElementById('main-title');
    const gddContainer = document.getElementById('gdd-container');
    const addCategoryButton = document.getElementById('add-category-button');
    const saveButton = document.getElementById('save-button');
    const loadFileInput = document.getElementById('load-file-input');
    const exportMdButton = document.getElementById('export-md-button');
    const exportPdfButton = document.getElementById('export-pdf-button');
    const searchInput = document.getElementById('search-input');
    const statusMessage = document.getElementById('status-message');
    const saveIndicator = saveButton.querySelector('.save-indicator');
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    const bionicToggleButton = document.getElementById('bionic-toggle-button');
    const speechToggleButton = document.getElementById('speech-toggle-button');
    const speechStatus = document.getElementById('speech-status');
    const viewToggleButton = document.getElementById('view-toggle-button');
    const timelineToggleButton = document.getElementById('timeline-toggle-button');
    const kanbanToggleButton = document.getElementById('kanban-toggle-button');
    const minimapContainer = document.getElementById('minimap');
    const graphContainer = document.getElementById('graph-container');
    const timelineContainer = document.getElementById('timeline-container');
    const mapGrid = document.getElementById('map-grid');
    const kanbanContainer = document.getElementById('kanban-container');
    const graphModalOverlay = document.getElementById('graph-modal-overlay');
    const graphModal = document.getElementById('graph-modal');
    const graphModalCloseBtn = graphModal.querySelector('.close-modal');
    const graphModalCardTitle = document.getElementById('graph-modal-card-title');
    const graphModalCardInfo = document.getElementById('graph-modal-card-info');

    //-----------------------------------------------------
    // Basic utilities
    //-----------------------------------------------------
    function markDirty(d = true) {
      isDirty = d;
      saveIndicator.style.display = isDirty ? 'inline' : 'none';
      if(isDirty) saveToLocalStorage();
    }
    function showStatus(msg, dur=3000){
      statusMessage.textContent=msg;
      setTimeout(()=>{
        if(statusMessage.textContent===msg){
          statusMessage.textContent='';
        }
      },dur);
    }

    //-----------------------------------------------------
    // Local Storage
    //-----------------------------------------------------
    function saveToLocalStorage(){
      if(!isDirty) return;
      try{
        const payload={
          title:projectTitle,
          order:categoryOrder,
          data:gddData,
          timestamp:Date.now(),
          tagColors
        };
        localStorage.setItem('soulboundGDDBackup',JSON.stringify(payload));
      } catch(e){
        console.error("LS save error:", e);
      }
    }
    function loadFromLocalStorage(){
      try{
        const backup=localStorage.getItem('soulboundGDDBackup');
        if(!backup) return null;
        return JSON.parse(backup);
      }catch(e){
        console.error("LS load error:",e);
        return null;
      }
    }

    //-----------------------------------------------------
    // Focus Management
    //-----------------------------------------------------
    document.addEventListener('focusin',(e)=>{
      if(e.target.classList.contains('editable')){
        lastFocusedEditable=e.target;
      }
    });

    //-----------------------------------------------------
    // Editing
    //-----------------------------------------------------
    function makeEditable(e){
      const t=e.target;
      if(t.classList.contains('editable')){
        t.contentEditable='true';
        t.focus();
        document.execCommand('selectAll', false, null);
      }
    }
    function stopEditing(e){
      const t=e.target;
      if(t.classList.contains('editable') && t.isContentEditable){
        t.contentEditable='false';
        const newVal=t.innerText.trim();
        if(t===mainTitleSpan){
          if(projectTitle!==newVal){
            projectTitle=newVal||"Game Dev Planner - Final";
            document.title=projectTitle;
            markDirty(true);
          }
          return;
        }
        const cardInfo=getCardInfo(t);
        const propInfo=getPropertyInfo(t);
        const categoryEl=t.closest('.category');
        if(cardInfo && propInfo){
          const fullValue=getCurrentValueFromDOM(propInfo.propertyContainer);
          updateCardDataValue(cardInfo.categoryKey, cardInfo.cardIndex, propInfo.propertyKey, fullValue);
        } else if(cardInfo && !propInfo && t.classList.contains('editable-title')){
          const {categoryKey,cardIndex}=cardInfo;
          if(gddData[categoryKey][cardIndex].name!==newVal){
            gddData[categoryKey][cardIndex].name=newVal;
            markDirty(true);
          }
        } else if(categoryEl && !cardInfo && t.classList.contains('editable-title')){
          const oldKey=categoryEl.dataset.key;
          if(oldKey!==newVal) renameCategoryKey(oldKey,newVal);
        }
      }
    }

    function getCurrentValueFromDOM(propContainer){
      const spans=propContainer.querySelectorAll('.property-values .editable-value');
      return Array.from(spans).map(s=>s.innerText).join('\n');
    }

    //-----------------------------------------------------
    // GDD Data
    //-----------------------------------------------------
    function updateCardDataValue(catKey,idx,prop,val){
      if(gddData[catKey]?.[idx]){
        gddData[catKey][idx][prop]=val;
        markDirty(true);
      }
    }
    function renameCategoryKey(oldKey,newKey){
      if(!gddData[oldKey]) return false;
      if(!newKey.trim()) return false;
      if(gddData[newKey]){
        alert(`Category "${newKey}" already exists.`);
        return false;
      }
      gddData[newKey]=gddData[oldKey];
      delete gddData[oldKey];
      const i=categoryOrder.indexOf(oldKey);
      if(i>-1) categoryOrder[i]=newKey;
      markDirty(true);
      renderGDD(gddData);
      return true;
    }

    //-----------------------------------------------------
    // Category & Card
    //-----------------------------------------------------
    function addCategory(){
      const name=prompt("New category name?");
      if(!name?.trim()) return;
      const c=name.trim();
      if(gddData[c]){
        alert(`Category "${c}" already exists.`);
        return;
      }
      gddData[c]=[];
      categoryOrder.push(c);
      markDirty(true);
      renderGDD(gddData);
    }
    function deleteCategory(button){
      const catEl=button.closest('.category');
      if(!catEl) return;
      const key=catEl.dataset.key;
      if(confirm(`Delete category "${key}"?`)){
        delete gddData[key];
        const i=categoryOrder.indexOf(key);
        if(i>-1) categoryOrder.splice(i,1);
        markDirty(true);
        catEl.remove();
        renderMinimap();
      }
    }
    function duplicateCategory(button){
      const catEl=button.closest('.category');
      if(!catEl) return;
      const key=catEl.dataset.key;
      let newKey=`${key}_Copy`;
      let i=1;
      while(gddData[newKey]) newKey=`${key}_Copy${++i}`;
      gddData[newKey]=JSON.parse(JSON.stringify(gddData[key]));
      const idx=categoryOrder.indexOf(key);
      categoryOrder.splice(idx+1,0,newKey);
      markDirty(true);
      renderGDD(gddData);
    }

    function addCardToCategory(catKey,catEl){
      const newCard=Object.assign(
        { name:"New Card", description:"" },
        cardDefaults
      );
      gddData[catKey].push(newCard);
      markDirty(true);
      const idx=gddData[catKey].length-1;
      renderCategory(catKey,gddData[catKey]);
      setTimeout(()=>{
        const newCardEl=catEl.querySelector(`.card[data-index="${idx}"]`);
        if(newCardEl) newCardEl.scrollIntoView({behavior:'smooth'});
      },100);
    }
    function deleteCard(catKey,idx){
      const cData=gddData[catKey][idx];
      if(!cData)return;
      if(confirm(`Delete card "${cData.name}"?`)){
        gddData[catKey].splice(idx,1);
        markDirty(true);
        renderCategory(catKey,gddData[catKey]);
      }
    }
    function duplicateCard(button){
      const info=getCardInfo(button);
      if(!info) return;
      const {categoryKey,cardIndex}=info;
      const orig=gddData[categoryKey][cardIndex];
      const copy=JSON.parse(JSON.stringify(orig));
      copy.name+=" (Copy)";
      copy.pinned=false;
      copy.completed=false;
      gddData[categoryKey].splice(cardIndex+1,0,copy);
      markDirty(true);
      renderCategory(categoryKey,gddData[categoryKey]);
    }

    function getCardInfo(el){
      const cardEl=el.closest('.card');
      if(!cardEl) return null;
      const categoryKey=cardEl.dataset.category;
      const cardIndex=parseInt(cardEl.dataset.index,10);
      return { cardElement:cardEl, categoryKey, cardIndex };
    }
    function getPropertyInfo(el){
      const p=el.closest('.property-container');
      if(!p)return null;
      const propertyKey=p.dataset.property;
      return {propertyContainer:p,propertyKey};
    }

    //-----------------------------------------------------
    // Card properties
    //-----------------------------------------------------
    function addCardProperty(button){
      const info=getCardInfo(button);
      if(!info)return;
      const propName=prompt("New property name?");
      if(!propName?.trim())return;
      const {categoryKey,cardIndex}=info;
      gddData[categoryKey][cardIndex][propName.trim()]="...";
      markDirty(true);
      renderCard(categoryKey,cardIndex);
    }
    function removeCardProperty(button){
      const cInfo=getCardInfo(button);
      const pInfo=getPropertyInfo(button);
      if(!cInfo||!pInfo)return;
      const {categoryKey,cardIndex,propertyKey}=Object.assign({},cInfo,pInfo);
      if(propertyKey==='name'){
        alert("Cannot remove 'name'.");
        return;
      }
      if(confirm(`Remove "${propertyKey}"?`)){
        delete gddData[categoryKey][cardIndex][propertyKey];
        markDirty(true);
        renderCard(categoryKey,cardIndex);
      }
    }
    function createPropertyLineElement(lineText,propertyKey){
      const lineDiv=document.createElement('div');
      lineDiv.className='property-line';

      const span=document.createElement('span');
      span.className='editable editable-value';
      span.innerText=lineText;
      span.setAttribute('data-original-html', span.innerHTML);
      span.addEventListener('blur', stopEditing);
      lineDiv.appendChild(span);

      const minusBtn=document.createElement('button');
      minusBtn.innerHTML="&ndash;";
      minusBtn.className='remove-line-button';
      minusBtn.onclick=e=>{
        e.stopPropagation();
        removePropertyLine(e.currentTarget);
      };
      lineDiv.appendChild(minusBtn);
      return lineDiv;
    }
    function addPropertyLine(button){
      const cInfo=getCardInfo(button);
      const pInfo=getPropertyInfo(button);
      if(!cInfo||!pInfo)return;
      const {categoryKey,cardIndex,propertyKey,propertyContainer}=Object.assign({},cInfo,pInfo);
      const linesContainer=propertyContainer.querySelector('.property-values');
      const newLine=createPropertyLineElement("...",propertyKey);
      linesContainer.appendChild(newLine);

      const fullVal=getCurrentValueFromDOM(propertyContainer);
      gddData[categoryKey][cardIndex][propertyKey]=fullVal;
      markDirty(true);
      makeEditable({target:newLine.querySelector('.editable-value')});
    }
    function removePropertyLine(button){
      const cInfo=getCardInfo(button);
      const pInfo=getPropertyInfo(button);
      if(!cInfo||!pInfo)return;
      const {categoryKey,cardIndex,propertyKey,propertyContainer}=Object.assign({},cInfo,pInfo);
      const line=button.closest('.property-line');
      if(line){
        line.remove();
        const newVal=getCurrentValueFromDOM(propertyContainer);
        gddData[categoryKey][cardIndex][propertyKey]=newVal;
        markDirty(true);
      }
    }

    //-----------------------------------------------------
    // Pin & Complete toggles
    //-----------------------------------------------------
    function togglePinCard(button){
      const info=getCardInfo(button);
      if(!info)return;
      const {categoryKey,cardIndex}=info;
      const cData=gddData[categoryKey][cardIndex];
      cData.pinned=!cData.pinned;
      markDirty(true);
      renderCard(categoryKey,cardIndex);
    }
    function toggleCompleteCard(button){
      const info=getCardInfo(button);
      if(!info)return;
      const {categoryKey,cardIndex}=info;
      const cData=gddData[categoryKey][cardIndex];
      cData.completed=!cData.completed;
      markDirty(true);
      renderCard(categoryKey,cardIndex);
    }

    //-----------------------------------------------------
    // Bionic Reading
    //-----------------------------------------------------
    function applyBionicReadingToElement(el){
      if(!el||el.hasAttribute('data-bionic-processed'))return;
      if(!el.hasAttribute('data-original-html')){
        el.setAttribute('data-original-html',el.innerHTML);
      } else{
        el.innerHTML=el.getAttribute('data-original-html');
      }
      const walker=document.createTreeWalker(el,NodeFilter.SHOW_TEXT);
      const toProcess=[];
      let node;
      while((node=walker.nextNode())){
        if(node.parentNode && !node.parentNode.closest('b,.tag,button,strong,[contenteditable="true"]')){
          toProcess.push(node);
        }
      }
      toProcess.forEach((txtNode)=>{
        const text=txtNode.textContent;
        if(!/\S/.test(text))return;
        const words=text.split(/(\s+|[.,!?()]+)/);
        const frag=document.createDocumentFragment();
        words.forEach((word)=>{
          if(/\w/.test(word) && word.length>1){
            const mid=Math.max(1,Math.ceil(word.length*0.45));
            const b=document.createElement('b');
            b.textContent=word.substring(0,mid);
            frag.appendChild(b);
            frag.appendChild(document.createTextNode(word.substring(mid)));
          } else{
            frag.appendChild(document.createTextNode(word));
          }
        });
        if(txtNode.parentNode){
          txtNode.parentNode.replaceChild(frag, txtNode);
        }
      });
      el.setAttribute('data-bionic-processed','true');
    }
    function removeBionicReadingFromElement(el){
      if(!el||!el.hasAttribute('data-bionic-processed'))return;
      if(el.hasAttribute('data-original-html')){
        el.innerHTML=el.getAttribute('data-original-html');
        el.removeAttribute('data-original-html');
      }
      el.removeAttribute('data-bionic-processed');
      el.querySelectorAll('[data-bionic-processed="true"]').forEach(removeBionicReadingFromElement);
    }
    function toggleBionicMode(){
      isBionicMode=!isBionicMode;
      bionicToggleButton.classList.toggle('active', isBionicMode);
      bionicToggleButton.textContent=isBionicMode?"Bionic (On)":"Bionic";
      const els=document.querySelectorAll('.editable-value, .editable-title');
      els.forEach(el=>{
        if(isBionicMode) applyBionicReadingToElement(el);
        else removeBionicReadingFromElement(el);
      });
    }

    //-----------------------------------------------------
    // Sorting & Searching
    //-----------------------------------------------------
    function sortCategory(button){
      const catEl=button.closest('.category');
      if(!catEl)return;
      const key=catEl.dataset.key;
      gddData[key].sort((a,b)=>{
        if(a.pinned && !b.pinned)return -1;
        if(b.pinned && !a.pinned)return 1;
        return (a.name||"").localeCompare(b.name||"");
      });
      markDirty(true);
      renderCategory(key,gddData[key]);
    }
    function filterCards(){
      const term=searchInput.value.toLowerCase().trim();
      categoryOrder.forEach(catKey=>{
        const catEl=document.getElementById(`category-${catKey.replace(/[^a-zA-Z0-9]/g,'-')}`);
        if(!catEl)return;
        let catHasMatch=catKey.toLowerCase().includes(term);

        gddData[catKey].forEach((card,idx)=>{
          const cardEl=catEl.querySelector(`.card[data-index="${idx}"]`);
          if(!cardEl)return;
          let matched=false;
          for(const k in card){
            // if blockedBy is an array
            if(k==='blockedBy' && Array.isArray(card[k])){
              const arr=card[k].map(x=> x.catKey+'|'+x.index).join(' ').toLowerCase();
              if(arr.includes(term)) matched=true;
            } else if(typeof card[k]==='string' && card[k].toLowerCase().includes(term)){
              matched=true;
            }
          }
          cardEl.classList.toggle('filtered-out',!matched);
          if(matched) catHasMatch=true;
        });
        catEl.classList.toggle('filtered-out',(!catHasMatch&&term!==''));
      });
    }

    //-----------------------------------------------------
    // DRAG & DROP - see below for code
    //-----------------------------------------------------
    // (All handled above)

    //-----------------------------------------------------
    // Rendering
    //-----------------------------------------------------
    function createCardElement(cData, catKey, idx){
      if(!categoryColorMap[catKey]){
        categoryColorMap[catKey]=predefinedCategoryColors[nextCategoryColorIndex++ % predefinedCategoryColors.length];
      }
      const cardDiv=document.createElement('div');
      cardDiv.className='card';
      cardDiv.dataset.category=catKey;
      cardDiv.dataset.index=idx;
      cardDiv.draggable=true;
      if(cData.pinned) cardDiv.classList.add('pinned');
      if(cData.completed) cardDiv.classList.add('completed');

      // Check if blocked by incomplete tasks
      if(Array.isArray(cData.blockedBy) && cData.blockedBy.some(ref=> {
        const bc=gddData[ref.catKey]?.[ref.index];
        return bc && !bc.completed;
      })){
        cardDiv.classList.add('blocked');
      }
      // Check if endDate < today => overdue
      if(cData.endDate){
        const dt=Date.parse(cData.endDate);
        if(!isNaN(dt)){
          const now=Date.now();
          if(dt<now && !cData.completed){
            cardDiv.classList.add('overdue');
          }
        }
      }

      // header
      const header=document.createElement('div');
      header.className='card-header';

      const titleH3=document.createElement('h3');
      const titleSpan=document.createElement('span');
      titleSpan.className='editable editable-title';
      titleSpan.textContent=cData.name||"Unnamed";
      titleSpan.addEventListener('blur',stopEditing);
      titleH3.appendChild(titleSpan);
      header.appendChild(titleH3);

      const actions=document.createElement('div');
      actions.className='card-actions';

      const completeBtn=document.createElement('button');
      completeBtn.className='complete-card-button';
      completeBtn.innerHTML=cData.completed?"✅":"⬜️";
      completeBtn.onclick=e=>{
        e.stopPropagation();
        toggleCompleteCard(completeBtn);
      };
      actions.appendChild(completeBtn);

      const pinBtn=document.createElement('button');
      pinBtn.className='pin-card-button'+(cData.pinned?' active':'');
      pinBtn.innerHTML='📌';
      pinBtn.onclick=e=>{
        e.stopPropagation();
        togglePinCard(pinBtn);
      };
      actions.appendChild(pinBtn);

      const dupBtn=document.createElement('button');
      dupBtn.className='duplicate-card-button';
      dupBtn.innerHTML='&#128441;';
      dupBtn.title='Duplicate Card';
      dupBtn.onclick=e=>{
        e.stopPropagation();
        duplicateCard(dupBtn);
      };
      actions.appendChild(dupBtn);

      const delBtn=document.createElement('button');
      delBtn.className='delete-card-button';
      delBtn.innerHTML='&times;';
      delBtn.onclick=e=>{
        e.stopPropagation();
        deleteCard(catKey,idx);
      };
      actions.appendChild(delBtn);

      header.appendChild(actions);
      cardDiv.appendChild(header);

      // normal fields except pinned, completed, kanbanStatus, startDate, endDate, blockedBy
      const skip=["name","pinned","completed","kanbanStatus","startDate","endDate","blockedBy"];
      Object.keys(cData).forEach(k=>{
        if(skip.includes(k))return;
        cardDiv.appendChild(renderTextProperty(catKey,idx,k,cData[k]));
      });

      const addPropBtn=document.createElement('button');
      addPropBtn.className='add-property-button';
      addPropBtn.textContent='+ Add Property';
      addPropBtn.onclick=e=>{
        e.stopPropagation();
        addCardProperty(addPropBtn);
      };
      cardDiv.appendChild(addPropBtn);

      cardDiv.addEventListener('dblclick',makeEditable);
      return cardDiv;
    }
    function renderTextProperty(catKey, idx, propKey, propVal){
      const container=document.createElement('div');
      container.className='property-container';
      container.dataset.property=propKey;

      const strong=document.createElement('strong');
      const labelSpan=document.createElement('span');
      labelSpan.textContent=propKey.charAt(0).toUpperCase()+propKey.slice(1)+": ";
      labelSpan.className='editable editable-label';
      labelSpan.addEventListener('blur',stopEditing);
      strong.appendChild(labelSpan);

      const rmBtn=document.createElement('button');
      rmBtn.innerHTML='&#8722;';
      rmBtn.className='remove-property-button';
      rmBtn.onclick=e=>{
        e.stopPropagation();
        removeCardProperty(rmBtn);
      };
      strong.appendChild(rmBtn);

      container.appendChild(strong);

      const valuesDiv=document.createElement('div');
      valuesDiv.className='property-values';
      const lines=(propVal||"").toString().split('\n');
      lines.forEach(line=>{
        valuesDiv.appendChild(createPropertyLineElement(line, propKey));
      });
      container.appendChild(valuesDiv);

      const addLineBtn=document.createElement('button');
      addLineBtn.innerHTML='&#43;';
      addLineBtn.className='add-line-button';
      addLineBtn.onclick=e=>{
        e.stopPropagation();
        addPropertyLine(addLineBtn);
      };
      container.appendChild(addLineBtn);

      return container;
    }
    function renderCategory(catKey,dataArr){
      if(!categoryColorMap[catKey]){
        categoryColorMap[catKey]=predefinedCategoryColors[nextCategoryColorIndex++ % predefinedCategoryColors.length];
      }
      const catId=`category-${catKey.replace(/[^a-zA-Z0-9]/g,'-')}`;
      let catEl=document.getElementById(catId);
      if(!catEl){
        catEl=document.createElement('div');
        catEl.className='category';
        catEl.id=catId;
        catEl.dataset.key=catKey;
        catEl.draggable=true;

        const catHeader=document.createElement('div');
        catHeader.className='category-header';

        const h2=document.createElement('h2');
        const titleSpan=document.createElement('span');
        titleSpan.className='editable editable-title';
        titleSpan.textContent=catKey;
        titleSpan.addEventListener('blur',stopEditing);
        titleSpan.onclick=e=>{
          e.stopPropagation();
          catEl.classList.toggle('collapsed');
        };
        titleSpan.addEventListener('dblclick',makeEditable);
        h2.appendChild(titleSpan);
        catHeader.appendChild(h2);

        const catActions=document.createElement('div');
        catActions.className='category-actions';

        const sortBtn=document.createElement('button');
        sortBtn.innerHTML='↕️';
        sortBtn.title='Sort Cards';
        sortBtn.className='sort-category-button';
        sortBtn.onclick=e=>{
          e.stopPropagation();
          sortCategory(sortBtn);
        };
        catActions.appendChild(sortBtn);

        const dupCatBtn=document.createElement('button');
        dupCatBtn.innerHTML='&#128441;';
        dupCatBtn.className='duplicate-category-button';
        dupCatBtn.onclick=e=>{
          e.stopPropagation();
          duplicateCategory(dupCatBtn);
        };
        catActions.appendChild(dupCatBtn);

        const delCatBtn=document.createElement('button');
        delCatBtn.innerHTML='&times;';
        delCatBtn.className='delete-category-button';
        delCatBtn.onclick=e=>{
          e.stopPropagation();
          deleteCategory(delCatBtn);
        };
        catActions.appendChild(delCatBtn);

        catHeader.appendChild(catActions);
        catEl.appendChild(catHeader);

        const cardsContainer=document.createElement('div');
        cardsContainer.className='cards-container';
        catEl.appendChild(cardsContainer);

        const addCardBtn=document.createElement('button');
        addCardBtn.className='add-card-button';
        addCardBtn.textContent='+ Add Card';
        addCardBtn.onclick=()=>addCardToCategory(catKey,catEl);
        catEl.appendChild(addCardBtn);

        gddContainer.appendChild(catEl);
      }

      const cardsContainer=catEl.querySelector('.cards-container');
      catEl.dataset.key=catKey;
      cardsContainer.innerHTML='';
      dataArr.forEach((card,i)=>{
        cardsContainer.appendChild(createCardElement(card,catKey,i));
      });
      return catEl;
    }
    function renderCard(catKey,idx){
      const oldEl=document.querySelector(`.card[data-category="${catKey}"][data-index="${idx}"]`);
      if(!oldEl){
        renderCategory(catKey,gddData[catKey]);
      } else{
        const cData=gddData[catKey][idx];
        const newEl=createCardElement(cData,catKey,idx);
        oldEl.parentNode.replaceChild(newEl,oldEl);
      }
    }
    function renderGDD(data){
      gddContainer.innerHTML='';
      categoryOrder.forEach(cat=>{
        if(!data[cat]) return;
        data[cat].forEach(c=>{
          // attach any missing fields
          Object.keys(cardDefaults).forEach(k=>{
            if(c[k]===undefined) c[k]=cardDefaults[k];
          });
          if(!Array.isArray(c.blockedBy)) c.blockedBy=[];
        });
        renderCategory(cat, data[cat]);
      });
      mainTitleSpan.textContent=projectTitle;
      filterCards();
      renderMinimap();
      markDirty(false);
    }

    //-----------------------------------------------------
    // Minimap
    //-----------------------------------------------------
    function renderMinimap(){
      minimapContainer.innerHTML='';
      categoryOrder.forEach(cat=>{
        const catId=`category-${cat.replace(/[^a-zA-Z0-9]/g,'-')}`;
        const catEl=document.getElementById(catId);
        if(catEl){
          const item=document.createElement('div');
          item.className='minimap-item';
          item.textContent=cat.substring(0,10)+(cat.length>10?'...':'');
          item.title=cat;
          item.onclick=()=>catEl.scrollIntoView({behavior:'smooth',block:'nearest',inline:'start'});
          minimapContainer.appendChild(item);
        }
      });
    }

    //-----------------------------------------------------
    // Save/Load
    //-----------------------------------------------------
    function saveProject(){
      try{
        const obj={
          title:projectTitle,
          order:categoryOrder,
          data:gddData,
          timestamp:Date.now(),
          tagColors
        };
        const js=JSON.stringify(obj,null,2);
        const blob=new Blob([js],{type:'application/json'});
        const url=URL.createObjectURL(blob);
        const a=document.createElement('a');
        a.href=url;
        a.download=`${projectTitle.replace(/[^a-z0-9 _-]/gi,'_')||'gdd'}_extended.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        markDirty(false);
        showStatus("Project Saved!");
      }catch(e){
        console.error("Save Error:",e);
        showStatus("Save failed!",5000);
      }
    }
    function loadProject(e){
      const f=e.target.files[0];
      if(!f)return;
      if(isDirty && !confirm("Unsaved changes exist. Load anyway?")){
        e.target.value=null;
        return;
      }
      const r=new FileReader();
      r.onload=function(evt){
        try{
          const data=JSON.parse(evt.target.result);
          if(typeof data==='object' && data!==null){
            projectTitle=data.title||"Game Dev Planner - Final";
            gddData=data.data||{};
            categoryOrder=data.order||Object.keys(gddData);
            tagColors=data.tagColors||{};
            renderGDD(gddData);
            localStorage.removeItem('soulboundGDDBackup');
            showStatus("Project Loaded!");
            markDirty(false);
          }
        }catch(e){
          console.error("Load error:",e);
          showStatus(`Load failed: ${e.message}`,5000);
        }finally{
          e.target.value=null;
        }
      };
      r.readAsText(f);
    }

    //-----------------------------------------------------
    // Export
    //-----------------------------------------------------
    function exportToMarkdown(){
      let md=`# ${projectTitle}\n\n`;
      categoryOrder.forEach(catKey=>{
        md+=`## ${catKey}\n\n`;
        (gddData[catKey]||[]).forEach(card=>{
          md+=`### ${card.name||"Unnamed"}\n\n`;
          if(card.pinned) md+=`- **Pinned**: true\n`;
          if(card.completed) md+=`- **Completed**: true\n`;
          Object.keys(card).forEach(k=>{
            if(["name","pinned","completed","kanbanStatus"].includes(k))return;
            if(k==="blockedBy" && Array.isArray(card.blockedBy)){
              const str=card.blockedBy.map(ref=> ref.catKey+"|"+ref.index).join(", ");
              md+=`- **blockedBy**: ${str}\n`;
            } else{
              md+=`- **${k}**: ${card[k]}\n`;
            }
          });
          md+='\n';
        });
        md+='\n';
      });
      const blob=new Blob([md],{type:'text/markdown'});
      const url=URL.createObjectURL(blob);
      const a=document.createElement('a');
      a.href=url;
      a.download=`${projectTitle.replace(/[^a-z0-9 _-]/gi,'_')||'gdd'}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      showStatus("Exported to Markdown!");
    }
    function exportToPDF(){
      const doc=new jsPDF({ orientation:'p', unit:'pt', format:'letter' });
      let xPos=40,yPos=60;
      doc.setFontSize(16);
      doc.text(projectTitle,xPos,yPos);
      yPos+=20;
      doc.setFontSize(10);

      categoryOrder.forEach(catKey=>{
        const catData=gddData[catKey]||[];
        if(!catData.length)return;
        doc.setFont(undefined,'bold');
        doc.text(`Category: ${catKey}`,xPos,yPos);
        yPos+=16;
        doc.setFont(undefined,'normal');
        catData.forEach(card=>{
          doc.text(card.name||"Unnamed", xPos+10,yPos);
          yPos+=14;
          if(card.pinned){
            doc.text("- Pinned: true",xPos+20,yPos);
            yPos+=12;
          }
          if(card.completed){
            doc.text("- Completed: true",xPos+20,yPos);
            yPos+=12;
          }
          Object.keys(card).forEach(k=>{
            if(["name","pinned","completed","kanbanStatus"].includes(k))return;
            const val=(k==="blockedBy" && Array.isArray(card.blockedBy))
              ? card.blockedBy.map(ref=> ref.catKey+"|"+ref.index).join(", ")
              : card[k]||"";
            const lines=doc.splitTextToSize(`- ${k}: ${val}`,480);
            lines.forEach(ln=>{
              if(yPos>700){
                doc.addPage();
                yPos=60;
              }
              doc.text(ln,xPos+20,yPos);
              yPos+=12;
            });
          });
          yPos+=8;
          if(yPos>700){
            doc.addPage();
            yPos=60;
          }
        });
        yPos+=10;
        if(yPos>700){
          doc.addPage();
          yPos=60;
        }
      });

      doc.save(`${projectTitle.replace(/[^a-z0-9 _-]/gi,'_')||'gdd_export'}.pdf`);
      showStatus("Exported to PDF!");
    }

    //-----------------------------------------------------
    // Dark mode
    //-----------------------------------------------------
    function toggleDarkMode(){
      document.body.classList.toggle('dark-mode');
      localStorage.setItem('darkModeEnabled', document.body.classList.contains('dark-mode'));
      darkModeToggle.textContent=document.body.classList.contains('dark-mode')?'☀️':'🌙';
      renderGDD(gddData);
    }

    //-----------------------------------------------------
    // Speech
    //-----------------------------------------------------
    const SpeechRecognition=window.SpeechRecognition||window.webkitSpeechRecognition;
    if(SpeechRecognition){
      recognition=new SpeechRecognition();
      recognition.continuous=true;
      recognition.lang='en-US';
      recognition.interimResults=true;
      recognition.onresult=(e)=>{
        let finalTrans='', interimTrans='';
        for(let i=e.resultIndex; i<e.results.length; i++){
          const part=e.results[i][0].transcript;
          if(e.results[i].isFinal) finalTrans+=part;
          else interimTrans+=part;
        }
        if(interimTrans) speechStatus.textContent=interimTrans;
        if(finalTrans){
          speechStatus.textContent='';
          let txt=finalTrans.trim();
          const words=txt.split(/\s+/);
          if(words.length>0){
            words[0]=words[0].charAt(0).toUpperCase()+words[0].slice(1);
          }
          txt=words.join(' ');
          if(words.length>=3 && !/[.!?]$/.test(txt)){
            txt+='.';
          }
          if(lastFocusedEditable && document.body.contains(lastFocusedEditable)){
            lastFocusedEditable.focus();
            document.execCommand('insertText',false," "+txt);
            lastFocusedEditable.blur();
          } else{
            showStatus("No field focused:\n"+txt,5000);
          }
        }
      };
      recognition.onerror=(ev)=>{
        speechStatus.textContent="Error: "+ev.error;
        stopSpeechRecognition();
      };
      recognition.onend=()=>{
        if(isSpeechRecognitionActive){
          try{ recognition.start(); }catch(e){ stopSpeechRecognition(); }
        }
      };
      speechToggleButton.onclick=()=>{
        if(!isSpeechRecognitionActive) startSpeechRecognition();
        else stopSpeechRecognition(true);
      };
    } else{
      speechToggleButton.disabled=true;
      speechToggleButton.title="Speech not supported.";
      speechStatus.textContent='Not supported.';
    }
    function startSpeechRecognition(){
      if(isSpeechRecognitionActive)return;
      if(!lastFocusedEditable){
        showStatus("Click a field first for speech input.");
        return;
      }
      isSpeechRecognitionActive=true;
      speechToggleButton.classList.add('listening');
      speechToggleButton.textContent='Stop Voice 🛑';
      speechStatus.textContent='Listening...';
      recognition.start();
    }
    function stopSpeechRecognition(){
      if(!isSpeechRecognitionActive)return;
      isSpeechRecognitionActive=false;
      try{recognition.abort();}catch(e){}
      speechToggleButton.classList.remove('listening');
      speechToggleButton.textContent='Voice Mode 🎤';
      speechStatus.textContent='';
    }

    //-----------------------------------------------------
    // Graph / Timeline / Kanban toggles
    //-----------------------------------------------------
    function toggleGraphView(){
      if(currentView==='graph'){
        currentView='cards';
        graphContainer.style.display='none';
        timelineContainer.style.display='none';
        kanbanContainer.style.display='none';
        document.getElementById('gdd-container-wrapper').style.display='flex';
        minimapContainer.style.display='flex';
        if(graphInstance) graphInstance.pauseAnimation();
        viewToggleButton.textContent='Graph View';
      } else{
        currentView='graph';
        document.getElementById('gdd-container-wrapper').style.display='none';
        minimapContainer.style.display='none';
        timelineContainer.style.display='none';
        kanbanContainer.style.display='none';
        graphContainer.style.display='block';
        viewToggleButton.textContent='Card View';
        renderGraph();
      }
    }
    function toggleTimelineView(){
      if(currentView==='timeline'){
        currentView='cards';
        timelineContainer.style.display='none';
        kanbanContainer.style.display='none';
        graphContainer.style.display='none';
        document.getElementById('gdd-container-wrapper').style.display='flex';
        minimapContainer.style.display='flex';
      } else{
        currentView='timeline';
        document.getElementById('gdd-container-wrapper').style.display='none';
        minimapContainer.style.display='none';
        graphContainer.style.display='none';
        kanbanContainer.style.display='none';
        timelineContainer.style.display='block';
        render2DMap();
      }
    }
    function toggleKanbanView(){
      if(currentView==='kanban'){
        currentView='cards';
        kanbanContainer.style.display='none';
        graphContainer.style.display='none';
        timelineContainer.style.display='none';
        document.getElementById('gdd-container-wrapper').style.display='flex';
        minimapContainer.style.display='flex';
      } else{
        currentView='kanban';
        document.getElementById('gdd-container-wrapper').style.display='none';
        minimapContainer.style.display='none';
        graphContainer.style.display='none';
        timelineContainer.style.display='none';
        kanbanContainer.style.display='block';
        renderKanban();
      }
    }

    //-----------------------------------------------------
    // 2D map for “timeline” using startDate
    //-----------------------------------------------------
    function render2DMap(){
      mapGrid.innerHTML='';
      // We define a 12(rows for months) x 31(cols for days) grid
      const totalRows=12, totalCols=31;
      for(let r=0; r<totalRows; r++){
        for(let c=0; c<totalCols; c++){
          const cell=document.createElement('div');
          cell.className='map-cell';
          mapGrid.appendChild(cell);
        }
      }

      // place cards that have a valid startDate => row=month-1, col=day-1
      categoryOrder.forEach(catKey=>{
        (gddData[catKey]||[]).forEach((card,idx)=>{
          if(card.startDate){
            const dt=Date.parse(card.startDate);
            if(!isNaN(dt)){
              const dObj=new Date(dt);
              const month=dObj.getMonth(); // 0-based
              const day=dObj.getDate();    // 1-based
              if(month>=0 && month<12 && day>=1 && day<=31){
                const row=month, col=day-1;
                const index=row*totalCols + col;
                const cell=mapGrid.children[index];
                if(cell){
                  const p=document.createElement('p');
                  p.style.margin='2px 0';
                  p.textContent=`[${catKey}] ${card.name}`;
                  cell.appendChild(p);
                }
              }
            }
          }
        });
      });
    }

    //-----------------------------------------------------
    // Kanban
    //-----------------------------------------------------
    function renderKanban(){
      kanbanContainer.innerHTML='';
      const columns=["","Doing","Done"];
      const colNames=["To Do","Doing","Done"];

      columns.forEach((colVal,i)=>{
        const colDiv=document.createElement('div');
        colDiv.className='kanban-column';
        colDiv.dataset.kanban=colVal;

        const h3=document.createElement('h3');
        h3.textContent=colNames[i];
        colDiv.appendChild(h3);

        categoryOrder.forEach(cat=>{
          (gddData[cat]||[]).forEach((card,idx)=>{
            const st=card.kanbanStatus||"";
            if((st==="" && colVal==="") || st===colVal){
              const cardEl=createKanbanCardElement(card,cat,idx);
              colDiv.appendChild(cardEl);
            }
          });
        });
        kanbanContainer.appendChild(colDiv);
      });
    }

    function createKanbanCardElement(cData, catKey, idx){
      const cardDiv=document.createElement('div');
      cardDiv.className='card';
      cardDiv.dataset.category=catKey;
      cardDiv.dataset.index=idx;
      cardDiv.draggable=true;

      // pinned, completed
      if(cData.pinned) cardDiv.classList.add('pinned');
      if(cData.completed) cardDiv.classList.add('completed');

      // Check blockers => highlight if blocked
      if(Array.isArray(cData.blockedBy) && cData.blockedBy.some(ref=>{
        const bc=gddData[ref.catKey]?.[ref.index];
        return bc && !bc.completed;
      })){
        cardDiv.classList.add('blocked');
      }
      // Check overdue
      if(cData.endDate){
        const dt=Date.parse(cData.endDate);
        if(!isNaN(dt)){
          if(dt<Date.now() && !cData.completed){
            cardDiv.classList.add('overdue');
          }
        }
      }

      // header
      const header=document.createElement('div');
      header.className='card-header';

      const titleH3=document.createElement('h3');
      const titleSpan=document.createElement('span');
      titleSpan.className='editable editable-title';
      titleSpan.textContent=cData.name||"Unnamed";
      titleSpan.addEventListener('blur',stopEditing);
      titleH3.appendChild(titleSpan);
      header.appendChild(titleH3);

      const actions=document.createElement('div');
      actions.className='card-actions';

      const completeBtn=document.createElement('button');
      completeBtn.className='complete-card-button';
      completeBtn.innerHTML=cData.completed?"✅":"⬜️";
      completeBtn.onclick=e=>{
        e.stopPropagation();
        toggleCompleteCard(completeBtn);
      };
      actions.appendChild(completeBtn);

      const pinBtn=document.createElement('button');
      pinBtn.className='pin-card-button'+(cData.pinned?' active':'');
      pinBtn.innerHTML='📌';
      pinBtn.onclick=e=>{
        e.stopPropagation();
        togglePinCard(pinBtn);
      };
      actions.appendChild(pinBtn);

      // schedule button
      const scheduleBtn=document.createElement('button');
      scheduleBtn.textContent='Schedule';
      scheduleBtn.style.fontSize='0.85em';
      scheduleBtn.style.backgroundColor='var(--button-info-bg)';
      scheduleBtn.style.color='white';
      scheduleBtn.onclick=e=>{
        e.stopPropagation();
        openScheduleForm(scheduleForm);
      };
      actions.appendChild(scheduleBtn);

      // delete
      const delBtn=document.createElement('button');
      delBtn.className='delete-card-button';
      delBtn.innerHTML='&times;';
      delBtn.onclick=e=>{
        e.stopPropagation();
        deleteCard(catKey,idx);
      };
      actions.appendChild(delBtn);

      header.appendChild(actions);
      cardDiv.appendChild(header);

      // maybe show card desc
      if(cData.description){
        const p=document.createElement('p');
        p.style.margin='4px 0';
        p.textContent=cData.description;
        cardDiv.appendChild(p);
      }

      // schedule form
      const scheduleForm=document.createElement('div');
      scheduleForm.className='schedule-form';

      const closeFormBtn=document.createElement('button');
      closeFormBtn.textContent='×';
      closeFormBtn.className='close-form';
      closeFormBtn.onclick=()=>{ scheduleForm.style.display='none'; };
      scheduleForm.appendChild(closeFormBtn);

      // start date
      const labelSd=document.createElement('label');
      labelSd.textContent='Start Date:';
      scheduleForm.appendChild(labelSd);
      const sdInput=document.createElement('input');
      sdInput.type='date';
      sdInput.value=cData.startDate||'';
      scheduleForm.appendChild(sdInput);

      // end date
      const labelEd=document.createElement('label');
      labelEd.textContent='End Date:';
      scheduleForm.appendChild(labelEd);
      const edInput=document.createElement('input');
      edInput.type='date';
      edInput.value=cData.endDate||'';
      scheduleForm.appendChild(edInput);

      // blockedBy multi-select
      const labelBlocked=document.createElement('label');
      labelBlocked.textContent='Blocked By:';
      scheduleForm.appendChild(labelBlocked);

      const blockedSelect=document.createElement('select');
      blockedSelect.multiple=true;
      // populate with all other cards
      const allOptions=[];
      categoryOrder.forEach((ck)=>{
        gddData[ck].forEach((cd,cdIdx)=>{
          if(ck===catKey && cdIdx===idx) return; // skip itself
          const opt=document.createElement('option');
          opt.value=ck+"|"+cdIdx;
          opt.textContent=`[${ck}] ${cd.name||"Unnamed"}`;
          // if cData.blockedBy includes {catKey:ck,index:cdIdx}, select it
          if(Array.isArray(cData.blockedBy) && cData.blockedBy.some(ref=>(ref.catKey===ck && ref.index===cdIdx))){
            opt.selected=true;
          }
          blockedSelect.appendChild(opt);
        });
      });
      scheduleForm.appendChild(blockedSelect);

      // save button
      const saveSchBtn=document.createElement('button');
      saveSchBtn.className='save-schedule-btn';
      saveSchBtn.textContent='Save';
      saveSchBtn.onclick=(ev)=>{
        ev.stopPropagation();
        cData.startDate=sdInput.value||"";
        cData.endDate=edInput.value||"";
        // parse multi-selected blockers
        const newBlockers=[];
        for(let i=0;i<blockedSelect.options.length;i++){
          const opt=blockedSelect.options[i];
          if(opt.selected){
            const [bCat,bIdx]=opt.value.split('|');
            newBlockers.push({catKey:bCat,index:parseInt(bIdx,10)});
          }
        }
        cData.blockedBy=newBlockers;
        markDirty(true);
        scheduleForm.style.display='none';
        renderKanban(); // to update highlights
      };
      scheduleForm.appendChild(saveSchBtn);

      cardDiv.appendChild(scheduleForm);

      cardDiv.addEventListener('dblclick',makeEditable);
      return cardDiv;
    }
    function openScheduleForm(form){
      form.style.display=(form.style.display==='block'?'none':'block');
    }

    //-----------------------------------------------------
    // Graph
    //-----------------------------------------------------
    function renderGraph(){
      graphContainer.innerHTML='';
      const data=prepareGraphData();
      if(!data.nodes.length){
        graphContainer.innerHTML='<p style="text-align:center; margin-top:20px;">No data for graph.</p>';
        return;
      }
      try{
        graphInstance=ForceGraph();
        graphInstance(graphContainer)
          .graphData(data)
          .nodeId('id')
          .nodeLabel(node=> node.name.length>25 ? node.name.slice(0,25)+"…" : node.name)
          .nodeVal('val')
          .nodeColor('color')
          .linkDirectionalParticles(1)
          .linkDirectionalParticleWidth(1)
          .linkDirectionalArrowLength(4)
          .linkDirectionalArrowRelPos(1)
          .linkWidth(1)
          .linkColor(link=> link.isBlocker?"rgba(255,0,0,0.7)":"rgba(80,80,80,0.6)")
          .onNodeClick(node=>{
            if(node.isCategory){
              showStatus(`Category: ${node.name}`,3000);
            } else{
              openGraphCardModal(node);
            }
          })
          .nodeCanvasObjectMode(()=> 'replace')
          .nodeCanvasObject((node, ctx, globalScale)=>{
            const label=node.name;
            const fontSize=node.isCategory?12:9;
            const scaledSize=fontSize/globalScale;
            ctx.font=`${scaledSize}px Sans-Serif`;
            ctx.textAlign='center';
            ctx.textBaseline='middle';
            ctx.lineWidth=4/globalScale;
            ctx.strokeStyle='rgba(0,0,0,0.5)';
            ctx.fillStyle='white';
            ctx.strokeText(label,node.x,node.y);
            ctx.fillText(label,node.x,node.y);
          })
          .width(graphContainer.clientWidth)
          .height(graphContainer.clientHeight);
      }catch(e){
        console.error("Graph error:", e);
        graphContainer.innerHTML='<p style="color:red;">Error rendering graph</p>';
      }
    }
    function prepareGraphData(){
      const nodes=[];
      const links=[];
      // categories
      categoryOrder.forEach(cat=>{
        const catColor=categoryColorMap[cat]||'#777';
        const catId=`cat_${cat}`;
        nodes.push({
          id:catId,
          name:cat,
          isCategory:true,
          val:15,
          color:catColor
        });
        (gddData[cat]||[]).forEach((card,i)=>{
          const cardId=`${cat}_${i}`;
          nodes.push({
            id:cardId,
            name:card.name||"Unnamed",
            isCategory:false,
            category:cat,
            cardIndex:i,
            val: card.pinned?8:5,
            color: card.completed?'rgba(0,255,0,0.7)':catColor
          });
          links.push({source:catId,target:cardId,isBlocker:false});
        });
      });
      // blockedBy => edges
      categoryOrder.forEach(cat=>{
        (gddData[cat]||[]).forEach((card,idx)=>{
          if(Array.isArray(card.blockedBy)){
            card.blockedBy.forEach(ref=>{
              if(gddData[ref.catKey] && gddData[ref.catKey][ref.index]){
                links.push({
                  source:`${ref.catKey}_${ref.index}`,
                  target:`${cat}_${idx}`,
                  isBlocker:true
                });
              }
            });
          }
        });
      });
      return {nodes,links};
    }
    function openGraphCardModal(node){
      graphModalOverlay.style.display='flex';
      graphModalCardTitle.textContent=node.name;
      graphModalCardInfo.innerHTML='';

      const cat=node.category;
      const idx=node.cardIndex;
      if(!cat||idx===undefined){
        // category node
        const p=document.createElement('p');
        p.textContent="Category node: "+node.name;
        graphModalCardInfo.appendChild(p);
        return;
      }
      const cData=gddData[cat][idx];
      const cardEl=createCardElement(cData,cat,idx);
      graphModalCardInfo.appendChild(cardEl);
    }
    graphModalCloseBtn.onclick=()=>{
      graphModalOverlay.style.display='none';
    };

    //-----------------------------------------------------
    // 2D map timeline
    //-----------------------------------------------------
    function render2DMap(){
      mapGrid.innerHTML='';
      // 12 rows for months, 31 columns for days
      const totalRows=12, totalCols=31;
      for(let r=0;r<totalRows;r++){
        for(let c=0;c<totalCols;c++){
          const cell=document.createElement('div');
          cell.className='map-cell';
          mapGrid.appendChild(cell);
        }
      }
      // place cards by startDate => row=month-1, col=day-1
      categoryOrder.forEach(cat=>{
        (gddData[cat]||[]).forEach((card,idx)=>{
          if(card.startDate){
            const dt=Date.parse(card.startDate);
            if(!isNaN(dt)){
              const dObj=new Date(dt);
              const month=dObj.getMonth();
              const day=dObj.getDate();
              if(month>=0 && month<12 && day>=1 && day<=31){
                const row=month, col=day-1;
                const index=row*totalCols + col;
                const cell=mapGrid.children[index];
                if(cell){
                  const p=document.createElement('p');
                  p.style.margin='2px 0';
                  p.textContent=`[${cat}] ${card.name}`;
                  cell.appendChild(p);
                }
              }
            }
          }
        });
      });
    }

    //-----------------------------------------------------
    // Initialization
    //-----------------------------------------------------
    darkModeToggle.onclick=toggleDarkMode;
    bionicToggleButton.onclick=toggleBionicMode;
    addCategoryButton.onclick=addCategory;
    saveButton.onclick=saveProject;
    loadFileInput.onchange=loadProject;
    exportMdButton.onclick=exportToMarkdown;
    exportPdfButton.onclick=exportToPDF;
    searchInput.oninput=filterCards;
    viewToggleButton.onclick=toggleGraphView;
    timelineToggleButton.onclick=toggleTimelineView;
    kanbanToggleButton.onclick=toggleKanbanView;

    window.addEventListener('beforeunload',e=>{
      if(isDirty){
        e.preventDefault();
        e.returnValue='';
      }
    });

    document.addEventListener('DOMContentLoaded',()=>{
      mainTitleSpan.addEventListener('dblclick',makeEditable);
      mainTitleSpan.addEventListener('blur',stopEditing);

      if(localStorage.getItem('darkModeEnabled')==='true'){
        document.body.classList.add('dark-mode');
        darkModeToggle.textContent='☀️';
      } else{
        darkModeToggle.textContent='🌙';
      }
      const backup=loadFromLocalStorage();
      if(backup && confirm(`Unsaved changes found from previous session (at ${new Date(backup.timestamp).toLocaleString()}). Restore?`)){
        projectTitle=backup.title||"Game Dev Planner - Final";
        gddData=backup.data||{};
        categoryOrder=backup.order||Object.keys(gddData);
        tagColors=backup.tagColors||{};
        renderGDD(gddData);
        markDirty(true);
      } else{
        if(!categoryOrder.length||!Object.keys(gddData).length){
          projectTitle="Game Dev Planner - Final";
          // an example
          gddData={
            "Example Category":[
              {
                name:"Sample Card",
                description:"Use 'Schedule' in Kanban to set dates / blockers.\nCheck Graph for dependency lines.",
                pinned:false,
                completed:false,
                kanbanStatus:"",
                startDate:"",
                endDate:"",
                blockedBy:[]
              }
            ]
          };
          categoryOrder=["Example Category"];
        }
        renderGDD(gddData);
      }
    });
  </script>
</body>
</html>
