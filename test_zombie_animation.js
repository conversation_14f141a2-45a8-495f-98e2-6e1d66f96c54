/**
 * Test script to verify the zombie animation handler fix
 * 
 * This script spawns a zombie enemy and verifies that its animation handler
 * is properly initialized and working.
 */

function testZombieAnimation() {
    console.log('=== TESTING ZOMBIE ANIMATION ===');
    
    // Get the DungeonHandler instance
    const dungeonHandler = window.gameInstance.dungeonHandler;
    
    if (!dungeonHandler) {
        console.error('DungeonHandler not found!');
        return false;
    }
    
    // Get player position
    const playerPosition = dungeonHandler.player.position.clone();
    
    // Spawn zombie 5 units in front of the player
    const spawnPosition = playerPosition.clone();
    spawnPosition.z += 5; // Assuming player is facing +Z
    
    console.log('Spawning zombie at position:', spawnPosition);
    
    // Spawn the zombie
    const zombie = dungeonHandler._spawnEnemy('zombie', spawnPosition);
    
    if (!zombie) {
        console.error('Failed to spawn zombie!');
        return false;
    }
    
    console.log('Zombie spawned successfully!');
    
    // Check if the animation handler was created
    if (zombie.userData && zombie.userData.animationHandler) {
        console.log('Animation handler created successfully:', zombie.userData.animationHandler);
        
        // Verify the animation handler is working by triggering different states
        console.log('Testing animation states...');
        
        // Set up a sequence of animation states to test
        const testStates = [
            { state: 'IDLE', duration: 2000 },
            { state: 'MOVING', duration: 2000 },
            { state: 'ATTACKING', duration: 2000 },
            { state: 'AIMING', duration: 2000 },
            { state: 'IDLE', duration: 1000 }
        ];
        
        // Function to test each animation state
        function testAnimationState(index) {
            if (index >= testStates.length) {
                console.log('Animation test complete!');
                return;
            }
            
            const stateTest = testStates[index];
            console.log(`Testing animation state: ${stateTest.state}`);
            
            // Set the state in the enemy state timer
            if (zombie.userData && zombie.userData.id) {
                dungeonHandler.enemyStateTimers.set(zombie.userData.id, { 
                    state: stateTest.state, 
                    timer: 0 
                });
                
                // Force an animation update
                dungeonHandler._updateEnemyAnimations(zombie, { state: stateTest.state });
                
                // Schedule the next state test
                setTimeout(() => {
                    testAnimationState(index + 1);
                }, stateTest.duration);
            } else {
                console.error('Cannot test animation states: zombie userData or id missing!');
            }
        }
        
        // Start testing animation states
        testAnimationState(0);
        
        return zombie;
    } else {
        console.error('Animation handler was not created!');
        console.log('Zombie userData:', zombie.userData);
        
        // Try to fix the animation handler
        console.log('Attempting to fix the animation handler...');
        const fixed = dungeonHandler.fixZombieAnimationHandler(zombie);
        
        if (fixed) {
            console.log('Successfully fixed animation handler!');
            return zombie;
        } else {
            console.error('Failed to fix animation handler!');
            return false;
        }
    }
}

// Run the test
testZombieAnimation();
