<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knockback Test</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #000; color: #fff; font-family: sans-serif; }
        canvas { display: block; width: 100%; height: 100%; }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="info">
        <h3>Knockback Test</h3>
        <p>Click on enemies to apply knockback</p>
        <p>WASD to shoot projectiles</p>
        <p>Arrow keys to move</p>
    </div>

    <canvas id="webgl-canvas"></canvas>

    <script type="importmap">
        {
            "imports": {
                "three": "./libs/three/build/three.module.js",
                "three/addons/": "./libs/three/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { createAIBrain, applyKnockback } from './src/ai/AIFactory.js';

        // Basic scene setup
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('webgl-canvas') });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x222222);

        // Add lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        scene.add(ambientLight);
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        scene.add(directionalLight);

        // Create floor
        const floorGeometry = new THREE.PlaneGeometry(20, 20);
        const floorMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2;
        scene.add(floor);

        // Create test enemy
        const enemyGeometry = new THREE.BoxGeometry(1, 2, 1);
        const enemyMaterial = new THREE.MeshLambertMaterial({ color: 0xff4444 });
        const enemy = new THREE.Mesh(enemyGeometry, enemyMaterial);
        enemy.position.set(0, 1, -5);
        enemy.name = "TestEnemy";
        scene.add(enemy);

        // Create player (simple representation)
        const playerGeometry = new THREE.BoxGeometry(0.5, 1, 0.5);
        const playerMaterial = new THREE.MeshLambertMaterial({ color: 0x4444ff });
        const player = new THREE.Mesh(playerGeometry, playerMaterial);
        player.position.set(0, 0.5, 0);
        scene.add(player);

        // Set up enemy AI
        const enemyData = {
            health: 10,
            maxHealth: 10,
            speed: 2.0,
            size: 0.5,
            mass: 1.0,
            aiType: 'melee'
        };

        enemy.userData = enemyData;

        // Create AI brain
        const aiBrain = createAIBrain('melee', enemy, enemyData, scene, player, 1.0);
        enemyData.aiBrain = aiBrain;

        // Position camera
        camera.position.set(0, 10, 10);
        camera.lookAt(0, 0, 0);

        // Mouse click handler for manual knockback testing
        function onMouseClick(event) {
            const mouse = new THREE.Vector2();
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

            const raycaster = new THREE.Raycaster();
            raycaster.setFromCamera(mouse, camera);

            const intersects = raycaster.intersectObjects([enemy]);
            if (intersects.length > 0) {
                console.log("Clicked on enemy - applying knockback!");

                // Calculate knockback direction from player to enemy
                const knockbackDirection = enemy.position.clone().sub(player.position).normalize();
                const knockbackStrength = 20.0; // Increased for better visual impact and testing

                console.log(`Applying knockback: direction=(${knockbackDirection.x.toFixed(2)}, ${knockbackDirection.z.toFixed(2)}), strength=${knockbackStrength}`);

                // Apply knockback
                if (enemyData.aiBrain) {
                    applyKnockback(enemyData.aiBrain, knockbackDirection, knockbackStrength);
                }
            }
        }

        window.addEventListener('click', onMouseClick);

        // Animation loop
        const clock = new THREE.Clock();
        function animate() {
            requestAnimationFrame(animate);

            const deltaTime = clock.getDelta();

            // Update AI brain
            if (enemyData.aiBrain) {
                enemyData.aiBrain.update(deltaTime, [], null);
            }

            renderer.render(scene, camera);
        }

        animate();

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
    </script>
</body>
</html>
