<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation Analyzer Test</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #111; color: #eee; font-family: Arial, sans-serif; }
        canvas { display: block; width: 100%; height: 100%; }

        #controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            max-width: 300px;
            max-height: 90vh;
            overflow-y: auto;
        }

        #analysis-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            width: 300px;
            max-height: 90vh;
            overflow-y: auto;
        }

        #reference-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            display: none; /* Hidden by default */
        }

        .reference-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr) 1.5fr; /* 4 pose columns + timing info */
            gap: 5px;
            margin-top: 10px;
            background-color: #222;
            border-radius: 5px;
            padding: 5px;
        }

        .reference-header-row {
            display: contents;
        }

        .reference-header {
            background-color: #333;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            border-radius: 3px;
        }

        .reference-row {
            display: contents;
        }

        .reference-cell {
            background-color: #444;
            padding: 5px;
            text-align: center;
            border-radius: 3px;
        }

        .reference-cell img {
            width: 100%;
            height: auto;
            border-radius: 3px;
        }

        .reference-cell.empty {
            background-color: #333;
        }

        .reference-timing {
            background-color: #333;
            padding: 10px;
            border-radius: 3px;
        }

        .mode-toggle-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 10px;
            width: 100%;
        }

        button {
            margin: 5px;
            padding: 5px 10px;
            cursor: pointer;
        }

        .active {
            background-color: #4CAF50;
            color: white;
        }

        .section {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #444;
        }

        .score-bar, .health-bar {
            height: 15px;
            background-color: #333;
            margin: 5px 0;
            border-radius: 3px;
            overflow: hidden;
        }

        .score-fill, .health-fill {
            height: 100%;
            transition: width 0.3s;
        }

        .score-excellent { background-color: #4CAF50; }
        .score-good { background-color: #8BC34A; }
        .score-average { background-color: #FFC107; }
        .score-poor { background-color: #FF9800; }
        .score-bad { background-color: #F44336; }

        .health-fill { background-color: #4CAF50; }
        .health-fill.damaged { background-color: #FFC107; }
        .health-fill.critical { background-color: #F44336; }

        .issue {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .issue-high { background-color: rgba(244, 67, 54, 0.3); border-left: 3px solid #F44336; }
        .issue-medium { background-color: rgba(255, 152, 0, 0.3); border-left: 3px solid #FF9800; }
        .issue-low { background-color: rgba(255, 193, 7, 0.3); border-left: 3px solid #FFC107; }

        .recommendation {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .recommendation-high { background-color: rgba(76, 175, 80, 0.3); border-left: 3px solid #4CAF50; }
        .recommendation-medium { background-color: rgba(139, 195, 74, 0.3); border-left: 3px solid #8BC34A; }
        .recommendation-low { background-color: rgba(255, 193, 7, 0.3); border-left: 3px solid #FFC107; }

        .damage-text {
            position: absolute;
            color: #F44336;
            font-weight: bold;
            font-size: 1.2em;
            text-shadow: 1px 1px 2px black;
            pointer-events: none;
            animation: damage-float 1s ease-out forwards;
        }

        @keyframes damage-float {
            0% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(-30px); opacity: 0; }
        }

        .enemy-info {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .projectile {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #FFC107;
            border-radius: 50%;
            pointer-events: none;
        }

        #combat-stats {
            position: absolute;
            bottom: 10px;
            right: 10px; /* Changed from left to right */
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            max-width: 300px;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .stat-label {
            font-weight: bold;
            margin-right: 10px;
        }

        .stat-value {
            color: #8BC34A;
        }

        .stat-config-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .stat-config-row label {
            flex: 1;
            margin-right: 10px;
        }

        .stat-config-row input {
            flex: 1;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid #555;
            color: #eee;
            padding: 3px 5px;
            border-radius: 3px;
        }


    </style>
</head>
<body>
    <div id="controls">
        <h3>Animation Analyzer Test</h3>

        <div class="section">
            <h4>Enemy Type</h4>
            <div>
                <select id="enemy-type-select" style="width: 100%; margin-bottom: 10px; padding: 5px;">
                    <!-- Enemy types will be populated dynamically -->
                </select>
                <select id="enemy-variant-select" style="width: 100%; padding: 5px;">
                    <!-- Variants will be populated dynamically -->
                </select>
                <button id="spawn-selected-btn" style="width: 100%; margin-top: 10px;">Spawn Selected Enemy</button>
            </div>
        </div>

        <div class="section">
            <h4>Animation State</h4>
            <div>
                <button id="idle-btn" class="active">Idle</button>
                <button id="walk-btn">Walk</button>
                <button id="aim-btn">Aim</button>
                <button id="shoot-btn">Shoot</button>
                <button id="hit-btn">Hit</button>
                <button id="dodge-btn">Dodge</button>
            </div>
        </div>

        <div class="section">
            <h4>Analysis Controls</h4>
            <div>
                <button id="analyze-btn">Analyze Animation</button>
                <button id="reset-btn">Reset Analysis</button>
                <button id="auto-analyze-btn">Toggle Auto-Analysis</button>
            </div>
        </div>

        <div class="section">
            <h4>Display Mode</h4>
            <div>
                <button id="analysis-mode-btn" class="active">Analysis Mode</button>
                <button id="reference-mode-btn">Reference Mode</button>
            </div>
        </div>

        <div class="section" id="reference-controls" style="display: none;">
            <h4>Reference Controls</h4>
            <div>
                <button id="capture-animation-btn">Capture Animation</button>
                <button id="clear-frames-btn">Clear Frames</button>
                <div style="margin-top: 5px;">
                    <label for="capture-interval-input">Capture Interval (s):</label>
                    <input type="number" id="capture-interval-input" value="0.33" min="0.1" max="2.0" step="0.05" style="width: 60px;">
                </div>
            </div>
        </div>

        <div class="section">
            <h4>Texture Analysis</h4>
            <div>
                <button id="check-textures-btn">Check for Texture Flickering</button>
                <div id="texture-report" style="margin-top: 10px; max-height: 100px; overflow-y: auto;"></div>
            </div>
        </div>

        <div class="section">
            <h4>View Controls</h4>
            <div>
                <button id="rotate-left-btn">Rotate Left</button>
                <button id="rotate-right-btn">Rotate Right</button>
                <button id="reset-view-btn">Reset View</button>
            </div>
        </div>

        <div class="section">
            <h4>Combat Simulation</h4>
            <div>
                <button id="combat-start-btn">Start Combat</button>
                <button id="combat-stop-btn">Stop Combat</button>
                <button id="spawn-all-btn">Spawn All Enemies</button>
                <button id="clear-all-btn">Clear All</button>
                <button id="reset-player-btn">Reset Player</button>
            </div>
        </div>

        <div class="section">
            <h4>AI Integration</h4>
            <div>
                <button id="use-game-ai-btn" class="active">Use Game AI</button>
                <button id="use-local-ai-btn">Use Local AI</button>
                <div>Current AI: <span id="current-ai-source">Game AI</span></div>
            </div>
        </div>

        <div class="section">
            <h4>Player Stats</h4>
            <div>
                <div class="stat-config-row">
                    <label for="player-preset-select">Preset:</label>
                    <select id="player-preset-select" style="width: 100%; margin-bottom: 10px;">
                        <option value="custom">Custom</option>
                        <option value="starting">Starting Character</option>
                        <option value="mid_game">Mid-Game Character</option>
                        <option value="end_game">End-Game Character</option>
                        <option value="overpowered">Overpowered</option>
                    </select>
                </div>
                <div class="stat-config-row">
                    <label for="player-health-input">Health:</label>
                    <input type="number" id="player-health-input" value="100" min="1" max="1000" step="5">
                </div>
                <div class="health-bar">
                    <div id="player-health-bar" class="health-fill" style="width: 100%"></div>
                </div>
                <div class="stat-config-row">
                    <label for="player-attack-input">Attack:</label>
                    <input type="number" id="player-attack-input" value="15" min="1" max="100" step="1">
                </div>
                <div class="stat-config-row">
                    <label for="player-defense-input">Defense:</label>
                    <input type="number" id="player-defense-input" value="5" min="0" max="50" step="1">
                </div>
                <div class="stat-config-row">
                    <label for="player-speed-input">Speed:</label>
                    <input type="number" id="player-speed-input" value="3" min="1" max="10" step="0.5">
                </div>
                <button id="apply-player-stats-btn" style="width: 100%; margin-top: 10px;">Apply Stats</button>
            </div>
        </div>

        <div class="section">
            <h4>Enemy Status</h4>
            <div id="enemy-status">
                <div>Count: <span id="enemy-counter">0</span></div>
                <div id="selected-enemy-info">No enemy selected</div>
            </div>
        </div>

        <div class="section">
            <h4>Enemy Stats</h4>
            <div>
                <div class="stat-config-row">
                    <label for="enemy-preset-select">Preset:</label>
                    <select id="enemy-preset-select" style="width: 100%; margin-bottom: 10px;">
                        <option value="custom">Custom</option>
                        <option value="catacombs">Catacombs (Level 1)</option>
                        <option value="fungal_caverns">Fungal Caverns (Level 2)</option>
                        <option value="flooded_ruins">Flooded Ruins (Level 3)</option>
                        <option value="crystal_caves">Crystal Caves (Level 4)</option>
                        <option value="lava_tubes">Lava Tubes (Level 5)</option>
                        <option value="boss">Boss Area</option>
                    </select>
                </div>
                <div class="stat-config-row">
                    <label for="enemy-health-input">Health:</label>
                    <input type="number" id="enemy-health-input" value="50" min="1" max="1000" step="5">
                </div>
                <div class="stat-config-row">
                    <label for="enemy-attack-input">Attack:</label>
                    <input type="number" id="enemy-attack-input" value="10" min="1" max="100" step="1">
                </div>
                <div class="stat-config-row">
                    <label for="enemy-defense-input">Defense:</label>
                    <input type="number" id="enemy-defense-input" value="3" min="0" max="50" step="1">
                </div>
                <div class="stat-config-row">
                    <label for="enemy-speed-input">Speed:</label>
                    <input type="number" id="enemy-speed-input" value="2" min="0.5" max="10" step="0.5">
                </div>
                <div class="stat-config-row">
                    <label for="enemy-attack-range-input">Attack Range:</label>
                    <input type="number" id="enemy-attack-range-input" value="2" min="0.5" max="10" step="0.5">
                </div>
                <div class="stat-config-row">
                    <label for="enemy-attack-cooldown-input">Attack Cooldown:</label>
                    <input type="number" id="enemy-attack-cooldown-input" value="1.5" min="0.5" max="5" step="0.1">
                </div>
                <button id="apply-enemy-stats-btn" style="width: 100%; margin-top: 10px;">Apply to New Enemies</button>
                <button id="apply-to-selected-btn" style="width: 100%; margin-top: 5px;">Apply to Selected</button>
            </div>
        </div>
    </div>

    <div id="combat-stats">
        <h3>Combat Statistics</h3>
        <div class="stat-row">
            <span class="stat-label">Hits Landed:</span>
            <span id="hits-landed" class="stat-value">0</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">Damage Dealt:</span>
            <span id="damage-dealt" class="stat-value">0</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">Damage Taken:</span>
            <span id="damage-taken" class="stat-value">0</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">Enemies Defeated:</span>
            <span id="enemies-defeated" class="stat-value">0</span>
        </div>
    </div>

    <div id="analysis-panel">
        <h3>Animation Analysis</h3>

        <div class="section">
            <h4>Overall Score: <span id="overall-score">N/A</span></h4>
            <div class="score-bar">
                <div id="overall-score-bar" class="score-fill score-excellent" style="width: 0%"></div>
            </div>
        </div>

        <div class="section">
            <h4>Detailed Scores</h4>

            <div>
                <div>Smoothness: <span id="smoothness-score">N/A</span></div>
                <div class="score-bar">
                    <div id="smoothness-score-bar" class="score-fill score-excellent" style="width: 0%"></div>
                </div>
            </div>

            <div>
                <div>Naturalness: <span id="naturalness-score">N/A</span></div>
                <div class="score-bar">
                    <div id="naturalness-score-bar" class="score-fill score-excellent" style="width: 0%"></div>
                </div>
            </div>

            <div>
                <div>Consistency: <span id="consistency-score">N/A</span></div>
                <div class="score-bar">
                    <div id="consistency-score-bar" class="score-fill score-excellent" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h4>Issues Detected</h4>
            <div id="issues-container">
                <div class="issue issue-medium">No issues detected</div>
            </div>
        </div>

        <div class="section">
            <h4>Recommendations</h4>
            <div id="recommendations-container">
                <div class="recommendation recommendation-medium">No recommendations available</div>
            </div>
        </div>
    </div>

    <div id="reference-panel">
        <button id="back-to-analysis-btn" class="mode-toggle-btn">Back to Analysis Mode</button>
        <h3>Animation Reference</h3>
        <div id="reference-container">
            <div class="empty-reference">No frames captured yet. Click "Capture Animation" to start.</div>
        </div>
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "./libs/three/build/three.module.js",
                "three/addons/": "./libs/three/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { createSkeletonEnemyModel } from './src/generators/prefabs/skeletonEnemy.js';
        import { AIStates } from './src/ai/AIStates.js';
        import AnimationAnalyzer from './src/utils/AnimationAnalyzer.js';
        import AnimationReference from './src/utils/AnimationReference.js';
        import { createAIBrain, updateAIBrain, applyKnockback } from './src/ai/AIFactory.js';
        import { AI_BRAIN_TYPES, ENEMY_TYPES, getEnemyData } from './src/entities/EnemyTypes.js';
        import { Areas } from './src/gameData/areas.js';
        import { ProjectileTypes } from './src/projectiles/ProjectileTypes.js';

        // Enemy Types Management (moved to top for initialization)
        let enemyTypes = {}; // Will store all enemy types from the main game
        let enemyVariants = {}; // Will store variants for each enemy type

        // Scene setup
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x111111);

        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 2, 5);

        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        document.body.appendChild(renderer.domElement);

        // Initialize enemy types from the main game
        initializeEnemyTypes();

        // Controls
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.target.set(0, 1, 0);
        controls.update();

        // Lighting
        const ambientLight = new THREE.AmbientLight(0x404040);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 1024;
        directionalLight.shadow.mapSize.height = 1024;
        scene.add(directionalLight);

        // Ground
        const groundGeometry = new THREE.PlaneGeometry(20, 20);
        const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        scene.add(ground);

        // Define floor bounds to prevent walking off the edge
        const floorBounds = {
            min: new THREE.Vector3(-10, 0, -10),
            max: new THREE.Vector3(10, 0, 10)
        };

        // Animation analyzer and reference
        const analyzer = new AnimationAnalyzer();
        const reference = new AnimationReference();

        // Skeleton model
        let skeleton = null;
        let leftLeg = null;
        let rightLeg = null;
        let leftArm = null;
        let rightArm = null;
        let currentState = AIStates.IDLE;
        let animationTime = 0;
        let timer = 0;
        let enemyType = 'archer';
        let autoAnalyze = false;
        let analyzeInterval = null;

        // Combat simulation
        let combatActive = false;
        let combatInterval = null;
        let enemies = [];
        let player = null;
        let playerHealth = 100;
        let playerAttackCooldown = 0;
        let playerMovementTarget = new THREE.Vector3();
        let playerIsAttacking = false;
        let playerAttackTimer = 0;
        let playerAttackDamage = 15;
        let playerDefense = 5;

        // Combat statistics
        let statsHitsLanded = 0;
        let statsDamageDealt = 0;
        let statsDamageTaken = 0;
        let statsEnemiesDefeated = 0;

        // Projectiles
        let projectiles = [];

        // AI Integration
        let useGameAI = true; // Toggle between game AI and local AI
        let aiBrains = new Map(); // Map of enemy ID to AI brain
        let collisionObjects = []; // Objects to check for collision

        // Other variables for enemy management

        // AI player states
        const PLAYER_STATES = {
            IDLE: 'IDLE',
            MOVING: 'MOVING',
            ATTACKING: 'ATTACKING',
            DODGING: 'DODGING',
            KNOCKBACK: 'KNOCKBACK'
        };
        let playerState = PLAYER_STATES.IDLE;

        // Player knockback variables
        let playerKnockbackDirection = new THREE.Vector3();
        let playerKnockbackStrength = 0;
        let playerKnockbackTimer = 0;
        let playerKnockbackDuration = 0.5;

        // Spawn skeleton
        function spawnSkeleton(type = 'archer', scale = 2.5) {
            // Remove existing skeleton if any
            if (skeleton) {
                scene.remove(skeleton);
            }

            // Create new skeleton
            skeleton = createSkeletonEnemyModel(scale);
            skeleton.position.set(0, 0, 0);
            skeleton.castShadow = true;
            skeleton.receiveShadow = true;

            // Get limbs
            leftLeg = skeleton.getObjectByName('leftLeg');
            rightLeg = skeleton.getObjectByName('rightLeg');
            leftArm = skeleton.getObjectByName('leftArm');
            rightArm = skeleton.getObjectByName('rightArm');

            // Set type-specific properties
            if (type === 'warrior') {
                skeleton.name = 'SkeletonWarrior';
                skeleton.userData.speed = 1.8;
            } else if (type === 'assassin') {
                skeleton.name = 'SkeletonAssassin';
                skeleton.userData.speed = 2.0;
            } else if (type === 'boss') {
                skeleton.name = 'SkeletonBoss';
                skeleton.userData.speed = 1.6;
                scale = 3.5;
                skeleton.scale.set(scale, scale, scale);
            } else {
                skeleton.name = 'SkeletonArcher';
                skeleton.userData.speed = 1.5;
            }

            scene.add(skeleton);

            // Reset animation state
            currentState = AIStates.IDLE;
            updateButtonStates();

            // Reset analyzer
            analyzer.reset();
        }

        // Animation functions
        function applyIdleAnimation() {
            if (!leftLeg || !rightLeg) return;

            const idleSpeed = 1.5;
            const idleBobAmplitude = 0.05;
            const idleSwayAmplitude = Math.PI / 32;

            // Subtle idle movement
            const bobOffset = Math.sin(animationTime * idleSpeed) * idleBobAmplitude;
            const swayOffset = Math.sin(animationTime * idleSpeed * 0.7) * idleSwayAmplitude;

            // Apply to limbs
            if (leftLeg) leftLeg.rotation.x = swayOffset;
            if (rightLeg) rightLeg.rotation.x = -swayOffset;
            if (leftArm) leftArm.rotation.x = swayOffset * 1.5;
            if (rightArm) rightArm.rotation.x = -swayOffset * 1.5;

            // Add slight body movement
            if (skeleton) skeleton.position.y = bobOffset * 0.1;
        }

        function applyWalkAnimation() {
            if (!leftLeg || !rightLeg) return;

            const walkSpeed = skeleton.userData.speed * 2.0;
            const walkAmplitude = Math.PI / 10;

            // Walk cycle
            if (leftLeg) leftLeg.rotation.x = Math.sin(animationTime * walkSpeed) * walkAmplitude;
            if (rightLeg) rightLeg.rotation.x = Math.sin(animationTime * walkSpeed + Math.PI) * walkAmplitude;

            // Arm swing
            if (leftArm) leftArm.rotation.x = Math.sin(animationTime * walkSpeed + Math.PI) * walkAmplitude * 0.5;
            if (rightArm) rightArm.rotation.x = Math.sin(animationTime * walkSpeed) * walkAmplitude * 0.5;
        }

        function applyAimAnimation() {
            if (!leftLeg || !rightLeg) return;

            const idleSwayAmplitude = Math.PI / 64; // Reduced sway
            const swayOffset = Math.sin(animationTime * 1.0) * idleSwayAmplitude; // Slower sway

            // Add slight breathing animation to prevent looking frozen
            const breathingOffset = Math.sin(animationTime * 1.5) * 0.005;
            if (skeleton) skeleton.position.y = breathingOffset;

            // Position arms for aiming
            if (leftArm) {
                leftArm.rotation.set(Math.PI / 8, 0, 0); // Extend arm forward
            }

            if (rightArm) {
                // Calculate pullback using rotation.y based on timer
                const aimDuration = 0.5; // Default aim duration
                const pullbackProgress = Math.min(1.0, timer / aimDuration); // Ensure progress is 0 to 1
                const pullbackAngleY = pullbackProgress * -Math.PI / 32; // Apply the minimal pullback angle

                // Add time-based animation to prevent getting stuck
                const armSwayY = Math.sin(animationTime * 0.9) * (Math.PI / 128);
                rightArm.rotation.set(0, pullbackAngleY + armSwayY, -swayOffset); // No X rotation, apply Y pullback, minimal sway
            }

            // Keep legs neutral during aiming but add slight movement
            if (leftLeg) leftLeg.rotation.set(Math.sin(animationTime * 0.7) * 0.02, 0, 0);
            if (rightLeg) rightLeg.rotation.set(Math.sin(animationTime * 0.7 + Math.PI) * 0.02, 0, 0);

            // Increment timer for pullback animation
            timer += 0.016; // Approximately 60fps
            if (timer > 0.5) timer = 0.5; // Cap at max pullback
        }

        function applyShootAnimation() {
            if (!leftLeg || !rightLeg) return;

            // Release animation for shooting with slight continuous movement
            if (leftArm) {
                const armSwayX = Math.sin(animationTime * 0.8) * (Math.PI / 128);
                leftArm.rotation.set(-Math.PI / 16 + armSwayX, 0, 0); // Steady with slight movement
            }

            if (rightArm) {
                const armSwayY = Math.sin(animationTime * 0.9) * (Math.PI / 128);
                rightArm.rotation.set(0, Math.PI / 16 + armSwayY, 0); // Follow through with slight movement
            }

            // Stable stance with minimal movement
            if (leftLeg) leftLeg.rotation.set(Math.sin(animationTime * 0.7) * 0.01, 0, 0);
            if (rightLeg) rightLeg.rotation.set(Math.sin(animationTime * 0.7 + Math.PI) * 0.01, 0, 0);
        }

        function applyHitReactionAnimation() {
            if (!leftLeg || !rightLeg) return;

            // Stagger backwards
            if (leftLeg) leftLeg.rotation.x = -Math.PI / 8; // Leg back
            if (rightLeg) rightLeg.rotation.x = -Math.PI / 6; // Leg further back

            // Arms flail slightly
            if (leftArm) {
                leftArm.rotation.x = Math.PI / 4; // Arm up
                leftArm.rotation.z = -Math.PI / 8; // Arm out
            }

            if (rightArm) {
                rightArm.rotation.x = Math.PI / 3; // Arm up higher
                rightArm.rotation.z = Math.PI / 6; // Arm out
            }
        }

        function applyDodgeAnimation() {
            if (!leftLeg || !rightLeg) return;

            // Dodge to the side (right)
            if (leftLeg) leftLeg.rotation.set(Math.PI / 6, 0, Math.PI / 12); // Leg bent, angled out
            if (rightLeg) rightLeg.rotation.set(-Math.PI / 8, 0, Math.PI / 8); // Pushing off

            // Arms for balance
            if (leftArm) leftArm.rotation.set(0, 0, -Math.PI / 6); // Out for balance
            if (rightArm) rightArm.rotation.set(Math.PI / 8, 0, Math.PI / 4); // Up for balance

            // Slight body tilt
            if (skeleton) {
                skeleton.rotation.z = -Math.PI / 16; // Tilt body

                // Dodge movement
                const dodgeProgress = (timer % 0.5) / 0.5; // 0 to 1 over 0.5 seconds
                const dodgeOffset = Math.sin(dodgeProgress * Math.PI) * 0.5; // Peak in middle
                skeleton.position.x = dodgeOffset;
            }

            // Increment timer for dodge animation
            timer += 0.016; // Approximately 60fps
            if (timer > 0.5) timer = 0; // Reset for continuous dodge
        }

        // Animation update
        function updateAnimation() {
            animationTime += 0.016; // Approximately 60fps

            switch (currentState) {
                case AIStates.IDLE:
                    applyIdleAnimation();
                    break;
                case AIStates.MOVING:
                    applyWalkAnimation();
                    break;
                case AIStates.AIMING:
                    applyAimAnimation();
                    break;
                case AIStates.SHOOTING:
                    applyShootAnimation();
                    break;
                case AIStates.KNOCKBACK:
                    applyHitReactionAnimation();
                    break;
                case AIStates.DODGING:
                    applyDodgeAnimation();
                    break;
                default:
                    // Reset pose for other states
                    if (leftLeg) leftLeg.rotation.set(0, 0, 0);
                    if (rightLeg) rightLeg.rotation.set(0, 0, 0);
                    if (leftArm) leftArm.rotation.set(0, 0, 0);
                    if (rightArm) rightArm.rotation.set(0, 0, 0);
                    break;
            }

            // Update analyzer and reference
            analyzer.update(skeleton, currentState, animationTime);
            reference.update(skeleton, currentState, animationTime, scene);
        }

        // Update analysis display
        function updateAnalysisDisplay() {
            const results = analyzer.getResults();
            const scores = results.scores;

            // Update overall score
            const overallScoreElement = document.getElementById('overall-score');
            const overallScoreBar = document.getElementById('overall-score-bar');
            overallScoreElement.textContent = (scores.overall * 100).toFixed(1) + '%';
            overallScoreBar.style.width = (scores.overall * 100) + '%';

            // Update score class based on value
            updateScoreClass(overallScoreBar, scores.overall);

            // Update detailed scores
            updateDetailedScore('smoothness', scores.smoothness);
            updateDetailedScore('naturalness', scores.naturalness);
            updateDetailedScore('consistency', scores.consistency);

            // Update issues
            const issuesContainer = document.getElementById('issues-container');
            issuesContainer.innerHTML = '';

            if (results.issues.length === 0) {
                issuesContainer.innerHTML = '<div class="issue issue-medium">No issues detected</div>';
            } else {
                for (const issue of results.issues) {
                    const issueElement = document.createElement('div');
                    issueElement.className = `issue issue-${issue.severity || 'medium'}`;

                    let issueText = '';
                    switch (issue.type) {
                        case 'joint_limit':
                            issueText = `${issue.limb} ${issue.axis}-axis rotation (${issue.value.toFixed(2)}) exceeds natural limits`;
                            break;
                        case 'jitter':
                            issueText = `Jitter detected in ${issue.limb} movement`;
                            break;
                        case 'acceleration':
                            issueText = `Unnatural acceleration in ${issue.limb} movement`;
                            break;
                        case 'cycle_inconsistency':
                            issueText = `Inconsistent timing in ${issue.animation} animation cycle`;
                            break;
                        default:
                            issueText = `Issue: ${issue.type}`;
                    }

                    issueElement.textContent = issueText;
                    issuesContainer.appendChild(issueElement);
                }
            }

            // Update recommendations
            const recommendationsContainer = document.getElementById('recommendations-container');
            recommendationsContainer.innerHTML = '';

            if (results.recommendations.length === 0) {
                recommendationsContainer.innerHTML = '<div class="recommendation recommendation-medium">No recommendations available</div>';
            } else {
                for (const recommendation of results.recommendations) {
                    const recommendationElement = document.createElement('div');
                    recommendationElement.className = `recommendation recommendation-${recommendation.priority || 'medium'}`;
                    recommendationElement.textContent = recommendation.message;
                    recommendationsContainer.appendChild(recommendationElement);
                }
            }
        }

        // Helper to update detailed score
        function updateDetailedScore(name, score) {
            const scoreElement = document.getElementById(`${name}-score`);
            const scoreBar = document.getElementById(`${name}-score-bar`);

            scoreElement.textContent = (score * 100).toFixed(1) + '%';
            scoreBar.style.width = (score * 100) + '%';

            updateScoreClass(scoreBar, score);
        }

        // Helper to update score class based on value
        function updateScoreClass(element, score) {
            // Remove all score classes
            element.classList.remove('score-excellent', 'score-good', 'score-average', 'score-poor', 'score-bad');

            // Add appropriate class
            if (score >= 0.9) {
                element.classList.add('score-excellent');
            } else if (score >= 0.75) {
                element.classList.add('score-good');
            } else if (score >= 0.6) {
                element.classList.add('score-average');
            } else if (score >= 0.4) {
                element.classList.add('score-poor');
            } else {
                element.classList.add('score-bad');
            }
        }

        // Button event handlers
        function updateButtonStates() {
            // Update animation state buttons
            document.querySelectorAll('#controls button').forEach(btn => {
                btn.classList.remove('active');
            });

            // Highlight current animation state button
            switch (currentState) {
                case AIStates.IDLE:
                    document.getElementById('idle-btn').classList.add('active');
                    break;
                case AIStates.MOVING:
                    document.getElementById('walk-btn').classList.add('active');
                    break;
                case AIStates.AIMING:
                    document.getElementById('aim-btn').classList.add('active');
                    break;
                case AIStates.SHOOTING:
                    document.getElementById('shoot-btn').classList.add('active');
                    break;
                case AIStates.KNOCKBACK:
                    document.getElementById('hit-btn').classList.add('active');
                    break;
                case AIStates.DODGING:
                    document.getElementById('dodge-btn').classList.add('active');
                    break;
            }

            // Highlight the spawn selected button
            document.getElementById('spawn-selected-btn').classList.add('active');

            // Highlight the AI integration buttons
            if (useGameAI) {
                document.getElementById('use-game-ai-btn').classList.add('active');
            } else {
                document.getElementById('use-local-ai-btn').classList.add('active');
            }
        }

        // Animation state buttons
        document.getElementById('idle-btn').addEventListener('click', () => {
            currentState = AIStates.IDLE;
            timer = 0;
            updateButtonStates();
            analyzer.reset();

            // Apply idle animation to all enemies immediately
            enemies.forEach(enemy => {
                enemy.state = AIStates.IDLE;
                applyIdleAnimationToEnemy(enemy.mesh);
            });
        });

        document.getElementById('walk-btn').addEventListener('click', () => {
            currentState = AIStates.MOVING;
            timer = 0;
            updateButtonStates();
            analyzer.reset();

            // Apply walk animation to all enemies immediately
            enemies.forEach(enemy => {
                enemy.state = AIStates.MOVING;
                applyWalkAnimationToEnemy(enemy.mesh);
            });
        });

        document.getElementById('aim-btn').addEventListener('click', () => {
            currentState = AIStates.AIMING;
            timer = 0;
            updateButtonStates();
            analyzer.reset();

            // Apply aim animation to all enemies immediately
            enemies.forEach(enemy => {
                enemy.state = AIStates.AIMING;
                applyAimAnimationToEnemy(enemy.mesh);
            });
        });

        document.getElementById('shoot-btn').addEventListener('click', () => {
            currentState = AIStates.SHOOTING;
            timer = 0;
            updateButtonStates();
            analyzer.reset();

            // Apply shoot animation to all enemies immediately
            enemies.forEach(enemy => {
                enemy.state = AIStates.SHOOTING;
                applyShootAnimationToEnemy(enemy.mesh);

                // Create a projectile for each enemy
                setTimeout(() => {
                    if (enemy.type === 'archer' || enemy.mesh.userData.projectileType) {
                        const projectileStartPos = enemy.mesh.position.clone();
                        projectileStartPos.y += 0.5; // Adjust height

                        // Try to find bow or weapon
                        const weapon = enemy.mesh.getObjectByName('SkeletonBow') || enemy.mesh.getObjectByName('Weapon');
                        if (weapon) {
                            // Get position from weapon
                            projectileStartPos.setFromMatrixPosition(weapon.matrixWorld);
                        }

                        const projectileDirection = new THREE.Vector3(0, 0, -1);
                        if (player) {
                            projectileDirection.copy(player.position.clone().sub(projectileStartPos).normalize());
                        }

                        createProjectile(
                            projectileStartPos,
                            projectileDirection,
                            enemy.mesh.userData.attackDamage || 5,
                            8.0, // Speed
                            enemy.mesh.userData.attackRange || 10,
                            enemy.mesh.userData.projectileType || 'arrow'
                        );
                    }
                }, 300); // Small delay to match animation
            });
        });

        document.getElementById('hit-btn').addEventListener('click', () => {
            currentState = AIStates.KNOCKBACK;
            timer = 0;
            updateButtonStates();
            analyzer.reset();

            // Apply hit animation to all enemies immediately
            enemies.forEach(enemy => {
                enemy.state = AIStates.KNOCKBACK;
                applyHitReactionToEnemy(enemy.mesh);

                // Apply a small knockback effect
                const knockbackDirection = new THREE.Vector3(Math.random() - 0.5, 0, Math.random() - 0.5).normalize();
                enemy.mesh.position.add(knockbackDirection.multiplyScalar(0.5));
            });
        });

        document.getElementById('dodge-btn').addEventListener('click', () => {
            currentState = AIStates.DODGING;
            timer = 0;
            updateButtonStates();
            analyzer.reset();

            // Apply dodge animation to all enemies immediately
            enemies.forEach(enemy => {
                enemy.state = AIStates.DODGING;
                applyDodgeAnimationToEnemy(enemy.mesh);

                // Apply a dodge movement
                const dodgeDirection = new THREE.Vector3(Math.random() - 0.5, 0, Math.random() - 0.5).normalize();
                enemy.mesh.position.add(dodgeDirection.multiplyScalar(1.0));
            });
        });

        // Function to initialize enemy types from the main game
        function initializeEnemyTypes() {
            try {
                // Get all enemy types from ENEMY_TYPES
                const enemyTypeSelect = document.getElementById('enemy-type-select');
                const enemyVariantSelect = document.getElementById('enemy-variant-select');

                // Check if the DOM elements exist
                if (!enemyTypeSelect || !enemyVariantSelect) {
                    console.error('Enemy type select elements not found in the DOM');
                    return;
                }

                // Clear existing options
                enemyTypeSelect.innerHTML = '';
                enemyVariantSelect.innerHTML = '';

                // Group enemy types by base type (e.g., skeleton, bat, ghost)
                const groupedEnemyTypes = {};

                // Process all enemy types
                console.log('ENEMY_TYPES:', ENEMY_TYPES);

                // Check if ENEMY_TYPES is empty or undefined
                if (!ENEMY_TYPES || Object.keys(ENEMY_TYPES).length === 0) {
                    console.warn('ENEMY_TYPES is empty or undefined, using fallback enemy types');
                    // Use fallback enemy types
                    useFallbackEnemyTypes(enemyTypeSelect, enemyVariantSelect);
                    return;
                }

                // Get all enemy type values
                const enemyTypeValues = Object.values(ENEMY_TYPES);
                console.log('Enemy type values:', enemyTypeValues);

                // Check if we have any valid enemy types
                let validEnemiesFound = false;

                for (const value of enemyTypeValues) {
                    // Skip special types
                    if (value === 'FINAL_BOSS') continue;

                    // Check if enemy data exists
                    const enemyData = getEnemyData(value);
                    if (!enemyData || !enemyData.modelPrefab) {
                        console.log(`Skipping enemy type ${value} - no valid data or model`);
                        continue;
                    }

                    validEnemiesFound = true;

                    // Extract base type (e.g., 'skeleton' from 'skeleton_archer')
                    let baseType = value;
                    let variant = '';

                    if (value.includes('_')) {
                        const parts = value.split('_');
                        baseType = parts[0];
                        variant = parts.slice(1).join('_');
                    }

                    // Initialize group if not exists
                    if (!groupedEnemyTypes[baseType]) {
                        groupedEnemyTypes[baseType] = [];
                    }

                    // Add variant
                    if (variant) {
                        groupedEnemyTypes[baseType].push({
                            id: value,
                            name: variant.charAt(0).toUpperCase() + variant.slice(1)
                        });
                    } else {
                        // Base type without variant
                        groupedEnemyTypes[baseType].push({
                            id: value,
                            name: 'Default'
                        });
                    }

                    console.log(`Added enemy type: ${value} (base: ${baseType}, variant: ${variant})`);
                }

                // If no valid enemies were found, use fallback
                if (!validEnemiesFound) {
                    console.warn('No valid enemy types found, using fallback enemy types');
                    useFallbackEnemyTypes(enemyTypeSelect, enemyVariantSelect);
                    return;
                }

                // Store for later use
                enemyTypes = groupedEnemyTypes;

                // Populate enemy type dropdown
                for (const baseType in groupedEnemyTypes) {
                    const option = document.createElement('option');
                    option.value = baseType;
                    option.textContent = baseType.charAt(0).toUpperCase() + baseType.slice(1);
                    enemyTypeSelect.appendChild(option);
                }

                // Populate variants for the first type
                if (Object.keys(groupedEnemyTypes).length > 0) {
                    const firstType = Object.keys(groupedEnemyTypes)[0];
                    populateVariants(firstType);
                }

                console.log('Enemy types initialized:', groupedEnemyTypes);
            } catch (error) {
                console.error('Error initializing enemy types:', error);
                // Use fallback enemy types in case of error
                const enemyTypeSelect = document.getElementById('enemy-type-select');
                const enemyVariantSelect = document.getElementById('enemy-variant-select');
                useFallbackEnemyTypes(enemyTypeSelect, enemyVariantSelect);
            }
        }

        // Function to use fallback enemy types when main game types can't be loaded
        function useFallbackEnemyTypes(enemyTypeSelect, enemyVariantSelect) {
            // Check if the DOM elements exist
            if (!enemyTypeSelect || !enemyVariantSelect) {
                console.error('Enemy type select elements not found in the DOM');
                return;
            }

            // Create a fallback grouping of enemy types
            const fallbackTypes = {
                'skeleton': [
                    { id: 'archer', name: 'Archer' },
                    { id: 'warrior', name: 'Warrior' },
                    { id: 'assassin', name: 'Assassin' },
                    { id: 'boss', name: 'Boss' }
                ]
            };

            // Store for later use
            enemyTypes = fallbackTypes;

            // Populate enemy type dropdown
            for (const baseType in fallbackTypes) {
                const option = document.createElement('option');
                option.value = baseType;
                option.textContent = baseType.charAt(0).toUpperCase() + baseType.slice(1);
                enemyTypeSelect.appendChild(option);
            }

            // Populate variants for the first type
            if (Object.keys(fallbackTypes).length > 0) {
                const firstType = Object.keys(fallbackTypes)[0];
                populateVariants(firstType);
            }

            console.log('Using fallback enemy types:', fallbackTypes);
        }

        // Function to populate variants for a selected enemy type
        function populateVariants(baseType) {
            const enemyVariantSelect = document.getElementById('enemy-variant-select');

            // Check if the DOM element exists
            if (!enemyVariantSelect) {
                console.error('Enemy variant select element not found in the DOM');
                return;
            }

            enemyVariantSelect.innerHTML = '';

            if (!enemyTypes || !enemyTypes[baseType]) return;

            // Add variants
            for (const variant of enemyTypes[baseType]) {
                const option = document.createElement('option');
                option.value = variant.id;
                option.textContent = variant.name;
                enemyVariantSelect.appendChild(option);
            }

            // Store current variants
            enemyVariants = enemyTypes[baseType];
        }

        // Function to spawn the selected enemy
        function spawnSelectedEnemy() {
            const enemyTypeSelect = document.getElementById('enemy-type-select');
            const enemyVariantSelect = document.getElementById('enemy-variant-select');
            const selectedBaseType = enemyTypeSelect.value;
            const selectedVariant = enemyVariantSelect.value;

            if (!selectedBaseType || !selectedVariant) return;

            try {
                // Check if we're using fallback types
                const isFallback = !ENEMY_TYPES || Object.keys(ENEMY_TYPES).length === 0 ||
                                  !getEnemyData(selectedVariant);

                if (isFallback) {
                    // Using fallback types, spawn directly
                    console.log(`Spawning fallback enemy type: ${selectedVariant}`);

                    // Spawn at a random position
                    const position = new THREE.Vector3(
                        Math.random() * 6 - 3,
                        0,
                        Math.random() * 6 - 3
                    );

                    // Spawn the enemy using the variant as the local AI type
                    spawnEnemyAt(selectedVariant, position);
                } else {
                    // Using game enemy types
                    // Get enemy data
                    const enemyData = getEnemyData(selectedVariant);
                    if (!enemyData) {
                        console.error('Enemy data not found for:', selectedVariant);
                        return;
                    }

                    // Determine type for local AI
                    let localAIType = 'archer'; // Default
                    if (enemyData.aiType === AI_BRAIN_TYPES.MELEE) {
                        localAIType = 'warrior';
                    } else if (enemyData.aiType === AI_BRAIN_TYPES.ASSASSIN) {
                        localAIType = 'assassin';
                    } else if (enemyData.aiType === AI_BRAIN_TYPES.BOSS) {
                        localAIType = 'boss';
                    }

                    // Spawn at a random position
                    const position = new THREE.Vector3(
                        Math.random() * 6 - 3,
                        0,
                        Math.random() * 6 - 3
                    );

                    // Spawn the enemy
                    const enemy = spawnEnemyAt(localAIType, position);

                    // Override with actual enemy data
                    enemy.mesh.userData.enemyType = selectedVariant;
                    enemy.mesh.userData.enemyData = enemyData;

                    console.log(`Spawned ${selectedVariant} enemy:`, enemy);
                }
            } catch (error) {
                console.error('Error spawning enemy:', error);
            }
        }

        // Event listeners for enemy type selection
        document.getElementById('enemy-type-select').addEventListener('change', (e) => {
            const selectedType = e.target.value;
            populateVariants(selectedType);
        });

        document.getElementById('spawn-selected-btn').addEventListener('click', () => {
            spawnSelectedEnemy();
        });

        // View control buttons
        document.getElementById('rotate-left-btn').addEventListener('click', () => {
            if (skeleton) skeleton.rotation.y += Math.PI / 8;
        });

        document.getElementById('rotate-right-btn').addEventListener('click', () => {
            if (skeleton) skeleton.rotation.y -= Math.PI / 8;
        });

        document.getElementById('reset-view-btn').addEventListener('click', () => {
            if (skeleton) {
                skeleton.rotation.y = Math.PI;
                skeleton.rotation.z = 0;
                skeleton.position.x = 0;
            }
            controls.reset();
        });

        // Analysis buttons
        document.getElementById('analyze-btn').addEventListener('click', () => {
            updateAnalysisDisplay();
        });

        document.getElementById('reset-btn').addEventListener('click', () => {
            analyzer.reset();
            updateAnalysisDisplay();
        });

        document.getElementById('auto-analyze-btn').addEventListener('click', () => {
            autoAnalyze = !autoAnalyze;

            if (autoAnalyze) {
                document.getElementById('auto-analyze-btn').classList.add('active');
                // Update analysis every second
                analyzeInterval = setInterval(updateAnalysisDisplay, 1000);
            } else {
                document.getElementById('auto-analyze-btn').classList.remove('active');
                // Clear interval
                if (analyzeInterval) {
                    clearInterval(analyzeInterval);
                    analyzeInterval = null;
                }
            }
        });

        // Combat simulation buttons
        document.getElementById('combat-start-btn').addEventListener('click', () => {
            startCombat();
            document.getElementById('combat-start-btn').classList.add('active');
            document.getElementById('combat-stop-btn').classList.remove('active');
        });

        document.getElementById('combat-stop-btn').addEventListener('click', () => {
            stopCombat();
            document.getElementById('combat-stop-btn').classList.add('active');
            document.getElementById('combat-start-btn').classList.remove('active');
        });

        document.getElementById('spawn-all-btn').addEventListener('click', () => {
            spawnAllEnemies();
        });

        document.getElementById('clear-all-btn').addEventListener('click', () => {
            clearAllEnemies();
        });

        document.getElementById('reset-player-btn').addEventListener('click', () => {
            createPlayer();
        });

        // AI Integration buttons
        document.getElementById('use-game-ai-btn').addEventListener('click', () => {
            useGameAI = true;
            document.getElementById('use-game-ai-btn').classList.add('active');
            document.getElementById('use-local-ai-btn').classList.remove('active');
            document.getElementById('current-ai-source').textContent = 'Game AI';

            // Recreate AI brains for all enemies
            recreateAIBrains();
        });

        document.getElementById('use-local-ai-btn').addEventListener('click', () => {
            useGameAI = false;
            document.getElementById('use-local-ai-btn').classList.add('active');
            document.getElementById('use-game-ai-btn').classList.remove('active');
            document.getElementById('current-ai-source').textContent = 'Local AI';

            // Clear AI brains
            aiBrains.clear();
        });

        // Function to recreate AI brains for all enemies
        function recreateAIBrains() {
            // Clear existing AI brains
            aiBrains.clear();

            // Create new AI brains for all enemies
            for (const enemy of enemies) {
                let aiType;
                switch (enemy.type) {
                    case 'warrior':
                        aiType = AI_BRAIN_TYPES.MELEE;
                        break;
                    case 'assassin':
                        aiType = AI_BRAIN_TYPES.ASSASSIN;
                        break;
                    case 'boss':
                        aiType = AI_BRAIN_TYPES.BOSS;
                        break;
                    default: // archer
                        aiType = AI_BRAIN_TYPES.RANGED;
                        break;
                }

                try {
                    // Create AI brain
                    const aiBrain = createAIBrain(aiType, enemy.mesh, enemy.mesh.userData, scene, player, 2);
                    aiBrains.set(enemy.id, aiBrain);
                    enemy.useLocalAI = false;
                    console.log(`Recreated ${aiType} AI brain for ${enemy.mesh.name}`);
                } catch (error) {
                    console.error(`Failed to recreate AI brain for ${enemy.mesh.name}:`, error);
                    // Fall back to local AI
                    enemy.useLocalAI = true;
                }
            }
        }

        // Window resize handler
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // Mouse click handler for selecting enemies
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();

        window.addEventListener('click', (event) => {
            // Calculate mouse position in normalized device coordinates
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

            // Update the picking ray with the camera and mouse position
            raycaster.setFromCamera(mouse, camera);

            // Calculate objects intersecting the picking ray
            const intersects = raycaster.intersectObjects(scene.children, true);

            // Reset selection
            selectedEnemy = null;

            // Check for enemy intersections
            for (const intersect of intersects) {
                // Find the enemy object that was clicked
                const enemyObj = enemies.find(e => e.mesh === intersect.object || e.mesh.getObjectById(intersect.object.id));
                if (enemyObj) {
                    // Select this enemy
                    selectedEnemy = enemyObj;

                    // Update the enemy info display
                    const infoElement = document.getElementById('selected-enemy-info');
                    infoElement.innerHTML = `
                        <div>Selected: ${enemyObj.mesh.name}</div>
                        <div>Health: ${enemyObj.health}/${enemyObj.mesh.userData.health}</div>
                        <div>Attack: ${enemyObj.mesh.userData.attackDamage}</div>
                        <div>Defense: ${enemyObj.mesh.userData.defense || 0}</div>
                        <div>Speed: ${enemyObj.mesh.userData.speed}</div>
                        <div>Range: ${enemyObj.mesh.userData.attackRange}</div>
                    `;

                    // Update the enemy stat inputs to match the selected enemy
                    document.getElementById('enemy-health-input').value = enemyObj.health;
                    document.getElementById('enemy-attack-input').value = enemyObj.mesh.userData.attackDamage;
                    document.getElementById('enemy-defense-input').value = enemyObj.mesh.userData.defense || 0;
                    document.getElementById('enemy-speed-input').value = enemyObj.mesh.userData.speed;
                    document.getElementById('enemy-attack-range-input').value = enemyObj.mesh.userData.attackRange;
                    document.getElementById('enemy-attack-cooldown-input').value = enemyObj.mesh.userData.attackCooldown;

                    console.log('Selected enemy:', enemyObj);
                    break;
                }
            }

            // If no enemy was selected, clear the info display
            if (!selectedEnemy) {
                document.getElementById('selected-enemy-info').innerHTML = 'No enemy selected';
            }
        });

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);

            if (skeleton) {
                updateAnimation();
            }

            controls.update();
            renderer.render(scene, camera);
        }

        // Create player character
        function createPlayer() {
            // Remove existing player if any
            if (player) {
                scene.remove(player);
            }

            // Create player mesh
            const playerGeometry = new THREE.BoxGeometry(0.5, 1.8, 0.5);
            const playerMaterial = new THREE.MeshStandardMaterial({ color: 0x3366ff });
            player = new THREE.Mesh(playerGeometry, playerMaterial);
            player.position.set(-3, 0.9, 0);
            player.castShadow = true;
            player.receiveShadow = true;
            player.name = 'Player';

            // Add weapon
            const weaponGeometry = new THREE.BoxGeometry(0.1, 0.1, 1.0);
            const weaponMaterial = new THREE.MeshStandardMaterial({ color: 0xcccccc });
            const weapon = new THREE.Mesh(weaponGeometry, weaponMaterial);
            weapon.position.set(0.3, 0, 0.5);
            weapon.name = 'Weapon';
            player.add(weapon);

            // Reset player state
            playerHealth = 100;
            playerState = PLAYER_STATES.IDLE;
            playerAttackCooldown = 0;

            scene.add(player);
            return player;
        }

        // Spawn enemy at position using actual enemy stats from the main game
        function spawnEnemyAt(type, position) {
            // Map variant types to main game enemy types
            let gameEnemyType;
            switch(type) {
                case 'warrior': gameEnemyType = 'skeleton_warrior'; break;
                case 'assassin': gameEnemyType = 'skeleton_assassin'; break;
                case 'boss': gameEnemyType = 'skeleton_boss'; break;
                case 'archer':
                default: gameEnemyType = 'skeleton_archer'; break;
            }

            // Get the actual enemy data from the main game
            const enemyData = getEnemyData(gameEnemyType);

            // Check if we have valid enemy data
            if (!enemyData || !enemyData.modelPrefab) {
                console.error(`Failed to get enemy data for type: ${gameEnemyType}`);
                return null;
            }

            // Create the enemy model using the size from the enemy data
            const size = type === 'boss' ? 3.5 : (enemyData.size || 2.5);
            const enemy = createSkeletonEnemyModel(size);
            enemy.position.copy(position);
            enemy.castShadow = true;
            enemy.receiveShadow = true;

            // Generate unique ID for this enemy
            const enemyId = 'enemy_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
            enemy.userData.id = enemyId;

            // Check if we have custom stats configured
            const customStats = window.enemyStatsConfig;

            // Set name based on type
            enemy.name = enemyData.name || `Skeleton${type.charAt(0).toUpperCase() + type.slice(1)}`;

            // Set userData properties from the actual enemy data, with custom stats override if available
            enemy.userData.type = gameEnemyType;
            enemy.userData.speed = customStats ? customStats.speed : enemyData.baseSpeed;
            enemy.userData.health = customStats ? customStats.health : enemyData.health;
            enemy.userData.attackRange = customStats ? customStats.attackRange :
                                        (enemyData.attackRange || enemyData.preferredRange || 2.0);
            enemy.userData.attackDamage = customStats ? customStats.attack :
                                         (enemyData.meleeDamage || enemyData.projectileDamage || 5);
            enemy.userData.attackCooldown = customStats ? customStats.attackCooldown : enemyData.attackCooldown;
            enemy.userData.defense = customStats ? customStats.defense : Math.floor(enemyData.mass || 2);

            // Set AI type from the enemy data
            const aiType = enemyData.aiType || AI_BRAIN_TYPES.RANGED;

            // Set projectile type if available
            if (enemyData.projectileType) {
                enemy.userData.projectileType = enemyData.projectileType;
                enemy.userData.projectileSpeed = enemyData.projectileSpeed;
                enemy.userData.projectileRange = enemyData.projectileRange;
            }

            // Add to scene and enemies array
            scene.add(enemy);
            const enemyObj = {
                id: enemyId,
                mesh: enemy,
                type: type,
                state: AIStates.IDLE,
                health: enemy.userData.health,
                attackCooldown: 0,
                stateTimer: 0
            };
            enemies.push(enemyObj);

            // Create AI brain if using game AI
            if (useGameAI) {
                try {
                    // Create AI brain
                    const aiBrain = createAIBrain(aiType, enemy, enemy.userData, scene, player, 2);
                    aiBrains.set(enemyId, aiBrain);
                    console.log(`Created ${aiType} AI brain for ${enemy.name}`);
                } catch (error) {
                    console.error(`Failed to create AI brain for ${enemy.name}:`, error);
                    // Fall back to local AI
                    enemyObj.useLocalAI = true;
                }
            }

            return enemy;
        }

        // Spawn all enemy types
        function spawnAllEnemies() {
            // Clear existing enemies
            clearAllEnemies();

            try {
                // Check if we're using fallback types
                const isFallback = !ENEMY_TYPES || Object.keys(ENEMY_TYPES).length === 0;

                if (isFallback) {
                    console.log('Using fallback enemy types for spawning all enemies');
                    // Spawn the fallback enemy types in a circle
                    const fallbackTypes = ['archer', 'warrior', 'assassin', 'boss'];
                    const radius = 5;
                    const angleStep = (2 * Math.PI) / fallbackTypes.length;

                    fallbackTypes.forEach((type, index) => {
                        const angle = angleStep * index;
                        const x = Math.cos(angle) * radius;
                        const z = Math.sin(angle) * radius;
                        const position = new THREE.Vector3(x, 0, z);
                        spawnEnemyAt(type, position);
                    });
                    return;
                }

                // Get all enemy types
                const allEnemyTypes = Object.values(ENEMY_TYPES);
                console.log('Spawning all enemies from types:', allEnemyTypes);

                // Filter out special types
                const validEnemyTypes = allEnemyTypes.filter(type => {
                    // Skip special types
                    if (type === 'FINAL_BOSS') return false;

                    // Check if enemy data exists
                    const enemyData = getEnemyData(type);
                    const isValid = enemyData && enemyData.modelPrefab;
                    if (!isValid) {
                        console.log(`Skipping enemy type ${type} - no valid data or model`);
                    }
                    return isValid;
                });

                console.log('Valid enemy types for spawning:', validEnemyTypes);

                // If no valid enemy types found, use fallback
                if (validEnemyTypes.length === 0) {
                    console.warn('No valid enemy types found, using fallback enemy types');
                    // Call this function again, which will use the fallback path
                    spawnAllEnemies();
                    return;
                }

                // Limit to 6 enemies max
                const enemiesToSpawn = validEnemyTypes.slice(0, 6);

                // Calculate positions in a circle
                const radius = 5;
                const angleStep = (2 * Math.PI) / enemiesToSpawn.length;

                // Spawn enemies
                enemiesToSpawn.forEach((enemyType, index) => {
                    const angle = angleStep * index;
                    const x = Math.cos(angle) * radius;
                    const z = Math.sin(angle) * radius;

                    // Get enemy data
                    const enemyData = getEnemyData(enemyType);

                    // Determine local AI type
                    let localAIType = 'archer'; // Default
                    if (enemyData.aiType === AI_BRAIN_TYPES.MELEE) {
                        localAIType = 'warrior';
                    } else if (enemyData.aiType === AI_BRAIN_TYPES.ASSASSIN) {
                        localAIType = 'assassin';
                    } else if (enemyData.aiType === AI_BRAIN_TYPES.BOSS) {
                        localAIType = 'boss';
                    }

                    // Spawn the enemy
                    const position = new THREE.Vector3(x, 0, z);
                    const enemy = spawnEnemyAt(localAIType, position);

                    // Override with actual enemy data
                    enemy.userData.enemyType = enemyType;
                    enemy.userData.enemyData = enemyData;

                    console.log(`Spawned ${enemyType} enemy:`, enemy);
                });
            } catch (error) {
                console.error('Error spawning all enemies:', error);

                // Fallback to basic enemies if there's an error
                const fallbackTypes = ['archer', 'warrior', 'assassin', 'boss'];
                const radius = 5;
                const angleStep = (2 * Math.PI) / fallbackTypes.length;

                fallbackTypes.forEach((type, index) => {
                    const angle = angleStep * index;
                    const x = Math.cos(angle) * radius;
                    const z = Math.sin(angle) * radius;
                    const position = new THREE.Vector3(x, 0, z);
                    spawnEnemyAt(type, position);
                });
            }
        }

        // Clear all enemies
        function clearAllEnemies() {
            for (const enemy of enemies) {
                scene.remove(enemy.mesh);
            }
            enemies = [];

            // Clear AI brains
            aiBrains.clear();

            // Clear projectiles
            for (const projectile of projectiles) {
                scene.remove(projectile.mesh);
            }
            projectiles = [];
        }

        // Start combat simulation
        function startCombat() {
            if (combatActive) return;

            // Create player if not exists
            if (!player) {
                createPlayer();
            }

            // Spawn enemies if none exist
            if (enemies.length === 0) {
                spawnAllEnemies();
            }

            // Set combat active
            combatActive = true;

            // Start combat update interval
            combatInterval = setInterval(updateCombat, 16); // ~60fps
        }

        // Stop combat simulation
        function stopCombat() {
            combatActive = false;

            if (combatInterval) {
                clearInterval(combatInterval);
                combatInterval = null;
            }

            // Reset player state
            playerState = PLAYER_STATES.IDLE;

            // Reset enemy states
            for (const enemy of enemies) {
                enemy.state = AIStates.IDLE;
            }
        }

        // Create projectile using actual projectile types from the main game
        function createProjectile(position, direction, damage, speed, range, type = 'arrow') {
            // Get projectile type definition from the main game
            const projectileTypeData = ProjectileTypes[type] || ProjectileTypes.arrow;

            // Create projectile mesh using the type's createMesh function if available
            let projectileMesh;

            if (projectileTypeData && typeof projectileTypeData.createMesh === 'function') {
                // Use the actual mesh creation function from the main game
                projectileMesh = projectileTypeData.createMesh(position);
            } else {
                // Fallback to a basic mesh if the type doesn't exist or doesn't have a createMesh function
                const projectileGeometry = new THREE.SphereGeometry(0.05, 8, 8);
                const projectileMaterial = new THREE.MeshBasicMaterial({
                    color: projectileTypeData?.color || 0xffcc00
                });
                projectileMesh = new THREE.Mesh(projectileGeometry, projectileMaterial);
                projectileMesh.position.copy(position);
            }

            // Add to scene
            scene.add(projectileMesh);

            // Create projectile object with properties from the type definition
            const projectile = {
                mesh: projectileMesh,
                direction: direction.clone().normalize(),
                speed: speed || projectileTypeData.speed || 10,
                damage: damage || projectileTypeData.damage || 5,
                range: range || projectileTypeData.range || 10,
                distanceTraveled: 0,
                type: type,
                typeData: projectileTypeData,
                // Add trail effect if specified in the type
                hasTrail: projectileTypeData.trailEffect || false,
                trailColor: projectileTypeData.trailColor,
                trailLength: projectileTypeData.trailLength || 10
            };

            // Create trail if needed
            if (projectile.hasTrail) {
                const trailGeometry = new THREE.BufferGeometry();
                const trailMaterial = new THREE.LineBasicMaterial({
                    color: projectile.trailColor || 0xffffff,
                    transparent: true,
                    opacity: 0.7
                });

                // Initialize trail points
                const trailPoints = [];
                for (let i = 0; i < projectile.trailLength; i++) {
                    trailPoints.push(position.clone());
                }

                trailGeometry.setFromPoints(trailPoints);
                const trail = new THREE.Line(trailGeometry, trailMaterial);
                scene.add(trail);

                projectile.trail = trail;
                projectile.trailPoints = trailPoints;
            }

            // Add to projectiles array
            projectiles.push(projectile);

            return projectile;
        }

        // Update projectiles with trail effects
        function updateProjectiles(deltaTime) {
            for (let i = projectiles.length - 1; i >= 0; i--) {
                const projectile = projectiles[i];

                // Move projectile
                const moveDistance = projectile.speed * deltaTime;
                projectile.mesh.position.addScaledVector(projectile.direction, moveDistance);
                projectile.distanceTraveled += moveDistance;

                // Update trail if present
                if (projectile.hasTrail && projectile.trail && projectile.trailPoints) {
                    // Shift trail points
                    for (let j = projectile.trailPoints.length - 1; j > 0; j--) {
                        projectile.trailPoints[j].copy(projectile.trailPoints[j - 1]);
                    }

                    // Set first point to current position
                    projectile.trailPoints[0].copy(projectile.mesh.position);

                    // Update trail geometry
                    projectile.trail.geometry.setFromPoints(projectile.trailPoints);
                    projectile.trail.geometry.attributes.position.needsUpdate = true;
                }

                // Apply gravity if specified in the projectile type
                if (projectile.typeData && projectile.typeData.gravity) {
                    // Apply gravity to the projectile's y velocity
                    projectile.direction.y += projectile.typeData.gravity * deltaTime * 0.1;
                    projectile.direction.normalize().multiplyScalar(projectile.speed);
                }

                // Check if projectile has reached its range
                if (projectile.distanceTraveled >= projectile.range) {
                    // Remove projectile and its trail
                    scene.remove(projectile.mesh);
                    if (projectile.trail) {
                        scene.remove(projectile.trail);
                    }
                    projectiles.splice(i, 1);
                    continue;
                }

                // Check for collision with player
                if (player && projectile.mesh.position.distanceTo(player.position) < 0.5) {
                    // Apply damage to player
                    const damageDealt = Math.max(1, projectile.damage - playerDefense / 2);
                    playerHealth -= damageDealt;
                    statsDamageTaken += damageDealt;

                    // Update player health display
                    document.getElementById('player-health-value').textContent = Math.max(0, playerHealth);

                    // Apply knockback to player
                    playerState = PLAYER_STATES.KNOCKBACK;
                    playerKnockbackTimer = playerKnockbackDuration;
                    playerKnockbackDirection.copy(player.position).sub(projectile.mesh.position).normalize();
                    playerKnockbackStrength = 5.0; // Adjust strength as needed

                    // Create damage text
                    createDamageText(player.position, damageDealt);

                    // Remove projectile and its trail
                    scene.remove(projectile.mesh);
                    if (projectile.trail) {
                        scene.remove(projectile.trail);
                    }
                    projectiles.splice(i, 1);
                    continue;
                }

                // Check for collision with enemies
                for (let j = 0; j < enemies.length; j++) {
                    const enemy = enemies[j];
                    if (projectile.mesh.position.distanceTo(enemy.mesh.position) < 0.5) {
                        // Apply damage to enemy
                        enemy.health -= projectile.damage;
                        statsHitsLanded++;
                        statsDamageDealt += projectile.damage;

                        // Set enemy state to hit reaction
                        enemy.state = AIStates.KNOCKBACK;
                        enemy.stateTimer = 0.5;

                        // Apply knockback to enemy
                        const knockbackDirection = new THREE.Vector3().subVectors(enemy.mesh.position, projectile.mesh.position).normalize();
                        enemy.mesh.position.add(knockbackDirection.multiplyScalar(0.5));

                        // Create damage text
                        createDamageText(enemy.mesh.position, projectile.damage);

                        // Create impact effect if specified
                        if (projectile.typeData && projectile.typeData.impactEffect) {
                            createImpactEffect(enemy.mesh.position, projectile.typeData.impactEffect);
                        }

                        // Remove projectile and its trail
                        scene.remove(projectile.mesh);
                        if (projectile.trail) {
                            scene.remove(projectile.trail);
                        }
                        projectiles.splice(i, 1);
                        break;
                    }
                }
            }
        }

        // Create impact effect
        function createImpactEffect(position, effectType) {
            // Create a simple particle effect
            const particleCount = 10;
            const particles = [];

            // Set color based on effect type
            let color;
            switch(effectType) {
                case 'fire_impact': color = 0xFF4500; break;
                case 'lightning_impact': color = 0xFFFF00; break;
                case 'shadow_impact': color = 0x800080; break;
                case 'energy_impact': color = 0xFFFFFF; break;
                case 'soul_impact': color = 0x00FFFF; break;
                case 'arrow_impact':
                default: color = 0x8B4513; break;
            }

            // Create particles
            for (let i = 0; i < particleCount; i++) {
                const particleGeometry = new THREE.SphereGeometry(0.05, 4, 4);
                const particleMaterial = new THREE.MeshBasicMaterial({
                    color: color,
                    transparent: true,
                    opacity: 0.8
                });
                const particle = new THREE.Mesh(particleGeometry, particleMaterial);

                // Set random position around impact point
                particle.position.copy(position);
                particle.position.x += (Math.random() - 0.5) * 0.5;
                particle.position.y += (Math.random() - 0.5) * 0.5;
                particle.position.z += (Math.random() - 0.5) * 0.5;

                // Set random velocity
                particle.userData.velocity = new THREE.Vector3(
                    (Math.random() - 0.5) * 2,
                    (Math.random() - 0.5) * 2,
                    (Math.random() - 0.5) * 2
                );

                // Set lifetime
                particle.userData.lifetime = 0.5 + Math.random() * 0.5;
                particle.userData.age = 0;

                scene.add(particle);
                particles.push(particle);

                // Remove particles after lifetime
                setTimeout(() => {
                    scene.remove(particle);
                    const index = particles.indexOf(particle);
                    if (index !== -1) {
                        particles.splice(index, 1);
                    }
                }, particle.userData.lifetime * 1000);
            }

            // Add point light for effect
            const light = new THREE.PointLight(color, 1, 3);
            light.position.copy(position);
            scene.add(light);

            // Remove light after a short time
            setTimeout(() => {
                scene.remove(light);
            }, 300);
        }

        // Create damage text
        function createDamageText(position, damage) {
            // Create damage text element
            const damageText = document.createElement('div');
            damageText.className = 'damage-text';
            damageText.textContent = damage;
            document.body.appendChild(damageText);

            // Convert 3D position to screen position
            const screenPosition = new THREE.Vector3(position.x, position.y, position.z);
            screenPosition.project(camera);

            // Set position
            const x = (screenPosition.x * 0.5 + 0.5) * window.innerWidth;
            const y = (-(screenPosition.y * 0.5) + 0.5) * window.innerHeight;
            damageText.style.left = x + 'px';
            damageText.style.top = y + 'px';

            // Remove after animation completes
            setTimeout(() => {
                document.body.removeChild(damageText);
            }, 1000);
        }

        // Update combat simulation
        function updateCombat() {
            const deltaTime = 0.016; // ~60fps

            // Update player
            updatePlayer(deltaTime);

            // Update enemies
            for (let i = enemies.length - 1; i >= 0; i--) {
                const enemy = enemies[i];
                updateEnemy(enemy, deltaTime);

                // Remove dead enemies
                if (enemy.health <= 0) {
                    scene.remove(enemy.mesh);
                    enemies.splice(i, 1);
                    statsEnemiesDefeated++;
                }
            }

            // Update projectiles
            updateProjectiles(deltaTime);

            // Update health display
            document.getElementById('player-health-bar').style.width = playerHealth + '%';
            document.getElementById('player-health-value').textContent = Math.max(0, playerHealth);

            if (playerHealth < 25) {
                document.getElementById('player-health-bar').classList.add('critical');
                document.getElementById('player-health-bar').classList.remove('damaged');
            } else if (playerHealth < 50) {
                document.getElementById('player-health-bar').classList.add('damaged');
                document.getElementById('player-health-bar').classList.remove('critical');
            } else {
                document.getElementById('player-health-bar').classList.remove('damaged');
                document.getElementById('player-health-bar').classList.remove('critical');
            }

            // Update enemy counter
            document.getElementById('enemy-counter').textContent = enemies.length;

            // Update combat stats
            document.getElementById('hits-landed').textContent = statsHitsLanded;
            document.getElementById('damage-dealt').textContent = statsDamageDealt;
            document.getElementById('damage-taken').textContent = statsDamageTaken;
            document.getElementById('enemies-defeated').textContent = statsEnemiesDefeated;
        }

        // Update player AI
        function updatePlayer(deltaTime) {
            // Decrease cooldowns
            if (playerAttackCooldown > 0) {
                playerAttackCooldown -= deltaTime;
            }

            // Find closest enemy
            let closestEnemy = null;
            let closestDistance = Infinity;

            for (const enemy of enemies) {
                const distance = player.position.distanceTo(enemy.mesh.position);
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestEnemy = enemy;
                }
            }

            // Update player state based on closest enemy
            if (closestEnemy) {
                // Decide what to do based on distance
                if (closestDistance < 2.0) {
                    // Close enough to attack
                    if (playerAttackCooldown <= 0) {
                        playerState = PLAYER_STATES.ATTACKING;
                        playerAttackTimer = 0;
                        playerAttackCooldown = 1.0; // 1 second cooldown

                        // Deal damage to enemy
                        closestEnemy.health -= 15;
                        closestEnemy.state = AIStates.KNOCKBACK;
                        closestEnemy.stateTimer = 0.5;
                    } else if (Math.random() < 0.1) {
                        // Sometimes dodge when close
                        playerState = PLAYER_STATES.DODGING;
                        playerAttackTimer = 0;

                        // Move to a random position around the enemy
                        const angle = Math.random() * Math.PI * 2;
                        playerMovementTarget.set(
                            closestEnemy.mesh.position.x + Math.cos(angle) * 3,
                            0,
                            closestEnemy.mesh.position.z + Math.sin(angle) * 3
                        );
                    } else {
                        // Move to a better position
                        playerState = PLAYER_STATES.MOVING;

                        // Move to a random position around the enemy
                        const angle = Math.random() * Math.PI * 2;
                        playerMovementTarget.set(
                            closestEnemy.mesh.position.x + Math.cos(angle) * 3,
                            0,
                            closestEnemy.mesh.position.z + Math.sin(angle) * 3
                        );
                    }
                } else if (closestDistance < 10.0) {
                    // Move towards enemy
                    playerState = PLAYER_STATES.MOVING;
                    playerMovementTarget.copy(closestEnemy.mesh.position);
                } else {
                    // Too far, just idle
                    playerState = PLAYER_STATES.IDLE;
                }
            } else {
                // No enemies, just idle
                playerState = PLAYER_STATES.IDLE;
            }

            // Ensure player stays on the floor and within bounds
            if (player.position.y !== 0) {
                player.position.y = 0;
            }

            // Keep player within floor bounds
            if (player.position.x < floorBounds.min.x) player.position.x = floorBounds.min.x;
            if (player.position.x > floorBounds.max.x) player.position.x = floorBounds.max.x;
            if (player.position.z < floorBounds.min.z) player.position.z = floorBounds.min.z;
            if (player.position.z > floorBounds.max.z) player.position.z = floorBounds.max.z;

            // Execute player state
            switch (playerState) {
                case PLAYER_STATES.IDLE:
                    // Just stand still
                    break;

                case PLAYER_STATES.MOVING:
                    // Move towards target
                    const direction = playerMovementTarget.clone().sub(player.position).normalize();
                    player.position.add(direction.multiplyScalar(3.0 * deltaTime));

                    // Face direction of movement
                    player.lookAt(playerMovementTarget);
                    break;

                case PLAYER_STATES.ATTACKING:
                    // Face closest enemy
                    if (closestEnemy) {
                        player.lookAt(closestEnemy.mesh.position);
                    }

                    // Attack animation
                    playerAttackTimer += deltaTime;
                    if (playerAttackTimer > 0.5) {
                        playerState = PLAYER_STATES.IDLE;
                    }
                    break;

                case PLAYER_STATES.DODGING:
                    // Move quickly towards target
                    const dodgeDirection = playerMovementTarget.clone().sub(player.position).normalize();
                    player.position.add(dodgeDirection.multiplyScalar(8.0 * deltaTime));

                    // Face direction of movement
                    player.lookAt(playerMovementTarget);

                    // End dodge after a short time
                    playerAttackTimer += deltaTime;
                    if (playerAttackTimer > 0.3) {
                        playerState = PLAYER_STATES.IDLE;
                    }
                    break;

                case PLAYER_STATES.KNOCKBACK:
                    // Apply knockback movement
                    if (playerKnockbackTimer > 0) {
                        // Move in knockback direction
                        player.position.add(playerKnockbackDirection.multiplyScalar(playerKnockbackStrength * deltaTime));

                        // Decrease knockback timer
                        playerKnockbackTimer -= deltaTime;
                        if (playerKnockbackTimer <= 0) {
                            playerState = PLAYER_STATES.IDLE;
                        }
                    } else {
                        playerState = PLAYER_STATES.IDLE;
                    }
                    break;
            }
        }

        // Update enemy AI
        function updateEnemy(enemy, deltaTime) {
            // Decrease cooldowns
            if (enemy.attackCooldown > 0) {
                enemy.attackCooldown -= deltaTime;
            }

            // Decrease state timer
            if (enemy.stateTimer > 0) {
                enemy.stateTimer -= deltaTime;
                if (enemy.stateTimer <= 0) {
                    // Reset state after timer expires
                    enemy.state = AIStates.IDLE;
                }
            }

            // Skip AI if in knockback state
            if (enemy.state === AIStates.KNOCKBACK) {
                // Apply hit reaction animation
                applyHitReactionToEnemy(enemy.mesh);
                return;
            }

            // Use game AI if enabled and available for this enemy
            if (useGameAI && !enemy.useLocalAI && aiBrains.has(enemy.id)) {
                updateEnemyWithGameAI(enemy, deltaTime);
                return;
            }

            // Fall back to local AI implementation
            updateEnemyWithLocalAI(enemy, deltaTime);
        }

        // Update enemy using game AI
        function updateEnemyWithGameAI(enemy, deltaTime) {
            const aiBrain = aiBrains.get(enemy.id);
            if (!aiBrain) return;

            try {
                // Update AI brain
                const stateData = updateAIBrain(aiBrain, deltaTime, collisionObjects, floorBounds);
                if (!stateData) return;

                // Update enemy state based on AI brain state
                enemy.state = stateData.state;
                enemy.stateTimer = stateData.timer || 0;

                // Apply animations based on state
                applyAnimationBasedOnState(enemy);

                // Handle projectile creation for ranged enemies
                if (enemy.state === AIStates.SHOOTING &&
                    (enemy.type === 'archer' || enemy.mesh.userData.projectileType)) {
                    // Create projectile if shooting
                    const projectileStartPos = enemy.mesh.position.clone();
                    projectileStartPos.y += 0.5; // Adjust height

                    // Try to find bow or weapon
                    const weapon = enemy.mesh.getObjectByName('SkeletonBow') || enemy.mesh.getObjectByName('Weapon');
                    if (weapon) {
                        // Get position from weapon
                        projectileStartPos.setFromMatrixPosition(weapon.matrixWorld);
                    }

                    const projectileDirection = player.position.clone().sub(projectileStartPos).normalize();

                    // Use the enemy's projectile type if available
                    const projectileType = enemy.mesh.userData.projectileType || 'arrow';
                    const projectileSpeed = enemy.mesh.userData.projectileSpeed || 8.0;
                    const projectileRange = enemy.mesh.userData.projectileRange || enemy.mesh.userData.attackRange || 10.0;

                    createProjectile(
                        projectileStartPos,
                        projectileDirection,
                        enemy.mesh.userData.attackDamage,
                        projectileSpeed,
                        projectileRange,
                        projectileType
                    );
                }

                // Handle melee damage
                if ((enemy.state === AIStates.ATTACKING || enemy.state === AIStates.SPECIAL_ATTACK) &&
                    enemy.mesh.position.distanceTo(player.position) < enemy.mesh.userData.attackRange) {
                    // Deal damage to player if in attack state and in range
                    const damageMultiplier = enemy.state === AIStates.SPECIAL_ATTACK ? 1.5 : 1.0;
                    const damageDealt = Math.max(1, enemy.mesh.userData.attackDamage * damageMultiplier - playerDefense / 2);
                    playerHealth -= damageDealt;
                    statsDamageTaken += damageDealt;
                    if (playerHealth < 0) playerHealth = 0;

                    // Create damage text
                    createDamageText(player.position, damageDealt);
                }
            } catch (error) {
                console.error(`Error updating AI brain for ${enemy.mesh.name}:`, error);
                // Fall back to local AI
                enemy.useLocalAI = true;
                updateEnemyWithLocalAI(enemy, deltaTime);
            }
        }

        // Apply animation based on enemy state
        function applyAnimationBasedOnState(enemy) {
            switch (enemy.state) {
                case AIStates.IDLE:
                    applyIdleAnimationToEnemy(enemy.mesh);
                    break;
                case AIStates.MOVING:
                case AIStates.STALKING:
                    applyWalkAnimationToEnemy(enemy.mesh);
                    break;
                case AIStates.FLEEING:
                    applyWalkAnimationToEnemy(enemy.mesh);
                    break;
                case AIStates.STRAFING:
                    applyWalkAnimationToEnemy(enemy.mesh);
                    break;
                case AIStates.AIMING:
                    applyAimAnimationToEnemy(enemy.mesh);
                    break;
                case AIStates.SHOOTING:
                    applyShootAnimationToEnemy(enemy.mesh);
                    break;
                case AIStates.ATTACKING:
                case AIStates.SPECIAL_ATTACK:
                    applyAttackAnimationToEnemy(enemy.mesh);
                    break;
                case AIStates.DODGING:
                    applyDodgeAnimationToEnemy(enemy.mesh);
                    break;
                case AIStates.KNOCKBACK:
                    applyHitReactionToEnemy(enemy.mesh);
                    break;
                case AIStates.BLOCKING:
                    // Apply block animation (not implemented yet)
                    applyIdleAnimationToEnemy(enemy.mesh);
                    break;
                case AIStates.CHARGING:
                    applyWalkAnimationToEnemy(enemy.mesh);
                    break;
                default:
                    applyIdleAnimationToEnemy(enemy.mesh);
                    break;
            }
        }

        // Update enemy with local AI implementation
        function updateEnemyWithLocalAI(enemy, deltaTime) {
            // Ensure enemy stays on the floor
            if (enemy.mesh.position.y !== 0) {
                enemy.mesh.position.y = 0;
            }

            // Keep enemy within floor bounds
            if (enemy.mesh.position.x < floorBounds.min.x) enemy.mesh.position.x = floorBounds.min.x;
            if (enemy.mesh.position.x > floorBounds.max.x) enemy.mesh.position.x = floorBounds.max.x;
            if (enemy.mesh.position.z < floorBounds.min.z) enemy.mesh.position.z = floorBounds.min.z;
            if (enemy.mesh.position.z > floorBounds.max.z) enemy.mesh.position.z = floorBounds.max.z;

            // Calculate distance to player
            const distanceToPlayer = enemy.mesh.position.distanceTo(player.position);

            // Look at player
            enemy.mesh.lookAt(player.position);

            // Update enemy state based on distance to player
            if (enemy.type === 'archer') {
                // Archer AI (Ranged Combat AI)
                const minRange = 3.0; // Minimum preferred distance
                const maxRange = enemy.mesh.userData.attackRange;
                const preferredRange = 6.0; // Optimal firing range

                if (distanceToPlayer < minRange) {
                    // Too close, move away
                    enemy.state = AIStates.FLEEING;
                    enemy.stateTimer = 1.0;
                } else if (distanceToPlayer <= maxRange) {
                    // In range to attack
                    if (enemy.attackCooldown <= 0) {
                        // Ready to attack
                        enemy.state = AIStates.AIMING;
                        enemy.stateTimer = 0.5;

                        // After aiming, shoot
                        setTimeout(() => {
                            if (enemy.state === AIStates.AIMING) {
                                enemy.state = AIStates.SHOOTING;
                                enemy.stateTimer = 0.5;
                                enemy.attackCooldown = enemy.mesh.userData.attackCooldown;

                                // Create projectile
                                const projectileStartPos = enemy.mesh.position.clone();
                                projectileStartPos.y += 0.5; // Adjust height
                                const projectileDirection = player.position.clone().sub(projectileStartPos).normalize();
                                createProjectile(
                                    projectileStartPos,
                                    projectileDirection,
                                    enemy.mesh.userData.attackDamage,
                                    8.0, // Speed
                                    enemy.mesh.userData.attackRange,
                                    'arrow'
                                );
                            }
                        }, 500);
                    } else if (Math.random() < 0.3 && distanceToPlayer < preferredRange) {
                        // Sometimes strafe to maintain distance
                        enemy.state = AIStates.STRAFING;
                        enemy.stateTimer = 0.8;
                        enemy.strafeDirection = Math.random() > 0.5 ? 1 : -1; // Left or right
                    } else {
                        // Not ready to attack, just aim or move to preferred range
                        if (Math.abs(distanceToPlayer - preferredRange) < 1.0) {
                            enemy.state = AIStates.AIMING;
                        } else if (distanceToPlayer < preferredRange) {
                            enemy.state = AIStates.FLEEING;
                            enemy.stateTimer = 0.5;
                        } else {
                            enemy.state = AIStates.MOVING;
                        }
                    }
                } else {
                    // Too far, move closer
                    enemy.state = AIStates.MOVING;
                }
            } else if (enemy.type === 'warrior') {
                // Warrior AI (Melee Combat AI)
                const attackRange = enemy.mesh.userData.attackRange;
                const chargeRange = attackRange * 2.0;

                if (distanceToPlayer < attackRange) {
                    // In range to attack
                    if (enemy.attackCooldown <= 0) {
                        // Ready to attack
                        enemy.state = AIStates.ATTACKING;
                        enemy.stateTimer = 0.5;
                        enemy.attackCooldown = enemy.mesh.userData.attackCooldown;

                        // Deal damage to player
                        const damageDealt = Math.max(1, enemy.mesh.userData.attackDamage - playerDefense / 2);
                        playerHealth -= damageDealt;
                        statsDamageTaken += damageDealt;
                        if (playerHealth < 0) playerHealth = 0;

                        // Create damage text
                        createDamageText(player.position, damageDealt);
                    } else if (Math.random() < 0.3) {
                        // Sometimes block after attacking
                        enemy.state = AIStates.BLOCKING;
                        enemy.stateTimer = 0.8;
                    } else {
                        // Not ready to attack, just idle or circle
                        if (Math.random() < 0.4) {
                            enemy.state = AIStates.STRAFING;
                            enemy.stateTimer = 0.8;
                            enemy.strafeDirection = Math.random() > 0.5 ? 1 : -1; // Left or right
                        } else {
                            enemy.state = AIStates.IDLE;
                        }
                    }
                } else if (distanceToPlayer < chargeRange && enemy.attackCooldown <= 0 && Math.random() < 0.4) {
                    // In charge range and ready to attack, consider charging
                    enemy.state = AIStates.CHARGING;
                    enemy.stateTimer = 0.8;
                } else {
                    // Too far, move closer
                    enemy.state = AIStates.MOVING;
                }
            } else if (enemy.type === 'boss') {
                // Boss AI (Enhanced Melee Combat AI with special attacks)
                const attackRange = enemy.mesh.userData.attackRange;
                const specialAttackRange = attackRange * 1.5;

                if (distanceToPlayer < attackRange) {
                    // In range to attack
                    if (enemy.attackCooldown <= 0) {
                        // Ready to attack - decide between normal and special attack
                        if (Math.random() < 0.3) {
                            // Special attack
                            enemy.state = AIStates.SPECIAL_ATTACK;
                            enemy.stateTimer = 1.0;
                            enemy.attackCooldown = enemy.mesh.userData.attackCooldown * 1.5;

                            // Deal more damage with special attack
                            const damageDealt = Math.max(1, enemy.mesh.userData.attackDamage * 1.5 - playerDefense / 2);
                            playerHealth -= damageDealt;
                            statsDamageTaken += damageDealt;
                            if (playerHealth < 0) playerHealth = 0;

                            // Create damage text
                            createDamageText(player.position, damageDealt);
                        } else {
                            // Normal attack
                            enemy.state = AIStates.ATTACKING;
                            enemy.stateTimer = 0.5;
                            enemy.attackCooldown = enemy.mesh.userData.attackCooldown;

                            // Deal damage to player
                            const damageDealt = Math.max(1, enemy.mesh.userData.attackDamage - playerDefense / 2);
                            playerHealth -= damageDealt;
                            statsDamageTaken += damageDealt;
                            if (playerHealth < 0) playerHealth = 0;

                            // Create damage text
                            createDamageText(player.position, damageDealt);
                        }
                    } else if (Math.random() < 0.4) {
                        // Sometimes block or dodge after attacking
                        if (Math.random() < 0.5) {
                            enemy.state = AIStates.BLOCKING;
                            enemy.stateTimer = 0.8;
                        } else {
                            enemy.state = AIStates.DODGING;
                            enemy.stateTimer = 0.5;
                        }
                    } else {
                        // Not ready to attack, just idle or circle
                        if (Math.random() < 0.6) {
                            enemy.state = AIStates.STRAFING;
                            enemy.stateTimer = 0.8;
                            enemy.strafeDirection = Math.random() > 0.5 ? 1 : -1; // Left or right
                        } else {
                            enemy.state = AIStates.IDLE;
                        }
                    }
                } else if (distanceToPlayer < specialAttackRange && enemy.attackCooldown <= 0 && Math.random() < 0.6) {
                    // In special attack range and ready to attack, consider charging
                    enemy.state = AIStates.CHARGING;
                    enemy.stateTimer = 0.8;
                } else {
                    // Too far, move closer
                    enemy.state = AIStates.MOVING;
                }
            } else if (enemy.type === 'assassin') {
                // Assassin AI (Stealth and ambush tactics)
                const attackRange = enemy.mesh.userData.attackRange;
                const stalkRange = attackRange * 3.0;

                // Check if behind player (for backstab bonus)
                const playerDirection = new THREE.Vector3(0, 0, -1).applyQuaternion(player.quaternion);
                const toEnemyDirection = enemy.mesh.position.clone().sub(player.position).normalize();
                const isBehindPlayer = playerDirection.dot(toEnemyDirection) > 0.5;

                // Check if player is looking at enemy (for stealth)
                const isSpottedByPlayer = playerDirection.dot(toEnemyDirection) < -0.7;

                // Track if assassin is in stealth mode
                if (!enemy.isStealthed) {
                    enemy.isStealthed = Math.random() < 0.3;
                }

                if (distanceToPlayer < attackRange) {
                    // In range to attack
                    if (enemy.attackCooldown <= 0) {
                        // Ready to attack
                        enemy.state = AIStates.ATTACKING;
                        enemy.stateTimer = 0.5;
                        enemy.attackCooldown = enemy.mesh.userData.attackCooldown;

                        // Deal damage to player (bonus damage if behind)
                        const backstabMultiplier = isBehindPlayer ? 1.5 : 1.0;
                        const stealthMultiplier = enemy.isStealthed ? 1.3 : 1.0;
                        const damageDealt = Math.max(1, enemy.mesh.userData.attackDamage * backstabMultiplier * stealthMultiplier - playerDefense / 2);

                        playerHealth -= damageDealt;
                        statsDamageTaken += damageDealt;
                        if (playerHealth < 0) playerHealth = 0;

                        // Create damage text
                        createDamageText(player.position, damageDealt);

                        // Exit stealth after attacking
                        enemy.isStealthed = false;
                    } else if (Math.random() < 0.6) {
                        // Frequently dodge
                        enemy.state = AIStates.DODGING;
                        enemy.stateTimer = 0.5;
                    } else {
                        // Not ready to attack, just idle or circle
                        if (Math.random() < 0.7) {
                            enemy.state = AIStates.STRAFING;
                            enemy.stateTimer = 0.8;
                            enemy.strafeDirection = Math.random() > 0.5 ? 1 : -1; // Left or right
                        } else {
                            enemy.state = AIStates.IDLE;
                        }
                    }
                } else if (distanceToPlayer < stalkRange) {
                    // In stalking range
                    if (isSpottedByPlayer && Math.random() < 0.4) {
                        // If spotted, consider vanishing/dodging
                        enemy.state = AIStates.DODGING;
                        enemy.stateTimer = 0.8;
                        enemy.isStealthed = true;
                    } else if (isBehindPlayer) {
                        // If behind player, stalk closer
                        enemy.state = AIStates.STALKING;
                        enemy.stateTimer = 1.0;
                    } else {
                        // Try to get behind player
                        enemy.state = AIStates.MOVING;

                        // Set a target position behind the player
                        const behindPlayerPos = player.position.clone().add(playerDirection.multiplyScalar(2));
                        enemy.targetPosition = behindPlayerPos;
                    }
                } else {
                    // Too far, move closer
                    enemy.state = AIStates.MOVING;
                }
            }

            // Execute enemy state
            switch (enemy.state) {
                case AIStates.IDLE:
                    // Apply idle animation
                    applyIdleAnimationToEnemy(enemy.mesh);

                    // Face player
                    enemy.mesh.lookAt(player.position);
                    break;

                case AIStates.MOVING:
                    // Move towards player
                    const direction = player.position.clone().sub(enemy.mesh.position).normalize();
                    enemy.mesh.position.add(direction.multiplyScalar(enemy.mesh.userData.speed * deltaTime));

                    // Face direction of movement
                    enemy.mesh.lookAt(player.position);

                    // Apply walk animation
                    applyWalkAnimationToEnemy(enemy.mesh);
                    break;

                case AIStates.FLEEING:
                    // Move away from player
                    const fleeDirection = enemy.mesh.position.clone().sub(player.position).normalize();
                    enemy.mesh.position.add(fleeDirection.multiplyScalar(enemy.mesh.userData.speed * 1.5 * deltaTime));

                    // Face direction of movement (backwards)
                    enemy.mesh.lookAt(player.position);

                    // Apply walk animation
                    applyWalkAnimationToEnemy(enemy.mesh);
                    break;

                case AIStates.AIMING:
                    // Face player
                    enemy.mesh.lookAt(player.position);

                    // Apply aim animation
                    applyAimAnimationToEnemy(enemy.mesh);
                    break;

                case AIStates.SHOOTING:
                    // Face player
                    enemy.mesh.lookAt(player.position);

                    // Apply shoot animation
                    applyShootAnimationToEnemy(enemy.mesh);
                    break;

                case AIStates.ATTACKING:
                    // Face player
                    enemy.mesh.lookAt(player.position);

                    // Apply attack animation
                    applyAttackAnimationToEnemy(enemy.mesh);
                    break;

                case AIStates.DODGING:
                    // Move to the side
                    const sideDirection = new THREE.Vector3(direction.z, 0, -direction.x);
                    enemy.mesh.position.add(sideDirection.multiplyScalar(enemy.mesh.userData.speed * 2 * deltaTime));

                    // Face player
                    enemy.mesh.lookAt(player.position);

                    // Apply dodge animation
                    applyDodgeAnimationToEnemy(enemy.mesh);
                    break;
            }
        }

        // Apply animations to enemy - using the same implementation as the main game
        function applyIdleAnimationToEnemy(enemy) {
            const leftLeg = enemy.getObjectByName('leftLeg');
            const rightLeg = enemy.getObjectByName('rightLeg');
            const leftArm = enemy.getObjectByName('leftArm');
            const rightArm = enemy.getObjectByName('rightArm');
            const torso = enemy.getObjectByName('torso');

            if (!leftLeg || !rightLeg) return;

            const time = performance.now() / 1000;
            const idleSpeed = 1.5;
            const idleBobAmplitude = 0.05;
            const idleSwayAmplitude = Math.PI / 32;

            // Subtle idle movement
            const bobOffset = Math.sin(time * idleSpeed) * idleBobAmplitude;
            const swayOffset = Math.sin(time * idleSpeed * 0.7) * idleSwayAmplitude;

            // Apply to limbs
            if (leftLeg) leftLeg.rotation.x = swayOffset;
            if (rightLeg) rightLeg.rotation.x = -swayOffset;
            if (leftArm) leftArm.rotation.z = -Math.PI / 16 + swayOffset;
            if (rightArm) rightArm.rotation.z = Math.PI / 16 - swayOffset;

            // Subtle torso movement
            if (torso) torso.rotation.x = Math.sin(time * idleSpeed * 0.5) * (idleSwayAmplitude * 0.5);

            // Add slight body movement
            enemy.position.y = bobOffset * 0.1;
        }

        function applyWalkAnimationToEnemy(enemy) {
            const leftLeg = enemy.getObjectByName('leftLeg');
            const rightLeg = enemy.getObjectByName('rightLeg');
            const leftArm = enemy.getObjectByName('leftArm');
            const rightArm = enemy.getObjectByName('rightArm');
            const torso = enemy.getObjectByName('torso');

            if (!leftLeg || !rightLeg) return;

            // Get enemy speed if available, or use default
            const enemyData = enemy.userData || {};
            const walkSpeed = (enemyData.speed || 1.8) * 2.0;
            const walkAmplitude = Math.PI / 10; // Reduced amplitude for more natural walk
            const time = performance.now() / 1000;

            // Walk cycle
            if (leftLeg) leftLeg.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude;
            if (rightLeg) rightLeg.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude;

            // Arms move opposite to legs
            if (leftArm) leftArm.rotation.x = Math.sin(time * walkSpeed + Math.PI) * (walkAmplitude * 0.7);
            if (rightArm) rightArm.rotation.x = Math.sin(time * walkSpeed) * (walkAmplitude * 0.7);

            // Subtle torso movement
            if (torso) {
                torso.rotation.z = Math.sin(time * walkSpeed) * (walkAmplitude * 0.2);
                torso.rotation.x = Math.abs(Math.sin(time * walkSpeed * 2)) * (walkAmplitude * 0.1);
            }

            // Add bob to the whole enemy
            const bobHeight = 0.03;
            enemy.position.y = Math.abs(Math.sin(time * walkSpeed)) * bobHeight;
        }

        function applyAimAnimationToEnemy(enemy) {
            const leftLeg = enemy.getObjectByName('leftLeg');
            const rightLeg = enemy.getObjectByName('rightLeg');
            const leftArm = enemy.getObjectByName('leftArm');
            const rightArm = enemy.getObjectByName('rightArm');
            const torso = enemy.getObjectByName('torso');

            if (!leftLeg || !rightLeg) return;

            const time = performance.now() / 1000;
            const idleSwayAmplitude = Math.PI / 64; // Reduced sway
            const swayOffset = Math.sin(time * 1.0) * idleSwayAmplitude; // Slower sway

            // Add slight breathing animation to prevent looking frozen
            const breathingOffset = Math.sin(time * 1.5) * 0.005;
            enemy.position.y = breathingOffset;

            // Stable stance
            if (leftLeg) leftLeg.rotation.set(Math.PI / 12, 0, -Math.PI / 24); // Slight bend, foot out
            if (rightLeg) rightLeg.rotation.set(-Math.PI / 12, 0, Math.PI / 24); // Slight back, foot out

            // Aiming pose - right arm extended, left arm supporting
            if (leftArm) {
                leftArm.rotation.x = Math.PI / 4; // Arm forward
                leftArm.rotation.z = -Math.PI / 6 + swayOffset; // Arm out with sway
            }

            if (rightArm) {
                rightArm.rotation.x = Math.PI / 2; // Arm forward horizontal
                rightArm.rotation.z = swayOffset; // Slight sway
            }

            // Slight torso lean
            if (torso) torso.rotation.x = Math.PI / 32 + swayOffset / 2;
        }

        function applyShootAnimationToEnemy(enemy) {
            const leftLeg = enemy.getObjectByName('leftLeg');
            const rightLeg = enemy.getObjectByName('rightLeg');
            const leftArm = enemy.getObjectByName('leftArm');
            const rightArm = enemy.getObjectByName('rightArm');
            const torso = enemy.getObjectByName('torso');

            if (!leftLeg || !rightLeg) return;

            const time = performance.now() / 1000;

            // Add slight breathing animation to prevent looking frozen
            const breathingOffset = Math.sin(time * 1.5) * 0.005;
            enemy.position.y = breathingOffset;

            // Similar to aim but with more tension
            if (leftLeg) leftLeg.rotation.set(Math.PI / 10, 0, -Math.PI / 20); // More bend
            if (rightLeg) rightLeg.rotation.set(-Math.PI / 10, 0, Math.PI / 20); // More back

            // Shooting pose - arms in final position
            if (leftArm) {
                leftArm.rotation.x = Math.PI / 3; // Arm more forward
                leftArm.rotation.z = -Math.PI / 5; // Arm more out
            }

            if (rightArm) {
                rightArm.rotation.x = Math.PI / 3; // Arm up higher
                rightArm.rotation.z = Math.PI / 6; // Arm out
            }

            // Torso leans slightly forward
            if (torso) torso.rotation.x = Math.PI / 24;
        }

        function applyAttackAnimationToEnemy(enemy) {
            const leftLeg = enemy.getObjectByName('leftLeg');
            const rightLeg = enemy.getObjectByName('rightLeg');
            const leftArm = enemy.getObjectByName('leftArm');
            const rightArm = enemy.getObjectByName('rightArm');
            const torso = enemy.getObjectByName('torso');

            if (!leftLeg || !rightLeg) return;

            // Stable but forward-leaning stance
            if (leftLeg) leftLeg.rotation.set(Math.PI / 8, 0, -Math.PI / 16); // Forward stance
            if (rightLeg) rightLeg.rotation.set(-Math.PI / 6, 0, Math.PI / 16); // Back leg pushing

            // Attack pose - right arm swinging forward
            if (leftArm) {
                leftArm.rotation.x = Math.PI / 6; // Arm slightly forward
                leftArm.rotation.z = -Math.PI / 8; // Arm slightly out
            }

            if (rightArm) {
                rightArm.rotation.x = Math.PI / 2; // Arm forward horizontal
                rightArm.rotation.z = Math.PI / 8; // Arm slightly out
                rightArm.rotation.y = -Math.PI / 6; // Arm swinging inward
            }

            // Torso leans forward into attack
            if (torso) torso.rotation.x = Math.PI / 16;

            // Slight forward movement
            enemy.position.z += 0.05;
        }

        function applyDodgeAnimationToEnemy(enemy) {
            const leftLeg = enemy.getObjectByName('leftLeg');
            const rightLeg = enemy.getObjectByName('rightLeg');
            const leftArm = enemy.getObjectByName('leftArm');
            const rightArm = enemy.getObjectByName('rightArm');
            const torso = enemy.getObjectByName('torso');

            if (!leftLeg || !rightLeg) return;

            // Dodge to the side (right)
            if (leftLeg) leftLeg.rotation.set(Math.PI / 6, 0, Math.PI / 12); // Leg bent, angled out
            if (rightLeg) rightLeg.rotation.set(-Math.PI / 8, 0, Math.PI / 8); // Pushing off

            // Arms for balance
            if (leftArm) leftArm.rotation.set(0, 0, -Math.PI / 6); // Out for balance
            if (rightArm) rightArm.rotation.set(Math.PI / 8, 0, Math.PI / 4); // Up for balance

            // Torso lean
            if (torso) torso.rotation.z = Math.PI / 10; // Lean to the side

            // Slight body tilt
            enemy.rotation.z = -Math.PI / 16; // Tilt body

            // Add a slight upward movement to simulate jumping/dodging
            enemy.position.y = 0.1;
        }

        function applyHitReactionToEnemy(enemy) {
            const leftLeg = enemy.getObjectByName('leftLeg');
            const rightLeg = enemy.getObjectByName('rightLeg');
            const leftArm = enemy.getObjectByName('leftArm');
            const rightArm = enemy.getObjectByName('rightArm');
            const torso = enemy.getObjectByName('torso');

            if (!leftLeg || !rightLeg) return;

            // Stagger backwards
            if (leftLeg) leftLeg.rotation.x = -Math.PI / 8; // Leg back
            if (rightLeg) rightLeg.rotation.x = -Math.PI / 6; // Leg further back
            if (torso) torso.rotation.x = -Math.PI / 12; // Bend back slightly

            // Arms flail slightly
            if (leftArm) {
                leftArm.rotation.x = Math.PI / 4; // Arm up
                leftArm.rotation.z = -Math.PI / 8; // Arm out
            }

            if (rightArm) {
                rightArm.rotation.x = Math.PI / 3; // Arm up higher
                rightArm.rotation.z = Math.PI / 6; // Arm out
            }

            // Add a slight downward movement to simulate impact
            enemy.position.y = -0.05;
        }

        // Note: applyDodgeAnimationToEnemy is already defined above

        // Variables for stats
        let playerAttack = 15;
        // playerDefense is already declared above
        let playerSpeed = 3;
        let selectedEnemy = null;

        // Load enemy presets from the main game's Areas data
        function loadEnemyPresetsFromGame() {
            const ENEMY_PRESETS = {};

            try {
                // Check if Areas is available
                if (!Areas) {
                    console.warn('Areas data not found, using fallback presets');
                    return getFallbackEnemyPresets();
                }

                // Process each area to create presets
                for (const [areaKey, areaData] of Object.entries(Areas)) {
                    // Skip non-area entries like 'random_pool' or 'end_sequence'
                    if (Array.isArray(areaData) || !areaData.enemies) continue;

                    // Get the first enemy type from the area's enemy list
                    const areaEnemies = areaData.enemies;
                    if (!areaEnemies || areaEnemies.length === 0) continue;

                    // Get the first enemy type (or a random one if multiple)
                    const enemyType = areaEnemies[0].type;

                    // Get enemy data for this type
                    const enemyData = getEnemyData(enemyType);
                    if (!enemyData) continue;

                    // Create preset based on this enemy's stats
                    ENEMY_PRESETS[areaKey] = {
                        health: enemyData.health || 30,
                        attack: enemyData.meleeDamage || enemyData.projectileDamage || 5,
                        defense: Math.floor(enemyData.mass || 2),
                        speed: enemyData.baseSpeed || 1.5,
                        attackRange: enemyData.attackRange || enemyData.preferredRange || 2.0,
                        attackCooldown: enemyData.attackCooldown || 2.0,
                        description: `Enemies from ${areaData.name || areaKey}`
                    };

                    console.log(`Loaded preset for area: ${areaKey}`, ENEMY_PRESETS[areaKey]);
                }

                // Add boss preset if not already included
                if (!ENEMY_PRESETS['boss']) {
                    const bossData = getEnemyData('skeleton_boss');
                    if (bossData) {
                        ENEMY_PRESETS['boss'] = {
                            health: bossData.health || 150,
                            attack: bossData.meleeDamage || bossData.projectileDamage || 25,
                            defense: Math.floor(bossData.mass || 15),
                            speed: bossData.baseSpeed || 2.0,
                            attackRange: bossData.attackRange || bossData.preferredRange || 5.0,
                            attackCooldown: bossData.attackCooldown || 2.5,
                            description: 'Boss-level enemies with high health and damage'
                        };
                    }
                }

                return ENEMY_PRESETS;
            } catch (error) {
                console.error('Error loading enemy presets from game:', error);
                return getFallbackEnemyPresets();
            }
        }

        // Fallback presets if game data can't be loaded
        function getFallbackEnemyPresets() {
            return {
                catacombs: {
                    health: 30,
                    attack: 5,
                    defense: 2,
                    speed: 1.5,
                    attackRange: 2.0,
                    attackCooldown: 2.0,
                    description: 'Basic enemies from the starting area'
                },
                fungal_caverns: {
                    health: 45,
                    attack: 8,
                    defense: 3,
                    speed: 1.7,
                    attackRange: 2.5,
                    attackCooldown: 1.8,
                    description: 'Stronger enemies from the second area'
                },
                flooded_ruins: {
                    health: 60,
                    attack: 12,
                    defense: 5,
                    speed: 1.9,
                    attackRange: 3.0,
                    attackCooldown: 1.6,
                    description: 'Challenging enemies from the third area'
                },
                crystal_caves: {
                    health: 80,
                    attack: 15,
                    defense: 7,
                    speed: 2.1,
                    attackRange: 3.5,
                    attackCooldown: 1.4,
                    description: 'Advanced enemies from the fourth area'
                },
                lava_tubes: {
                    health: 100,
                    attack: 18,
                    defense: 10,
                    speed: 2.3,
                    attackRange: 4.0,
                    attackCooldown: 1.2,
                    description: 'Elite enemies from the fifth area'
                },
                boss: {
                    health: 150,
                    attack: 25,
                    defense: 15,
                    speed: 2.0,
                    attackRange: 5.0,
                    attackCooldown: 2.5,
                    description: 'Boss-level enemies with high health and damage'
                }
            };
        }

        // Load enemy presets from the game
        const ENEMY_PRESETS = loadEnemyPresetsFromGame();

        // Define presets for player stats
        const PLAYER_PRESETS = {
            starting: {
                health: 100,
                attack: 15,
                defense: 5,
                speed: 3.0,
                description: 'Starting character with basic stats'
            },
            mid_game: {
                health: 150,
                attack: 25,
                defense: 10,
                speed: 3.5,
                description: 'Mid-game character with improved stats'
            },
            end_game: {
                health: 200,
                attack: 35,
                defense: 15,
                speed: 4.0,
                description: 'End-game character with powerful stats'
            },
            overpowered: {
                health: 500,
                attack: 50,
                defense: 25,
                speed: 5.0,
                description: 'Overpowered character for testing'
            }
        };

        // Event listener for clear all button
        document.getElementById('clear-all-btn').addEventListener('click', clearAllEnemies);

        // Event listeners for player stats
        document.getElementById('apply-player-stats-btn').addEventListener('click', applyPlayerStats);
        document.getElementById('player-preset-select').addEventListener('change', applyPlayerPreset);

        // Event listeners for enemy stats
        document.getElementById('apply-enemy-stats-btn').addEventListener('click', () => {
            // This will apply stats to new enemies that are spawned
            updateEnemyStatsConfig();
        });
        document.getElementById('enemy-preset-select').addEventListener('change', applyEnemyPreset);
        document.getElementById('apply-to-selected-btn').addEventListener('click', applyStatsToSelectedEnemy);

        // Function to apply player preset
        function applyPlayerPreset() {
            const presetSelect = document.getElementById('player-preset-select');
            const selectedPreset = presetSelect.value;

            if (selectedPreset === 'custom') {
                return; // Keep current values
            }

            const preset = PLAYER_PRESETS[selectedPreset];
            if (!preset) {
                console.warn(`Player preset '${selectedPreset}' not found`);
                return;
            }

            // Update input fields
            document.getElementById('player-health-input').value = preset.health;
            document.getElementById('player-attack-input').value = preset.attack;
            document.getElementById('player-defense-input').value = preset.defense;
            document.getElementById('player-speed-input').value = preset.speed;

            // Apply the stats
            applyPlayerStats();

            console.log(`Applied player preset: ${selectedPreset}`, preset);
        }

        // Function to apply enemy preset
        function applyEnemyPreset() {
            const presetSelect = document.getElementById('enemy-preset-select');
            const selectedPreset = presetSelect.value;

            if (selectedPreset === 'custom') {
                return; // Keep current values
            }

            const preset = ENEMY_PRESETS[selectedPreset];
            if (!preset) {
                console.warn(`Enemy preset '${selectedPreset}' not found`);
                return;
            }

            // Update input fields
            document.getElementById('enemy-health-input').value = preset.health;
            document.getElementById('enemy-attack-input').value = preset.attack;
            document.getElementById('enemy-defense-input').value = preset.defense;
            document.getElementById('enemy-speed-input').value = preset.speed;
            document.getElementById('enemy-attack-range-input').value = preset.attackRange;
            document.getElementById('enemy-attack-cooldown-input').value = preset.attackCooldown;

            // Update the enemy stats configuration
            updateEnemyStatsConfig();

            console.log(`Applied enemy preset: ${selectedPreset}`, preset);
        }

        // Function to apply player stats
        function applyPlayerStats() {
            const health = parseInt(document.getElementById('player-health-input').value) || 100;
            const attack = parseInt(document.getElementById('player-attack-input').value) || 15;
            const defense = parseInt(document.getElementById('player-defense-input').value) || 5;
            const speed = parseFloat(document.getElementById('player-speed-input').value) || 3;

            // Update player stats
            playerHealth = health;
            playerAttack = attack;
            playerDefense = defense;
            playerSpeed = speed;

            // Update player health display
            document.getElementById('player-health-bar').style.width = (playerHealth / 100 * 100) + '%';

            // Update player if it exists
            if (player) {
                player.userData.health = health;
                player.userData.attack = attack;
                player.userData.defense = defense;
                player.userData.speed = speed;
            }

            console.log('Applied player stats:', { health, attack, defense, speed });
        }

        // Function to update enemy stats configuration
        function updateEnemyStatsConfig() {
            // Store the current enemy stats configuration
            window.enemyStatsConfig = {
                health: parseInt(document.getElementById('enemy-health-input').value) || 50,
                attack: parseInt(document.getElementById('enemy-attack-input').value) || 10,
                defense: parseInt(document.getElementById('enemy-defense-input').value) || 3,
                speed: parseFloat(document.getElementById('enemy-speed-input').value) || 2,
                attackRange: parseFloat(document.getElementById('enemy-attack-range-input').value) || 2,
                attackCooldown: parseFloat(document.getElementById('enemy-attack-cooldown-input').value) || 1.5
            };

            console.log('Updated enemy stats configuration:', window.enemyStatsConfig);
        }

        // Function to apply stats to selected enemy
        function applyStatsToSelectedEnemy() {
            // Check if we have a selected enemy
            if (!selectedEnemy) {
                console.warn('No enemy selected');
                return;
            }

            // Get stats from inputs
            const health = parseInt(document.getElementById('enemy-health-input').value) || 50;
            const attack = parseInt(document.getElementById('enemy-attack-input').value) || 10;
            const defense = parseInt(document.getElementById('enemy-defense-input').value) || 3;
            const speed = parseFloat(document.getElementById('enemy-speed-input').value) || 2;
            const attackRange = parseFloat(document.getElementById('enemy-attack-range-input').value) || 2;
            const attackCooldown = parseFloat(document.getElementById('enemy-attack-cooldown-input').value) || 1.5;

            // Apply stats to the selected enemy
            selectedEnemy.health = health;
            selectedEnemy.mesh.userData.attackDamage = attack;
            selectedEnemy.mesh.userData.defense = defense;
            selectedEnemy.mesh.userData.speed = speed;
            selectedEnemy.mesh.userData.attackRange = attackRange;
            selectedEnemy.mesh.userData.attackCooldown = attackCooldown;

            // Update the enemy info display
            const infoElement = document.getElementById('selected-enemy-info');
            infoElement.innerHTML = `
                <div>Selected: ${selectedEnemy.mesh.name}</div>
                <div>Health: ${selectedEnemy.health}/${selectedEnemy.mesh.userData.health}</div>
                <div>Attack: ${selectedEnemy.mesh.userData.attackDamage}</div>
                <div>Defense: ${selectedEnemy.mesh.userData.defense || 0}</div>
                <div>Speed: ${selectedEnemy.mesh.userData.speed}</div>
                <div>Range: ${selectedEnemy.mesh.userData.attackRange}</div>
            `;

            console.log('Applied stats to selected enemy:', selectedEnemy);
        }

        // Initialize enemy stats configuration
        updateEnemyStatsConfig();

        // Function to check for texture flickering with detailed diagnostics
        function checkTextureFlickering() {
            const textureReport = document.getElementById('texture-report');
            textureReport.innerHTML = '<div>Analyzing textures...</div>';

            // Get all meshes in the scene
            const meshes = [];
            scene.traverse(object => {
                if (object.isMesh && object.material) {
                    meshes.push(object);
                }
            });

            // Check for potential texture issues
            const issues = [];
            const detailedReport = [];

            meshes.forEach(mesh => {
                // Check for missing textures
                if (mesh.material.map === null && mesh.material.color === undefined) {
                    const issue = {
                        type: 'missing_texture',
                        mesh: mesh.name || 'Unnamed mesh',
                        uuid: mesh.uuid,
                        position: `${mesh.position.x.toFixed(2)}, ${mesh.position.y.toFixed(2)}, ${mesh.position.z.toFixed(2)}`,
                        fix: 'Add a texture map or set a material color'
                    };
                    issues.push(`${issue.mesh}: Missing texture and color at position [${issue.position}]`);
                    detailedReport.push(issue);
                }

                // Check for overlapping UVs (potential flickering cause)
                if (mesh.geometry && mesh.geometry.attributes.uv) {
                    const uvs = mesh.geometry.attributes.uv.array;
                    const positions = mesh.geometry.attributes.position.array;
                    const {overlaps, details} = checkUVOverlaps(uvs, positions);

                    if (overlaps > 0) {
                        const issue = {
                            type: 'uv_overlap',
                            mesh: mesh.name || 'Unnamed mesh',
                            uuid: mesh.uuid,
                            position: `${mesh.position.x.toFixed(2)}, ${mesh.position.y.toFixed(2)}, ${mesh.position.z.toFixed(2)}`,
                            count: overlaps,
                            details: details,
                            fix: 'Unwrap UVs with padding between islands or use texture atlas'
                        };
                        issues.push(`${issue.mesh}: ${overlaps} UV overlaps at position [${issue.position}]`);
                        detailedReport.push(issue);
                    }
                }

                // Check for z-fighting (another cause of flickering)
                meshes.forEach(otherMesh => {
                    if (mesh !== otherMesh) {
                        const distance = mesh.position.distanceTo(otherMesh.position);
                        if (distance < 0.01) {
                            // Calculate the exact distance and check if they share the same space
                            const meshBounds = new THREE.Box3().setFromObject(mesh);
                            const otherBounds = new THREE.Box3().setFromObject(otherMesh);
                            const boundsIntersect = meshBounds.intersectsBox(otherBounds);

                            if (boundsIntersect) {
                                const issue = {
                                    type: 'z_fighting',
                                    mesh1: {
                                        name: mesh.name || 'Unnamed mesh',
                                        uuid: mesh.uuid,
                                        position: `${mesh.position.x.toFixed(2)}, ${mesh.position.y.toFixed(2)}, ${mesh.position.z.toFixed(2)}`
                                    },
                                    mesh2: {
                                        name: otherMesh.name || 'Unnamed mesh',
                                        uuid: otherMesh.uuid,
                                        position: `${otherMesh.position.x.toFixed(2)}, ${otherMesh.position.y.toFixed(2)}, ${otherMesh.position.z.toFixed(2)}`
                                    },
                                    distance: distance.toFixed(4),
                                    fix: `Offset one mesh by adding a small bias (0.01) to its position or adjust the material's polygonOffset property`
                                };
                                issues.push(`Z-fighting between ${issue.mesh1.name} and ${issue.mesh2.name} (distance: ${issue.distance}) at positions [${issue.mesh1.position}] and [${issue.mesh2.position}]`);
                                detailedReport.push(issue);
                            }
                        }
                    }
                });

                // Check for material issues
                if (mesh.material) {
                    // Check for transparent materials without proper sorting
                    if (mesh.material.transparent && !mesh.material.depthWrite) {
                        const issue = {
                            type: 'transparency_issue',
                            mesh: mesh.name || 'Unnamed mesh',
                            uuid: mesh.uuid,
                            position: `${mesh.position.x.toFixed(2)}, ${mesh.position.y.toFixed(2)}, ${mesh.position.z.toFixed(2)}`,
                            fix: 'For transparent materials, set material.depthWrite = true or ensure proper render order'
                        };
                        issues.push(`${issue.mesh}: Transparent material with depthWrite disabled at position [${issue.position}]`);
                        detailedReport.push(issue);
                    }

                    // Check for mipmapping issues
                    if (mesh.material.map && !mesh.material.map.generateMipmaps) {
                        const issue = {
                            type: 'mipmap_issue',
                            mesh: mesh.name || 'Unnamed mesh',
                            uuid: mesh.uuid,
                            position: `${mesh.position.x.toFixed(2)}, ${mesh.position.y.toFixed(2)}, ${mesh.position.z.toFixed(2)}`,
                            fix: 'Enable mipmaps for the texture with texture.generateMipmaps = true'
                        };
                        issues.push(`${issue.mesh}: Texture without mipmaps at position [${issue.position}]`);
                        detailedReport.push(issue);
                    }
                }
            });

            // Display results
            if (issues.length === 0) {
                textureReport.innerHTML = '<div style="color: #8BC34A">No texture issues detected</div>';
            } else {
                textureReport.innerHTML = '<div style="color: #FF5722">Potential texture issues found:</div>';
                issues.forEach(issue => {
                    textureReport.innerHTML += `<div>- ${issue}</div>`;
                });

                // Generate code to fix the issues
                let fixCode = '// Copy this code to fix the texture flickering issues\n';
                detailedReport.forEach(issue => {
                    switch(issue.type) {
                        case 'z_fighting':
                            fixCode += `\n// Fix z-fighting between ${issue.mesh1.name} and ${issue.mesh2.name}\n`;
                            fixCode += `// Option 1: Offset one of the meshes\n`;
                            fixCode += `scene.getObjectByProperty('uuid', '${issue.mesh1.uuid}').position.z += 0.01;\n`;
                            fixCode += `// Option 2: Use polygon offset\n`;
                            fixCode += `const material = scene.getObjectByProperty('uuid', '${issue.mesh1.uuid}').material;\n`;
                            fixCode += `material.polygonOffset = true;\n`;
                            fixCode += `material.polygonOffsetFactor = 1;\n`;
                            fixCode += `material.polygonOffsetUnits = 1;\n`;
                            break;

                        case 'missing_texture':
                            fixCode += `\n// Fix missing texture for ${issue.mesh}\n`;
                            fixCode += `const mesh = scene.getObjectByProperty('uuid', '${issue.uuid}');\n`;
                            fixCode += `mesh.material.color = new THREE.Color(0x808080); // Set a default color\n`;
                            break;

                        case 'uv_overlap':
                            fixCode += `\n// Fix UV overlaps for ${issue.mesh}\n`;
                            fixCode += `// This requires UV unwrapping in a 3D modeling software\n`;
                            fixCode += `// Alternatively, add padding between UV islands:\n`;
                            fixCode += `const mesh = scene.getObjectByProperty('uuid', '${issue.uuid}');\n`;
                            fixCode += `// Scale UVs to add padding\n`;
                            fixCode += `const uvs = mesh.geometry.attributes.uv.array;\n`;
                            fixCode += `for (let i = 0; i < uvs.length; i += 2) {\n`;
                            fixCode += `  // Scale around UV center (0.5, 0.5)\n`;
                            fixCode += `  uvs[i] = (uvs[i] - 0.5) * 0.95 + 0.5;\n`;
                            fixCode += `  uvs[i+1] = (uvs[i+1] - 0.5) * 0.95 + 0.5;\n`;
                            fixCode += `}\n`;
                            fixCode += `mesh.geometry.attributes.uv.needsUpdate = true;\n`;
                            break;

                        case 'transparency_issue':
                            fixCode += `\n// Fix transparency issue for ${issue.mesh}\n`;
                            fixCode += `const mesh = scene.getObjectByProperty('uuid', '${issue.uuid}');\n`;
                            fixCode += `mesh.material.depthWrite = true;\n`;
                            fixCode += `mesh.renderOrder = 1; // Adjust as needed\n`;
                            break;

                        case 'mipmap_issue':
                            fixCode += `\n// Fix mipmap issue for ${issue.mesh}\n`;
                            fixCode += `const mesh = scene.getObjectByProperty('uuid', '${issue.uuid}');\n`;
                            fixCode += `if (mesh.material.map) {\n`;
                            fixCode += `  mesh.material.map.generateMipmaps = true;\n`;
                            fixCode += `  mesh.material.map.needsUpdate = true;\n`;
                            fixCode += `}\n`;
                            break;
                    }
                });

                // Store the fix code for copying
                window.textureFixCode = fixCode;

                // Add a button to copy the fix code
                textureReport.innerHTML += `<div style="margin-top: 10px;"><button id="copy-fix-code-btn">Copy Fix Code</button></div>`;

                // Add event listener to copy button
                setTimeout(() => {
                    const copyBtn = document.getElementById('copy-fix-code-btn');
                    if (copyBtn) {
                        copyBtn.addEventListener('click', () => {
                            navigator.clipboard.writeText(window.textureFixCode)
                                .then(() => {
                                    alert('Fix code copied to clipboard!');
                                })
                                .catch(err => {
                                    console.error('Failed to copy: ', err);
                                    alert('Fix code:\n\n' + window.textureFixCode);
                                });
                        });
                    }
                }, 100);
            }
        }

        // Helper function to check for UV overlaps with detailed information
        function checkUVOverlaps(uvs, positions) {
            let overlaps = 0;
            const uvPositions = [];
            const details = [];

            // Sample a subset of UVs for performance
            const sampleSize = Math.min(uvs.length / 2, 100);
            const step = Math.floor(uvs.length / 2 / sampleSize);

            for (let i = 0; i < uvs.length; i += step * 2) {
                const u = uvs[i];
                const v = uvs[i + 1];
                const posIndex = Math.floor(i / 2) * 3;

                // Skip if we're out of bounds
                if (posIndex >= positions.length) continue;

                const x = positions[posIndex];
                const y = positions[posIndex + 1];
                const z = positions[posIndex + 2];

                // Check if this UV coordinate is used by multiple distant vertices
                for (let j = 0; j < uvPositions.length; j++) {
                    const existing = uvPositions[j];
                    if (Math.abs(existing.u - u) < 0.01 && Math.abs(existing.v - v) < 0.01) {
                        // Same UV, check if positions are far apart
                        const dist = Math.sqrt(
                            Math.pow(existing.x - x, 2) +
                            Math.pow(existing.y - y, 2) +
                            Math.pow(existing.z - z, 2)
                        );

                        if (dist > 0.1) {
                            overlaps++;

                            // Store detailed information about this overlap
                            details.push({
                                uvCoord: `(${u.toFixed(3)}, ${v.toFixed(3)})`,
                                vertex1: `(${existing.x.toFixed(2)}, ${existing.y.toFixed(2)}, ${existing.z.toFixed(2)})`,
                                vertex2: `(${x.toFixed(2)}, ${y.toFixed(2)}, ${z.toFixed(2)})`,
                                distance: dist.toFixed(3),
                                vertexIndex1: j * step * 2,
                                vertexIndex2: i
                            });

                            // Limit the number of detailed reports to avoid overwhelming
                            if (details.length >= 10) break;
                        }
                    }
                }

                uvPositions.push({ u, v, x, y, z });
            }

            return { overlaps, details };
        }

        // Event listener for texture check button
        document.getElementById('check-textures-btn').addEventListener('click', checkTextureFlickering);

        // Display mode toggle buttons
        document.getElementById('analysis-mode-btn').addEventListener('click', () => {
            document.getElementById('analysis-panel').style.display = 'block';
            document.getElementById('reference-panel').style.display = 'none';
            document.getElementById('reference-controls').style.display = 'none';
            document.getElementById('analysis-mode-btn').classList.add('active');
            document.getElementById('reference-mode-btn').classList.remove('active');
        });

        document.getElementById('reference-mode-btn').addEventListener('click', () => {
            document.getElementById('analysis-panel').style.display = 'none';
            document.getElementById('reference-panel').style.display = 'block';
            document.getElementById('reference-controls').style.display = 'block';
            document.getElementById('analysis-mode-btn').classList.remove('active');
            document.getElementById('reference-mode-btn').classList.add('active');

            // Update reference display
            updateReferenceDisplay();
        });

        document.getElementById('back-to-analysis-btn').addEventListener('click', () => {
            document.getElementById('analysis-panel').style.display = 'block';
            document.getElementById('reference-panel').style.display = 'none';
            document.getElementById('reference-controls').style.display = 'none';
            document.getElementById('analysis-mode-btn').classList.add('active');
            document.getElementById('reference-mode-btn').classList.remove('active');
        });

        // Reference control buttons
        document.getElementById('capture-animation-btn').addEventListener('click', () => {
            if (reference.isCapturing()) {
                reference.stopCapturing();
                document.getElementById('capture-animation-btn').textContent = 'Capture Animation';
            } else {
                // Update capture interval from input
                const captureInterval = parseFloat(document.getElementById('capture-interval-input').value) || 0.33;
                reference.config.captureInterval = captureInterval;

                reference.startCapturing(currentState);
                document.getElementById('capture-animation-btn').textContent = 'Stop Capturing';

                // Update reference display after a short delay
                setTimeout(updateReferenceDisplay, 100);
            }
        });

        document.getElementById('clear-frames-btn').addEventListener('click', () => {
            reference.clearFrames();
            updateReferenceDisplay();
        });

        // Function to update the reference display
        function updateReferenceDisplay() {
            const referenceContainer = document.getElementById('reference-container');
            referenceContainer.innerHTML = reference.generateReferenceHTML();
        }

        // Initialize
        animate();
    </script>
</body>
</html>
