# Reset Boss Timeline

If you're experiencing issues with the boss timeline not progressing correctly, you can use this command to reset the timeline and start fresh.

## Reset Command

Copy and paste this entire command into your browser console (F12) when you're in the catacomb boss room:

```javascript
// Reset boss timeline and start fresh
(function resetBossTimeline() {
    try {
        // Get references to key objects
        const boss = game.dungeonHandler.currentRoom.boss;
        if (!boss) {
            console.error("[RESET] No boss found in current room. Try teleporting to the boss first with F10.");
            return;
        }
        
        const bossController = boss.bossController;
        if (!bossController) {
            console.error("[RESET] Boss controller not found.");
            return;
        }
        
        // Reset the music analyzer
        if (bossController.musicAnalyzer) {
            console.log("[RESET] Stopping music analyzer...");
            bossController.musicAnalyzer.stop();
            
            // Reset the start time
            bossController.musicAnalyzer._startTime = Date.now();
            
            console.log("[RESET] Starting music analyzer...");
            bossController.musicAnalyzer.start();
        }
        
        // Reload the timeline
        console.log("[RESET] Reloading timeline...");
        bossController.loadTimeline("epic_battle", false);
        
        // Force an update to the current music time
        const musicTime = bossController.musicAnalyzer.getCurrentTime();
        bossController.musicTimeline.update(musicTime);
        
        // Verify current state
        console.log("[RESET] Current music time:", musicTime);
        console.log("[RESET] Current timeline time:", bossController.musicTimeline.currentTime);
        console.log("[RESET] Current pattern:", bossController.musicTimeline.currentPattern);
        console.log("[RESET] Current projectile:", bossController.musicTimeline.currentProjectileType);
        
        // Success message
        console.log("[RESET] Boss timeline successfully reset!");
        
    } catch (error) {
        console.error("[RESET] Error resetting boss timeline:", error);
    }
})();
```

## How to Use

1. Start a game and enter the catacomb boss room
   - If you're not at the boss, press F10 or T to teleport there
2. Press F12 to open the browser's developer console
3. Copy the entire command above and paste it into the console
4. Press Enter to execute the command
5. Check the console for confirmation messages

## What This Does

This command:

1. Stops the music analyzer
2. Resets the start time to the current time
3. Restarts the music analyzer
4. Reloads the epic battle timeline
5. Forces an update to the current music time

This effectively resets the timeline to start from the beginning, which should fix issues with the timeline not progressing correctly.

## Troubleshooting

If you still experience issues:

1. Make sure you're in a room with a boss
2. Try teleporting to the boss with F10 before running the command
3. Check the console for error messages
4. Try reloading the page and running the command again
